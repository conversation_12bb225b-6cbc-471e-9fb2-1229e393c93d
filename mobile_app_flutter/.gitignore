<<<<<<< HEAD
# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
=======
# Dependencies
node_modules/
/.pnp
.pnp.js

# Cache directories
.cache/
*.cache/

# Testing
/coverage

# Production build
# Note: We'll include build folder for GitHub Pages deployment
# /build

# Large files and cache
*.pack
*.tsbuildinfo

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
Thumbs.db

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
>>>>>>> 0b92c423baeef737e689d8b15a77a3401c75eef0
