.category-grid {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding: 2rem;
}

.category-card {
  flex: 0 0 auto;
  width: calc(33.33% - 1rem);
  background: white;
  border-radius: 10px;
  padding: 2rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer;
  margin-bottom: 1rem;
}

@media (max-width: 1200px) {
  .category-card {
    width: calc(50% - 1rem);
  }
}

@media (max-width: 768px) {
  .category-card {
    width: 100%;
  }
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.category-icon {
  margin-bottom: 1rem;
  color: #2196f3;
}

.category-card h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin: 1rem 2rem;
  color: #333;
}

.nested-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
  margin-left: 2rem;
  padding: 1rem;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border-left: 3px solid #2196f3;
  width: calc(100% - 2rem);
}

.nested-card {
  padding: 1.5rem;
  background-color: #f9f9f9;
}

.nested-card h3 {
  font-size: 1rem;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  transition: opacity 0.2s ease;
}

.page-header:hover {
  opacity: 0.8;
}

.current-card-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2em;
}

.card-page-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: white;
  border-radius: 10px;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: opacity 0.2s;
}

.card-page-header:hover {
  opacity: 0.8;
}

.card-page-header .card-icon {
  font-size: 2.5rem;
}

.card-page-header h2 {
  margin: 0;
  color: #333;
}

.child-cards-container {
  padding: 2rem;
}

.child-cards-container h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.breadcrumb-navigation {
  padding: 10px 0;
  margin-bottom: 20px;
  color: #666;
}

.breadcrumb-item {
  color: #1976d2;
  text-decoration: none;
}

.breadcrumb-item:hover {
  text-decoration: underline;
}


/* Add these styles for page content */
.page-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sections-container {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.section-item {
  margin-bottom: 15px;
}

.section-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.section-text {
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: white;
}

.btn-primary {
  padding: 8px 16px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: #0069d9;
}

.no-content-message {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: center;
  color: #666;
}


/* Add these styles for the new form fields */

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-item, .checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.radio-item input[type="radio"],
.checkbox-item input[type="checkbox"] {
  margin: 0;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  margin-top: 15px;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
  color: #333;
}

.file-upload {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.file-input {
  width: 100%;
  padding: 8px 0;
}

.file-label {
  display: inline-block;
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  font-size: 14px;
}

.file-label:hover {
  background-color: #e9e9e9;
}

input[type="number"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

input[type="date"] {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

/* Office access validation styles */
.access-loading {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.access-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.access-denied {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
}

.access-denied h3 {
  margin: 20px 0 10px 0;
  color: #333;
  font-size: 1.5rem;
}

.access-denied p {
  margin: 0 0 30px 0;
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

.back-to-forms-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.back-to-forms-btn:hover {
  background: #0056b3;
}