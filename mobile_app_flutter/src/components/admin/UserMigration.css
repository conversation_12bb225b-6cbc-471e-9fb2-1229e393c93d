.user-migration {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
}

.migration-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.migration-header h2 {
  margin: 0 0 10px 0;
  font-size: 2rem;
  font-weight: 600;
}

.migration-header p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.migration-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.action-card {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.action-card h3 {
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  color: #333;
}

.action-card p {
  margin: 0 0 15px 0;
  color: #666;
  line-height: 1.5;
}

.btn {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.error-message {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.error-message h4 {
  margin: 0 0 10px 0;
  font-size: 1.1rem;
}

.error-message p {
  margin: 0;
}

.validation-result {
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.validation-result.success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.validation-result.warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
}

.validation-result h4 {
  margin: 0 0 10px 0;
  font-size: 1.1rem;
}

.validation-result ul {
  margin: 0;
  padding-left: 20px;
}

.validation-result li {
  margin-bottom: 5px;
}

.migration-result {
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.migration-result h4 {
  margin: 0 0 15px 0;
  font-size: 1.3rem;
  color: #333;
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.stat-label {
  font-weight: 500;
  color: #333;
}

.stat-value {
  font-weight: 600;
  font-size: 1.1rem;
}

.stat-value.success {
  color: #28a745;
}

.stat-value.error {
  color: #dc3545;
}

.migration-errors {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  padding: 15px;
  margin-top: 15px;
}

.migration-errors h5 {
  margin: 0 0 10px 0;
  color: #721c24;
}

.migration-errors ul {
  margin: 0;
  padding-left: 20px;
  color: #721c24;
}

.migration-info {
  background: #e7f3ff;
  border: 1px solid #b8daff;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.migration-info h4 {
  margin: 0 0 15px 0;
  color: #004085;
  font-size: 1.2rem;
}

.migration-info ul {
  margin: 0;
  padding-left: 20px;
  color: #004085;
}

.migration-info li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.migration-steps {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 10px;
  padding: 20px;
}

.migration-steps h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 1.2rem;
}

.migration-steps ol {
  margin: 0;
  padding-left: 20px;
  color: #333;
}

.migration-steps li {
  margin-bottom: 10px;
  line-height: 1.5;
}

.migration-steps strong {
  color: #007bff;
}

@media (max-width: 768px) {
  .user-migration {
    padding: 15px;
  }
  
  .migration-actions {
    grid-template-columns: 1fr;
  }
  
  .result-stats {
    grid-template-columns: 1fr;
  }
  
  .migration-header h2 {
    font-size: 1.5rem;
  }
  
  .migration-header p {
    font-size: 1rem;
  }
}
