.page-builder {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.card-selector {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.add-card-button {
  padding: 0.5rem 1rem;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.new-card-form {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
}

.new-card-form h3 {
  margin-top: 0;
  margin-bottom: 1rem;
}

.new-card-form input {
  display: block;
  width: 100%;
  padding: 0.5rem;
  margin-bottom: 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.form-buttons {
  display: flex;
  gap: 1rem;
}

.form-buttons button {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.form-buttons button:first-child {
  background-color: #4CAF50;
  color: white;
}

.form-buttons button:last-child {
  background-color: #f44336;
  color: white;
}

.form-select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

.error-message {
  color: #dc3545;
  padding: 10px;
  margin: 10px 0;
  border: 1px solid #dc3545;
  border-radius: 4px;
  background-color: #f8d7da;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.builder-content {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.section-controls button {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.section-controls button:hover {
  background: #0056b3;
}

.sections-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.section-item {
  border: 1px solid #ddd;
  padding: 15px;
  border-radius: 4px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.section-header button {
  padding: 4px 8px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.dropdown-editor {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.options-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 10px;
  padding-left: 20px;
}

input[type="text"] {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.save-button {
  margin-top: 20px;
  padding: 10px 20px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  width: 100%;
}

.save-button:hover {
  background: #218838;
}

.save-button:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 18px;
}

.card-selector select option {
  padding: 8px;
}

.card-selector select option:not(:first-child) {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-selector .nested-card {
  padding-left: 20px;
}

/* Add these styles to your existing PageBuilder.css file */

.page-builder {
  padding: 20px;
  font-family: sans-serif; /* Example font */
}

.page-builder h2 {
  color: #333;
  margin-bottom: 20px;
}

.card {
  border: 1px solid #ddd;
  border-radius: 5px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
  background-color: #f8f9fa;
  padding: 10px 15px;
  border-bottom: 1px solid #ddd;
  font-weight: bold;
}

.card-body {
  padding: 15px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-control {
  display: block;
  width: 100%;
  padding: 8px 12px;
  font-size: 1rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-check {
  position: relative;
  display: block;
  padding-left: 1.25rem;
}

.form-check-input {
  position: absolute;
  margin-top: 0.3rem;
  margin-left: -1.25rem;
}

.form-check-label {
  margin-bottom: 0;
}

.btn {
  display: inline-block;
  font-weight: 400;
  color: #212529;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-color: transparent;
  border: 1px solid transparent;
  padding: 6px 12px;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 4px;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 3px;
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:hover {
  color: #fff;
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-outline-danger {
  color: #dc3545;
  border-color: #dc3545;
}

.btn-outline-danger:hover {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}

.input-group > .form-control,
.input-group > .btn {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  margin-bottom: 0;
}

.input-group > .form-control:not(:last-child),
.input-group > .btn:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group > .form-control:not(:first-child),
.input-group > .btn:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.d-flex {
  display: flex !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.align-items-center {
  align-items: center !important;
}

/* Add more styles as needed for specific elements or layouts */

.card-management {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.card-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.edit-button {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.delete-button {
  background-color: #f44336;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.edit-card-form {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.warning-text {
  color: #f44336;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 15px;
}

.modal-content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.modal-content li {
  margin-bottom: 5px;
}

/* Report Configuration Styles */
.report-configuration {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.report-configuration h5 {
  color: #495057;
  margin-bottom: 15px;
  font-weight: 600;
  border-bottom: 2px solid #007bff;
  padding-bottom: 8px;
}

.report-configuration .form-group {
  margin-bottom: 15px;
}

.report-configuration .form-label {
  font-weight: 500;
  color: #495057;
  margin-bottom: 5px;
}

.report-configuration .form-select {
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.report-configuration .form-select:focus {
  border-color: #007bff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.report-configuration .form-select:disabled {
  background-color: #e9ecef;
  opacity: 1;
  cursor: not-allowed;
}

.report-configuration .row {
  margin: 0 -10px;
}

.report-configuration .col-md-3 {
  padding: 0 10px;
}

/* Loading and error states */
.report-configuration .alert {
  margin-bottom: 20px;
  border-radius: 6px;
}

.report-configuration .spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

.report-configuration .visually-hidden {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Checkbox Dropdown Styles */
.dropdown-menu.show {
  display: block;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  z-index: 1000;
}

.dropdown-item {
  padding: 0.5rem 1rem;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item .form-check {
  margin-bottom: 0;
}

.dropdown-item .form-check-input {
  margin-right: 0.5rem;
}

.dropdown-item .form-check-label {
  cursor: pointer;
  user-select: none;
  width: 100%;
}

.dropdown-toggle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dropdown-toggle::after {
  margin-left: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .report-configuration .col-md-3 {
    margin-bottom: 15px;
  }

  .report-configuration {
    padding: 15px;
  }

  .dropdown-menu {
    max-height: 250px;
  }
}