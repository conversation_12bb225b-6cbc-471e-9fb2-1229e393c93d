{"name": "india-post-reports-management", "version": "1.0.0", "description": "India Post Western Region Reports Management System", "main": "index.js", "scripts": {"build": "cd web-app && npm install && npm run build", "start": "cd web-app && npm start", "install-web": "cd web-app && npm install", "dev": "cd web-app && npm start"}, "repository": {"type": "git", "url": "https://github.com/sathishnagarathinam/reportsmanagement.git"}, "keywords": ["india-post", "reports", "management", "react", "firebase", "supabase"], "author": "India Post Western Region", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "dependencies": {"@apollo/client": "^3.13.8", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@firebase/component": "^0.6.16", "@firebase/logger": "^0.4.4", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@reduxjs/toolkit": "^2.8.1", "@supabase/supabase-js": "^2.50.0", "@types/react": "^19.1.3", "@types/react-dnd": "^3.0.2", "@types/react-dnd-html5-backend": "^3.0.2", "@types/react-icons": "^3.0.0", "axios": "^1.9.0", "bootstrap": "^5.3.6", "firebase": "^11.7.3", "graphql": "^16.11.0", "react": "^19.1.0", "react-bootstrap": "^2.10.10", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.1.0", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.0"}, "devDependencies": {"@types/react-bootstrap": "^1.1.0", "@types/uuid": "^10.0.0", "typescript": "^4.9.5"}}