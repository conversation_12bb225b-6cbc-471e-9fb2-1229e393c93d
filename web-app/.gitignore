# Dependencies
node_modules/
/.pnp
.pnp.js

# Cache directories
.cache/
*.cache/

# Testing
/coverage

# Production build
# Note: We'll include build folder for GitHub Pages deployment
# /build

# Large files and cache
*.pack
*.tsbuildinfo

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
Thumbs.db

# Firebase
.firebase/
firebase-debug.log
firestore-debug.log

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
