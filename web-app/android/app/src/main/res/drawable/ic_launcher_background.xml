<?xml version="1.0" encoding="utf-8"?>
<!-- Custom app icon background matching app theme -->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- Background gradient matching app primary color -->
    <path
        android:fillType="evenOdd"
        android:pathData="M0,0h108v108h-108z">
        <aapt:attr name="android:fillColor" xmlns:aapt="http://schemas.android.com/aapt">
            <gradient
                android:type="linear"
                android:angle="270"
                android:startColor="#6A1B9A"
                android:centerColor="#8E24AA"
                android:endColor="#4A148C" />
        </aapt:attr>
    </path>
</vector>
