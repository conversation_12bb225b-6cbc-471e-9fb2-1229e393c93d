<?xml version="1.0" encoding="utf-8"?>
<!-- Custom app icon foreground with India Post logo -->
<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="108dp"
    android:height="108dp"
    android:viewportWidth="108"
    android:viewportHeight="108">
    
    <!-- India Post logo placeholder - will be replaced with actual logo -->
    <group android:scaleX="0.6"
           android:scaleY="0.6"
           android:pivotX="54"
           android:pivotY="54">
        
        <!-- White circle background for logo -->
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M54,20C65.05,20 74,28.95 74,40v28c0,11.05 -8.95,20 -20,20s-20,-8.95 -20,-20V40c0,-11.05 8.95,-20 20,-20z"/>
        
        <!-- India Post logo representation -->
        <path
            android:fillColor="#6A1B9A"
            android:pathData="M44,35h20v6H44z"/>
        <path
            android:fillColor="#6A1B9A"
            android:pathData="M44,45h20v6H44z"/>
        <path
            android:fillColor="#6A1B9A"
            android:pathData="M44,55h20v6H44z"/>
        <path
            android:fillColor="#6A1B9A"
            android:pathData="M44,65h12v6H44z"/>
    </group>
</vector>
