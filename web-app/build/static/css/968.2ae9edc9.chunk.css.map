{"version": 3, "file": "static/css/968.2ae9edc9.chunk.css", "mappings": "AAAA,eASE,kBAAmB,CAHnB,0BAAoC,CADpC,QAAS,CAET,YAAa,CACb,sBAAuB,CALvB,MAAO,CAFP,cAAe,CAGf,OAAQ,CAFR,KAAM,CAQN,YACF,CAEA,eACE,qBAAuB,CAEvB,iBAAkB,CAMlB,+BAAyC,CAHzC,eAAgB,CADhB,eAAgB,CADhB,eAAgB,CAGhB,eAAgB,CALhB,YAAa,CAMb,iBAEF,CAEA,cAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,iBAEE,gBAAiB,CADjB,QAEF,CAEA,cACE,eAAgB,CAChB,WAAY,CAIZ,UAAW,CAFX,cAAe,CADf,gBAAiB,CAEjB,SAEF,CAEA,oBACE,UACF,CAEA,YACE,kBACF,CCpDA,cAGE,aAAc,CADd,gBAEF,CAEA,eACE,YAAa,CACb,QAAS,CACT,kBACF,CAEA,iBAEE,wBAAyB,CAEzB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CALf,kBAMF,CAEA,eACE,wBAAyB,CAEzB,iBAAkB,CAClB,kBAAmB,CAFnB,YAGF,CAEA,kBAEE,kBAAmB,CADnB,YAEF,CAEA,qBAKE,qBAAsB,CACtB,iBAAkB,CALlB,aAAc,CAGd,kBAAmB,CADnB,aAAe,CADf,UAKF,CAEA,cACE,YAAa,CACb,QACF,CAEA,qBAEE,WAAY,CACZ,iBAAkB,CAClB,cAAe,CAHf,kBAIF,CAEA,iCACE,wBAAyB,CACzB,UACF,CAEA,gCACE,wBAAyB,CACzB,UACF,CAEA,aAGE,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CAHf,YAAa,CADb,UAKF,CAEA,eAME,wBAAyB,CAFzB,wBAAyB,CACzB,iBAAkB,CAJlB,aAAc,CAEd,aAAc,CADd,YAKF,CAEA,SAGE,UAAW,CADX,YAAa,CADb,iBAGF,CAEA,iBACE,eAAgB,CAEhB,iBAAkB,CAClB,8BAAqC,CAFrC,YAGF,CAEA,kBAEE,YAAa,CACb,QAAS,CAFT,aAGF,CAEA,yBAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CALf,gBAMF,CAEA,+BACE,kBACF,CAEA,eACE,YAAa,CACb,qBAAsB,CACtB,QACF,CAEA,cACE,qBAAsB,CAEtB,iBAAkB,CADlB,YAEF,CAEA,gBAGE,kBAAmB,CAFnB,YAAa,CACb,6BAA8B,CAE9B,kBACF,CAEA,uBAEE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CALf,eAMF,CAEA,iBAGE,QACF,CAEA,+BALE,YAAa,CACb,qBAUF,CANA,cAGE,OAAQ,CACR,eAAgB,CAChB,iBACF,CAEA,iBAEE,qBAAsB,CACtB,iBAAkB,CAClB,cAAe,CAHf,WAIF,CAEA,aAGE,kBAAmB,CAEnB,WAAY,CACZ,iBAAkB,CAFlB,UAAY,CAGZ,cAAe,CANf,eAAgB,CAChB,iBAAkB,CAMlB,UACF,CAEA,mBACE,kBACF,CAEA,sBACE,kBAAmB,CACnB,kBACF,CAEA,aAGE,UAAW,CACX,cAAe,CAFf,YAAa,CADb,iBAIF,CAEA,6BACE,WACF,CAEA,+CAEE,kBAAmB,CADnB,YAAa,CAEb,OACF,CAEA,4BACE,iBACF,CAIA,cAEE,sBAAuB,CADvB,YAEF,CAEA,iBACE,UAAW,CACX,kBACF,CAEA,MACE,qBAAsB,CACtB,iBAAkB,CAElB,8BAAqC,CADrC,kBAEF,CAEA,aACE,wBAAyB,CAEzB,4BAA6B,CAC7B,eAAiB,CAFjB,iBAGF,CAEA,WACE,YACF,CAEA,YACE,kBACF,CAEA,YACE,aAAc,CAEd,eAAiB,CADjB,iBAEF,CAEA,cAQE,2BAA4B,CAD5B,qBAAsB,CAEtB,wBAAyB,CACzB,iBAAkB,CAJlB,aAAc,CALd,aAAc,CAGd,cAAe,CACf,eAAgB,CAFhB,gBAAiB,CAQjB,oEAAwE,CATxE,UAUF,CAEA,oBACE,oBAAqB,CAErB,gCAAgD,CADhD,SAEF,CAEA,YAEE,aAAc,CACd,oBAAqB,CAFrB,iBAGF,CAEA,kBAGE,oBAAqB,CADrB,gBAAkB,CADlB,iBAGF,CAEA,kBACE,eACF,CAEA,KAOE,wBAA6B,CAC7B,sBAA6B,CAI7B,iBAAkB,CATlB,aAAc,CAGd,cAAe,CALf,oBAAqB,CASrB,cAAe,CARf,eAAgB,CAOhB,gBAAiB,CALjB,iBAAkB,CASlB,6HAAqI,CARrI,qBASF,CAEA,aALE,eAUF,CALA,QAIE,iBAAkB,CAFlB,iBAAmB,CADnB,eAIF,CAEA,eAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,qBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,YAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,kBAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,oBAEE,oBAAqB,CADrB,aAEF,CAEA,0BAEE,wBAAyB,CACzB,oBAAqB,CAFrB,UAGF,CAEA,aAIE,mBAAoB,CAFpB,YAAa,CACb,cAAe,CAFf,iBAAkB,CAIlB,UACF,CAEA,6CAGE,aAAc,CAEd,eAAgB,CAHhB,iBAAkB,CAElB,QAEF,CAEA,+EAGE,4BAA6B,CAD7B,yBAEF,CAEA,iFAGE,2BAA4B,CAD5B,wBAEF,CAEA,MACE,6BACF,CAEA,MACE,4BACF,CAEA,QACE,sBACF,CAEA,yBACE,uCACF,CAEA,oBACE,4BACF,CAIA,iBAGE,wBAAyB,CACzB,iBAAkB,CAHlB,aAAc,CACd,YAGF,CAEA,cACE,YAAa,CACb,QAAS,CACT,eACF,CAEA,aACE,wBASF,CAEA,4BAJE,kBAAmB,CALnB,WAAY,CAEZ,iBAAkB,CAHlB,UAAY,CAIZ,cAAe,CACf,YAAa,CAEb,OAAQ,CALR,gBAkBF,CAVA,eACE,wBASF,CAEA,gBAGE,wBAAyB,CACzB,iBAAkB,CAHlB,aAAc,CACd,YAGF,CAEA,cACE,aAAc,CAEd,cAAe,CADf,eAAiB,CAEjB,kBACF,CAEA,kBACE,aAAc,CACd,iBACF,CAEA,kBACE,iBACF,CAGA,sBACE,wBAAyB,CACzB,wBAAyB,CACzB,iBAAkB,CAElB,aAAc,CADd,YAEF,CAEA,yBAIE,+BAAgC,CAHhC,aAAc,CAEd,eAAgB,CADhB,kBAAmB,CAGnB,kBACF,CAEA,kCACE,kBACF,CAEA,kCAEE,aAAc,CADd,eAAgB,CAEhB,iBACF,CAEA,mCACE,wBAAyB,CACzB,iBAAkB,CAElB,cAAe,CADf,gBAAiB,CAEjB,oEACF,CAEA,yCACE,oBAAqB,CAErB,gCAAgD,CADhD,SAEF,CAEA,4CACE,wBAAyB,CAEzB,kBAAmB,CADnB,SAEF,CAEA,2BACE,cACF,CAEA,gCACE,cACF,CAGA,6BAEE,iBAAkB,CADlB,kBAEF,CAEA,yCAEE,WAAY,CADZ,UAEF,CAEA,uCAOE,4BAAiC,CAEjC,kBAAoB,CANpB,oBAAsB,CAEtB,qBAAuB,CACvB,yBAA2B,CAF3B,mBAAqB,CAHrB,2BAA6B,CAO7B,4BAA8B,CAN9B,mBAQF,CAGA,oBAEE,wBAAyB,CACzB,qBAAuB,CACvB,4CAAmD,CAHnD,aAAc,CAId,YACF,CAEA,eAGE,eAAgB,CADhB,WAAY,CADZ,kBAAoB,CAIpB,eAAgB,CADhB,UAEF,CAEA,qBACE,wBACF,CAEA,2BACE,eACF,CAEA,iCACE,kBACF,CAEA,iCACE,cAAe,CACf,wBAAiB,CAAjB,gBAAiB,CACjB,UACF,CAEA,iBAGE,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAH9B,iBAIF,CAEA,uBACE,gBACF,CAGA,yBACE,gCACE,kBACF,CAEA,sBACE,YACF,CAEA,eACE,gBACF,CACF", "sources": ["components/shared/Modal.css", "components/admin/business/PageBuilder.css"], "sourcesContent": [".modal-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  z-index: 1000;\n}\n\n.modal-content {\n  background-color: white;\n  padding: 20px;\n  border-radius: 8px;\n  min-width: 300px;\n  max-width: 500px;\n  max-height: 90vh;\n  overflow-y: auto;\n  position: relative;\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n}\n\n.modal-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.modal-header h2 {\n  margin: 0;\n  font-size: 1.5rem;\n}\n\n.close-button {\n  background: none;\n  border: none;\n  font-size: 1.5rem;\n  cursor: pointer;\n  padding: 0;\n  color: #666;\n}\n\n.close-button:hover {\n  color: #333;\n}\n\n.modal-body {\n  margin-bottom: 20px;\n}", ".page-builder {\n  padding: 20px;\n  max-width: 1200px;\n  margin: 0 auto;\n}\n\n.card-selector {\n  display: flex;\n  gap: 1rem;\n  margin-bottom: 1rem;\n}\n\n.add-card-button {\n  padding: 0.5rem 1rem;\n  background-color: #4CAF50;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.new-card-form {\n  background-color: #f5f5f5;\n  padding: 1rem;\n  border-radius: 4px;\n  margin-bottom: 1rem;\n}\n\n.new-card-form h3 {\n  margin-top: 0;\n  margin-bottom: 1rem;\n}\n\n.new-card-form input {\n  display: block;\n  width: 100%;\n  padding: 0.5rem;\n  margin-bottom: 1rem;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n}\n\n.form-buttons {\n  display: flex;\n  gap: 1rem;\n}\n\n.form-buttons button {\n  padding: 0.5rem 1rem;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.form-buttons button:first-child {\n  background-color: #4CAF50;\n  color: white;\n}\n\n.form-buttons button:last-child {\n  background-color: #f44336;\n  color: white;\n}\n\n.form-select {\n  width: 100%;\n  padding: 10px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 16px;\n}\n\n.error-message {\n  color: #dc3545;\n  padding: 10px;\n  margin: 10px 0;\n  border: 1px solid #dc3545;\n  border-radius: 4px;\n  background-color: #f8d7da;\n}\n\n.loading {\n  text-align: center;\n  padding: 20px;\n  color: #666;\n}\n\n.builder-content {\n  background: #fff;\n  padding: 20px;\n  border-radius: 8px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.section-controls {\n  margin: 20px 0;\n  display: flex;\n  gap: 10px;\n}\n\n.section-controls button {\n  padding: 8px 16px;\n  background: #007bff;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.section-controls button:hover {\n  background: #0056b3;\n}\n\n.sections-list {\n  display: flex;\n  flex-direction: column;\n  gap: 15px;\n}\n\n.section-item {\n  border: 1px solid #ddd;\n  padding: 15px;\n  border-radius: 4px;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.section-header button {\n  padding: 4px 8px;\n  background: #dc3545;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.dropdown-editor {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.options-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n  margin-top: 10px;\n  padding-left: 20px;\n}\n\ninput[type=\"text\"] {\n  padding: 8px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n}\n\n.save-button {\n  margin-top: 20px;\n  padding: 10px 20px;\n  background: #28a745;\n  color: white;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  width: 100%;\n}\n\n.save-button:hover {\n  background: #218838;\n}\n\n.save-button:disabled {\n  background: #6c757d;\n  cursor: not-allowed;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 40px;\n  color: #666;\n  font-size: 18px;\n}\n\n.card-selector select option {\n  padding: 8px;\n}\n\n.card-selector select option:not(:first-child) {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n}\n\n.card-selector .nested-card {\n  padding-left: 20px;\n}\n\n/* Add these styles to your existing PageBuilder.css file */\n\n.page-builder {\n  padding: 20px;\n  font-family: sans-serif; /* Example font */\n}\n\n.page-builder h2 {\n  color: #333;\n  margin-bottom: 20px;\n}\n\n.card {\n  border: 1px solid #ddd;\n  border-radius: 5px;\n  margin-bottom: 15px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.card-header {\n  background-color: #f8f9fa;\n  padding: 10px 15px;\n  border-bottom: 1px solid #ddd;\n  font-weight: bold;\n}\n\n.card-body {\n  padding: 15px;\n}\n\n.form-group {\n  margin-bottom: 15px;\n}\n\n.form-label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: bold;\n}\n\n.form-control {\n  display: block;\n  width: 100%;\n  padding: 8px 12px;\n  font-size: 1rem;\n  line-height: 1.5;\n  color: #495057;\n  background-color: #fff;\n  background-clip: padding-box;\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.form-control:focus {\n  border-color: #80bdff;\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.form-check {\n  position: relative;\n  display: block;\n  padding-left: 1.25rem;\n}\n\n.form-check-input {\n  position: absolute;\n  margin-top: 0.3rem;\n  margin-left: -1.25rem;\n}\n\n.form-check-label {\n  margin-bottom: 0;\n}\n\n.btn {\n  display: inline-block;\n  font-weight: 400;\n  color: #212529;\n  text-align: center;\n  vertical-align: middle;\n  cursor: pointer;\n  background-color: transparent;\n  border: 1px solid transparent;\n  padding: 6px 12px;\n  font-size: 1rem;\n  line-height: 1.5;\n  border-radius: 4px;\n  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.btn-sm {\n  padding: 4px 8px;\n  font-size: 0.875rem;\n  line-height: 1.5;\n  border-radius: 3px;\n}\n\n.btn-secondary {\n  color: #fff;\n  background-color: #6c757d;\n  border-color: #6c757d;\n}\n\n.btn-secondary:hover {\n  color: #fff;\n  background-color: #5a6268;\n  border-color: #545b62;\n}\n\n.btn-danger {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.btn-danger:hover {\n  color: #fff;\n  background-color: #c82333;\n  border-color: #bd2130;\n}\n\n.btn-outline-danger {\n  color: #dc3545;\n  border-color: #dc3545;\n}\n\n.btn-outline-danger:hover {\n  color: #fff;\n  background-color: #dc3545;\n  border-color: #dc3545;\n}\n\n.input-group {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  align-items: stretch;\n  width: 100%;\n}\n\n.input-group > .form-control,\n.input-group > .btn {\n  position: relative;\n  flex: 1 1 auto;\n  width: 1%;\n  margin-bottom: 0;\n}\n\n.input-group > .form-control:not(:last-child),\n.input-group > .btn:not(:last-child) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.input-group > .form-control:not(:first-child),\n.input-group > .btn:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.mb-2 {\n  margin-bottom: 0.5rem !important;\n}\n\n.mb-3 {\n  margin-bottom: 1rem !important;\n}\n\n.d-flex {\n  display: flex !important;\n}\n\n.justify-content-between {\n  justify-content: space-between !important;\n}\n\n.align-items-center {\n  align-items: center !important;\n}\n\n/* Add more styles as needed for specific elements or layouts */\n\n.card-management {\n  margin: 20px 0;\n  padding: 15px;\n  background-color: #f5f5f5;\n  border-radius: 5px;\n}\n\n.card-actions {\n  display: flex;\n  gap: 10px;\n  margin-top: 10px;\n}\n\n.edit-button {\n  background-color: #4CAF50;\n  color: white;\n  border: none;\n  padding: 8px 15px;\n  border-radius: 4px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.delete-button {\n  background-color: #f44336;\n  color: white;\n  border: none;\n  padding: 8px 15px;\n  border-radius: 4px;\n  cursor: pointer;\n  display: flex;\n  align-items: center;\n  gap: 5px;\n}\n\n.edit-card-form {\n  margin: 20px 0;\n  padding: 15px;\n  background-color: #f5f5f5;\n  border-radius: 5px;\n}\n\n.warning-text {\n  color: #f44336;\n  font-weight: bold;\n  font-size: 16px;\n  margin-bottom: 15px;\n}\n\n.modal-content ul {\n  margin: 15px 0;\n  padding-left: 20px;\n}\n\n.modal-content li {\n  margin-bottom: 5px;\n}\n\n/* Report Configuration Styles */\n.report-configuration {\n  background-color: #f8f9fa;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  padding: 20px;\n  margin: 20px 0;\n}\n\n.report-configuration h5 {\n  color: #495057;\n  margin-bottom: 15px;\n  font-weight: 600;\n  border-bottom: 2px solid #007bff;\n  padding-bottom: 8px;\n}\n\n.report-configuration .form-group {\n  margin-bottom: 15px;\n}\n\n.report-configuration .form-label {\n  font-weight: 500;\n  color: #495057;\n  margin-bottom: 5px;\n}\n\n.report-configuration .form-select {\n  border: 1px solid #ced4da;\n  border-radius: 4px;\n  padding: 8px 12px;\n  font-size: 14px;\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n}\n\n.report-configuration .form-select:focus {\n  border-color: #007bff;\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.report-configuration .form-select:disabled {\n  background-color: #e9ecef;\n  opacity: 1;\n  cursor: not-allowed;\n}\n\n.report-configuration .row {\n  margin: 0 -10px;\n}\n\n.report-configuration .col-md-3 {\n  padding: 0 10px;\n}\n\n/* Loading and error states */\n.report-configuration .alert {\n  margin-bottom: 20px;\n  border-radius: 6px;\n}\n\n.report-configuration .spinner-border-sm {\n  width: 1rem;\n  height: 1rem;\n}\n\n.report-configuration .visually-hidden {\n  position: absolute !important;\n  width: 1px !important;\n  height: 1px !important;\n  padding: 0 !important;\n  margin: -1px !important;\n  overflow: hidden !important;\n  clip: rect(0, 0, 0, 0) !important;\n  white-space: nowrap !important;\n  border: 0 !important;\n}\n\n/* Checkbox Dropdown Styles */\n.dropdown-menu.show {\n  display: block;\n  border: 1px solid #ced4da;\n  border-radius: 0.375rem;\n  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);\n  z-index: 1000;\n}\n\n.dropdown-item {\n  padding: 0.5rem 1rem;\n  border: none;\n  background: none;\n  width: 100%;\n  text-align: left;\n}\n\n.dropdown-item:hover {\n  background-color: #f8f9fa;\n}\n\n.dropdown-item .form-check {\n  margin-bottom: 0;\n}\n\n.dropdown-item .form-check-input {\n  margin-right: 0.5rem;\n}\n\n.dropdown-item .form-check-label {\n  cursor: pointer;\n  user-select: none;\n  width: 100%;\n}\n\n.dropdown-toggle {\n  position: relative;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n\n.dropdown-toggle::after {\n  margin-left: auto;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .report-configuration .col-md-3 {\n    margin-bottom: 15px;\n  }\n\n  .report-configuration {\n    padding: 15px;\n  }\n\n  .dropdown-menu {\n    max-height: 250px;\n  }\n}"], "names": [], "sourceRoot": ""}