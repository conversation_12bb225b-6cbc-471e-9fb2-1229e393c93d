{"version": 3, "file": "static/js/266.bf60e924.chunk.js", "mappings": "wOAUA,MAkBA,EAlBoCA,IAA2C,IAA1C,OAAEC,EAAM,QAAEC,EAAO,MAAEC,EAAK,SAAEC,GAAUJ,EACvE,OAAKC,GAGHI,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAgBC,QAASL,EAAQE,UAC9CI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAgBC,QAASE,GAAKA,EAAEC,kBAAkBN,SAAA,EAC/DI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcF,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,MAAAD,SAAKD,KACLE,EAAAA,EAAAA,KAAA,UAAQC,UAAU,eAAeC,QAASL,EAAQE,SAAC,aAErDC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYF,SACxBA,SAVW,IAaZ,E,cCpBH,MAeMO,EAAaA,CAACC,EAAgBC,KACzC,MAAMC,EAAOD,EAAcE,MAAKC,GAAKA,EAAEC,KAAOL,IAC9C,QAAOE,IAAQA,EAAKI,QAAgB,EAIzBC,EAAaA,CAACP,EAAgBC,KACjCA,EAAcO,MAAKJ,GAAKA,EAAEE,WAAaN,IAIpCS,EAAiBC,IAC5B,MAAMC,EAAmC,CAAC,EACpCC,EAAoB,GAW1B,OAVAF,EAAKG,SAAQC,IACXH,EAAIG,EAAKT,IAAM,IAAKS,EAAMtB,SAAU,GAAI,IAE1CkB,EAAKG,SAAQC,IAC+B,IAADC,EAArCD,EAAKR,UAAYK,EAAIG,EAAKR,UACD,QAA3BS,EAAAJ,EAAIG,EAAKR,UAAUd,gBAAQ,IAAAuB,GAA3BA,EAA6BC,KAAKL,EAAIG,EAAKT,KAE3CO,EAAMI,KAAKL,EAAIG,EAAKT,IACtB,IAEKO,CAAK,EAIDK,EAAsBA,CAACX,EAAkBL,KACpD,IAAIiB,EAAwB,GAC5B,MAAM1B,EAAWS,EAAckB,QAAOf,GAAKA,EAAEE,WAAaA,IAC1D,IAAK,MAAMc,KAAS5B,EAClB0B,EAAYF,KAAKI,EAAMf,IACvBa,EAAcA,EAAYG,OAAOJ,EAAoBG,EAAMf,GAAIJ,IAEjE,OAAOiB,CAAW,ECxBPI,EAAqBC,IAChC,MAAM,WACJC,EAAU,cACVC,EAAa,aACbC,EAAY,gBACZC,EAAe,UACfC,EAAS,aACTC,EAAY,aACZC,EAAY,gBACZC,EAAe,WACfC,EAAU,cACVC,EAAa,aACbC,EAAY,SACZC,EAAQ,WACRC,EAAU,oBACVC,EAAmB,mBACnBC,EAAkB,cAClBC,EAAa,UACbC,EAAS,eACTC,EAAc,iBACdC,EAAgB,gBAChBC,EAAe,0BACfC,GACErB,EAEEsB,GAAkBC,EAAAA,EAAAA,cAAYC,UAClCb,GAAa,GACb,IACE,MACMc,SADsBC,EAAAA,EAAAA,KAAQC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,gBACXC,KAAKzC,KAAI0C,IAAG,CAAOhD,GAAIgD,EAAIhD,MAAOgD,EAAIC,WAC9E7B,EAAcuB,EAChB,CAAE,MAAOO,GACPpB,EAAS,+BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,IACC,CAACT,EAAeS,EAAcC,IA+IjC,MAAO,CACLU,kBACAa,iBAzIuBX,UACvB,IAAKnB,IAAcE,EAEjB,YADAK,EAAS,qCAGXD,GAAa,GAEb,QAbuBa,WACvB,MAAMY,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc9C,GAErC,aADsBuD,EAAAA,EAAAA,IAAOD,IACdE,QAAQ,EASGC,CAAiBlC,GAIzC,OAFAO,EAAS,+DACTD,GAAa,GAGfA,GAAa,GACbG,GAAoB,EAAK,EA6HzB0B,oBA1H0BhB,UAAa,IAADiB,EACtC,IAAKpC,IAAcE,EAGf,OAFAK,EAAS,6CACTE,GAAoB,GAGxB,IAAI4B,EAA+B,KAChB,qBAAfjC,GAAqCN,EACvCuC,EAAgBvC,EACQ,qBAAfM,EACTiC,EAAgB,KACPvC,GAA+B,qBAAfM,EACvBiC,EAAgBvC,EACRA,GAA+B,qBAAfM,IACxBiC,EAAgB,MAGpB,MACMC,EAAU,GADGD,EAA4D,QAA/CD,EAAGxC,EAAWrB,MAAKC,GAAKA,EAAEC,KAAO4D,WAAc,IAAAD,OAAA,EAA5CA,EAA8CG,KAAO,iBACvDvC,IAAYwC,QAAQ,OAAQ,KAE7D,IACElC,GAAa,GACbG,GAAoB,GACpB,MAAMgC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcvB,IAC9B0C,KAAMC,EAAeC,MAAOC,GD/GRlF,KAChC,MAAMmF,EAAOnF,EACVoF,MAAM,IACNC,QAAO,CAACC,EAAKC,IAASD,EAAMC,EAAKC,WAAW,IAAI,GAE7CC,EAAQ,CAACC,EAAAA,IAAUC,EAAAA,IAAWC,EAAAA,IAAOC,EAAAA,KACrCC,EAAS,CAAC,UAAW,UAAW,UAAW,UAAW,WAK5D,MAAO,CAAEf,KAHIU,EAAMN,EAAOM,EAAMM,QAGjBd,MAFDa,EAAOX,EAAOW,EAAOC,QAEb,ECoGqCC,CAAkBzD,SAEnE0D,EAAAA,EAAAA,IAAOnB,EAAS,CACpBhE,GAAIuB,EACJrC,MAAOuC,EACPqC,KAAMD,EACN5D,SAAU2D,EACVwB,aAAa,IAAIC,MAAOC,cACxBrB,KAAMC,EAAcqB,KACpBpB,MAAOC,EACPoB,OAAQ,GACRC,QAAQ,EACRC,OAAQnE,UAGJiB,IAENhB,EAAa,IACbE,EAAgB,IAChBO,GAAmB,GACnBL,EAAc,IACdN,EAAgBC,GAChBQ,EAAW,WAAWN,qCACtBkE,YAAW,IAAM5D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPpB,EAAS,yDACTqB,QAAQC,MAAM,uBAAwBF,EACxC,CAAC,QACCrB,GAAa,EACf,GAqEA+D,eAlEsB/F,IACtBuC,EAAevC,GACf6B,EAAgB7B,EAAKX,OACrBmD,GAAiB,EAAK,EAgEtBwD,iBA7DuBnD,UACvB,MAAMoD,EAAc3E,EAAWrB,MAAKC,GAAKA,EAAEC,KAAOqB,IAClD,GAAKyE,GAAgBrE,EACrB,IACEI,GAAa,GACb,MAAMmC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcgD,EAAY9F,UAC5C+F,EAAAA,EAAAA,IAAU/B,EAAS,CAAE9E,MAAOuC,EAAc2D,aAAa,IAAIC,MAAOC,sBAClE9C,IACNH,GAAiB,GACjBD,EAAe,MACfV,EAAgB,IAChBK,EAAW,gCACX4D,YAAW,IAAM5D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,GA4CAmE,kBAzCyBrG,IACzB2C,EAAgB3C,GAChB4C,GAA0B,EAAK,EAwC/B0D,oBArC0BvD,UAC1B,GAAKrB,EAAL,CACAQ,GAAa,GACb,IACE,MAAMqE,GAAQC,EAAAA,EAAAA,IAAWrD,EAAAA,IACnBsD,EAAiBxF,EAAoBS,EAAcF,GACnDkF,EAAc,CAAChF,KAAiB+E,GAEtC,IAAK,MAAMpG,KAAMqG,EACfH,EAAMI,QAAOtD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc9C,IACnCkG,EAAMI,QAAOtD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAAS9C,UAE1BkG,EAAMK,eACN/D,IAEND,GAA0B,GAC1BD,EAAgB,MAChBhB,EAAgB,IAChBY,EAAc,MACdC,EAAU,IACVJ,EAAW,yDACX4D,YAAW,IAAM5D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,CA1ByB,CA0BzB,EAWD,E,cC1LI,MCgEP,EAjFkD9C,IAS3C,IAT4C,WACjDoC,EAAU,aACVE,EAAY,aACZmF,EAAY,WACZ7E,EAAU,eACV8E,EAAc,UACdC,EAAS,eACTC,EAAc,gBACdC,GACD7H,EACC,MAAM8H,EAAoB,SAACC,GAAwD,IAArCC,EAAKC,UAAA/B,OAAA,QAAAgC,IAAAD,UAAA,GAAAA,UAAA,GAAG,EACpD,OAAOF,EAAMI,SAAQrH,IAEnB,MAAMsH,EAAetH,EAAKX,OAAS,mBAEnC,MAAO,EACLE,EAAAA,EAAAA,KAAA,UAAsBgI,MAAOvH,EAAKG,GAAIqH,MAAO,CAAEC,YAAwB,GAARP,EAAH,MAAoB5H,SAC7E,GAAG,KAAKoI,OAAOR,MAAUI,KADftH,EAAKG,OAGdH,EAAKV,UAAYU,EAAKV,SAAS8F,OAAS,EAAI4B,EAAkBhH,EAAKV,SAAU4H,EAAQ,GAAK,GAC/F,GAEL,EAkBA,OACExH,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeF,SAAA,EAC5BI,EAAAA,EAAAA,MAAA,UACE6H,MAAO/F,EACPmG,SApBoBhI,IACxB,MAAMiI,EAAkBjI,EAAEkI,OAAON,MACjCZ,EAAaiB,EAAgB,EAmBzBpI,UAAU,cACVsI,SAAUjB,EAAUvH,SAAA,EAEpBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,GAAEjI,SAAEuH,EAAY,qBAAuB,gCACpDG,EAAkBzG,EAAce,QAGnC/B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4BAA2BF,UACxCI,EAAAA,EAAAA,MAAA,UACE6H,MAAOzF,EACP6F,SA1BoBhI,IAC1B,MAAMoI,EAAYpI,EAAEkI,OAAON,MAC3BX,EAAemB,GAEG,qBAAdA,GAAkD,qBAAdA,EACtCjB,IACuB,kBAAdiB,GACThB,GACF,EAmBMvH,UAAU,8BAA6BF,SAAA,EAEvCC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,GAAEjI,SAAC,sBACjBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,mBAAmBO,WAAYtG,EAAalC,SAAC,2BAG1DkC,IACC9B,EAAAA,EAAAA,MAAAsI,EAAAA,SAAA,CAAA1I,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,mBAAkBjI,SAAC,0BAGjCC,EAAAA,EAAAA,KAAA,UACEgI,MAAM,gBACNO,UAAWzH,EAAWmB,EAAcF,IAAezB,EAAW2B,EAAcF,GAAYhC,SACzF,mDAOL,EC9CV,EAnCsDJ,IAK/C,IALgD,aACrDsC,EAAY,WACZF,EAAU,WACV2G,EAAU,aACVC,GACDhJ,EACC,MAAMiJ,EAAmB7G,EAAWrB,MAAKC,GAAKA,EAAEC,KAAOqB,IAEvD,OAAK2G,GAKHzI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBF,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,oBAAkB6I,EAAiB9I,MAAM,QAC7CK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcF,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,UACED,QAASA,IAAMwI,EAAWE,GAC1B3I,UAAU,kDACVsI,UAAWtG,EAAalC,SAAA,CAEvB8I,EAAAA,cAAoBC,EAAAA,KAAoC,iBAE3D3I,EAAAA,EAAAA,MAAA,UACED,QAASA,IAAMyI,EAAa1G,GAC5BhC,UAAU,8CACVsI,UAAWtG,EAAalC,SAAA,CAEvB8I,EAAAA,cAAoBE,EAAAA,KAAqC,0BAnBzD,IAsBD,ECyNV,EAxPwDpJ,IAKjD,IAADqJ,EAAA,IALmD,MACvDC,EAAK,MACLC,EAAK,SACLC,EAAQ,SACRC,GACDzJ,EACC,MAAM0J,EAAqBA,CAACC,EAAkBtB,EAAeuB,KAC3D,MAAMC,EAAa,IAAKP,EAAMQ,SAAW,IACzCD,EAAWF,GAAY,IAAKE,EAAWF,GAAW,CAACC,GAAMvB,GACzDmB,EAASD,EAAO,IAAKD,EAAOQ,QAASD,GAAa,EAa9CE,EAA4BtJ,IAChC,MAAM,MAAE4H,EAAK,KAAE2B,GAASvJ,EAAEkI,OAC1B,IAAIsB,EAAuB5B,EACd,aAAT2B,IACFC,EAAmBxJ,EAAEkI,OAA4BuB,SAEnDV,EAASD,EAAO,IAAKD,EAAOa,aAAcF,GAAkB,EAG9D,OACEzJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,8BAA6BF,SAAA,EAC1CI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gEAA+DF,SAAA,EAC5EC,EAAAA,EAAAA,KAAA,UAAAD,SAASkJ,EAAMc,OAAS,kBAAyB,KAAGd,EAAMU,KAAK,KAC/DxJ,EAAAA,EAAAA,MAAA,UAAQD,QAASA,IAAMkJ,EAASF,GAAQjJ,UAAU,wBAAuBF,SAAA,CACtE8I,EAAAA,cAAoBE,EAAAA,KAAqC,iBAG9D5I,EAAAA,EAAAA,MAAA,OAAKF,UAAU,YAAWF,SAAA,EAExBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,cAAcd,IAASjJ,UAAU,aAAYF,SAAC,YAC9DI,EAAAA,EAAAA,MAAA,UACES,GAAI,cAAcsI,IAClBjJ,UAAU,eACV+H,MAAOiB,EAAMU,KACbvB,SAAWhI,GAAM+I,EAASD,EAAO,IAC5BD,EACHU,KAAMvJ,EAAEkI,OAAON,MACfyB,QAAwB,aAAfR,EAAMU,MAAsC,UAAfV,EAAMU,MAAmC,mBAAfV,EAAMU,UAA4B9B,EAAYoB,EAAMQ,QACpHQ,YAA4B,YAAfhB,EAAMU,MAAqC,WAAfV,EAAMU,UAAoB9B,EAAYoB,EAAMgB,cACpFlK,SAAA,EAEHC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,OAAMjI,SAAC,UACrBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,WAAUjI,SAAC,cACzBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,SAAQjI,SAAC,YACvBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,OAAMjI,SAAC,UACrBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,WAAUjI,SAAC,cACzBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,QAAOjI,SAAC,iBACtBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,WAAUjI,SAAC,uBACzBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,iBAAgBjI,SAAC,oBAC/BC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,SAAQjI,SAAC,YACvBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,OAAMjI,SAAC,iBACrBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,UAASjI,SAAC,oBACxBC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,SAAQjI,SAAC,kBAI3BI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,eAAed,IAASjJ,UAAU,aAAYF,SAAC,aAC/DC,EAAAA,EAAAA,KAAA,SACEY,GAAI,eAAesI,IACnBS,KAAK,OACL1J,UAAU,eACV+H,MAAOiB,EAAMc,MACb3B,SAAWhI,GAAM+I,EAASD,EAAO,IAAID,EAAOc,MAAO3J,EAAEkI,OAAON,QAC5DkC,UAAQ,OAIX,CAAC,OAAQ,WAAY,SAAU,QAAQC,SAASlB,EAAMU,QACrDxJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,qBAAqBd,IAASjJ,UAAU,aAAYF,SAAC,mBACrEC,EAAAA,EAAAA,KAAA,SACEY,GAAI,qBAAqBsI,IACzBS,KAAK,OACL1J,UAAU,eACV+H,MAAOiB,EAAMgB,aAAe,GAC5B7B,SAAWhI,GAAM+I,EAASD,EAAO,IAAID,EAAOgB,YAAa7J,EAAEkI,OAAON,aAKxD,WAAfiB,EAAMU,OACLxJ,EAAAA,EAAAA,MAAAsI,EAAAA,SAAA,CAAA1I,SAAA,EACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,aAAad,IAASjJ,UAAU,aAAYF,SAAC,iBAC7DC,EAAAA,EAAAA,KAAA,SACEY,GAAI,aAAasI,IACjBS,KAAK,SACL1J,UAAU,eACV+H,WAAqBH,IAAdoB,EAAMmB,IAAoB,GAAKnB,EAAMmB,IAC5ChC,SAAWhI,GAAM+I,EAASD,EAAO,IAAID,EAAOmB,IAAwB,KAAnBhK,EAAEkI,OAAON,WAAeH,EAAYwC,WAAWjK,EAAEkI,OAAON,eAG7G7H,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,aAAad,IAASjJ,UAAU,aAAYF,SAAC,iBAC7DC,EAAAA,EAAAA,KAAA,SACEY,GAAI,aAAasI,IACjBS,KAAK,SACL1J,UAAU,eACV+H,WAAqBH,IAAdoB,EAAMqB,IAAoB,GAAKrB,EAAMqB,IAC5ClC,SAAWhI,GAAM+I,EAASD,EAAO,IAAID,EAAOqB,IAAwB,KAAnBlK,EAAEkI,OAAON,WAAeH,EAAYwC,WAAWjK,EAAEkI,OAAON,iBAMhH,CAAC,WAAY,QAAS,kBAAkBmC,SAASlB,EAAMU,QACtDxJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kCAAiCF,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYF,SAAC,cAChB,QADiCiJ,EAC9CC,EAAMQ,eAAO,IAAAT,OAAA,EAAbA,EAAe9H,KAAI,CAACqJ,EAAKjB,KACxBnJ,EAAAA,EAAAA,MAAA,OAAoBF,UAAU,mBAAkBF,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,SACE2J,KAAK,OACL1J,UAAU,eACVgK,YAAY,eACZjC,MAAOuC,EAAIR,MACX3B,SAAWhI,GAAMiJ,EAAmBC,EAAUlJ,EAAEkI,OAAON,MAAO,YAEhEhI,EAAAA,EAAAA,KAAA,SACE2J,KAAK,OACL1J,UAAU,eACVgK,YAAY,eACZjC,MAAOuC,EAAIvC,MACXI,SAAWhI,GAAMiJ,EAAmBC,EAAUlJ,EAAEkI,OAAON,MAAO,YAEhEhI,EAAAA,EAAAA,KAAA,UAAQ2J,KAAK,SAASzJ,QAASA,IAzHvBoJ,KAAsB,IAADkB,EACzC,MAAMhB,EAA0B,QAAhBgB,EAAGvB,EAAMQ,eAAO,IAAAe,OAAA,EAAbA,EAAe9I,QAAO,CAAC+I,EAAGC,IAAMA,IAAMpB,IACzDH,EAASD,EAAO,IAAKD,EAAOQ,QAASD,GAAa,EAuHDmB,CAAarB,GAAWrJ,UAAU,yBAAwBF,SAAC,aAfxFuJ,MAoBZtJ,EAAAA,EAAAA,KAAA,UAAQ2J,KAAK,SAASzJ,QAnId0K,KAChB,MAAMpB,EAAa,IAAKP,EAAMQ,SAAW,GAAK,CAAEM,MAAO,GAAI/B,MAAO,KAClEmB,EAASD,EAAO,IAAKD,EAAOQ,QAASD,GAAa,EAiIAvJ,UAAU,2BAA0BF,SAAC,kBAOlF,CAAC,OAAQ,WAAY,SAAU,QAAQoK,SAASlB,EAAMU,QACnDxJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,uBAAuBd,IAASjJ,UAAU,aAAYF,SAAC,qBACvEC,EAAAA,EAAAA,KAAA,SACIY,GAAI,uBAAuBsI,IAC3BS,KAAqB,WAAfV,EAAMU,KAAoB,SAA0B,SAAfV,EAAMU,KAAkB,OAAS,OAC5E1J,UAAU,eACV+H,WAA8BH,IAAvBoB,EAAMa,aAA6B,GAAKe,OAAO5B,EAAMa,cAC5D1B,SAAUsB,QAKL,aAAfT,EAAMU,MAAsC,WAAfV,EAAMU,QACjCxJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,EAClCC,EAAAA,EAAAA,KAAA,SACIY,GAAI,uBAAuBsI,IAC3BS,KAAK,WACL1J,UAAU,mBACV4J,QAASiB,QAAQ7B,EAAMa,cACvB1B,SAAUsB,KAEd1J,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,uBAAuBd,IAASjJ,UAAU,mBAAkBF,SAAC,yBAIpF,CAAC,WAAY,SAASoK,SAASlB,EAAMU,OAASV,EAAMQ,SAAWR,EAAMQ,QAAQ5D,OAAS,IAClF1F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,uBAAuBd,IAASjJ,UAAU,aAAYF,SAAC,qBACvEI,EAAAA,EAAAA,MAAA,UACIS,GAAI,uBAAuBsI,IAC3BjJ,UAAU,eACV+H,WAA8BH,IAAvBoB,EAAMa,aAA6B,GAAKe,OAAO5B,EAAMa,cAC5D1B,SAAUsB,EAAyB3J,SAAA,EAEnCC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,GAAEjI,SAAC,yBAChBkJ,EAAMQ,QAAQvI,KAAIqJ,IAAOvK,EAAAA,EAAAA,KAAA,UAAwBgI,MAAOuC,EAAIvC,MAAMjI,SAAEwK,EAAIR,OAAlCQ,EAAIvC,eAKvC,mBAAfiB,EAAMU,OACHxJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACvBC,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYF,SAAC,wCAC9BC,EAAAA,EAAAA,KAAA,SACI2J,KAAK,OACL1J,UAAU,eACV+H,MAAO+C,MAAMC,QAAQ/B,EAAMa,cAAgBb,EAAMa,aAAamB,KAAK,KAAO,GAC1E7C,SAAWhI,GAAM+I,EAASD,EAAO,IAAID,EAAOa,aAAc1J,EAAEkI,OAAON,MAAM9C,MAAM,KAAKhE,KAAIgK,GAAKA,EAAEC,SAAQzJ,QAAOwJ,GAAKA,MACnHjB,YAAY,qBAKR,WAAfhB,EAAMU,OACLxJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,qBAAqBd,IAASjJ,UAAU,aAAYF,SAAC,mBACrEC,EAAAA,EAAAA,KAAA,SACEY,GAAI,qBAAqBsI,IACzBS,KAAK,OACL1J,UAAU,eACV+H,MAAOiB,EAAMmC,YAAc,GAC3BhD,SAAWhI,GAAM+I,EAASD,EAAO,IAAID,EAAOmC,WAAYhL,EAAEkI,OAAON,aAKvD,YAAfiB,EAAMU,OACLxJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,uBAAuBd,IAASjJ,UAAU,aAAYF,SAAC,qBACvEC,EAAAA,EAAAA,KAAA,SACEY,GAAI,uBAAuBsI,IAC3BS,KAAK,OACL1J,UAAU,eACV+H,MAAOiB,EAAMoC,cAAgB,GAC7BjD,SAAWhI,GAAM+I,EAASD,EAAO,IAAID,EAAOoC,aAAcjL,EAAEkI,OAAON,cAMvE,CAAC,SAAU,WAAWmC,SAASlB,EAAMU,QACrCxJ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,wBAAuBF,SAAA,EACpCC,EAAAA,EAAAA,KAAA,SACEY,GAAI,kBAAkBsI,IACtBS,KAAK,WACL1J,UAAU,mBACV4J,UAAWZ,EAAMiB,SACjB9B,SAAWhI,GAAM+I,EAASD,EAAO,IAAID,EAAOiB,SAAU9J,EAAEkI,OAAOuB,aAEjE7J,EAAAA,EAAAA,KAAA,SAAOgK,QAAS,kBAAkBd,IAASjJ,UAAU,mBAAkBF,SAAC,sBAI1E,EC/LV,EAhD8DJ,IASvD,IATwD,WAC7D2L,EAAU,OACVlF,EAAM,WACNmF,EAAU,cACVC,EAAa,cACbC,EAAa,OACbC,EAAM,UACNC,EAAS,QACTC,GACDjM,EACC,OACEQ,EAAAA,EAAAA,MAAA,OAAKF,UAAU,kBAAiBF,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,2BAAyBuL,EAAWxL,UAExCE,EAAAA,EAAAA,KAAA,MAAAD,SAAI,yBACHqG,EAAOlF,KAAI,CAAC+H,EAAOC,KAClBlJ,EAAAA,EAAAA,KAAC6L,EAAe,CAEd5C,MAAOA,EACPC,MAAOA,EACPC,SAAUqC,EACVpC,SAAUqC,GAJLxC,EAAMrI,IAAMsI,MAQrB/I,EAAAA,EAAAA,MAAA,UAAQD,QAASqL,EAAYtL,UAAU,oBAAmBF,SAAA,CACvD8I,EAAAA,cAAoBiD,EAAAA,KAAoC,iBAG3D3L,EAAAA,EAAAA,MAAA,UACED,QAASwL,EACTzL,UAAU,4BACVsI,SAAUqD,IAAYN,GAAgC,IAAlBlF,EAAOP,OAAa9F,SAAA,CAEvD8I,EAAAA,cAAoBkD,EAAAA,KAAoC,IAAEH,EAAU,YAAc,8BAGrF5L,EAAAA,EAAAA,KAAA,UACEE,QAASyL,EACT1L,UAAU,8BACVsI,UAAW+C,GAAgC,IAAlBlF,EAAOP,OAAa9F,SAC9C,mBAGG,ECuCGiM,EAAwC,CACnD,CAAEhE,MAAO,QAAS+B,MAAO,SACzB,CAAE/B,MAAO,SAAU+B,MAAO,UAC1B,CAAE/B,MAAO,UAAW+B,MAAO,Y,cCxFtB,MCkJP,EA/I0DpK,IAQnD,IARoD,GACzDiB,EAAE,MACFmJ,EAAK,QACLN,EAAO,eACPwC,EAAc,SACd7D,EAAQ,SACRG,GAAW,EAAK,YAChB0B,EAAc,wBACftK,EACC,MAAOC,EAAQsM,IAAaC,EAAAA,EAAAA,WAAS,GAC/BC,GAAcC,EAAAA,EAAAA,QAAuB,OAG3CC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAsBC,IACtBJ,EAAYK,UAAYL,EAAYK,QAAQC,SAASF,EAAMlE,SAC7D4D,GAAU,EACZ,EAIF,OADAS,SAASC,iBAAiB,YAAaL,GAChC,KACLI,SAASE,oBAAoB,YAAaN,EAAmB,CAC9D,GACA,IAEH,MA+BMO,EAAgBb,EAAepG,SAAW4D,EAAQ5D,QAAU4D,EAAQ5D,OAAS,EAC7EkH,EAAkBd,EAAepG,OAAS,GAAKoG,EAAepG,OAAS4D,EAAQ5D,OAErF,OACE1F,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAO6J,QAASpJ,EAAIX,UAAU,aAAYF,SAAA,CAAEgK,EAAM,QAClD5J,EAAAA,EAAAA,MAAA,OAAKF,UAAU,WAAW+M,IAAKZ,EAAYrM,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEY,GAAIA,EACJX,UAAW,+DAA8DsI,EAAW,WAAa,IACjGoB,KAAK,SACLzJ,QAASA,KAAOqI,GAAY2D,GAAWtM,GACvC2I,SAAUA,EACVN,MAAO,CACLgF,gBAAiB1E,EAAW,UAAY,QACxC2E,YAAa,WACbnN,UAEFC,EAAAA,EAAAA,KAAA,QAAMC,UAAqC,IAA1BgM,EAAepG,OAAe,aAAe,GAAG9F,SA7BlDoN,MACrB,GAA8B,IAA1BlB,EAAepG,OACjB,OAAOoE,EACF,GAA8B,IAA1BgC,EAAepG,OAAc,CACtC,MAAMuH,EAAiB3D,EAAQ/I,MAAK2M,GAAUA,EAAOzM,KAAOqL,EAAe,KAC3E,OAAqB,OAAdmB,QAAc,IAAdA,OAAc,EAAdA,EAAgBjH,OAAQ8D,CACjC,CACE,MAAO,GAAGgC,EAAepG,iBAC3B,EAsBSsH,OAIJvN,IAAW2I,IACVpI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,2BAA2BgI,MAAO,CAAEqF,UAAW,QAASC,UAAW,QAASxN,SAAA,CAExF0J,EAAQ5D,OAAS,IAChB1F,EAAAA,EAAAA,MAAAsI,EAAAA,SAAA,CAAA1I,SAAA,EACEC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeF,UAC5BI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SACEC,UAAU,mBACV0J,KAAK,WACL/I,GAAI,GAAGA,eACPiJ,QAASiD,EACTE,IAAMQ,IACAA,IAAOA,EAAMC,cAAgBV,EAAe,EAElD3E,SA3DIsF,KAClBzB,EAAepG,SAAW4D,EAAQ5D,OAEpCuC,EAAS,IAGTA,EAASqB,EAAQvI,KAAImM,GAAUA,EAAOzM,KACxC,KAsDgBT,EAAAA,EAAAA,MAAA,SAAOF,UAAU,2BAA2B+J,QAAS,GAAGpJ,eAAgBb,SAAA,CAAC,eAC1D0J,EAAQ5D,OAAO,aAIlC7F,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAKjBwJ,EAAQvI,KAAImM,IACXrN,EAAAA,EAAAA,KAAA,OAAqBC,UAAU,gBAAeF,UAC5CI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SACEC,UAAU,mBACV0J,KAAK,WACL/I,GAAI,GAAGA,KAAMyM,EAAOzM,KACpBiJ,QAASoC,EAAe9B,SAASkD,EAAOzM,IACxCwH,SAAUA,KAAMuF,OAzFJC,EAyFyBP,EAAOzM,QAxFxDqL,EAAe9B,SAASyD,GAE1BxF,EAAS6D,EAAevK,QAAOd,GAAMA,IAAOgN,KAG5CxF,EAAS,IAAI6D,EAAgB2B,KANHA,KAyFoC,KAElD5N,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmB+J,QAAS,GAAGpJ,KAAMyM,EAAOzM,KAAKb,SAC/DsN,EAAOlH,WAVJkH,EAAOzM,MAgBC,IAAnB6I,EAAQ5D,SACP7F,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2BAA0BF,UACvCC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,iCAQbkM,EAAepG,OAAS,IACvB1F,EAAAA,EAAAA,MAAA,SAAOF,UAAU,0BAAyBF,SAAA,CACvCkM,EAAepG,OAAO,OAAK4D,EAAQ5D,OAAO,iBAG3C,ECyBV,EArKgElG,IASzD,IAT0D,gBAC/DkO,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,GACDzO,EAEC,MAAM,QAAE0O,EAAO,UAAEC,EAAS,QAAEC,EAAO,QAAE3C,EAAO,MAAE5H,EAAK,QAAEwK,GFbpBC,MACjC,MAAOJ,EAASK,IAAcvC,EAAAA,EAAAA,UAAmB,KAC1CmC,EAAWK,IAAgBxC,EAAAA,EAAAA,UAAqB,KAChDoC,EAASK,IAAczC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASiD,IAAc1C,EAAAA,EAAAA,WAAkB,IACzCnI,EAAOtB,IAAYyJ,EAAAA,EAAAA,UAAwB,MAE5C2C,EAAkBxL,UACtB,IACEuL,GAAW,GACXnM,EAAS,MAETqB,QAAQgL,IAAI,0EAGZ,MAAMC,QAAgBC,EAAAA,EAAcC,qBAEpCnL,QAAQgL,IAAI,sCAAkCC,EAAQnJ,OAAQ,kBAG9D,MAAMsJ,EAAyB,OAAPH,QAAO,IAAPA,OAAO,EAAPA,EACpB9N,KAAIkO,GAAOA,EAAIC,SAChB3N,QAAO,CAAC4N,EAAQpG,EAAOqG,IAAUA,EAAMC,QAAQF,KAAYpG,IAC3DxH,QAAQ4N,GAAuC,MAAVA,GAAoC,KAAlBA,EAAOnE,SAC9DsE,OAIGC,GAAwC,OAAfP,QAAe,IAAfA,OAAe,EAAfA,EAAiBjO,KAAIyO,IAAU,CAC5D/O,GAAI+O,EAAWC,cAAcjL,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzEwB,KAAMwJ,QACD,GAGDE,EAA2B,OAAPb,QAAO,IAAPA,OAAO,EAAPA,EACtB9N,KAAIkO,IAAG,CAAOE,OAAQF,EAAIC,OAAQS,SAAUV,EAAIW,aACjDrO,QAAO,CAACL,EAAM6H,EAAOqG,IACpBA,EAAMS,WAAUC,GAAKA,EAAEX,SAAWjO,EAAKiO,QAAUW,EAAEH,WAAazO,EAAKyO,aAAc5G,IAEpFxH,QAAQL,GACQ,MAAfA,EAAKiO,QAAmC,MAAjBjO,EAAKyO,UACL,KAAvBzO,EAAKiO,OAAOnE,QAA0C,KAAzB9J,EAAKyO,SAAS3E,SAE5CsE,MAAK,CAACS,EAAGC,IAAMD,EAAEZ,OAAOc,cAAcD,EAAEb,SAAWY,EAAEJ,SAASM,cAAcD,EAAEL,YAE3EO,GAA8C,OAAjBR,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmB3O,KAAIG,IAAI,CAC5DT,GAAIS,EAAKyO,SAASF,cAAcjL,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC5EwB,KAAM9E,EAAKyO,SACXR,OAAQjO,EAAKiO,aACR,GAGDgB,GAAgC,OAAPtB,QAAO,IAAPA,OAAO,EAAPA,EAC3BtN,QAAO0N,GAAOA,EAAI,gBAAkBA,EAAIC,QAAUD,EAAIW,WACvD7O,KAAIkO,IAAG,CACNxO,GAAIwO,EAAI,eACRjJ,KAAMiJ,EAAI,eACVE,OAAQF,EAAIC,QAAU,GACtBS,SAAUV,EAAIW,UAAY,GAC1BQ,WAAYnB,EAAI,qBACX,GAITV,EAAWgB,GACXf,EAAa0B,GACbzB,EAAW0B,EAEb,CAAE,MAAOxM,GACPC,QAAQC,MAAM,8BAAqBF,GACnCpB,EAAS,iDACTgM,EAAW,IACXC,EAAa,IACbC,EAAW,GACb,CAAC,QACCC,GAAW,EACb,GAQF,OAJAvC,EAAAA,EAAAA,YAAU,KACRwC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA3C,UACA5H,QACAwK,QAASM,EACV,EE9EgE0B,GAG3DC,EAAsB5C,EAAgB3M,KAAIwP,IAAQ,IAAAC,EAAA,OAClB,QADkBA,EACtDtC,EAAQ3N,MAAKkQ,GAAKA,EAAEhQ,KAAO8P,WAAS,IAAAC,OAAA,EAApCA,EAAsCxK,IAAI,IAC1CzE,OAAOoJ,SAEH+F,EAAqBhD,EAAgBhI,OAAS,EAChDyI,EAAU5M,QAAOoO,GAAYW,EAAoBtG,SAAS2F,EAASR,UACnEhB,EAGEwC,EAAwBhD,EAAkB5M,KAAI6P,IAAU,IAAAC,EAAA,OACpB,QADoBA,EAC5D1C,EAAU5N,MAAKuQ,GAAKA,EAAErQ,KAAOmQ,WAAW,IAAAC,OAAA,EAAxCA,EAA0C7K,IAAI,IAC9CzE,OAAOoJ,SAEHoG,EAAmBpD,EAAkBjI,OAAS,EAChD0I,EAAQ7M,QAAOyP,GACbV,EAAoBtG,SAASgH,EAAO7B,SACpCwB,EAAsB3G,SAASgH,EAAOrB,YAExCjC,EAAgBhI,OAAS,EACvB0I,EAAQ7M,QAAOyP,GAAUV,EAAoBtG,SAASgH,EAAO7B,UAC7Df,EAiCN,OA9BAjC,EAAAA,EAAAA,YAAU,KACR,GAAIuB,EAAgBhI,OAAS,EAAG,CAE9B,MAAMuL,EAAiBtD,EAAkBpM,QAAOqP,IAC9C,MAAMjB,EAAWxB,EAAU5N,MAAKuQ,GAAKA,EAAErQ,KAAOmQ,IAC9C,OAAOjB,GAAYW,EAAoBtG,SAAS2F,EAASR,OAAO,IAG9D8B,EAAevL,SAAWiI,EAAkBjI,QAC9CqI,EAAkBkD,EAEtB,IACC,CAACvD,EAAiBC,EAAmBQ,EAAWmC,EAAqBvC,KAExE5B,EAAAA,EAAAA,YAAU,KACR,GAAIwB,EAAkBjI,OAAS,EAAG,CAEhC,MAAMwL,EAAetD,EAAgBrM,QAAO4P,IAC1C,MAAMH,EAAS5C,EAAQ7N,MAAK6Q,GAAKA,EAAE3Q,KAAO0Q,IAC1C,OAAOH,GACAV,EAAoBtG,SAASgH,EAAO7B,SACpCwB,EAAsB3G,SAASgH,EAAOrB,SAAS,IAGpDuB,EAAaxL,SAAWkI,EAAgBlI,QAC1CsI,EAAgBkD,EAEpB,IACC,CAACvD,EAAmBC,EAAiBQ,EAASkC,EAAqBK,EAAuB3C,KAG3FhO,EAAAA,EAAAA,MAAA,OAAKF,UAAU,iCAAgCF,SAAA,EAC7CC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,yBAEH6L,IACC5L,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBF,UAC/BI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,4BAA2BF,SAAA,EACxCC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAwCuR,KAAK,SAAQzR,UAClEC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBF,SAAC,iBAC9B,8BAMXiE,IACC7D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,qBAAoBF,SAAA,EACjCC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,WAAe,IAAEiE,GACzBhE,EAAAA,EAAAA,KAAA,UACEC,UAAU,qCACVC,QAASsO,EAAQzO,SAClB,cAMH6L,IAAY5H,IACZ7D,EAAAA,EAAAA,MAAA,OAAKF,UAAU,MAAKF,SAAA,EAClBC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUF,UACvBC,EAAAA,EAAAA,KAACyR,EAAgB,CACf7Q,GAAG,gBACHmJ,MAAM,iBACNN,QAAS4E,EACTpC,eAAgB4B,EAChBzF,SAAU6F,EACV1F,SAAUqD,EACV3B,YAAY,4BAIhBjK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUF,UACvBC,EAAAA,EAAAA,KAACyR,EAAgB,CACf7Q,GAAG,kBACHmJ,MAAM,mBACNN,QAASoH,EACT5E,eAAgB6B,EAChB1F,SAAU8F,EACV3F,SAAqC,IAA3BsF,EAAgBhI,QAAgB+F,EAC1C3B,YAAY,8BAIhBjK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUF,UACvBC,EAAAA,EAAAA,KAACyR,EAAgB,CACf7Q,GAAG,gBACHmJ,MAAM,iBACNN,QAASyH,EACTjF,eAAgB8B,EAChB3F,SAAU+F,EACV5F,SAAuC,IAA7BuF,EAAkBjI,QAAgB+F,EAC5C3B,YAAY,4BAIhBjK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUF,UACvBI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAO6J,QAAQ,mBAAmB/J,UAAU,aAAYF,SAAA,CAAC,sBACrCC,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaF,SAAC,UAElDI,EAAAA,EAAAA,MAAA,UACES,GAAG,mBACHX,UAAW,gBAAgB+N,EAAmC,GAAf,cAC/ChG,MAAOgG,EACP5F,SAAWhI,GAAMgO,EAAkBhO,EAAEkI,OAAON,OAC5CO,SAAUqD,EACV1B,UAAQ,EAAAnK,SAAA,EAERC,EAAAA,EAAAA,KAAA,UAAQgI,MAAM,GAAEjI,SAAC,2BAChBiM,EAAmB9K,KAAIwQ,IACtB1R,EAAAA,EAAAA,KAAA,UAA8BgI,MAAO0J,EAAU1J,MAAMjI,SAClD2R,EAAU3H,OADA2H,EAAU1J,aAKzBgG,IACAhO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBF,SAAC,4CAQxC,ECyMV,EAzW8B4R,KAAO,IAADC,EAAAC,EAElC,MAAMC,ECf2BC,MACjC,MAAOhQ,EAAYC,IAAiBmK,EAAAA,EAAAA,UAAqB,KAClDlK,EAAcC,IAAmBiK,EAAAA,EAAAA,UAAiB,KAClDb,EAAYxI,IAAiBqJ,EAAAA,EAAAA,UAA4B,OACzD/F,EAAQrD,IAAaoJ,EAAAA,EAAAA,UAAsB,KAC3C6F,EAAwBC,IAA6B9F,EAAAA,EAAAA,UAA6B,KAClF7E,EAAW7E,IAAgB0J,EAAAA,EAAAA,WAAkB,IAC7CP,EAASiD,IAAc1C,EAAAA,EAAAA,WAAS,IAChCnI,EAAOtB,IAAYyJ,EAAAA,EAAAA,UAAwB,OAC3C+F,EAASvP,IAAcwJ,EAAAA,EAAAA,UAAwB,OAE/CgG,EAAiBtP,IAAsBsJ,EAAAA,EAAAA,WAAkB,IACzDhK,EAAWC,IAAgB+J,EAAAA,EAAAA,UAAiB,KAC5C9J,EAAcC,IAAmB6J,EAAAA,EAAAA,UAAiB,KAClDiG,EAAkBxP,IAAuBuJ,EAAAA,EAAAA,WAAkB,IAE3DzF,EAAa1D,IAAkBmJ,EAAAA,EAAAA,UAA0B,OACzDkG,EAAepP,IAAoBkJ,EAAAA,EAAAA,WAAkB,IAErDmG,EAAcpP,IAAmBiJ,EAAAA,EAAAA,UAAwB,OACzDoG,EAAwBpP,IAA6BgJ,EAAAA,EAAAA,WAAkB,IAEvE5J,EAAYC,IAAiB2J,EAAAA,EAAAA,UAAiB,KAG9CqG,EAAeC,IAAoBtG,EAAAA,EAAAA,WAAS,IAC5CuG,EAAgBC,IAAqBxG,EAAAA,EAAAA,UAAS,KAG9C0B,EAAiB+E,IAAsBzG,EAAAA,EAAAA,UAAmB,KAC1D2B,EAAmB+E,IAAwB1G,EAAAA,EAAAA,UAAmB,KAC9D4B,EAAiB+E,IAAsB3G,EAAAA,EAAAA,UAAmB,KAC1D6B,EAAmB+E,IAAwB5G,EAAAA,EAAAA,UAAiB,IAEnE,MAAO,CAELpK,aACAE,eACAqJ,aACAlF,SACA4L,yBACA1K,YACAsE,UACA5H,QACAkO,UACAC,kBACAhQ,YACAE,eACA+P,mBACA1L,cACA2L,gBACAC,eACAC,yBACAhQ,aACAiQ,gBACAE,iBACA7E,kBACAC,oBACAC,kBACAC,oBAGAhM,gBACAE,kBACAY,gBACAC,YACAkP,4BACAxP,eACAoM,aACAnM,WACAC,aACAE,qBACAT,eACAE,kBACAM,sBACAI,iBACAC,mBACAC,kBACAC,4BACAX,gBACAiQ,mBACAE,oBACAC,qBACAC,uBACAC,qBACAC,uBACD,EDvEahB,GAKRiB,EAAiBnR,EAAkB,CACvCE,WAAY+P,EAAM/P,WAClBC,cAAe8P,EAAM9P,cACrBC,aAAc6P,EAAM7P,aACpBC,gBAAiB4P,EAAM5P,gBACvBC,UAAW2P,EAAM3P,UACjBC,aAAc0P,EAAM1P,aACpBC,aAAcyP,EAAMzP,aACpBC,gBAAiBwP,EAAMxP,gBACvBC,WAAYuP,EAAMvP,WAClBC,cAAesP,EAAMtP,cACrBC,aAAcqP,EAAMrP,aACpBC,SAAUoP,EAAMpP,SAChBC,WAAYmP,EAAMnP,WAClBC,oBAAqBkP,EAAMlP,oBAC3BC,mBAAoBiP,EAAMjP,mBAC1BC,cAAegP,EAAMhP,cACrBC,UAAW+O,EAAM/O,UACjBC,eAAgB8O,EAAM9O,eACtBC,iBAAkB6O,EAAM7O,iBACxBC,gBAAiB4O,EAAM5O,gBACvBC,0BAA2B2O,EAAM3O,4BAG7B8P,EThB6BnR,KACnC,MAAM,WACJC,EAAU,aACVE,EAAY,WACZqJ,EAAU,cACVxI,EAAa,OACbsD,EAAM,UACNrD,EAAS,0BACTkP,EAAyB,WACzBpD,EAAU,SACVnM,EAAQ,WACRC,EAAU,kBACVgQ,EAAiB,iBACjBF,EAAgB,gBAChB5E,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,mBACjB4E,EAAkB,qBAClBC,EAAoB,mBACpBC,EAAkB,qBAClBC,GACEjR,EAoTJ,MAAO,CACLoR,wBAnT6B7P,EAAAA,EAAAA,cAAYC,UACzC,GAAK6P,EAAL,CACApP,QAAQgL,IAAI,4CAA4CoE,KACxD,IACE,MAAMC,GAAgBxP,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,cAAeyP,GACvCE,QAAuBlP,EAAAA,EAAAA,IAAOiP,GACpC,GAAIC,EAAejP,SAAU,CAC3B,MAAMkP,EAAiBD,EAAexP,OACtCoO,EAA0BqB,EAAelN,QAAU,IACnDrC,QAAQgL,IAAI,0BAA2BuE,EAAelN,OACxD,MACErC,QAAQgL,IAAI,mDAAmDoE,KAC/DlB,EAA0B,GAE9B,CAAE,MAAOnO,GACPC,QAAQC,MAAM,sCAAuCF,GACrDpB,EAAS,wCACTuP,EAA0B,GAC5B,CAjBmB,CAiBnB,GACC,CAACA,EAA2BvP,IAiS7B6Q,gBA/RqBlQ,EAAAA,EAAAA,cAAYC,UACjC,GAAK/C,EAAL,CAIAwD,QAAQgL,IAAI,qCAAqCxO,KACjDsO,GAAW,GACXnM,EAAS,MACT,IAEE,MAAMwB,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASnD,GAC1BiT,QAAgBrP,EAAAA,EAAAA,IAAOD,GAE7B,IAAIL,EAA0B,KAE9B,GAAI2P,EAAQpP,SAEVP,EAAO2P,EAAQ3P,YAGf,IACEA,QAAa4P,EAAAA,EAAoBF,eAAehT,EAClD,CAAE,MAAOmT,GACP,CAIJ,GAAI7P,EACFf,EAAce,GACdd,EAAUc,EAAKuC,QAAU,IAEzBwM,EAAmB/O,EAAKgK,kBAAoBhK,EAAK8P,eAAiB,CAAC9P,EAAK8P,gBAAkB,KAC1Fd,EAAqBhP,EAAKiK,oBAAsBjK,EAAK+P,iBAAmB,CAAC/P,EAAK+P,kBAAoB,KAClGd,EAAmBjP,EAAKkK,kBAAoBlK,EAAKgQ,eAAiB,CAAChQ,EAAKgQ,gBAAkB,KAC1Fd,EAAqBlP,EAAKmK,mBAAqB,QAC1C,CAEL,MAAMvN,EAAOsB,EAAWrB,MAAKC,GAAKA,EAAEC,KAAOL,IAC3CuC,EAAc,CACZlC,GAAIL,EACJT,OAAW,OAAJW,QAAI,IAAJA,OAAI,EAAJA,EAAMX,QAAS,WACtBsG,OAAQ,GACRJ,aAAa,IAAIC,MAAOC,gBAE1BnD,EAAU,IAEV6P,EAAmB,IACnBC,EAAqB,IACrBC,EAAmB,IACnBC,EAAqB,GACvB,CACF,CAAE,MAAOjP,GACPpB,EAAS,sCACTqB,QAAQC,MAAMF,GACdhB,EAAc,MACdC,EAAU,GACZ,CAAC,QACC8L,GAAW,EACb,CAtDA,MAFE9K,QAAQgL,IAAI,uCAwDd,GACC,CAAChN,EAAY8M,EAAYnM,EAAUI,EAAeC,EAAW6P,EAAoBC,EAAsBC,EAAoBC,IAqO5He,SAnOeA,KACf,MAAMC,EAAsB,CAC1BnT,GAAI,SAASqF,KAAK+N,QAClBrK,KAAM,OACNI,MAAO,YACPE,YAAa,GACbR,QAAS,GACTS,UAAU,EACVoF,OAAQ,GACRQ,SAAU,GACVqB,OAAQ,IAEVpO,EAAU,IAAIqD,EAAQ2N,GAAU,EAwNhCE,oBArN2BC,IAC3BnQ,QAAQgL,IAAI,mCAAoCmF,GAChD,MAAMH,EAAsB,CAC1BnT,GAAIsT,EAAatT,GACjB+I,KAAMuK,EAAavK,KACnBI,MAAOmK,EAAanK,MACpBE,YAAaiK,EAAajK,YAC1BR,QAASyK,EAAazK,QAAUyK,EAAazK,QAAQvI,KAAKqJ,GACrC,kBAARA,EACF,CAAER,MAAOQ,EAAKvC,MAAOuC,GAErB,CAAER,MAAOQ,EAAIR,MAAO/B,MAAOuC,EAAIvC,cAErCH,EACLqC,SAAUgK,EAAahK,SACvBJ,aAAcoK,EAAapK,aAC3BM,IAAK8J,EAAa9J,IAClBE,IAAK4J,EAAa5J,IAClBe,kBAAcxD,EACdsM,aAAStM,EACTuD,gBAAYvD,EACZuM,gBAAYvM,EACZwM,mBAAexM,EACfG,WAAOH,GAGT,GAAIzB,EAAOrF,MAAKkI,GAASA,EAAMrI,KAAOmT,EAASnT,KAI3C,OAHAmD,QAAQuQ,KAAK,iCAAiCP,EAASnT,yBACvD8B,EAAS,kBAAkBqR,EAASnT,sDACpC2F,YAAW,IAAM7D,EAAS,OAAO,KAIrCqB,QAAQgL,IAAI,6BAA8BgF,GAC1ChR,EAAU,IAAIqD,EAAQ2N,IACtBpR,EAAW,gBAAgBoR,EAAShK,iCACpCxD,YAAW,IAAM5D,EAAW,OAAO,IAAK,EAkLxC4R,YA/KkBA,CAACrL,EAAesL,KAClC,MAAMC,EAAgB,IAAIrO,GAC1BqO,EAAcvL,GAASsL,EACvBzR,EAAU0R,EAAc,EA6KxBC,YA1KmBxL,IACnBnG,EAAUqD,EAAO1E,QAAO,CAAC+I,EAAGC,IAAMA,IAAMxB,IAAO,EA0K/CyL,WAvKiBrR,UACjB,GAAKrB,GAAiBqJ,EAMtB,GAAK0C,EAAL,CAKAa,GAAW,GACX9K,QAAQgL,IAAI,oDAAqD9M,GACjE8B,QAAQgL,IAAI,sBAAuB3I,GACnCrC,QAAQgL,IAAI,oBAAqBf,GAEjC,IAAK,IAADzJ,EACF,MAAMqQ,EAAgBxO,EAAOlF,KAAI+H,IAC/B,MAAM4L,EAAoB,CAAC,EAC3B,IAAK,MAAMtL,KAAON,OACGpB,IAAfoB,EAAMM,GACRsL,EAAatL,GAAON,EAAMM,GAE1BsL,EAAatL,GAAO,KAGxB,OAAOsL,CAAY,IAGfC,EAAgC,IACjCxJ,EACH1K,GAAIqB,EACJnC,OAAkD,QAA3CyE,EAAAxC,EAAWrB,MAAKC,GAAKA,EAAEC,KAAOqB,WAAa,IAAAsC,OAAA,EAA3CA,EAA6CzE,QAASwL,EAAWxL,MACxEsG,OAAQwO,EACR5O,aAAa,IAAIC,MAAOC,cACxB2H,kBACAC,oBACAC,kBACAC,qBAII+G,EAAe,GAGrBA,EAAaxT,MACXwE,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASzB,GAAe6S,GACpCE,OAAMlR,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAImR,MAAM,yBAAyBnR,EAAIoR,UAAU,KAK7DH,EAAaxT,KACXkS,EAAAA,EAAoB0B,eAAeL,GAChCE,OAAMlR,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAImR,MAAM,yBAAyBnR,EAAIoR,UAAU,WAKvDE,QAAQC,IAAIN,GAElBjS,EAAcgS,GACdnS,EAAW,0CACX4D,YAAW,IAAM5D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPC,QAAQC,MAAM,qCAAsCF,GACpDpB,EAAS,sCAAsCoB,aAAemR,MAAQnR,EAAIoR,QAAU,kBACtF,CAAC,QACCrG,GAAW,EACb,CAjEA,MAFEnM,EAAS,+EANTA,EAAS,mDAyEX,EA6FA4S,cA1FoBA,KACpB,IAAKhK,GAAgC,IAAlBlF,EAAOP,OAExB,YADA0P,MAAM,+CAIR,MAAMC,EAAmB,eACjBlK,EAAWxL,qCAEbsG,EAAOlF,KAAI+H,IAAU,IAADuB,EAAAxB,EACpB,IAAIyM,EAAY,GAChB,OAAQxM,EAAMU,MACZ,IAAK,OACL,IAAK,SACL,IAAK,OACL,IAAK,WACH8L,EAAY,gGAEoBxM,EAAMc,QAAQd,EAAMiB,SAAW,KAAO,8CACnDjB,EAAMU,2CAA2CV,EAAMgB,aAAe,OAAOhB,EAAMiB,SAAW,WAAa,gDAG9H,MACF,IAAK,WACHuL,EAAY,gGAEoBxM,EAAMc,QAAQd,EAAMiB,SAAW,KAAO,8DACnCjB,EAAMiB,SAAW,WAAa,oDACjCjB,EAAMc,wCACjB,QAAbS,EAAAvB,EAAMQ,eAAO,IAAAe,OAAA,EAAbA,EAAetJ,KAAImM,GAAU,kBAAkBA,EAAOrF,UAAUqF,EAAOtD,mBAAkBkB,KAAK,MAAO,0EAI7G,MACF,IAAK,WACHwK,EAAY,0HAE8CxM,EAAMrI,OAAOqI,EAAMiB,SAAW,WAAa,iEAC1DjB,EAAMrI,OAAOqI,EAAMc,QAAQd,EAAMiB,SAAW,KAAO,qDAG9F,MACF,IAAK,QACHuL,EAAY,gGAEoBxM,EAAMc,QAAQd,EAAMiB,SAAW,KAAO,kCACnD,QAAblB,EAAAC,EAAMQ,eAAO,IAAAT,OAAA,EAAbA,EAAe9H,KAAI,CAACmM,EAAQ3C,IAAM,4HAEqBzB,EAAMrI,WAAWqI,EAAMrI,MAAM8J,aAAa2C,EAAOrF,UAAUiB,EAAMiB,SAAW,WAAa,mEACvGjB,EAAMrI,MAAM8J,MAAM2C,EAAOtD,kEAEjEkB,KAAK,MAAO,6CAGnB,MACF,IAAK,UACHwK,EAAY,8FAEmBxM,EAAMoC,cAAgB,yNAMrD,MACF,IAAK,SACHoK,EAAY,wEAC2CxM,EAAMmC,YAAc,oCAE3E,MACF,QACEqK,EAAY,8BAA8BxM,EAAMU,WAEpD,OAAO8L,CAAS,IACfxK,KAAK,2BAIZ0H,EAAkB6C,GAClB/C,GAAiB,EAAK,EAYvB,ESnUyBiD,CAAqB,CAC7C3T,WAAY+P,EAAM/P,WAClBE,aAAc6P,EAAM7P,aACpBqJ,WAAYwG,EAAMxG,WAClBxI,cAAegP,EAAMhP,cACrBsD,OAAQ0L,EAAM1L,OACdrD,UAAW+O,EAAM/O,UACjBkP,0BAA2BH,EAAMG,0BACjCpD,WAAYiD,EAAMjD,WAClBnM,SAAUoP,EAAMpP,SAChBC,WAAYmP,EAAMnP,WAClBgQ,kBAAmBb,EAAMa,kBACzBF,iBAAkBX,EAAMW,iBACxB5E,gBAAiBiE,EAAMjE,gBACvBC,kBAAmBgE,EAAMhE,kBACzBC,gBAAiB+D,EAAM/D,gBACvBC,kBAAmB8D,EAAM9D,kBACzB4E,mBAAoBd,EAAMc,mBAC1BC,qBAAsBf,EAAMe,qBAC5BC,mBAAoBhB,EAAMgB,mBAC1BC,qBAAsBjB,EAAMiB,wBAI9BzG,EAAAA,EAAAA,YAAU,KACR0G,EAAe5P,iBAAiB,GAC/B,KAGHkJ,EAAAA,EAAAA,YAAU,KACJwF,EAAM7P,cAAgBnB,EAAWgR,EAAM7P,aAAc6P,EAAM/P,cAAgBzB,EAAWwR,EAAM7P,aAAc6P,EAAM/P,aAAoC,kBAArB+P,EAAMvP,YACvI0Q,EAAkBM,eAAezB,EAAM7P,cACvCgR,EAAkBC,uBAAuBpB,EAAM7P,gBACtC6P,EAAM7P,cAAkBnB,EAAWgR,EAAM7P,aAAc6P,EAAM/P,cAAezB,EAAWwR,EAAM7P,aAAc6P,EAAM/P,aAAqC,kBAArB+P,EAAMvP,WAItIuP,EAAM7P,eAChB6P,EAAMhP,cAAc,MACpBgP,EAAM/O,UAAU,IAChB+O,EAAMG,0BAA0B,IAChCH,EAAMtP,cAAc,MAPpBsP,EAAMhP,cAAc,MACpBgP,EAAM/O,UAAU,IAChB+O,EAAMG,0BAA0B,KAQ9BH,EAAM7P,cAAqC,kBAArB6P,EAAMvP,YAC5BuP,EAAMG,0BAA0B,GACpC,GACC,CAACH,EAAM7P,aAAc6P,EAAM/P,WAAY+P,EAAMvP,aAqEhD,OACEvC,EAAAA,EAAAA,KAAAyI,EAAAA,SAAA,CAAA1I,UACEI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcF,SAAA,CAC1B+R,EAAM9N,QAAShE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeF,SAAE+R,EAAM9N,QACrD8N,EAAMI,UACLlS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBF,SAC7B+R,EAAMI,WAGXlS,EAAAA,EAAAA,KAAA,MAAAD,SAAI,2BAEJC,EAAAA,EAAAA,KAAC2V,EAAY,CACX5T,WAAY+P,EAAM/P,WAClBE,aAAc6P,EAAM7P,aACpBmF,aAhFkB7G,IAGxB,GAFAuR,EAAM5P,gBAAgB3B,GACtBuR,EAAMtP,cAAc,IACfjC,EAGE,CACH,MAAMqV,EAAa9U,EAAWP,EAAQuR,EAAM/P,YACtC8T,EAAavV,EAAWC,EAAQuR,EAAM/P,YACxC6T,IAAcC,IACd/D,EAAMhP,cAAc,MACpBgP,EAAM/O,UAAU,IAExB,MATI+O,EAAMhP,cAAc,MACpBgP,EAAM/O,UAAU,GAQpB,EAoEMR,WAAYuP,EAAMvP,WAClB8E,eAlEoByO,IAC1BhE,EAAMtP,cAAcsT,EAAO,EAkErBxO,UAAWwK,EAAMxK,UACjBC,eAhEmBwO,KACzBjE,EAAM1P,aAAa,IACnB0P,EAAMxP,gBAAgB,IACtBwP,EAAMjP,oBAAmB,EAAK,EA8DxB2E,gBA3DoBwO,KACtBlE,EAAM7P,cAAgBnB,EAAWgR,EAAM7P,aAAc6P,EAAM/P,cAAgBzB,EAAWwR,EAAM7P,aAAc6P,EAAM/P,YAClHkR,EAAkBM,eAAezB,EAAM7P,cAC9B6P,EAAM7P,eACf6P,EAAMpP,SAAS,sFACfoP,EAAMhP,cAAc,MACpBgP,EAAM/O,UAAU,IAClB,IAwDK+O,EAAMK,kBACLnS,EAAAA,EAAAA,KAACiW,EAAK,CACJrW,OAAQkS,EAAMK,gBACdtS,QAASA,KACPiS,EAAMjP,oBAAmB,GACzBiP,EAAMtP,cAAc,IACpBsP,EAAM1P,aAAa,IACnB0P,EAAMxP,gBAAgB,GAAG,EAE3BxC,MACuB,qBAArBgS,EAAMvP,WAAoC,yBAC1CuP,EAAM7P,cAAqC,qBAArB6P,EAAMvP,WAAoC,4BAAmF,QAAnFqP,EAA4BE,EAAM/P,WAAWrB,MAAKC,GAAKA,EAAEC,KAAOkR,EAAM7P,sBAAa,IAAA2P,OAAA,EAAvDA,EAAyD9R,SACrJ,oBACDC,UAEDI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gBAAeF,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,SACE2J,KAAK,OACLM,YAAY,oCACZjC,MAAO8J,EAAM3P,UACbiG,SAAWhI,GAAM0R,EAAM1P,aAAahC,EAAEkI,OAAON,MAAM4H,cAAcjL,QAAQ,OAAQ,MACjF1E,UAAU,uBAEZD,EAAAA,EAAAA,KAAA,SACE2J,KAAK,OACLM,YAAY,eACZjC,MAAO8J,EAAMzP,aACb+F,SAAWhI,GAAM0R,EAAMxP,gBAAgBlC,EAAEkI,OAAON,OAChD/H,UAAU,uBAEZE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BF,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEE,QAAS8S,EAAe1O,oBACxBiE,SAAUuJ,EAAMxK,YAAcwK,EAAM3P,YAAc2P,EAAMzP,aACxDpC,UAAU,kBAAiBF,SAE1B+R,EAAMxK,UAAY,cAAgB,6BAErCtH,EAAAA,EAAAA,KAAA,UAAQE,QAASA,KACf4R,EAAMjP,oBAAmB,GACzBiP,EAAMtP,cAAc,IACpBsP,EAAM1P,aAAa,IACnB0P,EAAMxP,gBAAgB,GAAG,EACxBrC,UAAU,oBAAmBF,SAAC,mBASxC+R,EAAM7P,eACL9B,EAAAA,EAAAA,MAAAsI,EAAAA,SAAA,CAAA1I,SAAA,GAE0B,kBAArB+R,EAAMvP,YAAkCzB,EAAWgR,EAAM7P,aAAc6P,EAAM/P,cAAgBzB,EAAWwR,EAAM7P,aAAc6P,EAAM/P,aAAe+P,EAAMxG,cACxJtL,EAAAA,EAAAA,KAACkW,EAAc,CACbjU,aAAc6P,EAAM7P,aACpBF,WAAY+P,EAAM/P,WAClB2G,WAAYsK,EAAexM,eAC3BmC,aAAcqK,EAAepM,oBAKX,kBAArBkL,EAAMvP,YAAkCzB,EAAWgR,EAAM7P,aAAc6P,EAAM/P,cAAgBzB,EAAWwR,EAAM7P,aAAc6P,EAAM/P,cACjI/B,EAAAA,EAAAA,KAACmW,EAAmB,CAClBtI,gBAAiBiE,EAAMjE,gBACvBC,kBAAmBgE,EAAMhE,kBACzBC,gBAAiB+D,EAAM/D,gBACvBC,kBAAmB8D,EAAM9D,kBACzBC,gBA3HeI,IAC3ByD,EAAMc,mBAAmBvE,GAEzByD,EAAMe,qBAAqB,IAC3Bf,EAAMgB,mBAAmB,GAAG,EAwHhB5E,kBArHiBI,IAC7BwD,EAAMe,qBAAqBvE,GAE3BwD,EAAMgB,mBAAmB,GAAG,EAmHhB3E,gBAhHeI,IAC3BuD,EAAMgB,mBAAmBvE,EAAQ,EAgHrBH,kBA7GiBsD,IAC7BI,EAAMiB,qBAAqBrB,EAAU,IAiHP,kBAArBI,EAAMvP,YAAkCzB,EAAWgR,EAAM7P,aAAc6P,EAAM/P,cAAgBzB,EAAWwR,EAAM7P,aAAc6P,EAAM/P,aAAe+P,EAAMxG,aACtJtL,EAAAA,EAAAA,KAACoW,EAAkB,CACjB9K,WAAYwG,EAAMxG,WAClBlF,OAAQ0L,EAAM1L,OACdmF,WAAY0H,EAAkBa,SAC9BtI,cAAeyH,EAAkBsB,YACjC9I,cAAewH,EAAkByB,YACjChJ,OAAQuH,EAAkB0B,WAC1BhJ,UAAWsH,EAAkBqC,cAC7B1J,QAASkG,EAAMlG,UAKG,kBAArBkG,EAAMvP,cAAoCzB,EAAWgR,EAAM7P,aAAc6P,EAAM/P,aAAezB,EAAWwR,EAAM7P,aAAc6P,EAAM/P,eAClI/B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wDAAuDF,SAAC,iLAInD,kBAArB+R,EAAMvP,aAAmCzB,EAAWgR,EAAM7P,aAAc6P,EAAM/P,cAC7E/B,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kDAAiDF,SAAC,6IAOrE+R,EAAM7P,cAAqC,KAArB6P,EAAMvP,aAC5BpC,EAAAA,EAAAA,MAAA,OAAKF,UAAU,gDAA+CF,SAAA,EAC5DC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,+FACHC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,wJAKN+R,EAAMO,eAAiBP,EAAMpL,cAC5BvG,EAAAA,EAAAA,MAAC8V,EAAK,CACJrW,OAAQkS,EAAMO,cACdxS,QAASA,KACPiS,EAAM7O,kBAAiB,GACvB6O,EAAMxP,gBAAgB,IACtBwP,EAAM9O,eAAe,KAAK,EAE5BlD,MAAO,gBAAgBgS,EAAMpL,YAAY5G,QAAQC,SAAA,EAEjDC,EAAAA,EAAAA,KAAA,SACE2J,KAAK,OACL3B,MAAO8J,EAAMzP,aACb+F,SAAWhI,GAAM0R,EAAMxP,gBAAgBlC,EAAEkI,OAAON,OAChDiC,YAAY,mBACZhK,UAAU,uBAEZE,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BF,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEE,QAAS8S,EAAevM,iBACxBxG,UAAU,kBACVsI,SAAUuJ,EAAMxK,YAAcwK,EAAMzP,aAAa8I,OAAOpL,SAEvD+R,EAAMxK,UAAY,cAAgB,kBAErCtH,EAAAA,EAAAA,KAAA,UACEE,QAASA,KACP4R,EAAM7O,kBAAiB,GACvB6O,EAAMxP,gBAAgB,IACtBwP,EAAM9O,eAAe,KAAK,EAE5B/C,UAAU,oBAAmBF,SAC9B,iBAON+R,EAAMS,wBAA0BT,EAAMQ,eACrCnS,EAAAA,EAAAA,MAAC8V,EAAK,CACJrW,OAAQkS,EAAMS,uBACd1S,QAASA,IAAMiS,EAAM3O,2BAA0B,GAC/CrD,MAAM,mBAAkBC,SAAA,EAExBI,EAAAA,EAAAA,MAAA,KAAAJ,SAAA,CAAG,+CAAoG,QAAxD8R,EAACC,EAAM/P,WAAWrB,MAAKC,GAAKA,EAAEC,KAAOkR,EAAMQ,sBAAa,IAAAT,OAAA,EAAvDA,EAAyD/R,MAAM,qGAC/GK,EAAAA,EAAAA,MAAA,OAAKF,UAAU,6BAA4BF,SAAA,EACzCC,EAAAA,EAAAA,KAAA,UACEE,QAAS8S,EAAenM,oBACxB5G,UAAU,iBACVsI,SAAUuJ,EAAMxK,UAAUvH,SAEzB+R,EAAMxK,UAAY,cAAgB,oBAErCtH,EAAAA,EAAAA,KAAA,UACEE,QAASA,IAAM4R,EAAM3O,2BAA0B,GAC/ClD,UAAU,oBAAmBF,SAC9B,kBAQPC,EAAAA,EAAAA,KAACiW,EAAK,CACJrW,OAAQkS,EAAMU,cACd3S,QAASA,IAAMiS,EAAMW,kBAAiB,GACtC3S,MAAM,eAAcC,UAEpBC,EAAAA,EAAAA,KAAA,OAAKqW,wBAAyB,CAAEC,OAAQxE,EAAMY,wBAGjD,E,2MEpXA,SAAS6D,EAAwBC,GACtC,OAAOC,EAAAA,EAAAA,IAAqB,cAAeD,EAC7C,EACwBE,EAAAA,EAAAA,GAAuB,cAAe,CAAC,OAAQ,YAAa,QAAS,sBAAuB,UAAW,UAAW,UAAW,oBCArJ,MACA,GAD8BA,EAAAA,EAAAA,GAAuB,oBAAqB,CAAC,OAAQ,eAAgB,QAAS,sBAAuB,WAAY,UAAW,UAAW,aCH9J,SAASC,EAA8CH,GAC5D,OAAOC,EAAAA,EAAAA,IAAqB,6BAA8BD,EAC5D,EACuCE,EAAAA,EAAAA,GAAuB,6BAA8B,CAAC,OAAQ,mBAArG,MCgBME,GAA8BC,EAAAA,EAAAA,IAAO,MAAO,CAChD1Q,KAAM,6BACNqQ,KAAM,OACNM,kBAAmBA,CAAChV,EAAOiV,KACzB,MAAM,WACJC,GACElV,EACJ,MAAO,CAACiV,EAAOE,KAAMD,EAAWE,gBAAkBH,EAAOG,eAAe,GAPxCL,CASjC,CACDM,SAAU,WACVC,MAAO,GACPC,IAAK,MACLC,UAAW,mBACXC,SAAU,CAAC,CACTzV,MAAOnC,IAAA,IAAC,WACNqX,GACDrX,EAAA,OAAKqX,EAAWE,cAAc,EAC/BjP,MAAO,CACLmP,MAAO,OAUPI,EAAuC3O,EAAAA,YAAiB,SAAiC4O,EAASzK,GACtG,MAAMlL,GAAQ4V,EAAAA,EAAAA,GAAgB,CAC5B5V,MAAO2V,EACPtR,KAAM,gCAEF,UACJlG,KACG0X,GACD7V,EACE8V,EAAU/O,EAAAA,WAAiBgP,EAAAA,GAC3Bb,EAAa,IACdlV,EACHoV,eAAgBU,EAAQV,gBAEpBY,EArDkBd,KACxB,MAAM,eACJE,EAAc,QACdY,GACEd,EACEe,EAAQ,CACZd,KAAM,CAAC,OAAQC,GAAkB,mBAEnC,OAAOc,EAAAA,EAAAA,GAAeD,EAAOpB,EAA+CmB,EAAQ,EA6CpEG,CAAkBjB,GAClC,OAAoBhX,EAAAA,EAAAA,KAAK4W,EAA6B,CACpD3W,WAAWiY,EAAAA,EAAAA,GAAKJ,EAAQb,KAAMhX,GAC9B+W,WAAYA,EACZhK,IAAKA,KACF2K,GAEP,IAuBAH,EAAwBW,QAAU,0BAClC,UCtDaC,IAAevB,EAAAA,EAAAA,IAAO,MAAO,CACxC1Q,KAAM,cACNqQ,KAAM,OACNM,kBAzB+BA,CAAChV,EAAOiV,KACvC,MAAM,WACJC,GACElV,EACJ,MAAO,CAACiV,EAAOE,KAAMD,EAAWqB,OAAStB,EAAOsB,MAAiC,eAA1BrB,EAAWsB,YAA+BvB,EAAOwB,oBAAqBvB,EAAWwB,SAAWzB,EAAOyB,SAAUxB,EAAWE,gBAAkBH,EAAO0B,SAAUzB,EAAW0B,gBAAkB3B,EAAO4B,QAAS3B,EAAW4B,oBAAsB7B,EAAO8B,gBAAgB,GAkB7RhC,EAIzBiC,EAAAA,EAAAA,IAAUnZ,IAAA,IAAC,MACZoZ,GACDpZ,EAAA,MAAM,CACLqZ,QAAS,OACTC,eAAgB,aAChBX,WAAY,SACZnB,SAAU,WACV+B,eAAgB,OAChBC,MAAO,OACPC,UAAW,aACXC,UAAW,OACX9B,SAAU,CAAC,CACTzV,MAAOwX,IAAA,IAAC,WACNtC,GACDsC,EAAA,OAAMtC,EAAW0B,cAAc,EAChCzQ,MAAO,CACLsR,WAAY,EACZC,cAAe,IAEhB,CACD1X,MAAO2X,IAAA,IAAC,WACNzC,GACDyC,EAAA,OAAMzC,EAAW0B,gBAAkB1B,EAAWqB,KAAK,EACpDpQ,MAAO,CACLsR,WAAY,EACZC,cAAe,IAEhB,CACD1X,MAAO4X,IAAA,IAAC,WACN1C,GACD0C,EAAA,OAAM1C,EAAW0B,iBAAmB1B,EAAWE,cAAc,EAC9DjP,MAAO,CACLC,YAAa,GACbyR,aAAc,KAEf,CACD7X,MAAO8X,IAAA,IAAC,WACN5C,GACD4C,EAAA,OAAM5C,EAAW0B,kBAAoB1B,EAAW6B,eAAe,EAChE5Q,MAAO,CAGL0R,aAAc,KAEf,CACD7X,MAAO+X,IAAA,IAAC,WACN7C,GACD6C,EAAA,QAAO7C,EAAW6B,eAAe,EAClC5Q,MAAO,CACL,CAAC,QAAQ6R,EAAsB7C,QAAS,CACtC0C,aAAc,MAGjB,CACD7X,MAAO,CACLwW,WAAY,cAEdrQ,MAAO,CACLqQ,WAAY,eAEb,CACDxW,MAAOiY,IAAA,IAAC,WACN/C,GACD+C,EAAA,OAAK/C,EAAWwB,OAAO,EACxBvQ,MAAO,CACL+R,aAAc,cAAcjB,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,UACzD2B,eAAgB,gBAEjB,CACDrY,MAAOsY,IAAA,IAAC,WACNpD,GACDoD,EAAA,OAAKpD,EAAWqD,MAAM,EACvBpS,MAAO,CACLqS,WAAYvB,EAAMwB,YAAYC,OAAO,mBAAoB,CACvDC,SAAU1B,EAAMwB,YAAYE,SAASC,WAEvC,UAAW,CACTxB,eAAgB,OAChBjM,iBAAkB8L,EAAMkB,MAAQlB,GAAOmB,QAAQpE,OAAO6E,MAEtD,uBAAwB,CACtB1N,gBAAiB,kBAItB,CACDnL,MAAO8Y,IAAA,IAAC,WACN5D,GACD4D,EAAA,OAAK5D,EAAW4B,kBAAkB,EACnC3Q,MAAO,CAGL0R,aAAc,MAGnB,KACKkB,IAAoBhE,EAAAA,EAAAA,IAAO,KAAM,CACrC1Q,KAAM,cACNqQ,KAAM,aAFkBK,CAGvB,CACDM,SAAU,aAiPZ,GA3O8BtO,EAAAA,YAAiB,SAAkB4O,EAASzK,GACxE,MAAMlL,GAAQ4V,EAAAA,EAAAA,GAAgB,CAC5B5V,MAAO2V,EACPtR,KAAM,iBAEF,WACJmS,EAAa,SACbvY,SAAU+a,EAAY,UACtB7a,EACA8a,UAAWC,EAAa,WACxBC,EAAa,CAAC,EAAC,gBACfC,EAAkB,CAAC,EAAC,mBACpBC,EAAqB,KACrBC,gBACEnb,UAAWob,KACRD,GACD,CAAC,EAAC,MACN/C,GAAQ,EAAK,eACbnB,GAAiB,EAAK,eACtBwB,GAAiB,EAAK,QACtBF,GAAU,EAAK,gBACfK,EAAe,UACfyC,EAAY,CAAC,EAAC,MACdvD,EAAQ,CAAC,KACNJ,GACD7V,EACE8V,EAAU/O,EAAAA,WAAiBgP,EAAAA,GAC3B0D,EAAe1S,EAAAA,SAAc,KAAM,CACvCwP,MAAOA,GAAST,EAAQS,QAAS,EACjCC,aACApB,oBACE,CAACoB,EAAYV,EAAQS,MAAOA,EAAOnB,IACjCsE,EAAc3S,EAAAA,OAAa,MAC3B9I,EAAW8I,EAAAA,SAAe4S,QAAQX,GAGlClC,EAAqB7Y,EAAS8F,SAAU6V,EAAAA,EAAAA,GAAa3b,EAASA,EAAS8F,OAAS,GAAI,CAAC,4BACrFmR,EAAa,IACdlV,EACHwW,aACAD,MAAOkD,EAAalD,MACpBnB,iBACAwB,iBACAF,UACAI,sBAEId,EA5KkBd,KACxB,MAAM,WACJsB,EAAU,QACVR,EAAO,MACPO,EAAK,eACLnB,EAAc,eACdwB,EAAc,QACdF,EAAO,mBACPI,GACE5B,EACEe,EAAQ,CACZd,KAAM,CAAC,OAAQoB,GAAS,SAAUnB,GAAkB,WAAYwB,GAAkB,UAAWF,GAAW,UAA0B,eAAfF,GAA+B,sBAAuBM,GAAsB,mBAC/L+C,UAAW,CAAC,cAEd,OAAO3D,EAAAA,EAAAA,GAAeD,EAAOxB,EAAyBuB,EAAQ,EA8J9CG,CAAkBjB,GAC5B4E,GAAYC,EAAAA,EAAAA,GAAWL,EAAaxO,GACpC8O,EAAO/D,EAAMd,MAAQgE,EAAWa,MAAQ1D,GACxC2D,EAAYT,EAAUrE,MAAQiE,EAAgBjE,MAAQ,CAAC,EACvD+E,EAAiB,CACrB/b,WAAWiY,EAAAA,EAAAA,GAAKJ,EAAQb,KAAM8E,EAAU9b,UAAWA,MAChD0X,GAEL,IAAIsE,EAAYjB,GAAiB,KAGjC,OAAIpC,GAEFqD,EAAaD,EAAejB,WAAcC,EAAwBiB,EAAR,MAG/B,OAAvBd,IACgB,OAAdc,EACFA,EAAY,MAC0B,OAA7BD,EAAejB,YACxBiB,EAAejB,UAAY,SAGX/a,EAAAA,EAAAA,KAAK6X,EAAAA,EAAYqE,SAAU,CAC7ClU,MAAOuT,EACPxb,UAAuBI,EAAAA,EAAAA,MAAM0a,GAAmB,CAC9CsB,GAAIhB,EACJlb,WAAWiY,EAAAA,EAAAA,GAAKJ,EAAQ6D,UAAWN,GACnCrO,IAAK4O,EACL5E,WAAYA,KACToE,EACHrb,SAAU,EAAcC,EAAAA,EAAAA,KAAK8b,EAAM,IAC9BC,OACEK,EAAAA,EAAAA,GAAgBN,IAAS,CAC5BK,GAAIF,EACJjF,WAAY,IACPA,KACA+E,EAAU/E,gBAGdgF,EACHjc,SAAUA,IACRA,EAASsc,aAICrc,EAAAA,EAAAA,KAAK6X,EAAAA,EAAYqE,SAAU,CAC7ClU,MAAOuT,EACPxb,UAAuBI,EAAAA,EAAAA,MAAM2b,EAAM,IAC9BC,EACHI,GAAIF,EACJjP,IAAK4O,OACAQ,EAAAA,EAAAA,GAAgBN,IAAS,CAC5B9E,WAAY,IACPA,KACA+E,EAAU/E,gBAGdgF,EACHjc,SAAU,CAACA,EAAU8Y,IAAgC7Y,EAAAA,EAAAA,KAAKwX,EAAyB,CACjFzX,SAAU8Y,QAIlB,I,qCCxPA,MAeMyD,IAAmBzF,EAAAA,EAAAA,IAAO,MAAO,CACrC1Q,KAAM,kBACNqQ,KAAM,OACNM,kBAAmBA,CAAChV,EAAOiV,KACzB,MAAM,WACJC,GACElV,EACJ,MAAO,CAAC,CACN,CAAC,MAAMya,GAAAA,EAAoBC,WAAYzF,EAAOyF,SAC7C,CACD,CAAC,MAAMD,GAAAA,EAAoBE,aAAc1F,EAAO0F,WAC/C1F,EAAOE,KAAMD,EAAW0F,OAAS3F,EAAO2F,MAAO1F,EAAWwF,SAAWxF,EAAWyF,WAAa1F,EAAO4F,UAAW3F,EAAWqB,OAAStB,EAAOsB,MAAM,GAX9HxB,CAatB,CACD+F,KAAM,WACNC,SAAU,EACVC,UAAW,EACXC,aAAc,EACd,CAAC,IAAIC,GAAAA,EAAkB/F,iBAAiBsF,GAAAA,EAAoBC,YAAa,CACvExD,QAAS,SAEX,CAAC,IAAIgE,GAAAA,EAAkB/F,iBAAiBsF,GAAAA,EAAoBE,cAAe,CACzEzD,QAAS,SAEXzB,SAAU,CAAC,CACTzV,MAAOnC,IAAA,IAAC,WACNqX,GACDrX,EAAA,OAAKqX,EAAWwF,SAAWxF,EAAWyF,SAAS,EAChDxU,MAAO,CACL6U,UAAW,EACXC,aAAc,IAEf,CACDjb,MAAOwX,IAAA,IAAC,WACNtC,GACDsC,EAAA,OAAKtC,EAAW0F,KAAK,EACtBzU,MAAO,CACLC,YAAa,QAiKnB,GA7JkCW,EAAAA,YAAiB,SAAsB4O,EAASzK,GAChF,MAAMlL,GAAQ4V,EAAAA,EAAAA,GAAgB,CAC5B5V,MAAO2V,EACPtR,KAAM,qBAEF,SACJpG,EAAQ,UACRE,EAAS,kBACTgd,GAAoB,EAAK,MACzBP,GAAQ,EACRF,QAASU,EAAW,uBACpBC,EACAV,UAAWW,EAAa,yBACxBC,EAAwB,MACxBtF,EAAQ,CAAC,EAAC,UACVuD,EAAY,CAAC,KACV3D,GACD7V,GACE,MACJuW,GACExP,EAAAA,WAAiBgP,EAAAA,GACrB,IAAI2E,EAAyB,MAAfU,EAAsBA,EAAcnd,EAC9C0c,EAAYW,EAChB,MAAMpG,EAAa,IACdlV,EACHmb,oBACAP,QACAF,UAAWA,EACXC,YAAaA,EACbpE,SAEIP,EAvFkBd,KACxB,MAAM,QACJc,EAAO,MACP4E,EAAK,QACLF,EAAO,UACPC,EAAS,MACTpE,GACErB,EACEe,EAAQ,CACZd,KAAM,CAAC,OAAQyF,GAAS,QAASrE,GAAS,QAASmE,GAAWC,GAAa,aAC3ED,QAAS,CAAC,WACVC,UAAW,CAAC,cAEd,OAAOzE,EAAAA,EAAAA,GAAeD,EAAOuF,GAAAA,EAA6BxF,EAAQ,EA0ElDG,CAAkBjB,GAC5BuG,EAAyB,CAC7BxF,QACAuD,UAAW,CACTkB,QAASW,EACTV,UAAWY,KACR/B,KAGAkC,EAAUC,IAAiBC,EAAAA,GAAAA,GAAQ,OAAQ,CAChDzd,WAAWiY,EAAAA,EAAAA,GAAKJ,EAAQb,KAAMhX,GAC9B0d,YAAarB,GACbiB,uBAAwB,IACnBA,KACA5F,GAELX,aACAhK,SAEK4Q,EAAaC,IAAoBH,EAAAA,GAAAA,GAAQ,UAAW,CACzDzd,UAAW6X,EAAQ0E,QACnBmB,YAAaG,EAAAA,EACbP,yBACAvG,gBAEK+G,EAAeC,IAAsBN,EAAAA,GAAAA,GAAQ,YAAa,CAC/Dzd,UAAW6X,EAAQ2E,UACnBkB,YAAaG,EAAAA,EACbP,yBACAvG,eAkBF,OAhBe,MAAXwF,GAAmBA,EAAQ7S,OAASmU,EAAAA,GAAeb,IACrDT,GAAuBxc,EAAAA,EAAAA,KAAK4d,EAAa,CACvCK,QAAS5F,EAAQ,QAAU,QAC3B0C,UAAW8C,GAAkBI,aAAUpW,EAAY,UAChDgW,EACH9d,SAAUyc,KAGG,MAAbC,GAAqBA,EAAU9S,OAASmU,EAAAA,GAAeb,IACzDR,GAAyBzc,EAAAA,EAAAA,KAAK+d,EAAe,CAC3CE,QAAS,QACTlZ,MAAO,mBACJiZ,EACHje,SAAU0c,MAGMtc,EAAAA,EAAAA,MAAMqd,EAAU,IAC/BC,EACH1d,SAAU,CAACyc,EAASC,IAExB,I,0BC3IA,MAiBMyB,IAAcrH,EAAAA,EAAAA,IAAO,MAAO,CAChC1Q,KAAM,aACNqQ,KAAM,OACNM,kBAAmBA,CAAChV,EAAOiV,KACzB,MAAM,WACJC,GACElV,EACJ,MAAO,CAACiV,EAAOE,KAAMD,EAAWmH,UAAYpH,EAAOoH,SAAUpH,EAAOC,EAAWiH,SAAUjH,EAAWoH,OAASrH,EAAOqH,MAAkC,aAA3BpH,EAAWqH,aAA8BtH,EAAOuH,SAAUtH,EAAWuH,UAAYxH,EAAOwH,SAAUvH,EAAWjX,UAAYgX,EAAOyH,aAAcxH,EAAWjX,UAAuC,aAA3BiX,EAAWqH,aAA8BtH,EAAO0H,qBAA+C,UAAzBzH,EAAWqC,WAAoD,aAA3BrC,EAAWqH,aAA8BtH,EAAO2H,eAAyC,SAAzB1H,EAAWqC,WAAmD,aAA3BrC,EAAWqH,aAA8BtH,EAAO4H,cAAc,GAP3hB9H,EASjBiC,EAAAA,EAAAA,IAAUnZ,IAAA,IAAC,MACZoZ,GACDpZ,EAAA,MAAM,CACLif,OAAQ,EAERC,WAAY,EACZC,YAAa,EACbC,YAAa,QACb7R,aAAc6L,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,QAC3CwG,kBAAmB,OACnBzH,SAAU,CAAC,CACTzV,MAAO,CACLqc,UAAU,GAEZlW,MAAO,CACLkP,SAAU,WACV8H,OAAQ,EACRC,KAAM,EACN/F,MAAO,SAER,CACDrX,MAAO,CACLsc,OAAO,GAETnW,MAAO,CACLiF,YAAa6L,EAAMkB,KAAO,QAAQlB,EAAMkB,KAAKC,QAAQiF,0BAA2BC,EAAAA,GAAAA,IAAMrG,EAAMmB,QAAQ1B,QAAS,OAE9G,CACD1W,MAAO,CACLmc,QAAS,SAEXhW,MAAO,CACLoX,WAAY,KAEb,CACDvd,MAAO,CACLmc,QAAS,SACTI,YAAa,cAEfpW,MAAO,CACLoX,WAAYtG,EAAMuG,QAAQ,GAC1BC,YAAaxG,EAAMuG,QAAQ,KAE5B,CACDxd,MAAO,CACLmc,QAAS,SACTI,YAAa,YAEfpW,MAAO,CACL6U,UAAW/D,EAAMuG,QAAQ,GACzBvC,aAAchE,EAAMuG,QAAQ,KAE7B,CACDxd,MAAO,CACLuc,YAAa,YAEfpW,MAAO,CACLuX,OAAQ,OACRR,kBAAmB,EACnBS,iBAAkB,SAEnB,CACD3d,MAAO,CACLyc,UAAU,GAEZtW,MAAO,CACLyX,UAAW,UACXF,OAAQ,SAET,CACD1d,MAAOwX,IAAA,IAAC,WACNtC,GACDsC,EAAA,QAAOtC,EAAWjX,QAAQ,EAC3BkI,MAAO,CACL+Q,QAAS,OACTK,UAAW,SACXsG,OAAQ,EACRC,eAAgB,QAChBC,gBAAiB,QACjB,sBAAuB,CACrBC,QAAS,KACTJ,UAAW,YAGd,CACD5d,MAAO2X,IAAA,IAAC,WACNzC,GACDyC,EAAA,OAAKzC,EAAWjX,UAAuC,aAA3BiX,EAAWqH,WAA0B,EAClEpW,MAAO,CACL,sBAAuB,CACrBkR,MAAO,OACP4G,UAAW,eAAehH,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,UACvDoH,eAAgB,aAGnB,CACD9d,MAAO4X,IAAA,IAAC,WACN1C,GACD0C,EAAA,MAAgC,aAA3B1C,EAAWqH,aAA8BrH,EAAWjX,QAAQ,EAClEkI,MAAO,CACL+X,cAAe,SACf,sBAAuB,CACrBR,OAAQ,OACRS,WAAY,eAAelH,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,UACxDqH,gBAAiB,aAGpB,CACD/d,MAAO8X,IAAA,IAAC,WACN5C,GACD4C,EAAA,MAA8B,UAAzB5C,EAAWqC,WAAoD,aAA3BrC,EAAWqH,WAA0B,EAC/EpW,MAAO,CACL,YAAa,CACXkR,MAAO,OAET,WAAY,CACVA,MAAO,SAGV,CACDrX,MAAO+X,IAAA,IAAC,WACN7C,GACD6C,EAAA,MAA8B,SAAzB7C,EAAWqC,WAAmD,aAA3BrC,EAAWqH,WAA0B,EAC9EpW,MAAO,CACL,YAAa,CACXkR,MAAO,OAET,WAAY,CACVA,MAAO,UAId,KACK+G,IAAiBrJ,EAAAA,EAAAA,IAAO,OAAQ,CACpC1Q,KAAM,aACNqQ,KAAM,UACNM,kBAAmBA,CAAChV,EAAOiV,KACzB,MAAM,WACJC,GACElV,EACJ,MAAO,CAACiV,EAAOoJ,QAAoC,aAA3BnJ,EAAWqH,aAA8BtH,EAAOqJ,gBAAgB,GAPrEvJ,EASpBiC,EAAAA,EAAAA,IAAUiB,IAAA,IAAC,MACZhB,GACDgB,EAAA,MAAM,CACLf,QAAS,eACT9Q,YAAa,QAAQ6Q,EAAMuG,QAAQ,YACnC3F,aAAc,QAAQZ,EAAMuG,QAAQ,YACpCe,WAAY,SACZ9I,SAAU,CAAC,CACTzV,MAAO,CACLuc,YAAa,YAEfpW,MAAO,CACLsR,WAAY,QAAQR,EAAMuG,QAAQ,YAClC9F,cAAe,QAAQT,EAAMuG,QAAQ,eAG1C,KACKgB,GAAuBzX,EAAAA,YAAiB,SAAiB4O,EAASzK,GACtE,MAAMlL,GAAQ4V,EAAAA,EAAAA,GAAgB,CAC5B5V,MAAO2V,EACPtR,KAAM,gBAEF,SACJgY,GAAW,EAAK,SAChBpe,EAAQ,UACRE,EAAS,YACToe,EAAc,aAAY,UAC1BtD,GAAYhb,GAA4B,aAAhBse,EAA6B,MAAQ,MAAI,SACjEE,GAAW,EAAK,MAChBH,GAAQ,EAAK,KACb5M,GAAqB,OAAduJ,EAAqB,iBAAclT,GAAS,UACnDwR,EAAY,SAAQ,QACpB4E,EAAU,eACPtG,GACD7V,EACEkV,EAAa,IACdlV,EACHqc,WACApD,YACAwD,WACAH,QACAC,cACA7M,OACA6H,YACA4E,WAEInG,EAtNkBd,KACxB,MAAM,SACJmH,EAAQ,SACRpe,EAAQ,QACR+X,EAAO,SACPyG,EAAQ,MACRH,EAAK,YACLC,EAAW,UACXhF,EAAS,QACT4E,GACEjH,EACEe,EAAQ,CACZd,KAAM,CAAC,OAAQkH,GAAY,WAAYF,EAASG,GAAS,QAAyB,aAAhBC,GAA8B,WAAYE,GAAY,WAAYxe,GAAY,eAAgBA,GAA4B,aAAhBse,GAA8B,uBAAsC,UAAdhF,GAAyC,aAAhBgF,GAA8B,iBAAgC,SAAdhF,GAAwC,aAAhBgF,GAA8B,iBACjW8B,QAAS,CAAC,UAA2B,aAAhB9B,GAA8B,oBAErD,OAAOrG,EAAAA,EAAAA,GAAeD,EAAOwI,GAAAA,EAAwBzI,EAAQ,EAuM7CG,CAAkBjB,GAClC,OAAoBhX,EAAAA,EAAAA,KAAKke,GAAa,CACpC/B,GAAIpB,EACJ9a,WAAWiY,EAAAA,EAAAA,GAAKJ,EAAQb,KAAMhX,GAC9BuR,KAAMA,EACNxE,IAAKA,EACLgK,WAAYA,EACZ,mBAA6B,cAATxF,GAAuC,OAAduJ,GAAsC,aAAhBsD,OAA4CxW,EAAdwW,KAC9F1G,EACH5X,SAAUA,GAAwBC,EAAAA,EAAAA,KAAKkgB,GAAgB,CACrDjgB,UAAW6X,EAAQqI,QACnBnJ,WAAYA,EACZjX,SAAUA,IACP,MAET,IAMIugB,KACFA,GAAQE,sBAAuB,GAiEjC,YCtSaC,GAAwBA,KACnC,MAAOpS,EAASK,IAAcvC,EAAAA,EAAAA,UAAmB,KAC1CmC,EAAWK,IAAgBxC,EAAAA,EAAAA,UAAqB,KAChDoC,EAASK,IAAczC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASiD,IAAc1C,EAAAA,EAAAA,WAAkB,IACzCnI,EAAOtB,IAAYyJ,EAAAA,EAAAA,UAAwB,OAC3CuU,EAAcC,IAAmBxU,EAAAA,EAAAA,UAAiB,IAClDyU,EAAUC,IAAe1U,EAAAA,EAAAA,UAAiB,IAE3C2C,EAAkBxL,UACtB,IACEuL,GAAW,GACXnM,EAAS,MAETqB,QAAQgL,IAAI,mFAGZ,MAAM+R,QAAsB7R,EAAAA,EAAcC,qBAK1C,GAHAnL,QAAQgL,IAAI,wDAAoD+R,EAAcjb,OAAQ,WACtF8a,EAAgBG,EAAcjb,QAED,IAAzBib,EAAcjb,OAMhB,OALA9B,QAAQgL,IAAI,+DACZL,EAAW,IACXC,EAAa,IACbC,EAAW,SACXiS,EAAY,WAKd,MAAME,EAAgB,IAAIC,IAC1BF,EAAc1f,SAAQ+P,IAChBA,EAAO9B,QAAU8B,EAAO9B,OAAOlE,QACjC4V,EAAcE,IAAI9P,EAAO9B,OAAOlE,OAClC,IAGF,MAAMuE,EAAyB3E,MAAMmW,KAAKH,GACvCtR,OACAvO,KAAIyO,IAAU,CACb/O,GAAI+O,EAAWC,cAAcjL,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzEwB,KAAMwJ,MAGV5L,QAAQgL,IAAI,yDAAgDW,EAAa7J,QAGzE,MAAMsb,EAAkB,IAAIC,IAC5BN,EAAc1f,SAAQ+P,IAChBA,EAAOpB,UAAYoB,EAAOpB,SAAS5E,QAAUgG,EAAO9B,QAAU8B,EAAO9B,OAAOlE,QAC9EgW,EAAgBE,IAAIlQ,EAAOpB,SAAS5E,OAAQgG,EAAO9B,OAAOlE,OAC5D,IAGF,MAAMkF,EAA6BtF,MAAMmW,KAAKC,EAAgBG,WAC3D7R,MAAK,CAAA9P,EAAA2Z,KAAA,IAAEpJ,GAAEvQ,GAAGwQ,GAAEmJ,EAAA,OAAKpJ,EAAEE,cAAcD,EAAE,IACrCjP,KAAIuY,IAAA,IAAE8H,EAAc5R,GAAW8J,EAAA,MAAM,CACpC7Y,GAAI2gB,EAAa3R,cAAcjL,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC3EwB,KAAMob,EACNjS,OAAQK,EACT,IAEH5L,QAAQgL,IAAI,2DAAkDsB,EAAexK,QAG7E,MAAMyK,EAAyBwQ,EAC5Bpf,QAAOyP,GAAUA,EAAO,gBAAkBA,EAAO,eAAehG,SAChEjK,KAAIiQ,IAAM,CACTvQ,GAAIuQ,EAAO,eACXhL,KAAMgL,EAAO,eACb7B,OAAQ6B,EAAO9B,QAAU,GACzBS,SAAUqB,EAAOpB,UAAY,GAC7BQ,WAAYY,EAAO,mBAGvBpN,QAAQgL,IAAI,yDAAgDuB,EAAazK,QA8C/E,SACEib,EACAzS,EACAC,EACAC,GAQA,GANAxK,QAAQgL,IAAI,wEACZhL,QAAQgL,IAAI,oDAA0C+R,EAAcjb,UACpE9B,QAAQgL,IAAI,0DAAgDV,EAAQxI,UACpE9B,QAAQgL,IAAI,4DAAkDT,EAAUzI,UACxE9B,QAAQgL,IAAI,0DAAgDR,EAAQ1I,UAEhE0I,EAAQ1I,OAAS,EAAG,CAEtB,MAAM2b,EAAcjT,EAAQrN,KAAIqQ,GAAKA,EAAEpL,OAAMsJ,OAC7C1L,QAAQgL,IAAI,8DAAoDyS,EAAY,OAC5Ezd,QAAQgL,IAAI,6DAAmDyS,EAAYA,EAAY3b,OAAS,OAGhG,MAAM4b,EAA0C,CAAC,EACjDlT,EAAQnN,SAAQ+P,IACd,MAAMuQ,EAAcvQ,EAAOhL,KAAKwb,OAAO,GAAGC,cAC1CH,EAAaC,IAAgBD,EAAaC,IAAgB,GAAK,CAAC,IAGlE3d,QAAQgL,IAAI,4DACZ8S,OAAOC,KAAKL,GAAchS,OAAOrO,SAAQ2gB,IACvChe,QAAQgL,IAAI,uCAA6BgT,MAAWN,EAAaM,aAAkB,IAIrF,MAAMC,EAAkBzT,EAAQ7N,MAAK6Q,GAAKA,EAAEpL,KAAKyJ,cAAczF,SAAS,sBAClE8X,EAAqB1T,EAAQ7N,MAAK6Q,GAAKA,EAAEpL,KAAKyJ,cAAczF,SAAS,yBAsB3E,GApBApG,QAAQgL,IAAI,sEAA4DiT,KACxEje,QAAQgL,IAAI,yEAA+DkT,KAEvED,GACFje,QAAQgL,IAAI,gEAAsDiT,EAAgB7b,SAEhF8b,GACFle,QAAQgL,IAAI,mEAAyDkT,EAAmB9b,SAItFkI,EAAQxI,OAAS,IACnB9B,QAAQgL,IAAI,sDACZV,EAAQjN,SAAQkO,IACd,MAAM4S,EAAgB3T,EAAQ7M,QAAO6P,GAAKA,EAAEjC,SAAWA,EAAOnJ,OAC9DpC,QAAQgL,IAAI,uCAA6BO,EAAOnJ,SAAS+b,EAAcrc,iBAAiB,KAKxFyI,EAAUzI,OAAS,EAAG,CACxB9B,QAAQgL,IAAI,yEACWT,EAAUpN,KAAI4O,IAAQ,CAC3C3J,KAAM2J,EAAS3J,KACfgc,MAAO5T,EAAQ7M,QAAO6P,GAAKA,EAAEzB,WAAaA,EAAS3J,OAAMN,WACvD4J,MAAK,CAACS,EAAGC,IAAMA,EAAEgS,MAAQjS,EAAEiS,QAAOC,MAAM,EAAG,IAEhChhB,SAAQ0O,IACrB/L,QAAQgL,IAAI,uCAA6Be,EAAS3J,SAAS2J,EAASqS,gBAAgB,GAExF,CACF,CAEApe,QAAQgL,IAAI,6DACd,CA/GMsT,CAAoBvB,EAAepR,EAAcW,EAAgBC,GAGjE5B,EAAWgB,GACXf,EAAa0B,GACbzB,EAAW0B,GACXuQ,EAAY,uBAEZ9c,QAAQgL,IAAI,yDAEd,CAAE,MAAOjL,GACPC,QAAQC,MAAM,uCAAmCF,GACjDpB,EAAS,iDACTgM,EAAW,IACXC,EAAa,IACbC,EAAW,IACX+R,EAAgB,GAChBE,EAAY,QACd,CAAC,QACChS,GAAW,EACb,GAQF,OAJAvC,EAAAA,EAAAA,YAAU,KACRwC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA3C,UACA5H,QACAwK,QAASM,EACT4R,eACAE,WACD,EA4EH,MCwFA,GAvRoC0B,KAClC,MAAM,QACJjU,EAAO,UACPC,EAAS,QACTC,EAAO,QACP3C,EAAO,MACP5H,EAAK,aACL0c,EAAY,SACZE,EAAQ,QACRpS,GACEiS,KAEJ,GAAI7U,EACF,OACEzL,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACvJ,QAAQ,OAAOgH,cAAc,SAAS1H,WAAW,SAASkK,EAAG,EAAEziB,SAAA,EAClEC,EAAAA,EAAAA,KAACyiB,EAAAA,EAAgB,CAACC,KAAM,MACxB1iB,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAK0E,GAAI,CAAEC,GAAI,GAAI7iB,SAAC,0DAGxCC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAQlZ,MAAM,iBAAiB4d,GAAI,CAAEC,GAAI,GAAI7iB,SAAC,wEAOxE,GAAIiE,EACF,OACE7D,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACC,EAAG,EAAEziB,SAAA,EACRC,EAAAA,EAAAA,KAAC6iB,EAAAA,EAAK,CAACC,SAAS,QAAQH,GAAI,CAAEI,GAAI,GAAIhjB,SACnCiE,KAEHhE,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAOle,SAAC,4FAQlC,MAAMijB,EAAgD,CAAC,EACvDzU,EAAQnN,SAAQ+P,IACd,MAAMuQ,EAAcvQ,EAAOhL,KAAKwb,OAAO,GAAGC,cAC1CoB,EAAmBtB,IAAgBsB,EAAmBtB,IAAgB,GAAK,CAAC,IAG9E,MAAMuB,EAAoB1U,EAAQrN,KAAIqQ,GAAKA,EAAEpL,OAAMsJ,OAC7CuS,EAAkBzT,EAAQ7N,MAAK6Q,GAAKA,EAAEpL,KAAKyJ,cAAczF,SAAS,sBAClE8X,EAAqB1T,EAAQ7N,MAAK6Q,GAAKA,EAAEpL,KAAKyJ,cAAczF,SAAS,yBAGrE+Y,EAAe7U,EAAQnN,KAAIoO,IAAM,CACrCnJ,KAAMmJ,EAAOnJ,KACbgc,MAAO5T,EAAQ7M,QAAO6P,GAAKA,EAAEjC,SAAWA,EAAOnJ,OAAMN,WACnD4J,MAAK,CAACS,EAAGC,IAAMA,EAAEgS,MAAQjS,EAAEiS,QAAOC,MAAM,EAAG,GAGzCe,EAAiB7U,EAAUpN,KAAI4O,IAAQ,CAC3C3J,KAAM2J,EAAS3J,KACfgc,MAAO5T,EAAQ7M,QAAO6P,GAAKA,EAAEzB,WAAaA,EAAS3J,OAAMN,WACvD4J,MAAK,CAACS,EAAGC,IAAMA,EAAEgS,MAAQjS,EAAEiS,QAAOC,MAAM,EAAG,IAE/C,OACEjiB,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACC,EAAG,EAAEziB,SAAA,EACRC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAArjB,SAAC,+CAItCC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAQlZ,MAAM,iBAAiBse,WAAS,EAAAtjB,SAAC,uKAM7DI,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACvJ,QAAQ,OAAOsK,IAAK,EAAGX,GAAI,CAAEI,GAAI,EAAGQ,SAAU,QAASxjB,SAAA,EAC1DC,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAAC3F,KAAK,IAAIC,SAAS,QAAO9c,UAC5BC,EAAAA,EAAAA,KAACwjB,EAAAA,EAAI,CAAAzjB,UACHI,EAAAA,EAAAA,MAACsjB,EAAAA,EAAW,CAAA1jB,SAAA,EACVC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAKlZ,MAAM,UAAShF,SAAC,mBAGzCC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAIle,SACrB2gB,EAAagD,oBAEhB1jB,EAAAA,EAAAA,KAAC2jB,EAAAA,EAAI,CACH5Z,MAAO6W,EACP8B,KAAK,QACL3d,MAAO2b,EAAe,IAAO,UAAY,UACzCiC,GAAI,CAAEC,GAAI,aAMlB5iB,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAAC3F,KAAK,IAAIC,SAAS,QAAO9c,UAC5BC,EAAAA,EAAAA,KAACwjB,EAAAA,EAAI,CAAAzjB,UACHI,EAAAA,EAAAA,MAACsjB,EAAAA,EAAW,CAAA1jB,SAAA,EACVC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAKlZ,MAAM,UAAShF,SAAC,aAGzCC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAIle,SACrBsO,EAAQxI,iBAMjB7F,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAAC3F,KAAK,IAAIC,SAAS,QAAO9c,UAC5BC,EAAAA,EAAAA,KAACwjB,EAAAA,EAAI,CAAAzjB,UACHI,EAAAA,EAAAA,MAACsjB,EAAAA,EAAW,CAAA1jB,SAAA,EACVC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAKlZ,MAAM,UAAShF,SAAC,eAGzCC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAIle,SACrBuO,EAAUzI,iBAMnB7F,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAAC3F,KAAK,IAAIC,SAAS,QAAO9c,UAC5BC,EAAAA,EAAAA,KAACwjB,EAAAA,EAAI,CAAAzjB,UACHI,EAAAA,EAAAA,MAACsjB,EAAAA,EAAW,CAAA1jB,SAAA,EACVC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAKlZ,MAAM,UAAShF,SAAC,aAGzCC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAIle,SACrBwO,EAAQ1I,oBAQnB1F,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACvJ,QAAQ,OAAOsK,IAAK,EAAGX,GAAI,CAAEY,SAAU,QAASxjB,SAAA,EACnDC,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAAC3F,KAAK,IAAIC,SAAS,QAAO9c,UAC5BI,EAAAA,EAAAA,MAACyjB,EAAAA,EAAK,CAACjB,GAAI,CAAEH,EAAG,GAAIziB,SAAA,EAClBC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAArjB,SAAC,0BAItCI,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EACjBC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,YAAWle,SAAC,gCAGhCC,EAAAA,EAAAA,KAAC2jB,EAAAA,EAAI,CACH5Z,MAAO2W,EAAe,IAAO,aAAU,YACvC3b,MAAO2b,EAAe,IAAO,UAAY,QACzCgC,KAAK,cAITviB,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EACjBC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,YAAWle,SAAC,yBAGhCI,EAAAA,EAAAA,MAAC2d,EAAAA,EAAU,CAACG,QAAQ,QAAOle,SAAA,CAAC,WACjBkjB,EAAkB,IAAM,MAAM,QAEzC9iB,EAAAA,EAAAA,MAAC2d,EAAAA,EAAU,CAACG,QAAQ,QAAOle,SAAA,CAAC,UAClBkjB,EAAkBA,EAAkBpd,OAAS,IAAM,MAAM,WAIrE1F,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EACjBC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,YAAWle,SAAC,6BAGhCC,EAAAA,EAAAA,KAAC2jB,EAAAA,EAAI,CACH5Z,MAAOiY,EAAkB,aAAU,YACnCjd,MAAOid,EAAkB,UAAY,QACrCU,KAAK,UAENV,IACC7hB,EAAAA,EAAAA,MAAC2d,EAAAA,EAAU,CAACG,QAAQ,QAAQ0E,GAAI,CAAEC,GAAI,GAAI7iB,SAAA,CAAC,IACvCiiB,EAAgB7b,KAAK,WAK7BhG,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EACjBC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,YAAWle,SAAC,gCAGhCC,EAAAA,EAAAA,KAAC2jB,EAAAA,EAAI,CACH5Z,MAAOkY,EAAqB,aAAU,YACtCld,MAAOkd,EAAqB,UAAY,QACxCS,KAAK,UAENT,IACC9hB,EAAAA,EAAAA,MAAC2d,EAAAA,EAAU,CAACG,QAAQ,QAAQ0E,GAAI,CAAEC,GAAI,GAAI7iB,SAAA,CAAC,IACvCkiB,EAAmB9b,KAAK,gBAOpCnG,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAAC3F,KAAK,IAAIC,SAAS,QAAO9c,UAC5BI,EAAAA,EAAAA,MAACyjB,EAAAA,EAAK,CAACjB,GAAI,CAAEH,EAAG,GAAIziB,SAAA,EAClBC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAArjB,SAAC,yBAGtCC,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAACI,GAAI,CAAErV,UAAW,IAAKuW,SAAU,QAAS9jB,SAC3C8hB,OAAOC,KAAKkB,GAAoBvT,OAAOvO,KAAI6gB,IAC1C5hB,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAAcvJ,QAAQ,OAAOC,eAAe,gBAAgB0J,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EAC5EI,EAAAA,EAAAA,MAAC2d,EAAAA,EAAU,CAACG,QAAQ,QAAOle,SAAA,CAAEgiB,EAAO,QACpC5hB,EAAAA,EAAAA,MAAC2d,EAAAA,EAAU,CAACG,QAAQ,QAAOle,SAAA,CAAEijB,EAAmBjB,GAAQ,gBAFhDA,gBAUpB5hB,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACvJ,QAAQ,OAAOsK,IAAK,EAAGX,GAAI,CAAEC,GAAI,EAAGW,SAAU,QAASxjB,SAAA,EAC1DC,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAAC3F,KAAK,IAAIC,SAAS,QAAO9c,UAC5BI,EAAAA,EAAAA,MAACyjB,EAAAA,EAAK,CAACjB,GAAI,CAAEH,EAAG,GAAIziB,SAAA,EAClBC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAArjB,SAAC,mCAGtCC,EAAAA,EAAAA,KAAC8jB,EAAAA,EAAI,CAACzL,OAAK,EAAAtY,SACRmjB,EAAahiB,KAAI,CAACoO,EAAQpG,KACzB/I,EAAAA,EAAAA,MAAC0I,EAAAA,SAAc,CAAA9I,SAAA,EACbC,EAAAA,EAAAA,KAAC+jB,GAAQ,CAAAhkB,UACPC,EAAAA,EAAAA,KAACgkB,GAAY,CACXxH,QAASlN,EAAOnJ,KAChBsW,UAAW,GAAGnN,EAAO6S,oBAGxBjZ,EAAQga,EAAard,OAAS,IAAK7F,EAAAA,EAAAA,KAACsgB,GAAO,MAPzBhR,EAAOnJ,gBAcpCnG,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAAC3F,KAAK,IAAIC,SAAS,QAAO9c,UAC5BI,EAAAA,EAAAA,MAACyjB,EAAAA,EAAK,CAACjB,GAAI,CAAEH,EAAG,GAAIziB,SAAA,EAClBC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAArjB,SAAC,sCAGtCC,EAAAA,EAAAA,KAACuiB,EAAAA,EAAG,CAACI,GAAI,CAAErV,UAAW,IAAKuW,SAAU,QAAS9jB,UAC5CC,EAAAA,EAAAA,KAAC8jB,EAAAA,EAAI,CAACzL,OAAK,EAAAtY,SACRojB,EAAejiB,KAAI,CAAC4O,EAAU5G,KAC7B/I,EAAAA,EAAAA,MAAC0I,EAAAA,SAAc,CAAA9I,SAAA,EACbC,EAAAA,EAAAA,KAAC+jB,GAAQ,CAAAhkB,UACPC,EAAAA,EAAAA,KAACgkB,GAAY,CACXxH,QAAS1M,EAAS3J,KAClBsW,UAAW,GAAG3M,EAASqS,oBAG1BjZ,EAAQia,EAAetd,OAAS,IAAK7F,EAAAA,EAAAA,KAACsgB,GAAO,MAP3BxQ,EAAS3J,oBAiBzCua,EAAe,MACdvgB,EAAAA,EAAAA,MAAC0iB,EAAAA,EAAK,CAACC,SAAS,UAAUH,GAAI,CAAEC,GAAI,GAAI7iB,SAAA,EACtCC,EAAAA,EAAAA,KAAC8d,EAAAA,EAAU,CAACG,QAAQ,KAAIle,SAAC,8DAGzBI,EAAAA,EAAAA,MAAC2d,EAAAA,EAAU,CAACG,QAAQ,QAAOle,SAAA,CAAC,kCACM2gB,EAAagD,iBAAiB,gKAMhE,EClFV,GAjNqCO,KACnC,MAAOC,EAAUC,IAAehY,EAAAA,EAAAA,WAAS,IAClC+F,EAASvP,IAAcwJ,EAAAA,EAAAA,UAAS,KAChCnI,EAAOtB,IAAYyJ,EAAAA,EAAAA,UAAS,KAC5BiY,EAAaC,IAAkBlY,EAAAA,EAAAA,UAAgB,IAsFtD,OACEhM,EAAAA,EAAAA,MAAA,OAAK8H,MAAO,CACV0Q,QAAS,OACTiG,OAAQ,SACRe,OAAQ,oBACR2E,aAAc,MACdrX,gBAAiB,WACjBlN,SAAA,EACAI,EAAAA,EAAAA,MAAA,OAAK8H,MAAO,CAAE+Q,QAAS,OAAQV,WAAY,SAAUgL,IAAK,OAAQvG,aAAc,QAAShd,SAAA,CACtF8I,EAAAA,cAAoB0b,EAAAA,IAAqC,CAAE7B,KAAM,GAAI3d,MAAO,aAC7E/E,EAAAA,EAAAA,KAAA,MAAIiI,MAAO,CAAE2W,OAAQ,EAAG7Z,MAAO,WAAYhF,SAAC,qCAG9CC,EAAAA,EAAAA,KAAA,KAAGiI,MAAO,CAAE8U,aAAc,OAAQhY,MAAO,WAAYhF,SAAC,8IAIrDiE,IACChE,EAAAA,EAAAA,KAAA,OAAKiI,MAAO,CACV0Q,QAAS,OACToE,aAAc,OACd9P,gBAAiB,UACjBlI,MAAO,UACP4a,OAAQ,oBACR2E,aAAc,OACdvkB,SACCiE,IAIJkO,IACClS,EAAAA,EAAAA,KAAA,OAAKiI,MAAO,CACV0Q,QAAS,OACToE,aAAc,OACd9P,gBAAiB,UACjBlI,MAAO,UACP4a,OAAQ,oBACR2E,aAAc,OACdvkB,SACCmS,IAIJkS,EAAYve,OAAS,IACpB1F,EAAAA,EAAAA,MAAA,OAAK8H,MAAO,CACV0Q,QAAS,OACToE,aAAc,OACd9P,gBAAiB,UACjB0S,OAAQ,oBACR2E,aAAc,OACdvkB,SAAA,EACAC,EAAAA,EAAAA,KAAA,MAAIiI,MAAO,CAAE2W,OAAQ,aAAc7Z,MAAO,WAAYhF,SAAC,kBACtDqkB,EAAYljB,KAAI,CAACsjB,EAAOtb,KACvB/I,EAAAA,EAAAA,MAAA,OAAiB8H,MAAO,CAAE8U,aAAc,MAAO0H,SAAU,QAAS1kB,SAAA,EAChEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,QAAY,IAAEykB,EAAM5jB,GAAG,OAAGZ,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,WAAe,IAAEykB,EAAMA,QADzDtb,SAOhB/I,EAAAA,EAAAA,MAAA,OAAK8H,MAAO,CAAE+Q,QAAS,OAAQsK,IAAK,QAASvjB,SAAA,EAC3CI,EAAAA,EAAAA,MAAA,UACED,QAlJwBoD,UAC9B6gB,GAAY,GACZzhB,EAAS,IACTC,EAAW,IACX0hB,EAAe,IAEf,IAEE,MAAMK,QAAsBlhB,EAAAA,EAAAA,KAAQC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,UAC7CihB,EAAgB,GAEtBD,EAAc/gB,KAAKvC,SAAQwC,IACzB,MAAMC,EAAOD,EAAIC,OACX/D,EAAQ+D,EAAK/D,MAEdA,GAAmB,cAAVA,GAA0C,KAAjBA,EAAMqL,QAC3CwZ,EAAOpjB,KAAK,CACVkC,WAAY,QACZ7C,GAAIgD,EAAIhD,GACRiD,KAAMA,EACN2gB,MAAQ1kB,EAAoC,cAAVA,EAAwB,uBAAyB,cAAnE,iBAEpB,IAGFukB,EAAeM,GAEO,IAAlBA,EAAO9e,OACTlD,EAAW,8DAEXA,EAAW,sBAAYgiB,EAAO9e,0EAGlC,CAAE,MAAO/B,GACPC,QAAQC,MAAM,wCAAyCF,GACvDpB,EAAS,iEACX,CAAC,QACCyhB,GAAY,EACd,GA6GM5b,SAAU2b,EACVjc,MAAO,CACL0Q,QAAS,YACT1L,gBAAiBiX,EAAW,UAAY,UACxCnf,MAAOmf,EAAW,QAAU,UAC5BvE,OAAQ,OACR2E,aAAc,MACdM,OAAQV,EAAW,cAAgB,UACnCO,SAAU,OACVI,WAAY,OACZ7L,QAAS,OACTV,WAAY,SACZgL,IAAK,OACLvjB,SAAA,CAED8I,EAAAA,cAAoB0b,EAAAA,IAAqC,CAAE7B,KAAM,KACjEwB,EAAW,cAAgB,qBAG7BE,EAAYve,OAAS,IACpB1F,EAAAA,EAAAA,MAAA,UACED,QA/HkBoD,UAC1B6gB,GAAY,GACZzhB,EAAS,IACTC,EAAW,IAEX,IACE,IAAImiB,EAAa,EACbC,EAAe,EAEnB,IAAK,MAAMP,KAASJ,EAAa,CAC/B,MAAMlgB,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI8gB,EAAM/gB,WAAY+gB,EAAM5jB,IAG3C4jB,EAAM3gB,KAAKjD,SAA8BiH,IAAxB2c,EAAM3gB,KAAKhD,gBACxB8F,EAAAA,EAAAA,IAAUzC,EAAQ,CACtBpE,MAAO,MACPkG,YAAa,IAAIC,KACjB+e,YAAa,uDAEfF,YAGMG,EAAAA,EAAAA,IAAU/gB,GAChB6gB,IAEJ,CAEAV,EAAe,IACf1hB,EAAW,6BAAwBmiB,2BAAoCC,sEAGvExe,YAAW,KACT2e,OAAOC,SAASC,QAAQ,GACvB,IAEL,CAAE,MAAOthB,GACPC,QAAQC,MAAM,kCAAmCF,GACjDpB,EAAS,4DACX,CAAC,QACCyhB,GAAY,EACd,GAwFQ5b,SAAU2b,EACVjc,MAAO,CACL0Q,QAAS,YACT1L,gBAAiBiX,EAAW,UAAY,UACxCnf,MAAO,QACP4a,OAAQ,OACR2E,aAAc,MACdM,OAAQV,EAAW,cAAgB,UACnCO,SAAU,OACVI,WAAY,OACZ7L,QAAS,OACTV,WAAY,SACZgL,IAAK,OACLvjB,SAAA,CAED8I,EAAAA,cAAoBwc,EAAAA,IAAqC,CAAE3C,KAAM,KACjEwB,EAAW,YAAc,oCAKhC/jB,EAAAA,EAAAA,MAAA,OAAK8H,MAAO,CAAE6U,UAAW,OAAQ2H,SAAU,OAAQ1f,MAAO,WAAYhF,SAAA,EACpEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,wBACRI,EAAAA,EAAAA,MAAA,MAAI8H,MAAO,CAAE6U,UAAW,MAAO5U,YAAa,QAASnI,SAAA,EACnDC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,2DACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,kDACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,6CACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,+CAGJ,ECtJV,GAjD4BulB,KAC1B,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,MAEjBC,EAAUC,KADAC,EAAAA,EAAAA,OACexZ,EAAAA,EAAAA,UAAc,QACvCyZ,EAAgBC,IAAqB1Z,EAAAA,EAAAA,WAAkB,GAe9D,OAbAG,EAAAA,EAAAA,YAAU,KACchJ,WACpB,GAAIiiB,EAAa,CACf,MAAMO,GAAUliB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,YAAa6hB,EAAYQ,KAC3CC,QAAiB7hB,EAAAA,EAAAA,IAAO2hB,GAC1BE,EAAS5hB,UACXshB,EAAYM,EAASniB,OAEzB,GAEFoiB,EAAe,GACd,CAACV,KAGFplB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,sBAAqBF,SAAA,EAClCC,EAAAA,EAAAA,KAACkmB,EAAAA,EAAO,CAACT,SAAUA,KACnBtlB,EAAAA,EAAAA,MAAA,OAAKF,UAAU,eAAcF,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,OAAKF,UAAU,aAAYF,SAAA,CAAC,mBAE1BC,EAAAA,EAAAA,KAAA,UACEE,QAASA,IAAM2lB,GAAmBD,GAClC3d,MAAO,CACLoX,WAAY,OACZ1G,QAAS,WACT1L,gBAAiB2Y,EAAiB,UAAY,UAC9C7gB,MAAO,QACP4a,OAAQ,OACR2E,aAAc,MACdM,OAAQ,UACRH,SAAU,QACV1kB,SAED6lB,EAAiB,mBAAqB,iCAG3C5lB,EAAAA,EAAAA,KAACmmB,EAAAA,EAAU,KACXnmB,EAAAA,EAAAA,KAACikB,GAAkB,IAClB2B,GAAiB5lB,EAAAA,EAAAA,KAACsiB,GAAiB,KAAMtiB,EAAAA,EAAAA,KAAC2R,EAAW,SAEpD,C,sHCtDH,SAASyU,EAAoB5P,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,EACoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,S,aCOvD,MASM2P,GAAWxP,EAAAA,EAAAA,IAAO+M,EAAAA,EAAO,CAC7Bzd,KAAM,UACNqQ,KAAM,QAFSK,CAGd,CACDgN,SAAU,WAyDZ,EAvD0Bhb,EAAAA,YAAiB,SAAc4O,EAASzK,GAChE,MAAMlL,GAAQ4V,EAAAA,EAAAA,GAAgB,CAC5B5V,MAAO2V,EACPtR,KAAM,aAEF,UACJlG,EAAS,OACTqmB,GAAS,KACN3O,GACD7V,EACEkV,EAAa,IACdlV,EACHwkB,UAEIxO,EA7BkBd,KACxB,MAAM,QACJc,GACEd,EAIJ,OAAOgB,EAAAA,EAAAA,GAHO,CACZf,KAAM,CAAC,SAEoBmP,EAAqBtO,EAAQ,EAsB1CG,CAAkBjB,GAClC,OAAoBhX,EAAAA,EAAAA,KAAKqmB,EAAU,CACjCpmB,WAAWiY,EAAAA,EAAAA,GAAKJ,EAAQb,KAAMhX,GAC9BsmB,UAAWD,EAAS,OAAIze,EACxBmF,IAAKA,EACLgK,WAAYA,KACTW,GAEP,G,kEC/CO,SAAS2F,EAA4B9G,GAC1C,OAAOC,EAAAA,EAAAA,IAAqB,kBAAmBD,EACjD,CACA,MACA,GAD4BE,EAAAA,EAAAA,GAAuB,kBAAmB,CAAC,OAAQ,YAAa,QAAS,QAAS,UAAW,a,+DCFzH,MAAM8P,EAAQ,CACZ,CAAE1mB,MAAO,cAAekI,MAAO,QAC/B,CAAElI,MAAO,aAAckI,MAAO,gBAC9B,CAAElI,MAAO,oBAAqBkI,MAAO,MACrC,CAAElI,MAAO,MAAOkI,MAAO,oBAgBzB,EAb6Bme,KAEzBnmB,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYF,SACxBymB,EAAMtlB,KAAI,CAACulB,EAAMvd,KAChB/I,EAAAA,EAAAA,MAAA,OAAKF,UAAW,kBAAkBiJ,IAAQnJ,SAAA,EACxCC,EAAAA,EAAAA,KAAA,MAAAD,SAAK0mB,EAAK3mB,SACVE,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYF,SAAE0mB,EAAKze,UAFckB,M,kECZjD,SAASqX,EAAuB/J,GACrC,OAAOC,EAAAA,EAAAA,IAAqB,aAAcD,EAC5C,CACA,MACA,GADuBE,EAAAA,EAAAA,GAAuB,aAAc,CAAC,OAAQ,WAAY,YAAa,QAAS,SAAU,WAAY,QAAS,WAAY,eAAgB,uBAAwB,iBAAkB,gBAAiB,UAAW,mB,4GCHjO,SAASgQ,EAA2BlQ,GACzC,OAAOC,EAAAA,EAAAA,IAAqB,iBAAkBD,EAChD,EAC2BE,EAAAA,EAAAA,GAAuB,iBAAkB,CAAC,S,aCKrE,MASMiQ,GAAkB9P,EAAAA,EAAAA,IAAO,MAAO,CACpC1Q,KAAM,iBACNqQ,KAAM,QAFgBK,CAGrB,CACD8B,QAAS,GACT,eAAgB,CACda,cAAe,MAqDnB,EAlDiC3Q,EAAAA,YAAiB,SAAqB4O,EAASzK,GAC9E,MAAMlL,GAAQ4V,EAAAA,EAAAA,GAAgB,CAC5B5V,MAAO2V,EACPtR,KAAM,oBAEF,UACJlG,EAAS,UACT8a,EAAY,SACTpD,GACD7V,EACEkV,EAAa,IACdlV,EACHiZ,aAEIjD,EAhCkBd,KACxB,MAAM,QACJc,GACEd,EAIJ,OAAOgB,EAAAA,EAAAA,GAHO,CACZf,KAAM,CAAC,SAEoByP,EAA4B5O,EAAQ,EAyBjDG,CAAkBjB,GAClC,OAAoBhX,EAAAA,EAAAA,KAAK2mB,EAAiB,CACxCxK,GAAIpB,EACJ9a,WAAWiY,EAAAA,EAAAA,GAAKJ,EAAQb,KAAMhX,GAC9B+W,WAAYA,EACZhK,IAAKA,KACF2K,GAEP,G", "sources": ["components/shared/Modal.tsx", "components/admin/business/utils/cardUtils.ts", "components/admin/business/hooks/useCardManagement.ts", "components/admin/business/hooks/usePageConfiguration.ts", "components/admin/business/components/CardSelector.tsx", "components/admin/business/components/CardManagement.tsx", "components/admin/business/components/FieldConfigItem.tsx", "components/admin/business/components/PageBuilderContent.tsx", "components/admin/business/types/PageBuilderTypes.ts", "components/admin/business/hooks/useOfficeDataSimple.ts", "components/admin/business/components/CheckboxDropdown.tsx", "components/admin/business/components/ReportConfiguration.tsx", "components/admin/business/PageBuilder.tsx", "components/admin/business/hooks/usePageBuilderState.ts", "../node_modules/@mui/material/esm/ListItem/listItemClasses.js", "../node_modules/@mui/material/esm/ListItemButton/listItemButtonClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/listItemSecondaryActionClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/ListItemSecondaryAction.js", "../node_modules/@mui/material/esm/ListItem/ListItem.js", "../node_modules/@mui/material/esm/ListItemText/ListItemText.js", "../node_modules/@mui/material/esm/Divider/Divider.js", "components/admin/business/hooks/useOfficeDataEnhanced.ts", "components/admin/business/OfficeLoadingTest.tsx", "components/admin/FixUndefinedReport.tsx", "components/admin/AdminPage.tsx", "../node_modules/@mui/material/esm/Card/cardClasses.js", "../node_modules/@mui/material/esm/Card/Card.js", "../node_modules/@mui/material/esm/ListItemText/listItemTextClasses.js", "components/shared/StatsCards.tsx", "../node_modules/@mui/material/esm/Divider/dividerClasses.js", "../node_modules/@mui/material/esm/CardContent/cardContentClasses.js", "../node_modules/@mui/material/esm/CardContent/CardContent.js"], "sourcesContent": ["import React from 'react';\nimport './Modal.css';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n}\n\nconst Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={e => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2>{title}</h2>\n          <button className=\"close-button\" onClick={onClose}>&times;</button>\n        </div>\n        <div className=\"modal-body\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;", "import { <PERSON>a<PERSON>older, FaFile<PERSON>lt, Fa<PERSON>og, FaFolderOpen } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\n// Helper function to generate card style (icon and color)\nexport const generateCardStyle = (title: string) => {\n  const hash = title\n    .split('')\n    .reduce((acc, char) => acc + char.charCodeAt(0), 0);\n  \n  const icons = [FaFolder, FaFileAlt, FaCog, FaFolderOpen]; // Add more icons if needed\n  const colors = ['#FFC107', '#2196F3', '#4CAF50', '#E91E63', '#9C27B0'];\n\n  const icon = icons[hash % icons.length];\n  const color = colors[hash % colors.length];\n  \n  return { icon, color };\n};\n\n// Helper function to check if a card is a main card\nexport const isMainCard = (cardId: string, allCategories: Category[]): boolean => {\n  const card = allCategories.find(c => c.id === cardId);\n  return card ? !card.parentId : false;\n};\n\n// Helper function to check if a card is a leaf card\nexport const isLeafCard = (cardId: string, allCategories: Category[]): boolean => {\n  return !allCategories.some(c => c.parentId === cardId);\n};\n\n// Helper function to organize cards into a tree structure\nexport const organizeCards = (list: Category[]): Category[] => {\n  const map: { [key: string]: Category } = {};\n  const roots: Category[] = [];\n  list.forEach(item => {\n    map[item.id] = { ...item, children: [] }; \n  });\n  list.forEach(item => {\n    if (item.parentId && map[item.parentId]) {\n      map[item.parentId].children?.push(map[item.id]);\n    } else {\n      roots.push(map[item.id]);\n    }\n  });\n  return roots;\n};\n\n// Helper function to get all descendant IDs\nexport const getAllDescendantIds = (parentId: string, allCategories: Category[]): string[] => {\n  let descendants: string[] = [];\n  const children = allCategories.filter(c => c.parentId === parentId);\n  for (const child of children) {\n    descendants.push(child.id);\n    descendants = descendants.concat(getAllDescendantIds(child.id, allCategories));\n  }\n  return descendants;\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc, collection, getDocs, writeBatch, deleteDoc, updateDoc } from 'firebase/firestore';\nimport { Category } from '../types/PageBuilderTypes';\nimport { generateCardStyle, getAllDescendantIds } from '../utils/cardUtils';\n\ninterface UseCardManagementProps {\n  categories: Category[];\n  setCategories: (categories: Category[]) => void;\n  selectedCard: string;\n  setSelectedCard: (card: string) => void;\n  newCardId: string;\n  setNewCardId: (id: string) => void;\n  newCardTitle: string;\n  setNewCardTitle: (title: string) => void;\n  actionType: string;\n  setActionType: (type: string) => void;\n  setIsLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setShowConfirmModal: (show: boolean) => void;\n  setIsAddingNewCard: (adding: boolean) => void;\n  setPageConfig: (config: any) => void;\n  setFields: (fields: any[]) => void;\n  setEditingCard: (card: Category | null) => void;\n  setShowEditModal: (show: boolean) => void;\n  setCardToDelete: (id: string | null) => void;\n  setShowDeleteConfirmModal: (show: boolean) => void;\n}\n\nexport const useCardManagement = (props: UseCardManagementProps) => {\n  const {\n    categories,\n    setCategories,\n    selectedCard,\n    setSelectedCard,\n    newCardId,\n    setNewCardId,\n    newCardTitle,\n    setNewCardTitle,\n    actionType,\n    setActionType,\n    setIsLoading,\n    setError,\n    setSuccess,\n    setShowConfirmModal,\n    setIsAddingNewCard,\n    setPageConfig,\n    setFields,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n  } = props;\n\n  const fetchCategories = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const querySnapshot = await getDocs(collection(db, 'categories'));\n      const fetchedCategories = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Category));\n      setCategories(fetchedCategories);\n    } catch (err) {\n      setError('Failed to fetch categories.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [setCategories, setIsLoading, setError]);\n\n  const checkDuplicateId = async (id: string): Promise<boolean> => {\n    const docRef = doc(db, 'categories', id);\n    const docSnap = await getDoc(docRef);\n    return docSnap.exists();\n  };\n\n  const handleAddNewCard = async () => {\n    if (!newCardId || !newCardTitle) {\n      setError('Report ID and Title are required.');\n      return;\n    }\n    setIsLoading(true);\n    const isDuplicate = await checkDuplicateId(newCardId);\n    if (isDuplicate) {\n      setError('This Report ID already exists. Please use a unique ID.');\n      setIsLoading(false);\n      return;\n    }\n    setIsLoading(false);\n    setShowConfirmModal(true);\n  };\n\n  const handleConfirmCreate = async () => {\n    if (!newCardId || !newCardTitle) {\n        setError('Report ID and Title cannot be empty.');\n        setShowConfirmModal(false);\n        return;\n    }\n    let parentIdToSet: string | null = null;\n    if (actionType === 'createNestedCard' && selectedCard) {\n      parentIdToSet = selectedCard;\n    } else if (actionType === 'addNewCardGlobal') {\n      parentIdToSet = null; \n    } else if (selectedCard && actionType !== 'addNewCardGlobal') {\n        parentIdToSet = selectedCard;\n    } else if (!selectedCard && actionType !== 'createNestedCard') { \n        parentIdToSet = null;\n    }\n\n    const parentPath = parentIdToSet ? categories.find(c => c.id === parentIdToSet)?.path : '/categories';\n    const newPath = `${parentPath}/${newCardId}`.replace(/\\/+/g, '/');\n\n    try {\n      setIsLoading(true);\n      setShowConfirmModal(false);\n      const cardRef = doc(db, 'categories', newCardId);\n      const { icon: generatedIcon, color: generatedColor } = generateCardStyle(newCardTitle);\n      \n      await setDoc(cardRef, {\n        id: newCardId,\n        title: newCardTitle,\n        path: newPath,\n        parentId: parentIdToSet,\n        lastUpdated: new Date().toISOString(),\n        icon: generatedIcon.name,\n        color: generatedColor,\n        fields: [],\n        isPage: true,\n        pageId: newCardId,\n      });\n  \n      await fetchCategories(); \n      \n      setNewCardId('');\n      setNewCardTitle('');\n      setIsAddingNewCard(false);\n      setActionType(''); \n      setSelectedCard(newCardId);\n      setSuccess(`Report \"${newCardTitle}\" has been created successfully!`);\n      setTimeout(() => setSuccess(null), 3000);\n      \n    } catch (err) {\n      setError('Error creating new report. Check console for details.');\n      console.error('Error creating card:', err);\n    } finally {\n      setIsLoading(false); \n    }\n  };\n\n  const handleEditCard = (card: Category) => {\n    setEditingCard(card);\n    setNewCardTitle(card.title);\n    setShowEditModal(true);\n  };\n\n  const handleUpdateCard = async () => {\n    const editingCard = categories.find(c => c.id === selectedCard);\n    if (!editingCard || !newCardTitle) return;\n    try {\n      setIsLoading(true);\n      const cardRef = doc(db, 'categories', editingCard.id);\n      await updateDoc(cardRef, { title: newCardTitle, lastUpdated: new Date().toISOString() });\n      await fetchCategories();\n      setShowEditModal(false);\n      setEditingCard(null);\n      setNewCardTitle('');\n      setSuccess('Report updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to update report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDeleteClick = (cardId: string) => {\n    setCardToDelete(cardId);\n    setShowDeleteConfirmModal(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (!selectedCard) return;\n    setIsLoading(true);\n    try {\n      const batch = writeBatch(db);\n      const allDescendants = getAllDescendantIds(selectedCard, categories);\n      const idsToDelete = [selectedCard, ...allDescendants];\n\n      for (const id of idsToDelete) {\n        batch.delete(doc(db, 'categories', id));\n        batch.delete(doc(db, 'pages', id));\n      }\n      await batch.commit();\n      await fetchCategories();\n\n      setShowDeleteConfirmModal(false);\n      setCardToDelete(null);\n      setSelectedCard('');\n      setPageConfig(null);\n      setFields([]);\n      setSuccess('Report and all its nested items deleted successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to delete report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return {\n    fetchCategories,\n    handleAddNewCard,\n    handleConfirmCreate,\n    handleEditCard,\n    handleUpdateCard,\n    handleDeleteClick,\n    handleConfirmDelete,\n  };\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc } from 'firebase/firestore';\nimport { FormField, PageConfig, Category } from '../types/PageBuilderTypes';\nimport { <PERSON><PERSON>ield as DynamicFormField, FormConfig as DynamicFormConfig, FormFieldOption } from '../../../shared/DynamicForm';\nimport { supabasePageService } from '../services/supabasePageService';\n\ninterface UsePageConfigurationProps {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  setPageConfig: (config: PageConfig | null) => void;\n  fields: FormField[];\n  setFields: (fields: FormField[]) => void;\n  setAvailableDynamicFields: (fields: DynamicFormField[]) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setPreviewContent: (content: string) => void;\n  setIsPreviewOpen: (open: boolean) => void;\n  // New dropdown values - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  // Setters for dropdown values\n  setSelectedRegions: (regions: string[]) => void;\n  setSelectedDivisions: (divisions: string[]) => void;\n  setSelectedOffices: (offices: string[]) => void;\n  setSelectedFrequency: (frequency: string) => void;\n}\n\nexport const usePageConfiguration = (props: UsePageConfigurationProps) => {\n  const {\n    categories,\n    selectedCard,\n    pageConfig,\n    setPageConfig,\n    fields,\n    setFields,\n    setAvailableDynamicFields,\n    setLoading,\n    setError,\n    setSuccess,\n    setPreviewContent,\n    setIsPreviewOpen,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  } = props;\n\n  const fetchDynamicFormFields = useCallback(async (formId: string) => {\n    if (!formId) return;\n    console.log(`Fetching dynamic form fields for formId: ${formId}`);\n    try {\n      const formConfigRef = doc(db, 'formConfigs', formId);\n      const formConfigSnap = await getDoc(formConfigRef);\n      if (formConfigSnap.exists()) {\n        const formConfigData = formConfigSnap.data() as DynamicFormConfig;\n        setAvailableDynamicFields(formConfigData.fields || []);\n        console.log('Fetched dynamic fields:', formConfigData.fields);\n      } else {\n        console.log(`No dynamic form configuration found for formId: ${formId}`);\n        setAvailableDynamicFields([]);\n      }\n    } catch (err) {\n      console.error('Error fetching dynamic form fields:', err);\n      setError('Failed to fetch dynamic form fields.');\n      setAvailableDynamicFields([]);\n    }\n  }, [setAvailableDynamicFields, setError]);\n\n  const loadPageConfig = useCallback(async (cardId: string) => {\n    if (!cardId) {\n      console.log('loadPageConfig called with no cardId');\n      return;\n    }\n    console.log(`loadPageConfig called for cardId: ${cardId}`);\n    setLoading(true);\n    setError(null);\n    try {\n      // Try loading from Firebase first\n      const docRef = doc(db, 'pages', cardId);\n      const docSnap = await getDoc(docRef);\n\n      let data: PageConfig | null = null;\n\n      if (docSnap.exists()) {\n        // Loading from Firebase\n        data = docSnap.data() as PageConfig;\n      } else {\n        // If not found in Firebase, try Supabase\n        try {\n          data = await supabasePageService.loadPageConfig(cardId);\n        } catch (supabaseError) {\n          // Not found in either database, will create new config\n        }\n      }\n\n      if (data) {\n        setPageConfig(data);\n        setFields(data.fields || []);\n        // Load saved dropdown values - handle both old single values and new arrays\n        setSelectedRegions(data.selectedRegions || (data.selectedRegion ? [data.selectedRegion] : []));\n        setSelectedDivisions(data.selectedDivisions || (data.selectedDivision ? [data.selectedDivision] : []));\n        setSelectedOffices(data.selectedOffices || (data.selectedOffice ? [data.selectedOffice] : []));\n        setSelectedFrequency(data.selectedFrequency || '');\n      } else {\n        // Create new page config\n        const card = categories.find(c => c.id === cardId);\n        setPageConfig({\n          id: cardId,\n          title: card?.title || 'New Page',\n          fields: [],\n          lastUpdated: new Date().toISOString(),\n        });\n        setFields([]);\n        // Reset dropdown values for new page\n        setSelectedRegions([]);\n        setSelectedDivisions([]);\n        setSelectedOffices([]);\n        setSelectedFrequency('');\n      }\n    } catch (err) {\n      setError('Failed to load page configuration.');\n      console.error(err);\n      setPageConfig(null);\n      setFields([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [categories, setLoading, setError, setPageConfig, setFields, setSelectedRegions, setSelectedDivisions, setSelectedOffices, setSelectedFrequency]);\n\n  const addField = () => {\n    const newField: FormField = {\n      id: `field_${Date.now()}`,\n      type: 'text',\n      label: 'New Field',\n      placeholder: '',\n      options: [],\n      required: false,\n      region: '',\n      division: '',\n      office: '',\n    };\n    setFields([...fields, newField]);\n  };\n\n  const addFieldFromDynamic = (dynamicField: DynamicFormField) => {\n    console.log('Attempting to add dynamic field:', dynamicField);\n    const newField: FormField = {\n      id: dynamicField.id,\n      type: dynamicField.type,\n      label: dynamicField.label,\n      placeholder: dynamicField.placeholder,\n      options: dynamicField.options ? dynamicField.options.map((opt: string | FormFieldOption) => {\n        if (typeof opt === 'string') {\n          return { label: opt, value: opt };\n        } else {\n          return { label: opt.label, value: opt.value };\n        }\n      }) : undefined,\n      required: dynamicField.required,\n      defaultValue: dynamicField.defaultValue,\n      min: dynamicField.min,\n      max: dynamicField.max,\n      sectionTitle: undefined,\n      columns: undefined,\n      buttonText: undefined,\n      buttonType: undefined,\n      onClickAction: undefined,\n      value: undefined,\n    };\n\n    if (fields.some(field => field.id === newField.id)) {\n        console.warn(`Duplicate field ID detected: \"${newField.id}\". Field not added.`);\n        setError(`Field with ID \"${newField.id}\" already exists in the page configuration.`);\n        setTimeout(() => setError(null), 3000);\n        return;\n    }\n\n    console.log('Adding new field to state:', newField);\n    setFields([...fields, newField]);\n    setSuccess(`Added field \"${newField.label}\" to page configuration.`);\n    setTimeout(() => setSuccess(null), 3000);\n  };\n\n  const updateField = (index: number, updatedField: FormField) => {\n    const updatedFields = [...fields];\n    updatedFields[index] = updatedField;\n    setFields(updatedFields);\n  };\n\n  const removeField = (index: number) => {\n    setFields(fields.filter((_, i) => i !== index));\n  };\n\n  const handleSave = async () => {\n    if (!selectedCard || !pageConfig) {\n      setError('No report selected or page configuration loaded.');\n      return;\n    }\n\n    // Validate that report frequency is selected\n    if (!selectedFrequency) {\n      setError('Report frequency is required. Please select a frequency before saving.');\n      return;\n    }\n\n    setLoading(true);\n    console.log('Attempting to save page configuration for cardId:', selectedCard);\n    console.log('Fields being saved:', fields);\n    console.log('Report frequency:', selectedFrequency);\n\n    try {\n      const cleanedFields = fields.map(field => {\n        const cleanedField: any = {};\n        for (const key in field) {\n          if (field[key] !== undefined) {\n            cleanedField[key] = field[key];\n          } else {\n            cleanedField[key] = null;\n          }\n        }\n        return cleanedField;\n      });\n\n      const updatedPageConfig: PageConfig = {\n        ...pageConfig,\n        id: selectedCard,\n        title: categories.find(c => c.id === selectedCard)?.title || pageConfig.title,\n        fields: cleanedFields,\n        lastUpdated: new Date().toISOString(),\n        selectedRegions,\n        selectedDivisions,\n        selectedOffices,\n        selectedFrequency,\n      };\n\n      // Save to both Firebase and Supabase\n      const savePromises = [];\n\n      // Save to Firebase\n      savePromises.push(\n        setDoc(doc(db, 'pages', selectedCard), updatedPageConfig)\n          .catch(err => {\n            console.error('Firebase save failed:', err);\n            throw new Error(`Firebase save failed: ${err.message}`);\n          })\n      );\n\n      // Save to Supabase\n      savePromises.push(\n        supabasePageService.savePageConfig(updatedPageConfig)\n          .catch(err => {\n            console.error('Supabase save failed:', err);\n            throw new Error(`Supabase save failed: ${err.message}`);\n          })\n      );\n\n      // Wait for both saves to complete\n      await Promise.all(savePromises);\n\n      setPageConfig(updatedPageConfig);\n      setSuccess('Page configuration saved successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n\n    } catch (err) {\n      console.error('Failed to save page configuration:', err);\n      setError(`Failed to save page configuration: ${err instanceof Error ? err.message : 'Unknown error'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePreview = () => {\n    if (!pageConfig || fields.length === 0) {\n      alert('No page configuration or fields to preview.');\n      return;\n    }\n\n    const generatedPreview = `\n      <h1>${pageConfig.title}</h1>\n      <form>\n        ${fields.map(field => {\n          let fieldHtml = '';\n          switch (field.type) {\n            case 'text':\n            case 'number':\n            case 'date':\n            case 'textarea':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <input type=\"${field.type}\" class=\"form-control\" placeholder=\"${field.placeholder || ''}\" ${field.required ? 'required' : ''} />\n                </div>\n              `;\n              break;\n            case 'dropdown':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <select class=\"form-control\" ${field.required ? 'required' : ''}>\n                    <option value=\"\">Select ${field.label}</option>\n                    ${field.options?.map(option => `<option value=\"${option.value}\">${option.label}</option>`).join('') || ''}\n                  </select>\n                </div>\n              `;\n              break;\n            case 'checkbox':\n              fieldHtml = `\n                <div class=\"form-check mb-3\">\n                  <input type=\"checkbox\" class=\"form-check-input\" id=\"${field.id}\" ${field.required ? 'required' : ''} />\n                  <label class=\"form-check-label\" for=\"${field.id}\">${field.label}${field.required ? ' *' : ''}</label>\n                </div>\n              `;\n              break;\n            case 'radio':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  ${field.options?.map((option, i) => `\n                    <div class=\"form-check\">\n                      <input class=\"form-check-input\" type=\"radio\" name=\"${field.id}\" id=\"${field.id}-${i}\" value=\"${option.value}\" ${field.required ? 'required' : ''}>\n                      <label class=\"form-check-label\" for=\"${field.id}-${i}\">${option.label}</label>\n                    </div>\n                  `).join('') || ''}\n                </div>\n              `;\n              break;\n            case 'section':\n              fieldHtml = `\n                <div class=\"card mt-3 mb-3\">\n                  <div class=\"card-header\">${field.sectionTitle || 'Section'}</div>\n                  <div class=\"card-body\">\n                    <p>Fields for this section would appear here in the actual form.</p>\n                  </div>\n                </div>\n              `;\n              break;\n            case 'button':\n              fieldHtml = `\n                <button type=\"button\" class=\"btn btn-primary mt-3\">${field.buttonText || 'Button'}</button>\n              `;\n              break;\n            default:\n              fieldHtml = `<p>Unsupported field type: ${field.type}</p>`;\n          }\n          return fieldHtml;\n        }).join('')}\n      </form>\n    `;\n\n    setPreviewContent(generatedPreview);\n    setIsPreviewOpen(true);\n  };\n\n  return {\n    fetchDynamicFormFields,\n    loadPageConfig,\n    addField,\n    addFieldFromDynamic,\n    updateField,\n    removeField,\n    handleSave,\n    handlePreview,\n  };\n};\n", "import React from 'react';\nimport { Category } from '../types/PageBuilderTypes';\nimport { organizeCards, isLeafCard, isMainCard } from '../utils/cardUtils';\n\ninterface CardSelectorProps {\n  categories: Category[];\n  selectedCard: string;\n  onCardChange: (cardId: string) => void;\n  actionType: string;\n  onActionChange: (action: string) => void;\n  isLoading: boolean;\n  onCreateAction: () => void;\n  onWebPageAction: () => void;\n}\n\nconst CardSelector: React.FC<CardSelectorProps> = ({\n  categories,\n  selectedCard,\n  onCardChange,\n  actionType,\n  onActionChange,\n  isLoading,\n  onCreateAction,\n  onWebPageAction,\n}) => {\n  const renderCardOptions = (cards: Category[], level = 0): React.ReactElement[] => {\n    return cards.flatMap(card => {\n      // Handle undefined or empty titles gracefully\n      const displayTitle = card.title || '[Unnamed Report]';\n\n      return [\n        <option key={card.id} value={card.id} style={{ paddingLeft: `${level * 20}px` }}>\n          {`${'--'.repeat(level)} ${displayTitle}`}\n        </option>,\n        ...(card.children && card.children.length > 0 ? renderCardOptions(card.children, level + 1) : []),\n      ];\n    });\n  };\n\n  const handleCardChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newSelectedCard = e.target.value;\n    onCardChange(newSelectedCard);\n  };\n\n  const handleActionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newAction = e.target.value;\n    onActionChange(newAction);\n    \n    if (newAction === 'createNestedCard' || newAction === 'addNewCardGlobal') {\n      onCreateAction();\n    } else if (newAction === 'createWebPage') {\n      onWebPageAction();\n    }\n  };\n\n  return (\n    <div className=\"card-selector\">\n      <select\n        value={selectedCard}\n        onChange={handleCardChange}\n        className=\"form-select\"\n        disabled={isLoading}\n      >\n        <option value=\"\">{isLoading ? 'Loading Reports...' : 'Select or Create New Report'}</option>\n        {renderCardOptions(organizeCards(categories))}\n      </select>\n\n      <div className=\"action-dropdown-container\">\n        <select\n          value={actionType}\n          onChange={handleActionChange}\n          className=\"form-select action-dropdown\"\n        >\n          <option value=\"\">Select Action...</option>\n          <option value=\"addNewCardGlobal\" disabled={!!selectedCard}>\n            Create New Main Report\n          </option>\n          {selectedCard && (\n            <>\n              <option value=\"createNestedCard\">\n                Create Nested Report\n              </option>\n              <option\n                value=\"createWebPage\"\n                disabled={!isLeafCard(selectedCard, categories) || isMainCard(selectedCard, categories)}\n              >\n                Create/Edit Web Page for this Report\n              </option>\n            </>\n          )}\n        </select>\n      </div>\n    </div>\n  );\n};\n\nexport default CardSelector;\n", "import React from 'react';\nimport { FaEdit, FaTrash } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\ninterface CardManagementProps {\n  selectedCard: string;\n  categories: Category[];\n  onEditCard: (card: Category) => void;\n  onDeleteCard: (cardId: string) => void;\n}\n\nconst CardManagement: React.FC<CardManagementProps> = ({\n  selectedCard,\n  categories,\n  onEditCard,\n  onDeleteCard,\n}) => {\n  const selectedCategory = categories.find(c => c.id === selectedCard);\n\n  if (!selectedCategory) {\n    return null;\n  }\n\n  return (\n    <div className=\"card-management\">\n      <h3>Report Details: \"{selectedCategory.title}\"</h3>\n      <div className=\"card-actions\">\n        <button\n          onClick={() => onEditCard(selectedCategory)}\n          className=\"edit-button btn btn-outline-primary btn-sm me-2\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaEdit as React.ComponentType<any>)} Edit Name\n        </button>\n        <button\n          onClick={() => onDeleteCard(selectedCard)}\n          className=\"delete-button btn btn-outline-danger btn-sm\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaTrash as React.ComponentType<any>)} Delete Report\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CardManagement;\n", "import React from 'react';\nimport { FaTrash } from 'react-icons/fa';\nimport { FormField } from '../types/PageBuilderTypes';\n\ninterface FieldConfigItemProps {\n  field: FormField;\n  index: number;\n  onUpdate: (index: number, field: FormField) => void;\n  onRemove: (index: number) => void;\n}\n\nconst FieldConfigItem: React.FC<FieldConfigItemProps> = ({\n  field,\n  index,\n  onUpdate,\n  onRemove,\n}) => {\n  const handleOptionChange = (optIndex: number, value: string, key: 'label' | 'value') => {\n    const newOptions = [...(field.options || [])];\n    newOptions[optIndex] = { ...newOptions[optIndex], [key]: value };\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const addOption = () => {\n    const newOptions = [...(field.options || []), { label: '', value: '' }];\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const removeOption = (optIndex: number) => {\n    const newOptions = field.options?.filter((_, i) => i !== optIndex);\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const handleDefaultValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { value, type } = e.target;\n    let newDefaultValue: any = value;\n    if (type === 'checkbox') {\n      newDefaultValue = (e.target as HTMLInputElement).checked;\n    }\n    onUpdate(index, { ...field, defaultValue: newDefaultValue });\n  };\n\n  return (\n    <div className=\"field-config-item card mb-3\">\n      <div className=\"card-header d-flex justify-content-between align-items-center\">\n        <strong>{field.label || 'Unnamed Field'}</strong> ({field.type})\n        <button onClick={() => onRemove(index)} className=\"btn btn-danger btn-sm\">\n          {React.createElement(FaTrash as React.ComponentType<any>)} Remove\n        </button>\n      </div>\n      <div className=\"card-body\">\n        {/* Field Type Selector */}\n        <div className=\"form-group\">\n          <label htmlFor={`field-type-${index}`} className=\"form-label\">Type: </label>\n          <select\n            id={`field-type-${index}`}\n            className=\"form-control\"\n            value={field.type}\n            onChange={(e) => onUpdate(index, {\n              ...field, \n              type: e.target.value as FormField['type'], \n              options: field.type !== 'dropdown' && field.type !== 'radio' && field.type !== 'checkbox-group' ? undefined : field.options, \n              placeholder: field.type === 'section' || field.type === 'button' ? undefined : field.placeholder \n            })}\n          >\n            <option value=\"text\">Text</option>\n            <option value=\"textarea\">Textarea</option>\n            <option value=\"number\">Number</option>\n            <option value=\"date\">Date</option>\n            <option value=\"dropdown\">Dropdown</option>\n            <option value=\"radio\">Radio Group</option>\n            <option value=\"checkbox\">Checkbox (Single)</option>\n            <option value=\"checkbox-group\">Checkbox Group</option>\n            <option value=\"switch\">Switch</option>\n            <option value=\"file\">File Upload</option>\n            <option value=\"section\">Section Header</option>\n            <option value=\"button\">Button</option>\n          </select>\n        </div>\n\n        <div className=\"form-group\">\n          <label htmlFor={`field-label-${index}`} className=\"form-label\">Label: </label>\n          <input\n            id={`field-label-${index}`}\n            type=\"text\"\n            className=\"form-control\"\n            value={field.label}\n            onChange={(e) => onUpdate(index, {...field, label: e.target.value})}\n            required\n          />\n        </div>\n\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-placeholder-${index}`} className=\"form-label\">Placeholder: </label>\n            <input\n              id={`field-placeholder-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.placeholder || ''}\n              onChange={(e) => onUpdate(index, {...field, placeholder: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'number' && (\n          <>\n            <div className=\"form-group\">\n              <label htmlFor={`field-min-${index}`} className=\"form-label\">Min Value: </label>\n              <input\n                id={`field-min-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.min === undefined ? '' : field.min}\n                onChange={(e) => onUpdate(index, {...field, min: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor={`field-max-${index}`} className=\"form-label\">Max Value: </label>\n              <input\n                id={`field-max-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.max === undefined ? '' : field.max}\n                onChange={(e) => onUpdate(index, {...field, max: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n          </>\n        )}\n\n        {['dropdown', 'radio', 'checkbox-group'].includes(field.type) && (\n          <div className=\"form-group field-options-config\">\n            <label className=\"form-label\">Options: </label>\n            {field.options?.map((opt, optIndex) => (\n              <div key={optIndex} className=\"input-group mb-2\">\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Label\"\n                  value={opt.label}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'label')}\n                />\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Value\"\n                  value={opt.value}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'value')}\n                />\n                <button type=\"button\" onClick={() => removeOption(optIndex)} className=\"btn btn-outline-danger\">\n                  Remove\n                </button>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addOption} className=\"btn btn-secondary btn-sm\">\n              Add Option\n            </button>\n          </div>\n        )}\n\n        {/* Default Value - Type specific handling */}\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n            <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <input\n                    id={`field-default-value-${index}`}\n                    type={field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n            </div>\n        )}\n\n        {(field.type === 'checkbox' || field.type === 'switch') && (\n            <div className=\"form-group form-check\">\n                <input\n                    id={`field-default-value-${index}`}\n                    type=\"checkbox\"\n                    className=\"form-check-input\"\n                    checked={Boolean(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n                <label htmlFor={`field-default-value-${index}`} className=\"form-check-label\">Default Checked: </label>\n            </div>\n        )}\n\n        {['dropdown', 'radio'].includes(field.type) && field.options && field.options.length > 0 && (\n             <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <select\n                    id={`field-default-value-${index}`}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                >\n                    <option value=\"\">-- Select Default --</option>\n                    {field.options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}\n                </select>\n            </div>\n        )}\n\n        {field.type === 'checkbox-group' && (\n            <div className=\"form-group\">\n                <label className=\"form-label\">Default Values (comma-separated): </label>\n                <input\n                    type=\"text\"\n                    className=\"form-control\"\n                    value={Array.isArray(field.defaultValue) ? field.defaultValue.join(',') : ''}\n                    onChange={(e) => onUpdate(index, {...field, defaultValue: e.target.value.split(',').map(s => s.trim()).filter(s => s)})}\n                    placeholder=\"value1,value2\"\n                />\n            </div>\n        )}\n\n        {field.type === 'button' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-button-text-${index}`} className=\"form-label\">Button Text: </label>\n            <input\n              id={`field-button-text-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.buttonText || ''}\n              onChange={(e) => onUpdate(index, {...field, buttonText: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'section' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-section-title-${index}`} className=\"form-label\">Section Title: </label>\n            <input\n              id={`field-section-title-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.sectionTitle || ''}\n              onChange={(e) => onUpdate(index, {...field, sectionTitle: e.target.value})}\n            />\n          </div>\n        )}\n\n        {/* Required Checkbox (excluding button and section) */}\n        {!['button', 'section'].includes(field.type) && (\n          <div className=\"form-group form-check\">\n            <input\n              id={`field-required-${index}`}\n              type=\"checkbox\"\n              className=\"form-check-input\"\n              checked={!!field.required}\n              onChange={(e) => onUpdate(index, {...field, required: e.target.checked})}\n            />\n            <label htmlFor={`field-required-${index}`} className=\"form-check-label\"> Required</label>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FieldConfigItem;\n", "import React from 'react';\nimport { FaPlus, FaSave } from 'react-icons/fa';\nimport { FormField, PageConfig } from '../types/PageBuilderTypes';\nimport FieldConfigItem from './FieldConfigItem';\n\ninterface PageBuilderContentProps {\n  pageConfig: PageConfig;\n  fields: FormField[];\n  onAddField: () => void;\n  onUpdateField: (index: number, field: FormField) => void;\n  onRemoveField: (index: number) => void;\n  onSave: () => void;\n  onPreview: () => void;\n  loading: boolean;\n}\n\nconst PageBuilderContent: React.FC<PageBuilderContentProps> = ({\n  pageConfig,\n  fields,\n  onAddField,\n  onUpdateField,\n  onRemoveField,\n  onSave,\n  onPreview,\n  loading,\n}) => {\n  return (\n    <div className=\"builder-content\">\n      <h4>Page Configuration for: {pageConfig.title}</h4>\n      \n      <h5>Current Page Fields:</h5>\n      {fields.map((field, index) => (\n        <FieldConfigItem\n          key={field.id || index}\n          field={field}\n          index={index}\n          onUpdate={onUpdateField}\n          onRemove={onRemoveField}\n        />\n      ))}\n      \n      <button onClick={onAddField} className=\"btn btn-info mt-3\">\n        {React.createElement(FaPlus as React.ComponentType<any>)} Add Field\n      </button>\n      \n      <button \n        onClick={onSave} \n        className=\"btn btn-success mt-3 ms-2\" \n        disabled={loading || !pageConfig || fields.length === 0}\n      >\n        {React.createElement(FaSave as React.ComponentType<any>)} {loading ? 'Saving...' : 'Save Page Configuration'}\n      </button>\n      \n      <button \n        onClick={onPreview} \n        className=\"btn btn-secondary mt-3 ms-2\" \n        disabled={!pageConfig || fields.length === 0}\n      >\n        Preview Page\n      </button>\n    </div>\n  );\n};\n\nexport default PageBuilderContent;\n", "// Interfaces for PageBuilder component\n\nexport interface FormFieldOption {\n  label: string;\n  value: string;\n}\n\n// NOTE: This FormField interface is for the PageBuilder's internal state\n// and represents the configuration being built for a specific 'page'.\n// It's slightly different from the DynamicFormField used by the DynamicForm component.\nexport interface FormField {\n  id: string;\n  type: 'text' | 'textarea' | 'number' | 'date' | 'dropdown' | 'radio' | 'checkbox' | 'checkbox-group' | 'section' | 'button' | 'file' | 'switch';\n  label: string;\n  placeholder?: string;\n  region?: string;\n  division?: string;\n  office?: string;\n  options?: FormFieldOption[]; // For dropdown, radio, checkbox\n  required?: boolean;\n  value?: any; // Current value of the field (might not be used in builder, but kept for consistency)\n  // For section type\n  sectionTitle?: string;\n  columns?: number; // Number of columns for fields within the section\n  // For button type\n  buttonText?: string;\n  buttonType?: string;\n  onClickAction?: string;\n  defaultValue?: any;\n  min?: number;\n  max?: number;\n  [key: string]: any; // Add this index signature\n}\n\nexport interface PageConfig {\n  id: string;\n  title: string;\n  fields: FormField[];\n  lastUpdated: string;\n  isPage?: boolean; // New field\n  pageId?: string;\n  // Report configuration - updated to support both old single values and new arrays\n  selectedRegion?: string; // Keep for backward compatibility\n  selectedDivision?: string; // Keep for backward compatibility\n  selectedOffice?: string; // Keep for backward compatibility\n  selectedRegions?: string[]; // New array-based selections\n  selectedDivisions?: string[]; // New array-based selections\n  selectedOffices?: string[]; // New array-based selections\n  selectedFrequency?: string;\n}\n\nexport interface Category {\n  id: string;\n  title: string;\n  path: string; // e.g., /categories/parent-id/child-id\n  parentId: string | null;\n  children?: Category[];\n  icon?: string; // Icon name (e.g., 'FaFolder')\n  color?: string; // Color for the icon/card\n  fields?: FormField[]; // If storing form fields directly on category for some reason\n  lastUpdated?: string;\n  isPage: boolean; // New field\n  pageId: string; \n}\n\nexport interface PageBuilderState {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  fields: FormField[];\n  availableDynamicFields: any[];\n  isLoading: boolean;\n  loading: boolean;\n  error: string | null;\n  success: string | null;\n  isAddingNewCard: boolean;\n  newCardId: string;\n  newCardTitle: string;\n  showConfirmModal: boolean;\n  editingCard: Category | null;\n  showEditModal: boolean;\n  cardToDelete: string | null;\n  showDeleteConfirmModal: boolean;\n  actionType: string;\n  isPreviewOpen: boolean;\n  previewContent: string;\n  // New dropdown states - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n}\n\n// Report frequency options\nexport interface ReportFrequency {\n  value: string;\n  label: string;\n}\n\nexport const REPORT_FREQUENCIES: ReportFrequency[] = [\n  { value: 'daily', label: 'Daily' },\n  { value: 'weekly', label: 'Weekly' },\n  { value: 'monthly', label: 'Monthly' }\n];\n\n// Location hierarchy interfaces - matching Supabase table structure\nexport interface Region {\n  id: string;\n  name: string;\n}\n\nexport interface Division {\n  id: string;\n  name: string;\n  region: string; // matches the Region column in Supabase\n}\n\nexport interface Office {\n  id: string; // Now uses office name instead of facility ID\n  name: string;\n  region: string; // matches the Region column in Supabase\n  division: string; // matches the Division column in Supabase\n  facilityId?: string; // Keep facility ID for reference/mapping\n}\n\n// Supabase office record interface (matches actual table structure)\nexport interface SupabaseOfficeRecord {\n  'Facility ID': string;\n  Region: string;\n  Division: string;\n  'Office name': string;\n}\n", "import { useState, useEffect } from 'react';\nimport { supabase } from '../../../../config/supabaseClient';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n}\n\nexport const useOfficeDataSimple = (): UseOfficeDataReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataSimple: Fetching with enhanced pagination...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allData = await OfficeService.fetchAllOfficeData();\n\n      console.log('✅ useOfficeDataSimple: Fetched', allData.length, 'office records');\n\n      // Process regions exactly like SQL: SELECT DISTINCT \"Region\" FROM offices ORDER BY \"Region\"\n      const distinctRegions = allData\n        ?.map(row => row.Region)\n        .filter((region, index, array) => array.indexOf(region) === index)\n        .filter((region): region is string => region != null && region.trim() !== '') // Type guard to ensure string\n        .sort();\n\n      // Process regions successfully\n\n      const regionsArray: Region[] = distinctRegions?.map(regionName => ({\n        id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: regionName,\n      })) || [];\n\n      // Process divisions exactly like SQL: SELECT DISTINCT \"Region\", \"Division\" FROM offices ORDER BY \"Region\", \"Division\"\n      const distinctDivisions = allData\n        ?.map(row => ({ region: row.Region, division: row.Division }))\n        .filter((item, index, array) =>\n          array.findIndex(x => x.region === item.region && x.division === item.division) === index\n        )\n        .filter((item): item is { region: string; division: string } =>\n          item.region != null && item.division != null &&\n          item.region.trim() !== '' && item.division.trim() !== ''\n        )\n        .sort((a, b) => a.region.localeCompare(b.region) || a.division.localeCompare(b.division));\n\n      const divisionsArray: Division[] = distinctDivisions?.map(item => ({\n        id: item.division.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: item.division,\n        region: item.region,\n      })) || [];\n\n      // Process all offices - USE OFFICE NAME AS ID instead of Facility ID\n      const officesArray: Office[] = allData\n        ?.filter(row => row['Office name'] && row.Region && row.Division)\n        .map(row => ({\n          id: row['Office name'], // ✅ FIXED: Use office name as ID for form targeting\n          name: row['Office name'],\n          region: row.Region || '',\n          division: row.Division || '',\n          facilityId: row['Office name'], // Use office name as facility ID for consistency\n        })) || [];\n\n      // Data processing completed successfully\n\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n\n    } catch (err) {\n      console.error('🚨 SIMPLE: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n  };\n};\n", "import React, { useState, useRef, useEffect } from 'react';\n\ninterface Option {\n  id: string;\n  name: string;\n}\n\ninterface CheckboxDropdownProps {\n  id: string;\n  label: string;\n  options: Option[];\n  selectedValues: string[];\n  onChange: (values: string[]) => void;\n  disabled?: boolean;\n  placeholder?: string;\n}\n\nconst CheckboxDropdown: React.FC<CheckboxDropdownProps> = ({\n  id,\n  label,\n  options,\n  selectedValues,\n  onChange,\n  disabled = false,\n  placeholder = \"-- Select Options --\"\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleCheckboxChange = (optionId: string) => {\n    if (selectedValues.includes(optionId)) {\n      // Remove from selection\n      onChange(selectedValues.filter(id => id !== optionId));\n    } else {\n      // Add to selection\n      onChange([...selectedValues, optionId]);\n    }\n  };\n\n  const handleSelectAll = () => {\n    if (selectedValues.length === options.length) {\n      // Deselect all\n      onChange([]);\n    } else {\n      // Select all\n      onChange(options.map(option => option.id));\n    }\n  };\n\n  const getDisplayText = () => {\n    if (selectedValues.length === 0) {\n      return placeholder;\n    } else if (selectedValues.length === 1) {\n      const selectedOption = options.find(option => option.id === selectedValues[0]);\n      return selectedOption?.name || placeholder;\n    } else {\n      return `${selectedValues.length} selected`;\n    }\n  };\n\n  const isAllSelected = selectedValues.length === options.length && options.length > 0;\n  const isIndeterminate = selectedValues.length > 0 && selectedValues.length < options.length;\n\n  return (\n    <div className=\"form-group\">\n      <label htmlFor={id} className=\"form-label\">{label}:</label>\n      <div className=\"dropdown\" ref={dropdownRef}>\n        <button\n          id={id}\n          className={`btn btn-outline-secondary dropdown-toggle w-100 text-start ${disabled ? 'disabled' : ''}`}\n          type=\"button\"\n          onClick={() => !disabled && setIsOpen(!isOpen)}\n          disabled={disabled}\n          style={{ \n            backgroundColor: disabled ? '#e9ecef' : 'white',\n            borderColor: '#ced4da'\n          }}\n        >\n          <span className={selectedValues.length === 0 ? 'text-muted' : ''}>\n            {getDisplayText()}\n          </span>\n        </button>\n        \n        {isOpen && !disabled && (\n          <div className=\"dropdown-menu show w-100\" style={{ maxHeight: '300px', overflowY: 'auto' }}>\n            {/* Select All Option */}\n            {options.length > 1 && (\n              <>\n                <div className=\"dropdown-item\">\n                  <div className=\"form-check\">\n                    <input\n                      className=\"form-check-input\"\n                      type=\"checkbox\"\n                      id={`${id}-select-all`}\n                      checked={isAllSelected}\n                      ref={(input) => {\n                        if (input) input.indeterminate = isIndeterminate;\n                      }}\n                      onChange={handleSelectAll}\n                    />\n                    <label className=\"form-check-label fw-bold\" htmlFor={`${id}-select-all`}>\n                      Select All ({options.length})\n                    </label>\n                  </div>\n                </div>\n                <hr className=\"dropdown-divider\" />\n              </>\n            )}\n            \n            {/* Individual Options */}\n            {options.map(option => (\n              <div key={option.id} className=\"dropdown-item\">\n                <div className=\"form-check\">\n                  <input\n                    className=\"form-check-input\"\n                    type=\"checkbox\"\n                    id={`${id}-${option.id}`}\n                    checked={selectedValues.includes(option.id)}\n                    onChange={() => handleCheckboxChange(option.id)}\n                  />\n                  <label className=\"form-check-label\" htmlFor={`${id}-${option.id}`}>\n                    {option.name}\n                  </label>\n                </div>\n              </div>\n            ))}\n            \n            {options.length === 0 && (\n              <div className=\"dropdown-item text-muted\">\n                <em>No options available</em>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n      \n      {/* Selected count indicator */}\n      {selectedValues.length > 0 && (\n        <small className=\"text-muted mt-1 d-block\">\n          {selectedValues.length} of {options.length} selected\n        </small>\n      )}\n    </div>\n  );\n};\n\nexport default CheckboxDropdown;\n", "import React, { useEffect } from 'react';\nimport { REPORT_FREQUENCIES } from '../types/PageBuilderTypes';\nimport { useOfficeDataSimple as useOfficeData } from '../hooks/useOfficeDataSimple';\nimport CheckboxDropdown from './CheckboxDropdown';\n\ninterface ReportConfigurationProps {\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  onRegionsChange: (regions: string[]) => void;\n  onDivisionsChange: (divisions: string[]) => void;\n  onOfficesChange: (offices: string[]) => void;\n  onFrequencyChange: (frequency: string) => void;\n}\n\nconst ReportConfiguration: React.FC<ReportConfigurationProps> = ({\n  selectedRegions,\n  selectedDivisions,\n  selectedOffices,\n  selectedFrequency,\n  onRegionsChange,\n  onDivisionsChange,\n  onOfficesChange,\n  onFrequencyChange,\n}) => {\n  // Use custom hook to fetch office data from Supabase\n  const { regions, divisions, offices, loading, error, refetch } = useOfficeData();\n\n  // Filter divisions based on selected regions\n  const selectedRegionNames = selectedRegions.map(regionId =>\n    regions.find(r => r.id === regionId)?.name\n  ).filter(Boolean);\n\n  const availableDivisions = selectedRegions.length > 0\n    ? divisions.filter(division => selectedRegionNames.includes(division.region))\n    : divisions; // Show all divisions if no regions selected\n\n  // Filter offices based on selected divisions\n  const selectedDivisionNames = selectedDivisions.map(divisionId =>\n    divisions.find(d => d.id === divisionId)?.name\n  ).filter(Boolean);\n\n  const availableOffices = selectedDivisions.length > 0\n    ? offices.filter(office =>\n        selectedRegionNames.includes(office.region) &&\n        selectedDivisionNames.includes(office.division)\n      )\n    : selectedRegions.length > 0\n      ? offices.filter(office => selectedRegionNames.includes(office.region))\n      : offices; // Show all offices if no filters applied\n\n  // Reset dependent selections when parent selections change\n  useEffect(() => {\n    if (selectedRegions.length > 0) {\n      // Remove divisions that don't belong to selected regions\n      const validDivisions = selectedDivisions.filter(divisionId => {\n        const division = divisions.find(d => d.id === divisionId);\n        return division && selectedRegionNames.includes(division.region);\n      });\n\n      if (validDivisions.length !== selectedDivisions.length) {\n        onDivisionsChange(validDivisions);\n      }\n    }\n  }, [selectedRegions, selectedDivisions, divisions, selectedRegionNames, onDivisionsChange]);\n\n  useEffect(() => {\n    if (selectedDivisions.length > 0) {\n      // Remove offices that don't belong to selected regions/divisions\n      const validOffices = selectedOffices.filter(officeId => {\n        const office = offices.find(o => o.id === officeId);\n        return office &&\n               selectedRegionNames.includes(office.region) &&\n               selectedDivisionNames.includes(office.division);\n      });\n\n      if (validOffices.length !== selectedOffices.length) {\n        onOfficesChange(validOffices);\n      }\n    }\n  }, [selectedDivisions, selectedOffices, offices, selectedRegionNames, selectedDivisionNames, onOfficesChange]);\n\n  return (\n    <div className=\"report-configuration mt-3 mb-3\">\n      <h5>Report Configuration</h5>\n\n      {loading && (\n        <div className=\"alert alert-info\">\n          <div className=\"d-flex align-items-center\">\n            <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n            Loading office data...\n          </div>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"alert alert-danger\">\n          <strong>Error:</strong> {error}\n          <button\n            className=\"btn btn-sm btn-outline-danger ms-2\"\n            onClick={refetch}\n          >\n            Retry\n          </button>\n        </div>\n      )}\n\n      {!loading && !error && (\n        <div className=\"row\">\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"region-select\"\n              label=\"Select Regions\"\n              options={regions}\n              selectedValues={selectedRegions}\n              onChange={onRegionsChange}\n              disabled={loading}\n              placeholder=\"-- Select Regions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"division-select\"\n              label=\"Select Divisions\"\n              options={availableDivisions}\n              selectedValues={selectedDivisions}\n              onChange={onDivisionsChange}\n              disabled={selectedRegions.length === 0 || loading}\n              placeholder=\"-- Select Divisions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"office-select\"\n              label=\"Select Offices\"\n              options={availableOffices}\n              selectedValues={selectedOffices}\n              onChange={onOfficesChange}\n              disabled={selectedDivisions.length === 0 || loading}\n              placeholder=\"-- Select Offices --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <div className=\"form-group\">\n              <label htmlFor=\"frequency-select\" className=\"form-label\">\n                Report Frequency: <span className=\"text-danger\">*</span>\n              </label>\n              <select\n                id=\"frequency-select\"\n                className={`form-select ${!selectedFrequency ? 'is-invalid' : ''}`}\n                value={selectedFrequency}\n                onChange={(e) => onFrequencyChange(e.target.value)}\n                disabled={loading}\n                required\n              >\n                <option value=\"\">-- Select Frequency --</option>\n                {REPORT_FREQUENCIES.map(frequency => (\n                  <option key={frequency.value} value={frequency.value}>\n                    {frequency.label}\n                  </option>\n                ))}\n              </select>\n              {!selectedFrequency && (\n                <div className=\"invalid-feedback\">\n                  Report frequency is required.\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ReportConfiguration;\n", "import React, { useEffect } from 'react';\nimport Modal from '../../shared/Modal';\nimport './PageBuilder.css';\n\n// Import refactored components and hooks\nimport { usePageBuilderState } from './hooks/usePageBuilderState';\nimport { useCardManagement } from './hooks/useCardManagement';\nimport { usePageConfiguration } from './hooks/usePageConfiguration';\nimport CardSelector from './components/CardSelector';\nimport CardManagement from './components/CardManagement';\nimport PageBuilderContent from './components/PageBuilderContent';\nimport ReportConfiguration from './components/ReportConfiguration';\n// Debug components removed - Supabase integration working\nimport { isLeafCard, isMainCard } from './utils/cardUtils';\n\n// All interfaces and utilities are now imported from separate files\n\nconst PageBuilder: React.FC = () => {\n  // Use custom hooks for state management\n  const state = usePageBuilderState();\n\n  // Debug mode removed - using working SQL-based implementation\n\n  // Initialize custom hooks\n  const cardManagement = useCardManagement({\n    categories: state.categories,\n    setCategories: state.setCategories,\n    selectedCard: state.selectedCard,\n    setSelectedCard: state.setSelectedCard,\n    newCardId: state.newCardId,\n    setNewCardId: state.setNewCardId,\n    newCardTitle: state.newCardTitle,\n    setNewCardTitle: state.setNewCardTitle,\n    actionType: state.actionType,\n    setActionType: state.setActionType,\n    setIsLoading: state.setIsLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setShowConfirmModal: state.setShowConfirmModal,\n    setIsAddingNewCard: state.setIsAddingNewCard,\n    setPageConfig: state.setPageConfig,\n    setFields: state.setFields,\n    setEditingCard: state.setEditingCard,\n    setShowEditModal: state.setShowEditModal,\n    setCardToDelete: state.setCardToDelete,\n    setShowDeleteConfirmModal: state.setShowDeleteConfirmModal,\n  });\n\n  const pageConfiguration = usePageConfiguration({\n    categories: state.categories,\n    selectedCard: state.selectedCard,\n    pageConfig: state.pageConfig,\n    setPageConfig: state.setPageConfig,\n    fields: state.fields,\n    setFields: state.setFields,\n    setAvailableDynamicFields: state.setAvailableDynamicFields,\n    setLoading: state.setLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setPreviewContent: state.setPreviewContent,\n    setIsPreviewOpen: state.setIsPreviewOpen,\n    selectedRegions: state.selectedRegions,\n    selectedDivisions: state.selectedDivisions,\n    selectedOffices: state.selectedOffices,\n    selectedFrequency: state.selectedFrequency,\n    setSelectedRegions: state.setSelectedRegions,\n    setSelectedDivisions: state.setSelectedDivisions,\n    setSelectedOffices: state.setSelectedOffices,\n    setSelectedFrequency: state.setSelectedFrequency,\n  });\n\n  // Initialize data on component mount\n  useEffect(() => {\n    cardManagement.fetchCategories();\n  }, []);\n\n  // Handle card and action changes\n  useEffect(() => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.actionType === 'createWebPage') {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n      pageConfiguration.fetchDynamicFormFields(state.selectedCard);\n    } else if (state.selectedCard && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && state.actionType === 'createWebPage') {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n    } else if (!state.selectedCard) {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n      state.setActionType('');\n    }\n\n    if (state.selectedCard && state.actionType !== 'createWebPage') {\n        state.setAvailableDynamicFields([]);\n    }\n  }, [state.selectedCard, state.categories, state.actionType]);\n\n  // Event handlers for UI interactions\n  const handleCardChange = (cardId: string) => {\n    state.setSelectedCard(cardId);\n    state.setActionType('');\n    if (!cardId) {\n        state.setPageConfig(null);\n        state.setFields([]);\n    } else {\n        const cardIsLeaf = isLeafCard(cardId, state.categories);\n        const cardIsMain = isMainCard(cardId, state.categories);\n        if(!cardIsLeaf || cardIsMain) {\n            state.setPageConfig(null);\n            state.setFields([]);\n        }\n    }\n  };\n\n  const handleActionChange = (action: string) => {\n    state.setActionType(action);\n  };\n\n  const handleCreateAction = () => {\n    state.setNewCardId('');\n    state.setNewCardTitle('');\n    state.setIsAddingNewCard(true);\n  };\n\n  const handleWebPageAction = () => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories)) {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n    } else if (state.selectedCard) {\n      state.setError('Web page can only be created/edited for a final nested report (not a main report).');\n      state.setPageConfig(null);\n      state.setFields([]);\n    }\n  };\n\n  // Event handlers for report configuration dropdowns - updated for arrays\n  const handleRegionsChange = (regions: string[]) => {\n    state.setSelectedRegions(regions);\n    // Reset dependent dropdowns when regions change\n    state.setSelectedDivisions([]);\n    state.setSelectedOffices([]);\n  };\n\n  const handleDivisionsChange = (divisions: string[]) => {\n    state.setSelectedDivisions(divisions);\n    // Reset dependent dropdown when divisions change\n    state.setSelectedOffices([]);\n  };\n\n  const handleOfficesChange = (offices: string[]) => {\n    state.setSelectedOffices(offices);\n  };\n\n  const handleFrequencyChange = (frequency: string) => {\n    state.setSelectedFrequency(frequency);\n  };\n\n  // All card management functions are now handled by the useCardManagement hook\n\n  // All page builder functions are now handled by the usePageConfiguration hook\n\n  // All rendering functions are now handled by separate components\n  \n  // All field rendering is now handled by the FieldConfigItem component\n\n  return (\n    <>\n      <div className=\"page-builder\">\n        {state.error && <div className=\"error-message\">{state.error}</div>}\n        {state.success && (\n          <div className=\"success-message\">\n            {state.success}\n          </div>\n        )}\n        <h2>Report & Page Builder</h2>\n\n        <CardSelector\n          categories={state.categories}\n          selectedCard={state.selectedCard}\n          onCardChange={handleCardChange}\n          actionType={state.actionType}\n          onActionChange={handleActionChange}\n          isLoading={state.isLoading}\n          onCreateAction={handleCreateAction}\n          onWebPageAction={handleWebPageAction}\n        />\n\n        {/* Modal for adding/creating new card */}\n        {state.isAddingNewCard && (\n          <Modal\n            isOpen={state.isAddingNewCard}\n            onClose={() => {\n              state.setIsAddingNewCard(false);\n              state.setActionType('');\n              state.setNewCardId('');\n              state.setNewCardTitle('');\n            }}\n            title={\n              state.actionType === 'addNewCardGlobal' ? \"Create New Main Report\" :\n              state.selectedCard && state.actionType === 'createNestedCard' ? `Add Nested Report under \"${state.categories.find(c => c.id === state.selectedCard)?.title}\"` :\n              \"Create New Report\"\n            }\n          >\n            <div className=\"new-card-form\">\n              <input\n                type=\"text\"\n                placeholder=\"Report ID (e.g., 'new-report-id')\"\n                value={state.newCardId}\n                onChange={(e) => state.setNewCardId(e.target.value.toLowerCase().replace(/\\s+/g, '-'))}\n                className=\"form-control mb-2\"\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Report Title\"\n                value={state.newCardTitle}\n                onChange={(e) => state.setNewCardTitle(e.target.value)}\n                className=\"form-control mb-2\"\n              />\n              <div className=\"form-buttons modal-buttons\">\n                <button\n                  onClick={cardManagement.handleConfirmCreate}\n                  disabled={state.isLoading || !state.newCardId || !state.newCardTitle}\n                  className=\"btn btn-primary\"\n                >\n                  {state.isLoading ? 'Creating...' : 'Confirm & Create Report'}\n                </button>\n                <button onClick={() => {\n                  state.setIsAddingNewCard(false);\n                  state.setActionType('');\n                  state.setNewCardId('');\n                  state.setNewCardTitle('');\n                }} className=\"btn btn-secondary\">\n                  Cancel\n                </button>\n              </div>\n            </div>\n          </Modal>\n        )}\n\n        {/* Conditional Rendering for Card Management OR Page Builder OR Warnings */}\n        {state.selectedCard && (\n          <>\n            {/* Card Management Section */}\n            {!(state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig) && (\n              <CardManagement\n                selectedCard={state.selectedCard}\n                categories={state.categories}\n                onEditCard={cardManagement.handleEditCard}\n                onDeleteCard={cardManagement.handleDeleteClick}\n              />\n            )}\n\n            {/* Report Configuration Dropdowns */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && (\n              <ReportConfiguration\n                selectedRegions={state.selectedRegions}\n                selectedDivisions={state.selectedDivisions}\n                selectedOffices={state.selectedOffices}\n                selectedFrequency={state.selectedFrequency}\n                onRegionsChange={handleRegionsChange}\n                onDivisionsChange={handleDivisionsChange}\n                onOfficesChange={handleOfficesChange}\n                onFrequencyChange={handleFrequencyChange}\n              />\n            )}\n\n            {/* Page Builder Content */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig && (\n              <PageBuilderContent\n                pageConfig={state.pageConfig}\n                fields={state.fields}\n                onAddField={pageConfiguration.addField}\n                onUpdateField={pageConfiguration.updateField}\n                onRemoveField={pageConfiguration.removeField}\n                onSave={pageConfiguration.handleSave}\n                onPreview={pageConfiguration.handlePreview}\n                loading={state.loading}\n              />\n            )}\n\n            {/* Warning Messages */}\n            {state.actionType === 'createWebPage' && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && (\n              <div className=\"warning-message mt-3 p-2 bg-warning text-dark rounded\">\n                Page configuration is only available for final nested reports (which are not main reports). Please select an appropriate nested report to configure its page, or create one.\n              </div>\n            )}\n            {state.actionType !== 'createWebPage' && !isLeafCard(state.selectedCard, state.categories) && (\n              <div className=\"info-message mt-3 p-2 bg-info text-dark rounded\">\n                This is a parent report. You can create nested reports under it or select an existing nested report to manage or configure its page.\n              </div>\n            )}\n          </>\n        )}\n\n        {!state.selectedCard && state.actionType === '' && (\n          <div className=\"info-message mt-3 p-3 bg-light border rounded\">\n            <p>Select a report from the dropdown to manage it or configure its web page (if applicable).</p>\n            <p>If no reports exist, or to create a new top-level report, choose \"Create New Main Report\" from the action dropdown after clearing any selection.</p>\n          </div>\n        )}\n\n        {/* Modals for Edit and Delete Confirmation */}\n        {state.showEditModal && state.editingCard && (\n          <Modal\n            isOpen={state.showEditModal}\n            onClose={() => {\n              state.setShowEditModal(false);\n              state.setNewCardTitle('');\n              state.setEditingCard(null);\n            }}\n            title={`Edit Report: ${state.editingCard.title}`}\n          >\n            <input\n              type=\"text\"\n              value={state.newCardTitle}\n              onChange={(e) => state.setNewCardTitle(e.target.value)}\n              placeholder=\"New Report Title\"\n              className=\"form-control mb-2\"\n            />\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleUpdateCard}\n                className=\"btn btn-primary\"\n                disabled={state.isLoading || !state.newCardTitle.trim()}\n              >\n                {state.isLoading ? 'Updating...' : 'Update Title'}\n              </button>\n              <button\n                onClick={() => {\n                  state.setShowEditModal(false);\n                  state.setNewCardTitle('');\n                  state.setEditingCard(null);\n                }}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {state.showDeleteConfirmModal && state.cardToDelete && (\n          <Modal\n            isOpen={state.showDeleteConfirmModal}\n            onClose={() => state.setShowDeleteConfirmModal(false)}\n            title=\"Confirm Deletion\"\n          >\n            <p>Are you sure you want to delete the report \"{state.categories.find(c => c.id === state.cardToDelete)?.title}\" and ALL its nested reports and associated page configurations? This action cannot be undone.</p>\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleConfirmDelete}\n                className=\"btn btn-danger\"\n                disabled={state.isLoading}\n              >\n                {state.isLoading ? 'Deleting...' : 'Confirm Delete'}\n              </button>\n              <button\n                onClick={() => state.setShowDeleteConfirmModal(false)}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {/* Preview Modal */}\n        <Modal\n          isOpen={state.isPreviewOpen}\n          onClose={() => state.setIsPreviewOpen(false)}\n          title=\"Page Preview\"\n        >\n          <div dangerouslySetInnerHTML={{ __html: state.previewContent }} />\n        </Modal>\n      </div>\n    </>\n  );\n};\n\nexport default PageBuilder;\n\n\n", "import { useState } from 'react';\nimport { Category, FormField, PageConfig } from '../types/PageBuilderTypes';\nimport { FormField as DynamicFormField } from '../../../shared/DynamicForm';\n\nexport const usePageBuilderState = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [selectedCard, setSelectedCard] = useState<string>('');\n  const [pageConfig, setPageConfig] = useState<PageConfig | null>(null);\n  const [fields, setFields] = useState<FormField[]>([]);\n  const [availableDynamicFields, setAvailableDynamicFields] = useState<DynamicFormField[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const [isAddingNewCard, setIsAddingNewCard] = useState<boolean>(false);\n  const [newCardId, setNewCardId] = useState<string>('');\n  const [newCardTitle, setNewCardTitle] = useState<string>('');\n  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);\n\n  const [editingCard, setEditingCard] = useState<Category | null>(null);\n  const [showEditModal, setShowEditModal] = useState<boolean>(false);\n\n  const [cardToDelete, setCardToDelete] = useState<string | null>(null);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<boolean>(false);\n\n  const [actionType, setActionType] = useState<string>('');\n\n  // State for Preview Modal\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false);\n  const [previewContent, setPreviewContent] = useState('');\n\n  // New dropdown states - updated to arrays for multiple selections\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\n  const [selectedDivisions, setSelectedDivisions] = useState<string[]>([]);\n  const [selectedOffices, setSelectedOffices] = useState<string[]>([]);\n  const [selectedFrequency, setSelectedFrequency] = useState<string>('');\n\n  return {\n    // State values\n    categories,\n    selectedCard,\n    pageConfig,\n    fields,\n    availableDynamicFields,\n    isLoading,\n    loading,\n    error,\n    success,\n    isAddingNewCard,\n    newCardId,\n    newCardTitle,\n    showConfirmModal,\n    editingCard,\n    showEditModal,\n    cardToDelete,\n    showDeleteConfirmModal,\n    actionType,\n    isPreviewOpen,\n    previewContent,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n\n    // State setters\n    setCategories,\n    setSelectedCard,\n    setPageConfig,\n    setFields,\n    setAvailableDynamicFields,\n    setIsLoading,\n    setLoading,\n    setError,\n    setSuccess,\n    setIsAddingNewCard,\n    setNewCardId,\n    setNewCardTitle,\n    setShowConfirmModal,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n    setActionType,\n    setIsPreviewOpen,\n    setPreviewContent,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  };\n};\n", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemUtilityClass(slot) {\n  return generateUtilityClass('MuiListItem', slot);\n}\nconst listItemClasses = generateUtilityClasses('MuiListItem', ['root', 'container', 'dense', 'alignItemsFlexStart', 'divider', 'gutters', 'padding', 'secondaryAction']);\nexport default listItemClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemButton', slot);\n}\nconst listItemButtonClasses = generateUtilityClasses('MuiListItemButton', ['root', 'focusVisible', 'dense', 'alignItemsFlexStart', 'disabled', 'divider', 'gutters', 'selected']);\nexport default listItemButtonClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemSecondaryActionClassesUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemSecondaryAction', slot);\n}\nconst listItemSecondaryActionClasses = generateUtilityClasses('MuiListItemSecondaryAction', ['root', 'disableGutters']);\nexport default listItemSecondaryActionClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemSecondaryActionClassesUtilityClass } from \"./listItemSecondaryActionClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disableGutters,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getListItemSecondaryActionClassesUtilityClass, classes);\n};\nconst ListItemSecondaryActionRoot = styled('div', {\n  name: 'MuiListItemSecondaryAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.disableGutters && styles.disableGutters];\n  }\n})({\n  position: 'absolute',\n  right: 16,\n  top: '50%',\n  transform: 'translateY(-50%)',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.disableGutters,\n    style: {\n      right: 0\n    }\n  }]\n});\n\n/**\n * Must be used as the last child of ListItem to function properly.\n *\n * @deprecated Use the `secondaryAction` prop in the `ListItem` component instead. This component will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n */\nconst ListItemSecondaryAction = /*#__PURE__*/React.forwardRef(function ListItemSecondaryAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemSecondaryAction'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const ownerState = {\n    ...props,\n    disableGutters: context.disableGutters\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemSecondaryActionRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemSecondaryAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or selection control.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nListItemSecondaryAction.muiName = 'ListItemSecondaryAction';\nexport default ListItemSecondaryAction;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport isMuiElement from \"../utils/isMuiElement.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemUtilityClass } from \"./listItemClasses.js\";\nimport { listItemButtonClasses } from \"../ListItemButton/index.js\";\nimport ListItemSecondaryAction from \"../ListItemSecondaryAction/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.dense && styles.dense, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart, ownerState.divider && styles.divider, !ownerState.disableGutters && styles.gutters, !ownerState.disablePadding && styles.padding, ownerState.hasSecondaryAction && styles.secondaryAction];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes,\n    dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  } = ownerState;\n  const slots = {\n    root: ['root', dense && 'dense', !disableGutters && 'gutters', !disablePadding && 'padding', divider && 'divider', alignItems === 'flex-start' && 'alignItemsFlexStart', hasSecondaryAction && 'secondaryAction'],\n    container: ['container']\n  };\n  return composeClasses(slots, getListItemUtilityClass, classes);\n};\nexport const ListItemRoot = styled('div', {\n  name: 'MuiListItem',\n  slot: 'Root',\n  overridesResolver\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'flex-start',\n  alignItems: 'center',\n  position: 'relative',\n  textDecoration: 'none',\n  width: '100%',\n  boxSizing: 'border-box',\n  textAlign: 'left',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding,\n    style: {\n      paddingTop: 8,\n      paddingBottom: 8\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && ownerState.dense,\n    style: {\n      paddingTop: 4,\n      paddingBottom: 4\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !ownerState.disableGutters,\n    style: {\n      paddingLeft: 16,\n      paddingRight: 16\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !!ownerState.secondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.secondaryAction,\n    style: {\n      [`& > .${listItemButtonClasses.root}`]: {\n        paddingRight: 48\n      }\n    }\n  }, {\n    props: {\n      alignItems: 'flex-start'\n    },\n    style: {\n      alignItems: 'flex-start'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.divider,\n    style: {\n      borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,\n      backgroundClip: 'padding-box'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.button,\n    style: {\n      transition: theme.transitions.create('background-color', {\n        duration: theme.transitions.duration.shortest\n      }),\n      '&:hover': {\n        textDecoration: 'none',\n        backgroundColor: (theme.vars || theme).palette.action.hover,\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: 'transparent'\n        }\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hasSecondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }]\n})));\nconst ListItemContainer = styled('li', {\n  name: 'MuiListItem',\n  slot: 'Container'\n})({\n  position: 'relative'\n});\n\n/**\n * Uses an additional container component if `ListItemSecondaryAction` is the last child.\n */\nconst ListItem = /*#__PURE__*/React.forwardRef(function ListItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItem'\n  });\n  const {\n    alignItems = 'center',\n    children: childrenProp,\n    className,\n    component: componentProp,\n    components = {},\n    componentsProps = {},\n    ContainerComponent = 'li',\n    ContainerProps: {\n      className: ContainerClassName,\n      ...ContainerProps\n    } = {},\n    dense = false,\n    disableGutters = false,\n    disablePadding = false,\n    divider = false,\n    secondaryAction,\n    slotProps = {},\n    slots = {},\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const childContext = React.useMemo(() => ({\n    dense: dense || context.dense || false,\n    alignItems,\n    disableGutters\n  }), [alignItems, context.dense, dense, disableGutters]);\n  const listItemRef = React.useRef(null);\n  const children = React.Children.toArray(childrenProp);\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  const hasSecondaryAction = children.length && isMuiElement(children[children.length - 1], ['ListItemSecondaryAction']);\n  const ownerState = {\n    ...props,\n    alignItems,\n    dense: childContext.dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleRef = useForkRef(listItemRef, ref);\n  const Root = slots.root || components.Root || ListItemRoot;\n  const rootProps = slotProps.root || componentsProps.root || {};\n  const componentProps = {\n    className: clsx(classes.root, rootProps.className, className),\n    ...other\n  };\n  let Component = componentProp || 'li';\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  if (hasSecondaryAction) {\n    // Use div by default.\n    Component = !componentProps.component && !componentProp ? 'div' : Component;\n\n    // Avoid nesting of li > li.\n    if (ContainerComponent === 'li') {\n      if (Component === 'li') {\n        Component = 'div';\n      } else if (componentProps.component === 'li') {\n        componentProps.component = 'div';\n      }\n    }\n    return /*#__PURE__*/_jsx(ListContext.Provider, {\n      value: childContext,\n      children: /*#__PURE__*/_jsxs(ListItemContainer, {\n        as: ContainerComponent,\n        className: clsx(classes.container, ContainerClassName),\n        ref: handleRef,\n        ownerState: ownerState,\n        ...ContainerProps,\n        children: [/*#__PURE__*/_jsx(Root, {\n          ...rootProps,\n          ...(!isHostComponent(Root) && {\n            as: Component,\n            ownerState: {\n              ...ownerState,\n              ...rootProps.ownerState\n            }\n          }),\n          ...componentProps,\n          children: children\n        }), children.pop()]\n      })\n    });\n  }\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsxs(Root, {\n      ...rootProps,\n      as: Component,\n      ref: handleRef,\n      ...(!isHostComponent(Root) && {\n        ownerState: {\n          ...ownerState,\n          ...rootProps.ownerState\n        }\n      }),\n      ...componentProps,\n      children: [children, secondaryAction && /*#__PURE__*/_jsx(ListItemSecondaryAction, {\n        children: secondaryAction\n      })]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Defines the `align-items` style property.\n   * @default 'center'\n   */\n  alignItems: PropTypes.oneOf(['center', 'flex-start']),\n  /**\n   * The content of the component if a `ListItemSecondaryAction` is used it must\n   * be the last child.\n   */\n  children: chainPropTypes(PropTypes.node, props => {\n    const children = React.Children.toArray(props.children);\n\n    // React.Children.toArray(props.children).findLastIndex(isListItemSecondaryAction)\n    let secondaryActionIndex = -1;\n    for (let i = children.length - 1; i >= 0; i -= 1) {\n      const child = children[i];\n      if (isMuiElement(child, ['ListItemSecondaryAction'])) {\n        secondaryActionIndex = i;\n        break;\n      }\n    }\n\n    //  is ListItemSecondaryAction the last child of ListItem\n    if (secondaryActionIndex !== -1 && secondaryActionIndex !== children.length - 1) {\n      return new Error('MUI: You used an element after ListItemSecondaryAction. ' + 'For ListItem to detect that it has a secondary action ' + 'you must pass it as the last child to ListItem.');\n    }\n    return null;\n  }),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The container component used when a `ListItemSecondaryAction` is the last child.\n   * @default 'li'\n   * @deprecated Use the `component` or `slots.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerComponent: elementTypeAcceptingRef,\n  /**\n   * Props applied to the container component if used.\n   * @default {}\n   * @deprecated Use the `slotProps.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerProps: PropTypes.object,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used.\n   * The prop defaults to the value inherited from the parent List component.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * If `true`, all padding is removed.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * If `true`, a 1px light border is added to the bottom of the list item.\n   * @default false\n   */\n  divider: PropTypes.bool,\n  /**\n   * The element to display at the end of ListItem.\n   */\n  secondaryAction: PropTypes.node,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItem;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography, { typographyClasses } from \"../Typography/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport listItemTextClasses, { getListItemTextUtilityClass } from \"./listItemTextClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    inset,\n    primary,\n    secondary,\n    dense\n  } = ownerState;\n  const slots = {\n    root: ['root', inset && 'inset', dense && 'dense', primary && secondary && 'multiline'],\n    primary: ['primary'],\n    secondary: ['secondary']\n  };\n  return composeClasses(slots, getListItemTextUtilityClass, classes);\n};\nconst ListItemTextRoot = styled('div', {\n  name: 'MuiListItemText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${listItemTextClasses.primary}`]: styles.primary\n    }, {\n      [`& .${listItemTextClasses.secondary}`]: styles.secondary\n    }, styles.root, ownerState.inset && styles.inset, ownerState.primary && ownerState.secondary && styles.multiline, ownerState.dense && styles.dense];\n  }\n})({\n  flex: '1 1 auto',\n  minWidth: 0,\n  marginTop: 4,\n  marginBottom: 4,\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.primary})`]: {\n    display: 'block'\n  },\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.secondary})`]: {\n    display: 'block'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.primary && ownerState.secondary,\n    style: {\n      marginTop: 6,\n      marginBottom: 6\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.inset,\n    style: {\n      paddingLeft: 56\n    }\n  }]\n});\nconst ListItemText = /*#__PURE__*/React.forwardRef(function ListItemText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemText'\n  });\n  const {\n    children,\n    className,\n    disableTypography = false,\n    inset = false,\n    primary: primaryProp,\n    primaryTypographyProps,\n    secondary: secondaryProp,\n    secondaryTypographyProps,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const {\n    dense\n  } = React.useContext(ListContext);\n  let primary = primaryProp != null ? primaryProp : children;\n  let secondary = secondaryProp;\n  const ownerState = {\n    ...props,\n    disableTypography,\n    inset,\n    primary: !!primary,\n    secondary: !!secondary,\n    dense\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      primary: primaryTypographyProps,\n      secondary: secondaryTypographyProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: ListItemTextRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    ref\n  });\n  const [PrimarySlot, primarySlotProps] = useSlot('primary', {\n    className: classes.primary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SecondarySlot, secondarySlotProps] = useSlot('secondary', {\n    className: classes.secondary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  if (primary != null && primary.type !== Typography && !disableTypography) {\n    primary = /*#__PURE__*/_jsx(PrimarySlot, {\n      variant: dense ? 'body2' : 'body1',\n      component: primarySlotProps?.variant ? undefined : 'span',\n      ...primarySlotProps,\n      children: primary\n    });\n  }\n  if (secondary != null && secondary.type !== Typography && !disableTypography) {\n    secondary = /*#__PURE__*/_jsx(SecondarySlot, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      ...secondarySlotProps,\n      children: secondary\n    });\n  }\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [primary, secondary]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Alias for the `primary` prop.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the children won't be wrapped by a Typography component.\n   * This can be useful to render an alternative Typography variant by wrapping\n   * the `children` (or `primary`) text, and optional `secondary` text\n   * with the Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * If `true`, the children are indented.\n   * This should be used if there is no left avatar or left icon.\n   * @default false\n   */\n  inset: PropTypes.bool,\n  /**\n   * The main content element.\n   */\n  primary: PropTypes.node,\n  /**\n   * These props will be forwarded to the primary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.primary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  primaryTypographyProps: PropTypes.object,\n  /**\n   * The secondary content element.\n   */\n  secondary: PropTypes.node,\n  /**\n   * These props will be forwarded to the secondary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.secondary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  secondaryTypographyProps: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    primary: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    secondary: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    primary: PropTypes.elementType,\n    root: PropTypes.elementType,\n    secondary: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemText;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDividerUtilityClass } from \"./dividerClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    absolute,\n    children,\n    classes,\n    flexItem,\n    light,\n    orientation,\n    textAlign,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', absolute && 'absolute', variant, light && 'light', orientation === 'vertical' && 'vertical', flexItem && 'flexItem', children && 'withChildren', children && orientation === 'vertical' && 'withChildrenVertical', textAlign === 'right' && orientation !== 'vertical' && 'textAlignRight', textAlign === 'left' && orientation !== 'vertical' && 'textAlignLeft'],\n    wrapper: ['wrapper', orientation === 'vertical' && 'wrapperVertical']\n  };\n  return composeClasses(slots, getDividerUtilityClass, classes);\n};\nconst DividerRoot = styled('div', {\n  name: 'MuiDivider',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.absolute && styles.absolute, styles[ownerState.variant], ownerState.light && styles.light, ownerState.orientation === 'vertical' && styles.vertical, ownerState.flexItem && styles.flexItem, ownerState.children && styles.withChildren, ownerState.children && ownerState.orientation === 'vertical' && styles.withChildrenVertical, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && styles.textAlignRight, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && styles.textAlignLeft];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 0,\n  // Reset browser default style.\n  flexShrink: 0,\n  borderWidth: 0,\n  borderStyle: 'solid',\n  borderColor: (theme.vars || theme).palette.divider,\n  borderBottomWidth: 'thin',\n  variants: [{\n    props: {\n      absolute: true\n    },\n    style: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      width: '100%'\n    }\n  }, {\n    props: {\n      light: true\n    },\n    style: {\n      borderColor: theme.vars ? `rgba(${theme.vars.palette.dividerChannel} / 0.08)` : alpha(theme.palette.divider, 0.08)\n    }\n  }, {\n    props: {\n      variant: 'inset'\n    },\n    style: {\n      marginLeft: 72\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'horizontal'\n    },\n    style: {\n      marginLeft: theme.spacing(2),\n      marginRight: theme.spacing(2)\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'vertical'\n    },\n    style: {\n      marginTop: theme.spacing(1),\n      marginBottom: theme.spacing(1)\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      height: '100%',\n      borderBottomWidth: 0,\n      borderRightWidth: 'thin'\n    }\n  }, {\n    props: {\n      flexItem: true\n    },\n    style: {\n      alignSelf: 'stretch',\n      height: 'auto'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.children,\n    style: {\n      display: 'flex',\n      textAlign: 'center',\n      border: 0,\n      borderTopStyle: 'solid',\n      borderLeftStyle: 'solid',\n      '&::before, &::after': {\n        content: '\"\"',\n        alignSelf: 'center'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.children && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before, &::after': {\n        width: '100%',\n        borderTop: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderTopStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.orientation === 'vertical' && ownerState.children,\n    style: {\n      flexDirection: 'column',\n      '&::before, &::after': {\n        height: '100%',\n        borderLeft: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderLeftStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '90%'\n      },\n      '&::after': {\n        width: '10%'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '10%'\n      },\n      '&::after': {\n        width: '90%'\n      }\n    }\n  }]\n})));\nconst DividerWrapper = styled('span', {\n  name: 'MuiDivider',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.wrapper, ownerState.orientation === 'vertical' && styles.wrapperVertical];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-block',\n  paddingLeft: `calc(${theme.spacing(1)} * 1.2)`,\n  paddingRight: `calc(${theme.spacing(1)} * 1.2)`,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      paddingTop: `calc(${theme.spacing(1)} * 1.2)`,\n      paddingBottom: `calc(${theme.spacing(1)} * 1.2)`\n    }\n  }]\n})));\nconst Divider = /*#__PURE__*/React.forwardRef(function Divider(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDivider'\n  });\n  const {\n    absolute = false,\n    children,\n    className,\n    orientation = 'horizontal',\n    component = children || orientation === 'vertical' ? 'div' : 'hr',\n    flexItem = false,\n    light = false,\n    role = component !== 'hr' ? 'separator' : undefined,\n    textAlign = 'center',\n    variant = 'fullWidth',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    absolute,\n    component,\n    flexItem,\n    light,\n    orientation,\n    role,\n    textAlign,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DividerRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    role: role,\n    ref: ref,\n    ownerState: ownerState,\n    \"aria-orientation\": role === 'separator' && (component !== 'hr' || orientation === 'vertical') ? orientation : undefined,\n    ...other,\n    children: children ? /*#__PURE__*/_jsx(DividerWrapper, {\n      className: classes.wrapper,\n      ownerState: ownerState,\n      children: children\n    }) : null\n  });\n});\n\n/**\n * The following flag is used to ensure that this component isn't tabbable i.e.\n * does not get highlight/focus inside of MUI List.\n */\nif (Divider) {\n  Divider.muiSkipListHighlight = true;\n}\nprocess.env.NODE_ENV !== \"production\" ? Divider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Absolutely position the element.\n   * @default false\n   */\n  absolute: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, a vertical divider will have the correct height when used in flex container.\n   * (By default, a vertical divider will have a calculated height of `0px` if it is the child of a flex container.)\n   * @default false\n   */\n  flexItem: PropTypes.bool,\n  /**\n   * If `true`, the divider will have a lighter color.\n   * @default false\n   * @deprecated Use <Divider sx={{ opacity: 0.6 }} /> (or any opacity or color) instead. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  light: PropTypes.bool,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The text alignment.\n   * @default 'center'\n   */\n  textAlign: PropTypes.oneOf(['center', 'left', 'right']),\n  /**\n   * The variant to use.\n   * @default 'fullWidth'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['fullWidth', 'inset', 'middle']), PropTypes.string])\n} : void 0;\nexport default Divider;", "import { useState, useEffect } from 'react';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataEnhancedReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n  totalRecords: number;\n  approach: string;\n}\n\n/**\n * Enhanced office data hook with comprehensive pagination\n * Mirrors the successful Flutter implementation to overcome 1000-record limit\n */\nexport const useOfficeDataEnhanced = (): UseOfficeDataEnhancedReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [totalRecords, setTotalRecords] = useState<number>(0);\n  const [approach, setApproach] = useState<string>('');\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataEnhanced: Starting comprehensive office data fetch...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allOfficeData = await OfficeService.fetchAllOfficeData();\n      \n      console.log('✅ useOfficeDataEnhanced: Fetched office records:', allOfficeData.length, 'records');\n      setTotalRecords(allOfficeData.length);\n\n      if (allOfficeData.length === 0) {\n        console.log('⚠️ useOfficeDataEnhanced: No office records found');\n        setRegions([]);\n        setDivisions([]);\n        setOffices([]);\n        setApproach('no-data');\n        return;\n      }\n\n      // Process regions - get unique regions\n      const uniqueRegions = new Set<string>();\n      allOfficeData.forEach(office => {\n        if (office.Region && office.Region.trim()) {\n          uniqueRegions.add(office.Region.trim());\n        }\n      });\n\n      const regionsArray: Region[] = Array.from(uniqueRegions)\n        .sort()\n        .map(regionName => ({\n          id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed regions:', regionsArray.length);\n\n      // Process divisions - get unique divisions with their regions\n      const uniqueDivisions = new Map<string, string>();\n      allOfficeData.forEach(office => {\n        if (office.Division && office.Division.trim() && office.Region && office.Region.trim()) {\n          uniqueDivisions.set(office.Division.trim(), office.Region.trim());\n        }\n      });\n\n      const divisionsArray: Division[] = Array.from(uniqueDivisions.entries())\n        .sort(([a], [b]) => a.localeCompare(b))\n        .map(([divisionName, regionName]) => ({\n          id: divisionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: divisionName,\n          region: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed divisions:', divisionsArray.length);\n\n      // Process offices - use office name as ID for consistency\n      const officesArray: Office[] = allOfficeData\n        .filter(office => office['Office name'] && office['Office name'].trim())\n        .map(office => ({\n          id: office['Office name'], // Use office name as ID for form targeting\n          name: office['Office name'],\n          region: office.Region || '',\n          division: office.Division || '',\n          facilityId: office['Office name'], // Keep for reference\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed offices:', officesArray.length);\n\n      // Log comprehensive statistics\n      logOfficeStatistics(allOfficeData, regionsArray, divisionsArray, officesArray);\n\n      // Set the processed data\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n      setApproach('enhanced-pagination');\n\n      console.log('✅ useOfficeDataEnhanced: Data processing complete');\n\n    } catch (err) {\n      console.error('❌ useOfficeDataEnhanced: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n      setTotalRecords(0);\n      setApproach('error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n    totalRecords,\n    approach,\n  };\n};\n\n/**\n * Log comprehensive statistics about the processed office data\n */\nfunction logOfficeStatistics(\n  allOfficeData: any[],\n  regions: Region[],\n  divisions: Division[],\n  offices: Office[]\n): void {\n  console.log('📊 useOfficeDataEnhanced: === COMPREHENSIVE STATISTICS ===');\n  console.log(`📊 useOfficeDataEnhanced: Raw records: ${allOfficeData.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed regions: ${regions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed divisions: ${divisions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed offices: ${offices.length}`);\n\n  if (offices.length > 0) {\n    // Alphabetical range\n    const sortedNames = offices.map(o => o.name).sort();\n    console.log(`📊 useOfficeDataEnhanced: Office range - First: \"${sortedNames[0]}\"`);\n    console.log(`📊 useOfficeDataEnhanced: Office range - Last: \"${sortedNames[sortedNames.length - 1]}\"`);\n\n    // Letter distribution\n    const letterCounts: { [key: string]: number } = {};\n    offices.forEach(office => {\n      const firstLetter = office.name.charAt(0).toUpperCase();\n      letterCounts[firstLetter] = (letterCounts[firstLetter] || 0) + 1;\n    });\n\n    console.log('📊 useOfficeDataEnhanced: Letter distribution:');\n    Object.keys(letterCounts).sort().forEach(letter => {\n      console.log(`📊 useOfficeDataEnhanced: ${letter}: ${letterCounts[letter]} offices`);\n    });\n\n    // Check for specific offices\n    const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n    const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n    \n    console.log(`📊 useOfficeDataEnhanced: Contains \"Tirupur division\": ${!!tirupurDivision}`);\n    console.log(`📊 useOfficeDataEnhanced: Contains \"Coimbatore division\": ${!!coimbatoreDivision}`);\n\n    if (tirupurDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Tirupur division: \"${tirupurDivision.name}\"`);\n    }\n    if (coimbatoreDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Coimbatore division: \"${coimbatoreDivision.name}\"`);\n    }\n\n    // Region breakdown\n    if (regions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Regions found:');\n      regions.forEach(region => {\n        const regionOffices = offices.filter(o => o.region === region.name);\n        console.log(`📊 useOfficeDataEnhanced: ${region.name}: ${regionOffices.length} offices`);\n      });\n    }\n\n    // Division breakdown\n    if (divisions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Top 10 divisions by office count:');\n      const divisionCounts = divisions.map(division => ({\n        name: division.name,\n        count: offices.filter(o => o.division === division.name).length\n      })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n      divisionCounts.forEach(division => {\n        console.log(`📊 useOfficeDataEnhanced: ${division.name}: ${division.count} offices`);\n      });\n    }\n  }\n\n  console.log('📊 useOfficeDataEnhanced: === END STATISTICS ===');\n}\n\nexport default useOfficeDataEnhanced;\n", "import React from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n  Chip,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport { useOfficeDataEnhanced } from './hooks/useOfficeDataEnhanced';\n\n/**\n * Test component to verify enhanced office loading functionality\n * This component displays comprehensive statistics about the loaded office data\n */\nconst OfficeLoadingTest: React.FC = () => {\n  const {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    totalRecords,\n    approach,\n    refetch\n  } = useOfficeDataEnhanced();\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" flexDirection=\"column\" alignItems=\"center\" p={4}>\n        <CircularProgress size={60} />\n        <Typography variant=\"h6\" sx={{ mt: 2 }}>\n          Loading office data with comprehensive pagination...\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n          This may take a moment as we fetch ALL records from the database\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={4}>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n        <Typography variant=\"body2\">\n          Failed to load office data. Please check the console for detailed error information.\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Calculate statistics\n  const letterDistribution: { [key: string]: number } = {};\n  offices.forEach(office => {\n    const firstLetter = office.name.charAt(0).toUpperCase();\n    letterDistribution[firstLetter] = (letterDistribution[firstLetter] || 0) + 1;\n  });\n\n  const sortedOfficeNames = offices.map(o => o.name).sort();\n  const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n  const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n\n  // Top regions by office count\n  const regionCounts = regions.map(region => ({\n    name: region.name,\n    count: offices.filter(o => o.region === region.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 5);\n\n  // Top divisions by office count\n  const divisionCounts = divisions.map(division => ({\n    name: division.name,\n    count: offices.filter(o => o.division === division.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n  return (\n    <Box p={4}>\n      <Typography variant=\"h4\" gutterBottom>\n        Office Loading Test - Enhanced Pagination\n      </Typography>\n      \n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\n        This test verifies that the enhanced office loading system can fetch ALL records from the Supabase database,\n        overcoming the default 1000-record pagination limit.\n      </Typography>\n\n      {/* Summary Cards */}\n      <Box display=\"flex\" gap={3} sx={{ mb: 4, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Total Records\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalRecords.toLocaleString()}\n              </Typography>\n              <Chip\n                label={approach}\n                size=\"small\"\n                color={totalRecords > 1000 ? \"success\" : \"warning\"}\n                sx={{ mt: 1 }}\n              />\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Regions\n              </Typography>\n              <Typography variant=\"h4\">\n                {regions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Divisions\n              </Typography>\n              <Typography variant=\"h4\">\n                {divisions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Offices\n              </Typography>\n              <Typography variant=\"h4\">\n                {offices.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n      </Box>\n\n      {/* Verification Results */}\n      <Box display=\"flex\" gap={3} sx={{ flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Verification Results\n            </Typography>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Records exceed 1000 limit:\n              </Typography>\n              <Chip\n                label={totalRecords > 1000 ? \"✅ YES\" : \"❌ NO\"}\n                color={totalRecords > 1000 ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Alphabetical Range:\n              </Typography>\n              <Typography variant=\"body2\">\n                First: \"{sortedOfficeNames[0] || 'N/A'}\"\n              </Typography>\n              <Typography variant=\"body2\">\n                Last: \"{sortedOfficeNames[sortedOfficeNames.length - 1] || 'N/A'}\"\n              </Typography>\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Tirupur Division Found:\n              </Typography>\n              <Chip\n                label={tirupurDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={tirupurDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {tirupurDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{tirupurDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Coimbatore Division Found:\n              </Typography>\n              <Chip\n                label={coimbatoreDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={coimbatoreDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {coimbatoreDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{coimbatoreDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Letter Distribution\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {Object.keys(letterDistribution).sort().map(letter => (\n                <Box key={letter} display=\"flex\" justifyContent=\"space-between\" sx={{ mb: 1 }}>\n                  <Typography variant=\"body2\">{letter}:</Typography>\n                  <Typography variant=\"body2\">{letterDistribution[letter]} offices</Typography>\n                </Box>\n              ))}\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      <Box display=\"flex\" gap={3} sx={{ mt: 3, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 5 Regions by Office Count\n            </Typography>\n            <List dense>\n              {regionCounts.map((region, index) => (\n                <React.Fragment key={region.name}>\n                  <ListItem>\n                    <ListItemText\n                      primary={region.name}\n                      secondary={`${region.count} offices`}\n                    />\n                  </ListItem>\n                  {index < regionCounts.length - 1 && <Divider />}\n                </React.Fragment>\n              ))}\n            </List>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 10 Divisions by Office Count\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              <List dense>\n                {divisionCounts.map((division, index) => (\n                  <React.Fragment key={division.name}>\n                    <ListItem>\n                      <ListItemText\n                        primary={division.name}\n                        secondary={`${division.count} offices`}\n                      />\n                    </ListItem>\n                    {index < divisionCounts.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      {/* Success Message */}\n      {totalRecords > 1000 && (\n        <Alert severity=\"success\" sx={{ mt: 3 }}>\n          <Typography variant=\"h6\">\n            🎉 Success! Enhanced Office Loading is Working\n          </Typography>\n          <Typography variant=\"body2\">\n            The system successfully loaded {totalRecords.toLocaleString()} office records, \n            which exceeds the default 1000-record Supabase limit. This confirms that the \n            comprehensive pagination solution is working correctly.\n          </Typography>\n        </Alert>\n      )}\n    </Box>\n  );\n};\n\nexport default OfficeLoadingTest;\n", "import React, { useState } from 'react';\nimport { collection, getDocs, doc, updateDoc, deleteDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { FaTruck, FaTools } from 'react-icons/fa';\n\nconst FixUndefinedReport: React.FC = () => {\n  const [isFixing, setIsFixing] = useState(false);\n  const [success, setSuccess] = useState('');\n  const [error, setError] = useState('');\n  const [foundIssues, setFoundIssues] = useState<any[]>([]);\n\n  const scanForUndefinedReports = async () => {\n    setIsFixing(true);\n    setError('');\n    setSuccess('');\n    setFoundIssues([]);\n\n    try {\n      // Check Firebase 'pages' collection\n      const pagesSnapshot = await getDocs(collection(db, 'pages'));\n      const issues: any[] = [];\n\n      pagesSnapshot.docs.forEach(doc => {\n        const data = doc.data();\n        const title = data.title;\n        \n        if (!title || title === 'undefined' || title.trim() === '') {\n          issues.push({\n            collection: 'pages',\n            id: doc.id,\n            data: data,\n            issue: !title ? 'Missing title' : title === 'undefined' ? 'Title is \"undefined\"' : 'Empty title'\n          });\n        }\n      });\n\n      setFoundIssues(issues);\n      \n      if (issues.length === 0) {\n        setSuccess('✅ No undefined reports found! Your database is clean.');\n      } else {\n        setSuccess(`🔍 Found ${issues.length} report(s) with title issues. Click \"Fix Issues\" to resolve them.`);\n      }\n\n    } catch (err) {\n      console.error('Error scanning for undefined reports:', err);\n      setError('❌ Failed to scan for undefined reports. Please try again.');\n    } finally {\n      setIsFixing(false);\n    }\n  };\n\n  const fixUndefinedReports = async () => {\n    setIsFixing(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      let fixedCount = 0;\n      let deletedCount = 0;\n\n      for (const issue of foundIssues) {\n        const docRef = doc(db, issue.collection, issue.id);\n        \n        // If the document has some meaningful data, rename it to MMU\n        if (issue.data.id || issue.data.parentId !== undefined) {\n          await updateDoc(docRef, {\n            title: 'MMU',\n            lastUpdated: new Date(),\n            description: 'Mail Motor Unit - Vehicle management and logistics'\n          });\n          fixedCount++;\n        } else {\n          // If it's completely empty/meaningless, delete it\n          await deleteDoc(docRef);\n          deletedCount++;\n        }\n      }\n\n      setFoundIssues([]);\n      setSuccess(`✅ Successfully fixed ${fixedCount} report(s) and removed ${deletedCount} empty report(s). The \"undefined\" report is now renamed to \"MMU\"!`);\n      \n      // Refresh the page after 2 seconds to show the changes\n      setTimeout(() => {\n        window.location.reload();\n      }, 2000);\n\n    } catch (err) {\n      console.error('Error fixing undefined reports:', err);\n      setError('❌ Failed to fix undefined reports. Please try again.');\n    } finally {\n      setIsFixing(false);\n    }\n  };\n\n  return (\n    <div style={{\n      padding: '20px',\n      margin: '20px 0',\n      border: '2px solid #ffc107',\n      borderRadius: '8px',\n      backgroundColor: '#fff3cd'\n    }}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>\n        {React.createElement(FaTools as React.ComponentType<any>, { size: 24, color: '#856404' })}\n        <h3 style={{ margin: 0, color: '#856404' }}>Fix \"undefined\" Report Issue</h3>\n      </div>\n      \n      <p style={{ marginBottom: '15px', color: '#856404' }}>\n        If you're seeing \"undefined\" in the report dropdown, this tool will help you fix it by renaming it to \"MMU\" or removing invalid entries.\n      </p>\n\n      {error && (\n        <div style={{\n          padding: '10px',\n          marginBottom: '15px',\n          backgroundColor: '#f8d7da',\n          color: '#721c24',\n          border: '1px solid #f5c6cb',\n          borderRadius: '4px'\n        }}>\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          padding: '10px',\n          marginBottom: '15px',\n          backgroundColor: '#d4edda',\n          color: '#155724',\n          border: '1px solid #c3e6cb',\n          borderRadius: '4px'\n        }}>\n          {success}\n        </div>\n      )}\n\n      {foundIssues.length > 0 && (\n        <div style={{\n          padding: '15px',\n          marginBottom: '15px',\n          backgroundColor: '#f8f9fa',\n          border: '1px solid #dee2e6',\n          borderRadius: '4px'\n        }}>\n          <h4 style={{ margin: '0 0 10px 0', color: '#495057' }}>Issues Found:</h4>\n          {foundIssues.map((issue, index) => (\n            <div key={index} style={{ marginBottom: '8px', fontSize: '14px' }}>\n              <strong>ID:</strong> {issue.id} - <strong>Issue:</strong> {issue.issue}\n            </div>\n          ))}\n        </div>\n      )}\n\n      <div style={{ display: 'flex', gap: '10px' }}>\n        <button\n          onClick={scanForUndefinedReports}\n          disabled={isFixing}\n          style={{\n            padding: '12px 24px',\n            backgroundColor: isFixing ? '#6c757d' : '#ffc107',\n            color: isFixing ? 'white' : '#212529',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: isFixing ? 'not-allowed' : 'pointer',\n            fontSize: '16px',\n            fontWeight: 'bold',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          }}\n        >\n          {React.createElement(FaTools as React.ComponentType<any>, { size: 16 })}\n          {isFixing ? 'Scanning...' : 'Scan for Issues'}\n        </button>\n\n        {foundIssues.length > 0 && (\n          <button\n            onClick={fixUndefinedReports}\n            disabled={isFixing}\n            style={{\n              padding: '12px 24px',\n              backgroundColor: isFixing ? '#6c757d' : '#28a745',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: isFixing ? 'not-allowed' : 'pointer',\n              fontSize: '16px',\n              fontWeight: 'bold',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '8px'\n            }}\n          >\n            {React.createElement(FaTruck as React.ComponentType<any>, { size: 16 })}\n            {isFixing ? 'Fixing...' : 'Fix Issues (Rename to MMU)'}\n          </button>\n        )}\n      </div>\n\n      <div style={{ marginTop: '15px', fontSize: '14px', color: '#6c757d' }}>\n        <strong>What this will do:</strong>\n        <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>\n          <li>Scan Firebase for reports with undefined/empty titles</li>\n          <li>Rename meaningful undefined reports to \"MMU\"</li>\n          <li>Remove completely empty/invalid reports</li>\n          <li>Clean up your admin panel dropdown</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default FixUndefinedReport;\n", "import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Sidebar from '../shared/Sidebar';\nimport StatsCards from '../shared/StatsCards';\nimport PageBuilder from './business/PageBuilder';\nimport OfficeLoadingTest from './business/OfficeLoadingTest';\nimport FixUndefinedReport from './FixUndefinedReport';\n\nconst AdminPage: React.FC = () => {\n  const { currentUser } = useAuth();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState<any>(null);\n  const [showOfficeTest, setShowOfficeTest] = useState<boolean>(false);\n\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        const userRef = doc(db, 'employees', currentUser.uid);\n        const userSnap = await getDoc(userRef);\n        if (userSnap.exists()) {\n          setUserData(userSnap.data());\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n      <div className=\"main-content\">\n        <div className=\"page-title\">\n          Admin Dashboard\n          <button\n            onClick={() => setShowOfficeTest(!showOfficeTest)}\n            style={{\n              marginLeft: '20px',\n              padding: '8px 16px',\n              backgroundColor: showOfficeTest ? '#dc3545' : '#007bff',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {showOfficeTest ? 'Hide Office Test' : 'Show Office Loading Test'}\n          </button>\n        </div>\n        <StatsCards />\n        <FixUndefinedReport />\n        {showOfficeTest ? <OfficeLoadingTest /> : <PageBuilder />}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPage;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root']);\nexport default cardClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getCardUtilityClass } from \"./cardClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardUtilityClass, classes);\n};\nconst CardRoot = styled(Paper, {\n  name: 'MuiCard',\n  slot: 'Root'\n})({\n  overflow: 'hidden'\n});\nconst Card = /*#__PURE__*/React.forwardRef(function Card(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCard'\n  });\n  const {\n    className,\n    raised = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    raised\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardRoot, {\n    className: clsx(classes.root, className),\n    elevation: raised ? 8 : undefined,\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Card.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the card will use raised styling.\n   * @default false\n   */\n  raised: chainPropTypes(PropTypes.bool, props => {\n    if (props.raised && props.variant === 'outlined') {\n      return new Error('MUI: Combining `raised={true}` with `variant=\"outlined\"` has no effect.');\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Card;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemTextUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemText', slot);\n}\nconst listItemTextClasses = generateUtilityClasses('MuiListItemText', ['root', 'multiline', 'dense', 'inset', 'primary', 'secondary']);\nexport default listItemTextClasses;", "import React from 'react';\nimport './StatsCards.css';\n\nconst stats = [\n  { title: 'SB Accounts', value: 123456 },\n  { title: 'BD Revenue', value: '₹24,343' },\n  { title: 'No. Aadhaar Trans', value: 1259 },\n  { title: 'PLI', value: '₹99,99,999' }\n];\n\nconst StatsCards: React.FC = () => {\n  return (\n    <div className=\"stats-grid\">\n      {stats.map((stat, index) => (\n        <div className={`stat-card card-${index}`} key={index}>\n          <h3>{stat.title}</h3>\n          <p className=\"stat-value\">{stat.value}</p>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default StatsCards;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDividerUtilityClass(slot) {\n  return generateUtilityClass('MuiDivider', slot);\n}\nconst dividerClasses = generateUtilityClasses('MuiDivider', ['root', 'absolute', 'fullWidth', 'inset', 'middle', 'flexItem', 'light', 'vertical', 'withChildren', 'withChildrenVertical', 'textAlignRight', 'textAlignLeft', 'wrapper', 'wrapperVertical']);\nexport default dividerClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardContentUtilityClass(slot) {\n  return generateUtilityClass('MuiCardContent', slot);\n}\nconst cardContentClasses = generateUtilityClasses('MuiCardContent', ['root']);\nexport default cardContentClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getCardContentUtilityClass } from \"./cardContentClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardContentUtilityClass, classes);\n};\nconst CardContentRoot = styled('div', {\n  name: 'MuiCardContent',\n  slot: 'Root'\n})({\n  padding: 16,\n  '&:last-child': {\n    paddingBottom: 24\n  }\n});\nconst CardContent = /*#__PURE__*/React.forwardRef(function CardContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardContent'\n  });\n  const {\n    className,\n    component = 'div',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardContentRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardContent;"], "names": ["_ref", "isOpen", "onClose", "title", "children", "_jsx", "className", "onClick", "_jsxs", "e", "stopPropagation", "isMainCard", "cardId", "allCategories", "card", "find", "c", "id", "parentId", "isLeafCard", "some", "organizeCards", "list", "map", "roots", "for<PERSON>ach", "item", "_map$item$parentId$ch", "push", "getAllDescendantIds", "descendants", "filter", "child", "concat", "useCardManagement", "props", "categories", "setCategories", "selected<PERSON><PERSON>", "setSelectedCard", "newCardId", "setNewCardId", "newCardTitle", "setNewCardTitle", "actionType", "setActionType", "setIsLoading", "setError", "setSuccess", "setShowConfirmModal", "setIsAddingNewCard", "setPageConfig", "setFields", "setEditingCard", "setShowEditModal", "setCardToDelete", "setShowDeleteConfirmModal", "fetchCategories", "useCallback", "async", "fetchedCategories", "getDocs", "collection", "db", "docs", "doc", "data", "err", "console", "error", "handleAddNewCard", "doc<PERSON>ef", "getDoc", "exists", "checkDuplicateId", "handleConfirmCreate", "_categories$find", "parentIdToSet", "newPath", "path", "replace", "cardRef", "icon", "generatedIcon", "color", "generatedColor", "hash", "split", "reduce", "acc", "char", "charCodeAt", "icons", "FaFolder", "FaFileAlt", "FaCog", "FaFolderOpen", "colors", "length", "generateCardStyle", "setDoc", "lastUpdated", "Date", "toISOString", "name", "fields", "isPage", "pageId", "setTimeout", "handleEditCard", "handleUpdateCard", "editingCard", "updateDoc", "handleDeleteClick", "handleConfirmDelete", "batch", "writeBatch", "allDescendants", "idsToDelete", "delete", "commit", "onCardChange", "onActionChange", "isLoading", "onCreateAction", "onWebPageAction", "renderCardOptions", "cards", "level", "arguments", "undefined", "flatMap", "displayTitle", "value", "style", "paddingLeft", "repeat", "onChange", "newSelectedCard", "target", "disabled", "newAction", "_Fragment", "onEditCard", "onDeleteCard", "selectedCate<PERSON><PERSON>", "React", "FaEdit", "FaTrash", "_field$options2", "field", "index", "onUpdate", "onRemove", "handleOptionChange", "optIndex", "key", "newOptions", "options", "handleDefaultValueChange", "type", "newDefaultValue", "checked", "defaultValue", "label", "htmlFor", "placeholder", "required", "includes", "min", "parseFloat", "max", "opt", "_field$options", "_", "i", "removeOption", "addOption", "String", "Boolean", "Array", "isArray", "join", "s", "trim", "buttonText", "sectionTitle", "pageConfig", "onAddField", "onUpdateField", "onRemoveField", "onSave", "onPreview", "loading", "FieldConfigItem", "FaPlus", "FaSave", "REPORT_FREQUENCIES", "<PERSON><PERSON><PERSON><PERSON>", "setIsOpen", "useState", "dropdownRef", "useRef", "useEffect", "handleClickOutside", "event", "current", "contains", "document", "addEventListener", "removeEventListener", "isAllSelected", "isIndeterminate", "ref", "backgroundColor", "borderColor", "getDisplayText", "selectedOption", "option", "maxHeight", "overflowY", "input", "indeterminate", "handleSelectAll", "handleCheckboxChange", "optionId", "selectedRegions", "selectedDivisions", "selectedOffices", "selectedFrequency", "onRegionsChange", "onDivisionsChange", "onOfficesChange", "onFrequencyChange", "regions", "divisions", "offices", "refetch", "useOfficeDataSimple", "setRegions", "setDivisions", "setOffices", "setLoading", "fetchOfficeData", "log", "allData", "OfficeService", "fetchAllOfficeData", "distinctRegions", "row", "Region", "region", "array", "indexOf", "sort", "regionsArray", "regionName", "toLowerCase", "distinctDivisions", "division", "Division", "findIndex", "x", "a", "b", "localeCompare", "divisionsArray", "officesArray", "facilityId", "useOfficeData", "selectedRegionNames", "regionId", "_regions$find", "r", "availableDivisions", "selectedDivisionNames", "divisionId", "_divisions$find", "d", "availableOffices", "office", "validDivisions", "validOffices", "officeId", "o", "role", "CheckboxDropdown", "frequency", "PageBuilder", "_state$categories$fin", "_state$categories$fin2", "state", "usePageBuilderState", "availableDynamicFields", "setAvailableDynamicFields", "success", "isAddingNewCard", "showConfirmModal", "showEditModal", "cardToDelete", "showDeleteConfirmModal", "isPreviewOpen", "setIsPreviewOpen", "previewContent", "setPreviewContent", "setSelectedRegions", "setSelectedDivisions", "setSelectedOffices", "setSelectedFrequency", "cardManagement", "pageConfiguration", "fetchDynamicFormFields", "formId", "formConfigRef", "formConfigSnap", "formConfigData", "loadPageConfig", "docSnap", "supabasePageService", "supabaseError", "selectedRegion", "selectedDivision", "selectedOffice", "addField", "newField", "now", "addFieldFromDynamic", "dynamicField", "columns", "buttonType", "onClickAction", "warn", "updateField", "updatedField", "<PERSON><PERSON><PERSON>s", "removeField", "handleSave", "cleanedFields", "cleanedField", "updatedPageConfig", "savePromises", "catch", "Error", "message", "savePageConfig", "Promise", "all", "handlePreview", "alert", "generatedPreview", "fieldHtml", "usePageConfiguration", "CardSelector", "cardIsLeaf", "cardIsMain", "action", "handleCreateAction", "handleWebPageAction", "Modal", "CardManagement", "ReportConfiguration", "PageBuilderContent", "dangerouslySetInnerHTML", "__html", "getListItemUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "getListItemSecondaryActionClassesUtilityClass", "ListItemSecondaryActionRoot", "styled", "overridesResolver", "styles", "ownerState", "root", "disableGutters", "position", "right", "top", "transform", "variants", "ListItemSecondaryAction", "inProps", "useDefaultProps", "other", "context", "ListContext", "classes", "slots", "composeClasses", "useUtilityClasses", "clsx", "mui<PERSON><PERSON>", "ListItemRoot", "dense", "alignItems", "alignItemsFlexStart", "divider", "gutters", "disablePadding", "padding", "hasSecondaryAction", "secondaryAction", "memoTheme", "theme", "display", "justifyContent", "textDecoration", "width", "boxSizing", "textAlign", "_ref2", "paddingTop", "paddingBottom", "_ref3", "_ref4", "paddingRight", "_ref5", "_ref6", "listItemButtonClasses", "_ref7", "borderBottom", "vars", "palette", "backgroundClip", "_ref8", "button", "transition", "transitions", "create", "duration", "shortest", "hover", "_ref9", "ListItemContainer", "childrenProp", "component", "componentProp", "components", "componentsProps", "ContainerComponent", "ContainerProps", "ContainerClassName", "slotProps", "childContext", "listItemRef", "toArray", "isMuiElement", "container", "handleRef", "useForkRef", "Root", "rootProps", "componentProps", "Component", "Provider", "as", "isHostComponent", "pop", "ListItemTextRoot", "listItemTextClasses", "primary", "secondary", "inset", "multiline", "flex", "min<PERSON><PERSON><PERSON>", "marginTop", "marginBottom", "typographyClasses", "disableTypography", "primaryProp", "primaryTypographyProps", "secondaryProp", "secondaryTypographyProps", "getListItemTextUtilityClass", "externalForwardedProps", "RootSlot", "rootSlotProps", "useSlot", "elementType", "PrimarySlot", "primarySlotProps", "Typography", "SecondarySlot", "secondarySlotProps", "variant", "DividerRoot", "absolute", "light", "orientation", "vertical", "flexItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withChildrenVertical", "textAlignRight", "textAlignLeft", "margin", "flexShrink", "borderWidth", "borderStyle", "borderBottomWidth", "bottom", "left", "dividerChannel", "alpha", "marginLeft", "spacing", "marginRight", "height", "borderRightWidth", "alignSelf", "border", "borderTopStyle", "borderLeftStyle", "content", "borderTop", "flexDirection", "borderLeft", "DividerWrapper", "wrapper", "wrapperVertical", "whiteSpace", "Divider", "getDividerUtilityClass", "muiSkipListHighlight", "useOfficeDataEnhanced", "totalRecords", "setTotalRecords", "approach", "setApproach", "allOfficeData", "uniqueRegions", "Set", "add", "from", "uniqueDivisions", "Map", "set", "entries", "divisionName", "sortedNames", "letterCounts", "firstLetter", "char<PERSON>t", "toUpperCase", "Object", "keys", "letter", "tirupurDivision", "coimbatoreDivision", "regionOffices", "count", "slice", "logOfficeStatistics", "OfficeLoadingTest", "Box", "p", "CircularProgress", "size", "sx", "mt", "<PERSON><PERSON>", "severity", "mb", "letterDistribution", "sortedOfficeNames", "regionCounts", "divisionCounts", "gutterBottom", "paragraph", "gap", "flexWrap", "Card", "<PERSON><PERSON><PERSON><PERSON>", "toLocaleString", "Chip", "Paper", "overflow", "List", "ListItem", "ListItemText", "FixUndefinedReport", "isFixing", "setIsFixing", "foundIssues", "setFoundIssues", "borderRadius", "FaTools", "issue", "fontSize", "pagesSnapshot", "issues", "cursor", "fontWeight", "fixedCount", "deletedCount", "description", "deleteDoc", "window", "location", "reload", "FaTruck", "AdminPage", "currentUser", "useAuth", "userData", "setUserData", "useNavigate", "showOfficeTest", "setShowOfficeTest", "userRef", "uid", "userSnap", "fetchUserData", "Sidebar", "StatsCards", "getCardUtilityClass", "CardRoot", "raised", "elevation", "stats", "stat", "getCardContentUtilityClass", "CardContentRoot"], "sourceRoot": ""}