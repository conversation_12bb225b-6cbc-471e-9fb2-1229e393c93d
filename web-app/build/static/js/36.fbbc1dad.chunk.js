"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[36],{6036:(e,s,t)=>{t.r(s),t.d(s,{default:()=>l});var d=t(5043),i=t(5472),n=t(2073),r=t(579);const l=()=>{const[e,s]=(0,d.useState)([]),[t,l]=(0,d.useState)(!0);return(0,d.useEffect)((()=>{(async()=>{try{const e=(0,i.rJ)(n.db,"employees"),t=(await(0,i.GG)(e)).docs.map((e=>({id:e.id,...e.data()})));s(t)}catch(e){console.error("Error fetching employees:",e)}finally{l(!1)}})()}),[]),t?(0,r.jsx)("div",{children:"Loading..."}):(0,r.jsxs)("div",{className:"employees-list",children:[(0,r.jsx)("h1",{children:"Employees"}),(0,r.jsxs)("table",{children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{children:"Name"}),(0,r.jsx)("th",{children:"Email"}),(0,r.jsx)("th",{children:"Department"}),(0,r.jsx)("th",{children:"Actions"})]})}),(0,r.jsx)("tbody",{children:e.map((e=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{children:e.name}),(0,r.jsx)("td",{children:e.email}),(0,r.jsx)("td",{children:e.department}),(0,r.jsxs)("td",{children:[(0,r.jsx)("button",{children:"Edit"}),(0,r.jsx)("button",{children:"Delete"})]})]},e.id)))})]})]})}}}]);
//# sourceMappingURL=36.fbbc1dad.chunk.js.map