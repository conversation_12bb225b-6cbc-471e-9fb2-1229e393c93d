{"version": 3, "file": "static/js/36.fbbc1dad.chunk.js", "mappings": "+LAWA,MA0DA,EA1DgCA,KAC9B,MAAOC,EAAWC,IAAgBC,EAAAA,EAAAA,UAAqB,KAChDC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAsBvC,OApBAG,EAAAA,EAAAA,YAAU,KACeC,WACrB,IACE,MAAMC,GAAsBC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,aAErCC,SAD0BC,EAAAA,EAAAA,IAAQJ,IACAK,KAAKC,KAAIC,IAAG,CAClDC,GAAID,EAAIC,MACLD,EAAIE,WAETf,EAAaS,EACf,CAAE,MAAOO,GACPC,QAAQD,MAAM,4BAA6BA,EAC7C,CAAC,QACCb,GAAW,EACb,GAGFe,EAAgB,GACf,IAEChB,GACKiB,EAAAA,EAAAA,KAAA,OAAAC,SAAK,gBAIZC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBF,SAAA,EAC7BD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,eACJC,EAAAA,EAAAA,MAAA,SAAAD,SAAA,EACED,EAAAA,EAAAA,KAAA,SAAAC,UACEC,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAAC,SAAI,UACJD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,WACJD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,gBACJD,EAAAA,EAAAA,KAAA,MAAAC,SAAI,kBAGRD,EAAAA,EAAAA,KAAA,SAAAC,SACGrB,EAAUa,KAAIW,IACbF,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACED,EAAAA,EAAAA,KAAA,MAAAC,SAAKG,EAASC,QACdL,EAAAA,EAAAA,KAAA,MAAAC,SAAKG,EAASE,SACdN,EAAAA,EAAAA,KAAA,MAAAC,SAAKG,EAASG,cACdL,EAAAA,EAAAA,MAAA,MAAAD,SAAA,EACED,EAAAA,EAAAA,KAAA,UAAAC,SAAQ,UACRD,EAAAA,EAAAA,KAAA,UAAAC,SAAQ,gBANHG,EAAST,aAYpB,C", "sources": ["components/employees/EmployeesList.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { collection, getDocs } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\n\ninterface Employee {\n  id: string;\n  name: string;\n  email: string;\n  department: string;\n}\n\nconst EmployeesList: React.FC = () => {\n  const [employees, setEmployees] = useState<Employee[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchEmployees = async () => {\n      try {\n        const employeesCollection = collection(db, 'employees');\n        const employeesSnapshot = await getDocs(employeesCollection);\n        const employeesList = employeesSnapshot.docs.map(doc => ({\n          id: doc.id,\n          ...doc.data()\n        })) as Employee[];\n        setEmployees(employeesList);\n      } catch (error) {\n        console.error('Error fetching employees:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchEmployees();\n  }, []);\n\n  if (loading) {\n    return <div>Loading...</div>;\n  }\n\n  return (\n    <div className=\"employees-list\">\n      <h1>Employees</h1>\n      <table>\n        <thead>\n          <tr>\n            <th>Name</th>\n            <th>Email</th>\n            <th>Department</th>\n            <th>Actions</th>\n          </tr>\n        </thead>\n        <tbody>\n          {employees.map(employee => (\n            <tr key={employee.id}>\n              <td>{employee.name}</td>\n              <td>{employee.email}</td>\n              <td>{employee.department}</td>\n              <td>\n                <button>Edit</button>\n                <button>Delete</button>\n              </td>\n            </tr>\n          ))}\n        </tbody>\n      </table>\n    </div>\n  );\n};\n\nexport default EmployeesList;"], "names": ["EmployeesList", "employees", "setEmployees", "useState", "loading", "setLoading", "useEffect", "async", "employeesCollection", "collection", "db", "employeesList", "getDocs", "docs", "map", "doc", "id", "data", "error", "console", "fetchEmployees", "_jsx", "children", "_jsxs", "className", "employee", "name", "email", "department"], "sourceRoot": ""}