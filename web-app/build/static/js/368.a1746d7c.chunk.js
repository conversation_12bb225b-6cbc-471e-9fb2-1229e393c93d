"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[368],{907:()=>{},3393:(a,s,e)=>{e.d(s,{A:()=>i});e(5043),e(9482);var t=e(579);const c=[{title:"SB Accounts",value:123456},{title:"BD Revenue",value:"\u20b924,343"},{title:"No. Aadhaar Trans",value:1259},{title:"PLI",value:"\u20b999,99,999"}],i=()=>(0,t.jsx)("div",{className:"stats-grid",children:c.map(((a,s)=>(0,t.jsxs)("div",{className:`stat-card card-${s}`,children:[(0,t.jsx)("h3",{children:a.title}),(0,t.jsx)("p",{className:"stat-value",children:a.value})]},s)))})},4368:(a,s,e)=>{e.r(s),e.d(s,{default:()=>o});var t=e(9002),c=e(5472),i=e(2073),n=e(5043),l=e(9066),r=e(1103),d=e(3393),h=(e(907),e(579));const o=()=>{const{currentUser:a}=(0,l.A)(),s=(0,t.Zp)(),[e,o]=(0,n.useState)(null);(0,n.useEffect)((()=>{(async()=>{if(a){const s=(0,c.H9)(i.db,"employees",a.uid),e=await(0,c.x7)(s);e.exists()&&o(e.data())}})()}),[a]);const u=a=>{s(a)};return(0,h.jsxs)("div",{className:"dashboard-container",children:[(0,h.jsx)(r.A,{userData:e}),(0,h.jsxs)("div",{className:"main-content",children:[(0,h.jsx)("div",{className:"page-title",children:"Dashboard"}),(0,h.jsx)(d.A,{}),(0,h.jsxs)("div",{className:"charts-container",children:[(0,h.jsxs)("div",{className:"chart-box main-chart",onClick:()=>u("/data-entry"),children:[(0,h.jsx)("h3",{children:"Data Entry"}),(0,h.jsx)("div",{className:"chart-content"})]}),(0,h.jsxs)("div",{className:"chart-box main-chart",onClick:()=>u("/reports"),children:[(0,h.jsx)("h3",{children:"Reports"}),(0,h.jsx)("div",{className:"chart-content"})]})]})]})]})}}}]);
//# sourceMappingURL=368.a1746d7c.chunk.js.map