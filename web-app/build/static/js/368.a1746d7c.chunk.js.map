{"version": 3, "file": "static/js/368.a1746d7c.chunk.js", "mappings": "gLAGA,MAAMA,EAAQ,CACZ,CAAEC,MAAO,cAAeC,MAAO,QAC/B,CAAED,MAAO,aAAcC,MAAO,gBAC9B,CAAED,MAAO,oBAAqBC,MAAO,MACrC,CAAED,MAAO,MAAOC,MAAO,oBAgBzB,EAb6BC,KAEzBC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,SACxBN,EAAMO,KAAI,CAACC,EAAMC,KAChBC,EAAAA,EAAAA,MAAA,OAAKL,UAAW,kBAAkBI,IAAQH,SAAA,EACxCF,EAAAA,EAAAA,KAAA,MAAAE,SAAKE,EAAKP,SACVG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYC,SAAEE,EAAKN,UAFcO,M,2ICLxD,MAoDA,EApD4BE,KAC1B,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,KAClBC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAc,OAE9CC,EAAAA,EAAAA,YAAU,KACcC,WACpB,GAAIR,EAAa,CACf,MAAMS,GAAUC,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAaX,EAAYY,KAC3CC,QAAiBC,EAAAA,EAAAA,IAAOL,GAC1BI,EAASE,UACXV,EAAYQ,EAASG,OAEzB,GAEFC,EAAe,GACd,CAACjB,IAEJ,MAAMkB,EAAoBC,IACxBjB,EAASiB,EAAK,EAGhB,OACErB,EAAAA,EAAAA,MAAA,OAAKL,UAAU,sBAAqBC,SAAA,EAClCF,EAAAA,EAAAA,KAAC4B,EAAAA,EAAO,CAAChB,SAAUA,KAGnBN,EAAAA,EAAAA,MAAA,OAAKL,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,SAAC,eAE5BF,EAAAA,EAAAA,KAACD,EAAAA,EAAU,KAGXO,EAAAA,EAAAA,MAAA,OAAKL,UAAU,mBAAkBC,SAAA,EAC/BI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,uBAAuB4B,QAASA,IAAMH,EAAiB,eAAexB,SAAA,EACnFF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,gBACJF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,sBAIjBK,EAAAA,EAAAA,MAAA,OAAKL,UAAU,uBAAuB4B,QAASA,IAAMH,EAAiB,YAAYxB,SAAA,EAChFF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,aACJF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,6BAMjB,C", "sources": ["components/shared/StatsCards.tsx", "components/dashboard/Dashboard.tsx"], "sourcesContent": ["import React from 'react';\nimport './StatsCards.css';\n\nconst stats = [\n  { title: 'SB Accounts', value: 123456 },\n  { title: 'BD Revenue', value: '₹24,343' },\n  { title: 'No. Aadhaar Trans', value: 1259 },\n  { title: 'PLI', value: '₹99,99,999' }\n];\n\nconst StatsCards: React.FC = () => {\n  return (\n    <div className=\"stats-grid\">\n      {stats.map((stat, index) => (\n        <div className={`stat-card card-${index}`} key={index}>\n          <h3>{stat.title}</h3>\n          <p className=\"stat-value\">{stat.value}</p>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default StatsCards;", "import { useNavigate } from 'react-router-dom';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport React, { useEffect, useState } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Sidebar from '../shared/Sidebar';\nimport StatsCards from '../shared/StatsCards';\nimport './Dashboard.css';\n\nconst Dashboard: React.FC = () => {\n  const { currentUser } = useAuth();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState<any>(null);\n\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        const userRef = doc(db, 'employees', currentUser.uid);\n        const userSnap = await getDoc(userRef);\n        if (userSnap.exists()) {\n          setUserData(userSnap.data());\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  const handleNavigation = (path: string) => {\n    navigate(path);\n  };\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n\n      {/* Main Content */}\n      <div className=\"main-content\">\n        <div className=\"page-title\">Dashboard</div>\n        \n        <StatsCards />\n\n        {/* Charts Section */}\n        <div className=\"charts-container\">\n          <div className=\"chart-box main-chart\" onClick={() => handleNavigation('/data-entry')}>\n            <h3>Data Entry</h3>\n            <div className=\"chart-content\">\n              {/* Add Chart.js or Recharts implementation here */}\n            </div>\n          </div>\n          <div className=\"chart-box main-chart\" onClick={() => handleNavigation('/reports')}>\n            <h3>Reports</h3>\n            <div className=\"chart-content\">\n              {/* Add Chart.js or Recharts implementation here */}\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;"], "names": ["stats", "title", "value", "StatsCards", "_jsx", "className", "children", "map", "stat", "index", "_jsxs", "Dashboard", "currentUser", "useAuth", "navigate", "useNavigate", "userData", "setUserData", "useState", "useEffect", "async", "userRef", "doc", "db", "uid", "userSnap", "getDoc", "exists", "data", "fetchUserData", "handleNavigation", "path", "Sidebar", "onClick"], "sourceRoot": ""}