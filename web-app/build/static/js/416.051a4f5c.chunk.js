"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[416],{3393:(e,t,s)=>{s.d(t,{A:()=>c});s(5043),s(9482);var n=s(579);const a=[{title:"SB Accounts",value:123456},{title:"BD Revenue",value:"\u20b924,343"},{title:"No. Aadhaar Trans",value:1259},{title:"PLI",value:"\u20b999,99,999"}],c=()=>(0,n.jsx)("div",{className:"stats-grid",children:a.map(((e,t)=>(0,n.jsxs)("div",{className:`stat-card card-${t}`,children:[(0,n.jsx)("h3",{children:e.title}),(0,n.jsx)("p",{className:"stat-value",children:e.value})]},t)))})},8035:(e,t,s)=>{s.r(t),s.d(t,{default:()=>p});var n=s(5043),a=s(5472),c=s(2073),r=s(9002),l=s(9066),i=s(1103),o=s(3393),d=s(3204),u=s(900),h=(s(3643),s(579));const p=()=>{const{currentUser:e}=(0,l.A)(),t=(0,r.Zp)(),[s,p]=(0,n.useState)(null),[m,g]=(0,n.useState)([]);(0,n.useEffect)((()=>{(async()=>{if(e){const t=(0,a.H9)(c.db,"employees",e.uid),s=await(0,a.x7)(t);s.exists()&&p(s.data())}})()}),[e]),(0,n.useEffect)((()=>{(async()=>{try{const e=(0,a.rJ)(c.db,"categories"),t=(await(0,a.GG)(e)).docs.map((e=>{const t=e.data();return{id:e.id,title:t.title,path:t.path,icon:t.icon,color:t.color,parentId:t.parentId||null}}));if(console.log("Raw categories data:",t),t.length>0){const e=f(t);console.log("Organized categories:",e),g(e)}}catch(e){console.error("Error fetching categories:",e)}})()}),[]);const f=e=>{console.log("Organizing cards, total count:",e.length);const t=e.filter((e=>e.parentId&&null!==e.parentId));console.log("Cards with parentId:",t);const s=e.filter((e=>!e.parentId||null===e.parentId)),n=e.filter((e=>e.parentId&&null!==e.parentId));return console.log("Top level cards:",s.length),console.log("Nested cards:",n.length),s.map((e=>{const t=n.filter((t=>t.parentId===e.id));return console.log(`Children for card ${e.id}:`,t.length),{...e,children:t}}))},y=e=>{const s=(e=>{const t=e.toLowerCase();return t.includes("business")?d._yv:t.includes("tech")?d.tut:t.includes("building")?d.ymh:t.includes("payment")?d.XVP:t.includes("bank")?d.oMH:t.includes("ippb")?d.MdY:t.includes("recruitment")?d.YXz:t.includes("investigation")?d.KSO:t.includes("mmu")||t.includes("mail motor")?d.dv1:d.HFM})(e.title);return(0,h.jsxs)("div",{className:"category-card",onClick:async()=>{if(e.path.includes("/dynamic-form/")){const n=e.path.split("/dynamic-form/")[1];if(n){console.log("\ud83d\udd12 DataEntry: Checking access for form:",n);try{await u.I.canUserAccessForm(n)?(console.log("\u2705 DataEntry: Access granted for form:",n),t(e.path)):(console.log("\u274c DataEntry: Access denied for form:",n),alert("Access denied: This form is not available for your office."))}catch(s){console.error("\u274c DataEntry: Error checking form access:",s),alert("Error checking form access. Please try again.")}}else t(e.path)}else t(e.path)},children:[(0,h.jsx)("div",{className:"category-icon",style:{color:(e=>{const t=e.toLowerCase();return t.includes("business")?"#4CAF50":t.includes("tech")?"#2196F3":t.includes("building")?"#FF9800":t.includes("payment")?"#9C27B0":t.includes("bank")?"#F44336":t.includes("ippb")?"#3F51B5":t.includes("recruitment")?"#009688":t.includes("investigation")?"#795548":t.includes("mmu")||t.includes("mail motor")?"#FF5722":"#607D8B"})(e.title)},children:n.createElement(s,{size:40})}),(0,h.jsx)("h3",{children:e.title})]},e.id)};return(0,h.jsxs)("div",{className:"dashboard-container",children:[(0,h.jsx)(i.A,{userData:s}),(0,h.jsxs)("div",{className:"main-content",children:[(0,h.jsx)("h1",{className:"page-title",children:"Data Entry"}),(0,h.jsx)(o.A,{}),(0,h.jsx)("div",{className:"category-grid",children:m.filter((e=>!e.parentId)).map((e=>y(e)))})]})]})}}}]);
//# sourceMappingURL=416.051a4f5c.chunk.js.map