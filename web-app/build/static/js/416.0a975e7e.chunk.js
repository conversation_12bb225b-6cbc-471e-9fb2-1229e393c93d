"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[416],{3393:(e,n,t)=>{t.d(n,{A:()=>i});t(5043),t(9482);var s=t(579);const c=[{title:"SB Accounts",value:123456},{title:"BD Revenue",value:"\u20b924,343"},{title:"No. Aadhaar Trans",value:1259},{title:"PLI",value:"\u20b999,99,999"}],i=()=>(0,s.jsx)("div",{className:"stats-grid",children:c.map(((e,n)=>(0,s.jsxs)("div",{className:`stat-card card-${n}`,children:[(0,s.jsx)("h3",{children:e.title}),(0,s.jsx)("p",{className:"stat-value",children:e.value})]},n)))})},8035:(e,n,t)=>{t.r(n),t.d(n,{default:()=>p});var s=t(5043),c=t(5472),i=t(2073),a=t(9002),l=t(9066),r=t(1103),d=t(3393),o=t(3204),u=t(900),h=(t(3643),t(579));const p=()=>{const{currentUser:e}=(0,l.A)(),n=(0,a.Zp)(),[t,p]=(0,s.useState)(null),[f,g]=(0,s.useState)([]);(0,s.useEffect)((()=>{(async()=>{if(e){const n=(0,c.H9)(i.db,"employees",e.uid),t=await(0,c.x7)(n);t.exists()&&p(t.data())}})()}),[e]),(0,s.useEffect)((()=>{(async()=>{try{const e=(0,c.rJ)(i.db,"categories"),n=(await(0,c.GG)(e)).docs.map((e=>{const n=e.data();return{id:e.id,title:n.title,path:n.path,icon:n.icon,color:n.color,parentId:n.parentId||null}}));if(console.log("Raw categories data:",n),n.length>0){const e=m(n);console.log("Organized categories:",e),g(e)}}catch(e){console.error("Error fetching categories:",e)}})()}),[]);const m=e=>{console.log("Organizing cards, total count:",e.length);const n=e.filter((e=>e.parentId&&null!==e.parentId));console.log("Cards with parentId:",n);const t=e.filter((e=>!e.parentId||null===e.parentId)),s=e.filter((e=>e.parentId&&null!==e.parentId));return console.log("Top level cards:",t.length),console.log("Nested cards:",s.length),t.map((e=>{const n=s.filter((n=>n.parentId===e.id));return console.log(`Children for card ${e.id}:`,n.length),{...e,children:n}}))},v=e=>{const t=(e=>{if(!e||"string"!==typeof e)return o.HFM;const n=e.toLowerCase();return n.includes("mmu")||n.includes("vehicle")&&n.includes("maintenance")?o.dv1:n.includes("fuel")?o.dD7:n.includes("driver")?o.p7b:n.includes("route")?o.$Fg:n.includes("tracking")?o.vq8:n.includes("schedule")||n.includes("maintenance")?o.bfZ:n.includes("business")?o._yv:n.includes("tech")?o.tut:n.includes("building")?o.ymh:n.includes("payment")?o.XVP:n.includes("bank")?o.oMH:n.includes("ippb")?o.MdY:n.includes("recruitment")?o.YXz:n.includes("investigation")?o.KSO:o.HFM})(e.title);return(0,h.jsxs)("div",{className:"category-card",onClick:async()=>{if(e.path.includes("/dynamic-form/")){const s=e.path.split("/dynamic-form/")[1];if(s){console.log("\ud83d\udd12 DataEntry: Checking access for form:",s);try{await u.I.canUserAccessForm(s)?(console.log("\u2705 DataEntry: Access granted for form:",s),n(e.path)):(console.log("\u274c DataEntry: Access denied for form:",s),alert("Access denied: This form is not available for your office."))}catch(t){console.error("\u274c DataEntry: Error checking form access:",t),alert("Error checking form access. Please try again.")}}else n(e.path)}else n(e.path)},children:[(0,h.jsx)("div",{className:"category-icon",style:{color:(e=>{if(!e||"string"!==typeof e)return"#607D8B";const n=e.toLowerCase();return n.includes("mmu")||n.includes("vehicle")&&n.includes("maintenance")?"#28a745":n.includes("fuel")?"#fd7e14":n.includes("driver")?"#6f42c1":n.includes("route")?"#20c997":n.includes("tracking")?"#dc3545":n.includes("schedule")||n.includes("maintenance")?"#0dcaf0":n.includes("business")?"#4CAF50":n.includes("tech")?"#2196F3":n.includes("building")?"#FF9800":n.includes("payment")?"#9C27B0":n.includes("bank")?"#F44336":n.includes("ippb")?"#3F51B5":n.includes("recruitment")?"#009688":n.includes("investigation")?"#795748":"#607D8B"})(e.title)},children:s.createElement(t,{size:40})}),(0,h.jsx)("h3",{children:e.title||"Unnamed Category"})]},e.id)};return(0,h.jsxs)("div",{className:"dashboard-container",children:[(0,h.jsx)(r.A,{userData:t}),(0,h.jsxs)("div",{className:"main-content",children:[(0,h.jsx)("h1",{className:"page-title",children:"Data Entry"}),(0,h.jsx)(d.A,{}),(0,h.jsx)("div",{className:"category-grid",children:f.filter((e=>!e.parentId)).filter((e=>e.title&&""!==e.title.trim()&&"undefined"!==e.title)).map((e=>v(e)))})]})]})}}}]);
//# sourceMappingURL=416.0a975e7e.chunk.js.map