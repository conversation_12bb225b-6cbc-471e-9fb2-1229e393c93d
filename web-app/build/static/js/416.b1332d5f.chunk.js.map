{"version": 3, "file": "static/js/416.b1332d5f.chunk.js", "mappings": "qKAGA,MAAMA,EAAQ,CACZ,CAAEC,MAAO,cAAeC,MAAO,QAC/B,CAAED,MAAO,aAAcC,MAAO,gBAC9B,CAAED,MAAO,oBAAqBC,MAAO,MACrC,CAAED,MAAO,MAAOC,MAAO,oBAgBzB,EAb6BC,KAEzBC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,SACxBN,EAAMO,KAAI,CAACC,EAAMC,KAChBC,EAAAA,EAAAA,MAAA,OAAKL,UAAW,kBAAkBI,IAAQH,SAAA,EACxCF,EAAAA,EAAAA,KAAA,MAAAE,SAAKE,EAAKP,SACVG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYC,SAAEE,EAAKN,UAFcO,M,+JCQxD,MAuLA,EAvL4BE,KAC1B,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,KAClBC,GAAWC,EAAAA,EAAAA,OACVC,EAAUC,IAAeC,EAAAA,EAAAA,UAAc,OACvCC,EAAYC,IAAiBF,EAAAA,EAAAA,UAAqB,KAEzDG,EAAAA,EAAAA,YAAU,KACcC,WACpB,GAAIV,EAAa,CACf,MAAMW,GAAUC,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAab,EAAYc,KAC3CC,QAAiBC,EAAAA,EAAAA,IAAOL,GAC1BI,EAASE,UACXZ,EAAYU,EAASG,OAEzB,GAEFC,EAAe,GACd,CAACnB,KAEJS,EAAAA,EAAAA,YAAU,KACgBC,WACtB,IACE,MAAMU,GAAuBC,EAAAA,EAAAA,IAAWR,EAAAA,GAAI,cAEtCS,SAD2BC,EAAAA,EAAAA,IAAQH,IACCI,KAAK7B,KAAIiB,IACjD,MAAMM,EAAON,EAAIM,OACjB,MAAO,CACLO,GAAIb,EAAIa,GACRpC,MAAO6B,EAAK7B,MACZqC,KAAMR,EAAKQ,KACXC,KAAMT,EAAKS,KACXC,MAAOV,EAAKU,MACZC,SAAUX,EAAKW,UAAY,KAC5B,IAKH,GAFAC,QAAQC,IAAI,uBAAwBT,GAEhCA,EAAeU,OAAS,EAAG,CAE7B,MAAMC,EAAsBC,EAAcZ,GAC1CQ,QAAQC,IAAI,wBAAyBE,GACrCzB,EAAcyB,EAChB,CACF,CAAE,MAAOE,GACPL,QAAQM,MAAM,6BAA8BD,EAC9C,GAGFE,EAAiB,GAChB,IAGH,MAAMH,EAAiBI,IACrBR,QAAQC,IAAI,iCAAkCO,EAAMN,QAGpD,MAAMO,EAAkBD,EAAME,QAAOC,GAAQA,EAAKZ,UAA8B,OAAlBY,EAAKZ,WACnEC,QAAQC,IAAI,uBAAwBQ,GAEpC,MAAMG,EAAWJ,EAAME,QAAOC,IAASA,EAAKZ,UAA8B,OAAlBY,EAAKZ,WACvDc,EAASL,EAAME,QAAOC,GAAQA,EAAKZ,UAA8B,OAAlBY,EAAKZ,WAK1D,OAHAC,QAAQC,IAAI,mBAAoBW,EAASV,QACzCF,QAAQC,IAAI,gBAAiBY,EAAOX,QAE7BU,EAAS/C,KAAI8C,IAClB,MAAM/C,EAAWiD,EAAOH,QAAOI,GAAKA,EAAEf,WAAaY,EAAKhB,KAExD,OADAK,QAAQC,IAAI,qBAAqBU,EAAKhB,MAAO/B,EAASsC,QAC/C,IACFS,EACH/C,SAAUA,EACX,GACD,EAyBEmD,EAAcC,IAClB,MAAMC,EApBkB1D,KAExB,IAAKA,GAA0B,kBAAVA,EACnB,OAAO2D,EAAAA,IAGT,MAAMC,EAAkB5D,EAAM6D,cAC9B,OAAID,EAAgBE,SAAS,YAAoBC,EAAAA,IAC7CH,EAAgBE,SAAS,QAAgBE,EAAAA,IACzCJ,EAAgBE,SAAS,YAAoBG,EAAAA,IAC7CL,EAAgBE,SAAS,WAAmBI,EAAAA,IAC5CN,EAAgBE,SAAS,QAAgBK,EAAAA,IACzCP,EAAgBE,SAAS,QAAgBM,EAAAA,IACzCR,EAAgBE,SAAS,eAAuBO,EAAAA,IAChDT,EAAgBE,SAAS,iBAAyBQ,EAAAA,IAC/CX,EAAAA,GAAW,EAKIY,CAAiBd,EAASzD,OAmBhD,OACES,EAAAA,EAAAA,MAAA,OAEEL,UAAU,gBACVoE,QAASnD,UAIP,GAFsBoC,EAASpB,KAAKyB,SAAS,kBAE1B,CAEjB,MAAMW,EAAShB,EAASpB,KAAKqC,MAAM,kBAAkB,GAErD,GAAID,EAAQ,CACVhC,QAAQC,IAAI,oDAA2C+B,GAEvD,UAE0BE,EAAAA,EAAqBC,kBAAkBH,IAG7DhC,QAAQC,IAAI,6CAAyC+B,GACrD5D,EAAS4C,EAASpB,QAElBI,QAAQC,IAAI,4CAAwC+B,GACpDI,MAAM,8DAEV,CAAE,MAAO9B,GACPN,QAAQM,MAAM,gDAA4CA,GAC1D8B,MAAM,gDACR,CACF,MAEEhE,EAAS4C,EAASpB,KAEtB,MAEExB,EAAS4C,EAASpB,KACpB,EACAhC,SAAA,EAEFF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAgB0E,MAAO,CAAEvC,MA1DtBvC,KAEpB,IAAKA,GAA0B,kBAAVA,EACnB,MAAO,UAGT,MAAM4D,EAAkB5D,EAAM6D,cAC9B,OAAID,EAAgBE,SAAS,YAAoB,UAC7CF,EAAgBE,SAAS,QAAgB,UACzCF,EAAgBE,SAAS,YAAoB,UAC7CF,EAAgBE,SAAS,WAAmB,UAC5CF,EAAgBE,SAAS,QAAgB,UACzCF,EAAgBE,SAAS,QAAgB,UACzCF,EAAgBE,SAAS,eAAuB,UAChDF,EAAgBE,SAAS,iBAAyB,UAC/C,SAAS,EA2CiCiB,CAAatB,EAASzD,QAASK,SAC3E2E,EAAAA,cAAoBtB,EAA2C,CAAEuB,KAAM,QAE1E9E,EAAAA,EAAAA,KAAA,MAAAE,SAAKoD,EAASzD,OAAS,uBAzClByD,EAASrB,GA0CV,EAIV,OACE3B,EAAAA,EAAAA,MAAA,OAAKL,UAAU,sBAAqBC,SAAA,EAClCF,EAAAA,EAAAA,KAAC+E,EAAAA,EAAO,CAACnE,SAAUA,KACnBN,EAAAA,EAAAA,MAAA,OAAKL,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,MAAIC,UAAU,aAAYC,SAAC,gBAC3BF,EAAAA,EAAAA,KAACD,EAAAA,EAAU,KACXC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SAC3Ba,EACEiC,QAAOM,IAAaA,EAASjB,WAC7BW,QAAOM,GAAYA,EAASzD,OAAmC,KAA1ByD,EAASzD,MAAMmF,QAAoC,cAAnB1B,EAASzD,QAC9EM,KAAImD,GAAYD,EAAWC,YAG9B,C", "sources": ["components/shared/StatsCards.tsx", "components/DataEntry/DataEntry.tsx"], "sourcesContent": ["import React from 'react';\nimport './StatsCards.css';\n\nconst stats = [\n  { title: 'SB Accounts', value: 123456 },\n  { title: 'BD Revenue', value: '₹24,343' },\n  { title: 'No. Aadhaar Trans', value: 1259 },\n  { title: 'PLI', value: '₹99,99,999' }\n];\n\nconst StatsCards: React.FC = () => {\n  return (\n    <div className=\"stats-grid\">\n      {stats.map((stat, index) => (\n        <div className={`stat-card card-${index}`} key={index}>\n          <h3>{stat.title}</h3>\n          <p className=\"stat-value\">{stat.value}</p>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default StatsCards;", "import React, { useEffect, useState } from 'react';\nimport { doc, getDoc, collection, getDocs } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Sidebar from '../shared/Sidebar';\nimport StatsCards from '../shared/StatsCards';\nimport { FaBriefcase, FaLaptopCode, FaBuilding, FaMoneyBill, FaPiggyBank, FaUniversity, FaUsers, FaSearch, FaEllipsisH } from 'react-icons/fa';\nimport { IconType } from 'react-icons';\nimport { FormFilteringService } from '../../services/formFilteringService';\nimport './DataEntry.css';\n\ninterface Category {\n  id: string;\n  title: string;\n  path: string;\n  icon?: string;\n  color?: string;\n  parentId?: string; // Add parentId for nested cards\n  children?: Category[]; // Add children array for nested cards\n}\n\nconst DataEntry: React.FC = () => {\n  const { currentUser } = useAuth();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState<any>(null);\n  const [categories, setCategories] = useState<Category[]>([]);\n\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        const userRef = doc(db, 'employees', currentUser.uid);\n        const userSnap = await getDoc(userRef);\n        if (userSnap.exists()) {\n          setUserData(userSnap.data());\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  useEffect(() => {\n    const fetchCategories = async () => {\n      try {\n        const categoriesCollection = collection(db, 'categories');\n        const categoriesSnapshot = await getDocs(categoriesCollection);\n        const categoriesData = categoriesSnapshot.docs.map(doc => {\n          const data = doc.data();\n          return {\n            id: doc.id,\n            title: data.title,\n            path: data.path,\n            icon: data.icon,\n            color: data.color,\n            parentId: data.parentId || null // Ensure parentId is explicitly set\n          } as Category;\n        });\n        \n        console.log('Raw categories data:', categoriesData); // Log raw data\n        \n        if (categoriesData.length > 0) {\n          // Organize cards into hierarchical structure\n          const organizedCategories = organizeCards(categoriesData);\n          console.log('Organized categories:', organizedCategories); // Log organized data\n          setCategories(organizedCategories);\n        }\n      } catch (err) {\n        console.error('Error fetching categories:', err);\n      }\n    };\n    \n    fetchCategories();\n  }, []);\n\n  // Function to organize cards into hierarchical structure\n  const organizeCards = (cards: Category[]) => {\n    console.log('Organizing cards, total count:', cards.length);\n    \n    // Log cards with parentId to verify they exist\n    const cardsWithParent = cards.filter(card => card.parentId && card.parentId !== null);\n    console.log('Cards with parentId:', cardsWithParent);\n    \n    const topLevel = cards.filter(card => !card.parentId || card.parentId === null);\n    const nested = cards.filter(card => card.parentId && card.parentId !== null);\n    \n    console.log('Top level cards:', topLevel.length);\n    console.log('Nested cards:', nested.length);\n  \n    return topLevel.map(card => {\n      const children = nested.filter(n => n.parentId === card.id);\n      console.log(`Children for card ${card.id}:`, children.length);\n      return {\n        ...card,\n        children: children\n      };\n    });\n  };\n\n  // Recursive function to render cards with their children\n  // Modify the renderCard function to remove child card rendering\n  // Add this function before renderCard\n  const getIconComponent = (title: string | undefined | null): IconType => {\n    // Handle undefined, null, or empty titles\n    if (!title || typeof title !== 'string') {\n      return FaEllipsisH; // Default icon for undefined/null titles\n    }\n\n    const normalizedTitle = title.toLowerCase();\n    if (normalizedTitle.includes('business')) return FaBriefcase;\n    if (normalizedTitle.includes('tech')) return FaLaptopCode;\n    if (normalizedTitle.includes('building')) return FaBuilding;\n    if (normalizedTitle.includes('payment')) return FaMoneyBill;\n    if (normalizedTitle.includes('bank')) return FaPiggyBank;\n    if (normalizedTitle.includes('ippb')) return FaUniversity;\n    if (normalizedTitle.includes('recruitment')) return FaUsers;\n    if (normalizedTitle.includes('investigation')) return FaSearch;\n    return FaEllipsisH;\n  };\n\n  // Update the renderCard function's getIconColor\n  const renderCard = (category: Category) => {\n    const IconComponent = getIconComponent(category.title);\n    const getIconColor = (title: string | undefined | null) => {\n      // Handle undefined, null, or empty titles\n      if (!title || typeof title !== 'string') {\n        return '#607D8B'; // Default color for undefined/null titles\n      }\n\n      const normalizedTitle = title.toLowerCase();\n      if (normalizedTitle.includes('business')) return '#4CAF50';\n      if (normalizedTitle.includes('tech')) return '#2196F3';\n      if (normalizedTitle.includes('building')) return '#FF9800';\n      if (normalizedTitle.includes('payment')) return '#9C27B0';\n      if (normalizedTitle.includes('bank')) return '#F44336';\n      if (normalizedTitle.includes('ippb')) return '#3F51B5';\n      if (normalizedTitle.includes('recruitment')) return '#009688';\n      if (normalizedTitle.includes('investigation')) return '#795548';\n      return '#607D8B';\n    };\n\n    return (\n      <div\n        key={category.id}\n        className=\"category-card\"\n        onClick={async () => {\n          // Check if this is a dynamic form route\n          const isDynamicForm = category.path.includes('/dynamic-form/');\n\n          if (isDynamicForm) {\n            // Extract form ID from path (e.g., '/dynamic-form/123' -> '123')\n            const formId = category.path.split('/dynamic-form/')[1];\n\n            if (formId) {\n              console.log('🔒 DataEntry: Checking access for form:', formId);\n\n              try {\n                // Check if user can access this form\n                const hasAccess = await FormFilteringService.canUserAccessForm(formId);\n\n                if (hasAccess) {\n                  console.log('✅ DataEntry: Access granted for form:', formId);\n                  navigate(category.path);\n                } else {\n                  console.log('❌ DataEntry: Access denied for form:', formId);\n                  alert('Access denied: This form is not available for your office.');\n                }\n              } catch (error) {\n                console.error('❌ DataEntry: Error checking form access:', error);\n                alert('Error checking form access. Please try again.');\n              }\n            } else {\n              // Invalid form ID, navigate anyway\n              navigate(category.path);\n            }\n          } else {\n            // Not a dynamic form, navigate normally\n            navigate(category.path);\n          }\n        }}\n      >\n        <div className=\"category-icon\" style={{ color: getIconColor(category.title) }}>\n          {React.createElement(IconComponent as React.ComponentType<any>, { size: 40 })}\n        </div>\n        <h3>{category.title || 'Unnamed Category'}</h3>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n      <div className=\"main-content\">\n        <h1 className=\"page-title\">Data Entry</h1>\n        <StatsCards />\n        <div className=\"category-grid\">\n          {categories\n            .filter(category => !category.parentId) // Only show top-level cards\n            .filter(category => category.title && category.title.trim() !== '' && category.title !== 'undefined') // Filter out undefined titles\n            .map(category => renderCard(category))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DataEntry;"], "names": ["stats", "title", "value", "StatsCards", "_jsx", "className", "children", "map", "stat", "index", "_jsxs", "DataEntry", "currentUser", "useAuth", "navigate", "useNavigate", "userData", "setUserData", "useState", "categories", "setCategories", "useEffect", "async", "userRef", "doc", "db", "uid", "userSnap", "getDoc", "exists", "data", "fetchUserData", "categoriesCollection", "collection", "categoriesData", "getDocs", "docs", "id", "path", "icon", "color", "parentId", "console", "log", "length", "organizedCategories", "organizeCards", "err", "error", "fetchCategories", "cards", "cardsWithParent", "filter", "card", "topLevel", "nested", "n", "renderCard", "category", "IconComponent", "FaEllipsisH", "normalizedTitle", "toLowerCase", "includes", "FaBriefcase", "FaLaptopCode", "FaBuilding", "FaMoneyBill", "FaPiggyBank", "FaUniversity", "FaUsers", "FaSearch", "getIconComponent", "onClick", "formId", "split", "FormFilteringService", "canUserAccessForm", "alert", "style", "getIconColor", "React", "size", "Sidebar", "trim"], "sourceRoot": ""}