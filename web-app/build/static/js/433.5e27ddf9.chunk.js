"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[433],{8814:(e,t,s)=>{s.r(t),s.d(t,{default:()=>a});var r=s(5043),i=s(215),n=s(579);const a=()=>{const[e,t]=(0,r.useState)([]),[s,a]=(0,r.useState)(!1),o=e=>{t((t=>[...t,`${(new Date).toLocaleTimeString()}: ${e}`]))};return(0,n.jsxs)("div",{style:{padding:"2rem",maxWidth:"800px",margin:"0 auto"},children:[(0,n.jsx)("h2",{children:"\ud83d\udd0d Basic Supabase Connection Test"}),(0,n.jsx)("p",{children:"This will test the most basic Supabase functionality."}),(0,n.jsxs)("div",{style:{marginBottom:"2rem"},children:[(0,n.jsx)("button",{onClick:async()=>{a(!0),t([]);try{o("\ud83d\udd0d Starting basic Supabase test..."),o("\ud83d\udce1 Testing basic connection...");try{const{data:e,error:t}=await i.N.from("simple_test_table").select("*");t?(o(`\u274c Connection failed: ${t.message}`),o(`\ud83d\udca1 Error details: ${JSON.stringify(t)}`)):(o(`\u2705 Connection successful! Found ${(null===e||void 0===e?void 0:e.length)||0} records`),e&&e.length>0&&o(`\ud83d\udcc4 Sample data: ${JSON.stringify(e[0])}`))}catch(e){o(`\ud83d\udca5 Connection error: ${e}`)}o("\ud83d\udd27 Checking configuration...");const t="https://bvxsdjbpuujegeikuipi.supabase.co",s="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ2eHNkamJwdXVqZWdlaWt1aXBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc1NTE0MDksImV4cCI6MjA2MzEyNzQwOX0.U_1GP7rHL7uGSeLAeEH6tv-8BjZOqMxXIG_DhgtVis0";o("\ud83d\udccd Supabase URL: "+(t?"Set":"Missing")),o("\ud83d\udd11 Supabase Key: "+(s?"Set":"Missing")),t&&o(`\ud83c\udf10 URL starts with: ${t.substring(0,30)}...`),o("\ud83d\udcdd Testing data insertion...");try{const{data:e,error:t}=await i.N.from("simple_test_table").insert([{message:`Test from React at ${(new Date).toISOString()}`}]).select();o(t?`\u274c Insert failed: ${t.message}`:`\u2705 Insert successful! Created record: ${JSON.stringify(e)}`)}catch(e){o(`\ud83d\udca5 Insert error: ${e}`)}o("\ud83d\udcca Testing count query...");try{const{count:e,error:t}=await i.N.from("simple_test_table").select("*",{count:"exact",head:!0});o(t?`\u274c Count failed: ${t.message}`:`\u2705 Count successful! Total records: ${e}`)}catch(e){o(`\ud83d\udca5 Count error: ${e}`)}o("\ud83c\udf89 Basic test completed!")}catch(s){o(`\ud83d\udca5 Fatal error: ${s}`)}finally{a(!1)}},disabled:s,style:{padding:"0.75rem 1.5rem",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",marginRight:"1rem"},children:s?"\ud83d\udd04 Testing...":"\ud83e\uddea Run Basic Test"}),(0,n.jsx)("button",{onClick:async()=>{a(!0),o("\ud83d\udd0d Testing reports-related tables...");const e=["dynamic_form_submissions","reports_test_data","reports_data_view"];for(const s of e)try{o(`\ud83d\udccb Testing table: ${s}`);const{count:e,error:t}=await i.N.from(s).select("*",{count:"exact",head:!0});if(t)o(`\u274c ${s}: ${t.message}`);else if(o(`\u2705 ${s}: ${e} records found`),e&&e>0){const{data:e,error:t}=await i.N.from(s).select("*").limit(1);!t&&e&&e.length>0&&o(`\ud83d\udcc4 ${s} sample: ${JSON.stringify(e[0])}`)}}catch(t){o(`\ud83d\udca5 ${s} error: ${t}`)}a(!1)},disabled:s,style:{padding:"0.75rem 1.5rem",backgroundColor:"#28a745",color:"white",border:"none",borderRadius:"4px"},children:s?"\ud83d\udd04 Testing...":"\ud83d\udcca Test Reports Tables"})]}),(0,n.jsxs)("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"4px",padding:"1rem",maxHeight:"500px",overflowY:"auto"},children:[(0,n.jsx)("h4",{children:"\ud83d\udccb Test Results:"}),0===e.length?(0,n.jsx)("p",{style:{color:"#666",fontStyle:"italic"},children:"Click a test button to start..."}):(0,n.jsx)("div",{style:{fontFamily:"monospace",fontSize:"0.875rem"},children:e.map(((e,t)=>(0,n.jsx)("div",{style:{marginBottom:"0.5rem",wordBreak:"break-word"},children:e},t)))})]}),(0,n.jsxs)("div",{style:{marginTop:"2rem",padding:"1rem",backgroundColor:"#e3f2fd",borderRadius:"4px"},children:[(0,n.jsx)("h4",{children:"\ud83d\udccb Instructions:"}),(0,n.jsxs)("ol",{children:[(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"First:"})," Run the BASIC_SUPABASE_TEST.sql script in Supabase SQL Editor"]}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Then:"}),' Click "Run Basic Test" to verify connection']}),(0,n.jsxs)("li",{children:[(0,n.jsx)("strong",{children:"Finally:"}),' Click "Test Reports Tables" to check reports data']})]}),(0,n.jsx)("h4",{children:"\u2705 Success Indicators:"}),(0,n.jsxs)("ul",{children:[(0,n.jsx)("li",{children:'\u2705 "Connection successful!" message'}),(0,n.jsx)("li",{children:'\u2705 Environment variables are "Set"'}),(0,n.jsx)("li",{children:"\u2705 Insert and count operations work"}),(0,n.jsx)("li",{children:"\u2705 At least one reports table has data"})]})]})]})}}}]);
//# sourceMappingURL=433.5e27ddf9.chunk.js.map