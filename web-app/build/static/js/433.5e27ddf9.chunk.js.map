{"version": 3, "file": "static/js/433.5e27ddf9.chunk.js", "mappings": "qLAGA,MAkNA,EAlNoCA,KAClC,MAAOC,EAASC,IAAcC,EAAAA,EAAAA,UAAmB,KAC1CC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAEjCG,EAAaC,IACjBL,GAAWM,GAAQ,IAAIA,EAAM,IAAG,IAAIC,MAAOC,yBAAyBH,MAAW,EA+HjF,OACEI,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CAAEC,QAAS,OAAQC,SAAU,QAASC,OAAQ,UAAWC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,iDACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,2DAEHL,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CAAEM,aAAc,QAASF,SAAA,EACnCC,EAAAA,EAAAA,KAAA,UACEE,QAnIaC,UACnBf,GAAW,GACXH,EAAW,IAEX,IACEI,EAAU,gDAGVA,EAAU,4CACV,IACE,MAAM,KAAEe,EAAI,MAAEC,SAAgBC,EAAAA,EAC3BC,KAAK,qBACLC,OAAO,KAENH,GACFhB,EAAU,6BAAwBgB,EAAMf,WACxCD,EAAU,+BAAqBoB,KAAKC,UAAUL,QAE9ChB,EAAU,wCAAsC,OAAJe,QAAI,IAAJA,OAAI,EAAJA,EAAMO,SAAU,aACxDP,GAAQA,EAAKO,OAAS,GACxBtB,EAAU,6BAAmBoB,KAAKC,UAAUN,EAAK,OAGvD,CAAE,MAAOQ,GACPvB,EAAU,kCAAwBuB,IACpC,CAGAvB,EAAU,0CACV,MAAMwB,EAAcC,2CACdC,EAAcD,mNAEpBzB,EAAU,+BAAoBwB,EAAc,MAAQ,YACpDxB,EAAU,+BAAoB0B,EAAc,MAAQ,YAEhDF,GACFxB,EAAU,iCAAuBwB,EAAYG,UAAU,EAAG,UAI5D3B,EAAU,0CACV,IACE,MAAM,KAAEe,EAAI,MAAEC,SAAgBC,EAAAA,EAC3BC,KAAK,qBACLU,OAAO,CAAC,CAAE3B,QAAS,uBAAsB,IAAIE,MAAO0B,mBACpDV,SAGDnB,EADEgB,EACQ,yBAAoBA,EAAMf,UAE1B,6CAAwCmB,KAAKC,UAAUN,KAErE,CAAE,MAAOQ,GACPvB,EAAU,8BAAoBuB,IAChC,CAGAvB,EAAU,uCACV,IACE,MAAM,MAAE8B,EAAK,MAAEd,SAAgBC,EAAAA,EAC5BC,KAAK,qBACLC,OAAO,IAAK,CAAEW,MAAO,QAASC,MAAM,IAGrC/B,EADEgB,EACQ,wBAAmBA,EAAMf,UAEzB,2CAAsC6B,IAEpD,CAAE,MAAOP,GACPvB,EAAU,6BAAmBuB,IAC/B,CAEAvB,EAAU,qCAEZ,CAAE,MAAOgB,GACPhB,EAAU,6BAAmBgB,IAC/B,CAAC,QACCjB,GAAW,EACb,GAsDMiC,SAAUlC,EACVQ,MAAO,CACLC,QAAS,iBACT0B,gBAAiB,UACjBC,MAAO,QACPC,OAAQ,OACRC,aAAc,MACdC,YAAa,QACb3B,SAEDZ,EAAU,0BAAkB,iCAG/Ba,EAAAA,EAAAA,KAAA,UACEE,QAjEiBC,UACvBf,GAAW,GACXC,EAAU,kDAEV,MAAMsC,EAAe,CACnB,2BACA,oBACA,qBAGF,IAAK,MAAMC,KAAaD,EACtB,IACEtC,EAAU,+BAAqBuC,KAE/B,MAAM,MAAET,EAAK,MAAEd,SAAgBC,EAAAA,EAC5BC,KAAKqB,GACLpB,OAAO,IAAK,CAAEW,MAAO,QAASC,MAAM,IAEvC,GAAIf,EACFhB,EAAU,UAAKuC,MAAcvB,EAAMf,gBAKnC,GAHAD,EAAU,UAAKuC,MAAcT,mBAGzBA,GAASA,EAAQ,EAAG,CACtB,MAAQf,KAAMyB,EAAYxB,MAAOyB,SAAsBxB,EAAAA,EACpDC,KAAKqB,GACLpB,OAAO,KACPuB,MAAM,IAEJD,GAAeD,GAAcA,EAAWlB,OAAS,GACpDtB,EAAU,gBAAMuC,aAAqBnB,KAAKC,UAAUmB,EAAW,MAEnE,CAEJ,CAAE,MAAOjB,GACPvB,EAAU,gBAAMuC,YAAoBhB,IACtC,CAGFxB,GAAW,EAAM,EA0BXiC,SAAUlC,EACVQ,MAAO,CACLC,QAAS,iBACT0B,gBAAiB,UACjBC,MAAO,QACPC,OAAQ,OACRC,aAAc,OACd1B,SAEDZ,EAAU,0BAAkB,yCAIjCO,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CACV2B,gBAAiB,UACjBE,OAAQ,oBACRC,aAAc,MACd7B,QAAS,OACToC,UAAW,QACXC,UAAW,QACXlC,SAAA,EACAC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,+BACgB,IAAnBf,EAAQ2B,QACPX,EAAAA,EAAAA,KAAA,KAAGL,MAAO,CAAE4B,MAAO,OAAQW,UAAW,UAAWnC,SAAC,qCAElDC,EAAAA,EAAAA,KAAA,OAAKL,MAAO,CAAEwC,WAAY,YAAaC,SAAU,YAAarC,SAC3Df,EAAQqD,KAAI,CAACC,EAAQC,KACpBvC,EAAAA,EAAAA,KAAA,OAAiBL,MAAO,CAAEM,aAAc,SAAUuC,UAAW,cAAezC,SACzEuC,GADOC,WAQlB7C,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CAAE8C,UAAW,OAAQ7C,QAAS,OAAQ0B,gBAAiB,UAAWG,aAAc,OAAQ1B,SAAA,EAClGC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,gCACJL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACEL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,WAAe,qEAC3BL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,UAAc,mDAC1BL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aAAiB,4DAG/BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,gCACJL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACEC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,6CACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,4CACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,6CACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,uDAGJ,C", "sources": ["components/Reports/BasicSupabaseTest.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { supabase } from '../../config/supabaseClient';\n\nconst BasicSupabaseTest: React.FC = () => {\n  const [results, setResults] = useState<string[]>([]);\n  const [loading, setLoading] = useState(false);\n\n  const addResult = (message: string) => {\n    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);\n  };\n\n  const runBasicTest = async () => {\n    setLoading(true);\n    setResults([]);\n    \n    try {\n      addResult('🔍 Starting basic Supabase test...');\n\n      // Test 1: Simple query\n      addResult('📡 Testing basic connection...');\n      try {\n        const { data, error } = await supabase\n          .from('simple_test_table')\n          .select('*');\n        \n        if (error) {\n          addResult(`❌ Connection failed: ${error.message}`);\n          addResult(`💡 Error details: ${JSON.stringify(error)}`);\n        } else {\n          addResult(`✅ Connection successful! Found ${data?.length || 0} records`);\n          if (data && data.length > 0) {\n            addResult(`📄 Sample data: ${JSON.stringify(data[0])}`);\n          }\n        }\n      } catch (err) {\n        addResult(`💥 Connection error: ${err}`);\n      }\n\n      // Test 2: Check environment variables\n      addResult('🔧 Checking configuration...');\n      const supabaseUrl = process.env.REACT_APP_SUPABASE_URL;\n      const supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;\n      \n      addResult(`📍 Supabase URL: ${supabaseUrl ? 'Set' : 'Missing'}`);\n      addResult(`🔑 Supabase Key: ${supabaseKey ? 'Set' : 'Missing'}`);\n      \n      if (supabaseUrl) {\n        addResult(`🌐 URL starts with: ${supabaseUrl.substring(0, 30)}...`);\n      }\n\n      // Test 3: Try to create a record\n      addResult('📝 Testing data insertion...');\n      try {\n        const { data, error } = await supabase\n          .from('simple_test_table')\n          .insert([{ message: `Test from React at ${new Date().toISOString()}` }])\n          .select();\n        \n        if (error) {\n          addResult(`❌ Insert failed: ${error.message}`);\n        } else {\n          addResult(`✅ Insert successful! Created record: ${JSON.stringify(data)}`);\n        }\n      } catch (err) {\n        addResult(`💥 Insert error: ${err}`);\n      }\n\n      // Test 4: Count records\n      addResult('📊 Testing count query...');\n      try {\n        const { count, error } = await supabase\n          .from('simple_test_table')\n          .select('*', { count: 'exact', head: true });\n        \n        if (error) {\n          addResult(`❌ Count failed: ${error.message}`);\n        } else {\n          addResult(`✅ Count successful! Total records: ${count}`);\n        }\n      } catch (err) {\n        addResult(`💥 Count error: ${err}`);\n      }\n\n      addResult('🎉 Basic test completed!');\n\n    } catch (error) {\n      addResult(`💥 Fatal error: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testReportsTable = async () => {\n    setLoading(true);\n    addResult('🔍 Testing reports-related tables...');\n\n    const tablesToTest = [\n      'dynamic_form_submissions',\n      'reports_test_data',\n      'reports_data_view'\n    ];\n\n    for (const tableName of tablesToTest) {\n      try {\n        addResult(`📋 Testing table: ${tableName}`);\n        \n        const { count, error } = await supabase\n          .from(tableName)\n          .select('*', { count: 'exact', head: true });\n        \n        if (error) {\n          addResult(`❌ ${tableName}: ${error.message}`);\n        } else {\n          addResult(`✅ ${tableName}: ${count} records found`);\n          \n          // If records exist, try to fetch one\n          if (count && count > 0) {\n            const { data: sampleData, error: sampleError } = await supabase\n              .from(tableName)\n              .select('*')\n              .limit(1);\n            \n            if (!sampleError && sampleData && sampleData.length > 0) {\n              addResult(`📄 ${tableName} sample: ${JSON.stringify(sampleData[0])}`);\n            }\n          }\n        }\n      } catch (err) {\n        addResult(`💥 ${tableName} error: ${err}`);\n      }\n    }\n\n    setLoading(false);\n  };\n\n  return (\n    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>\n      <h2>🔍 Basic Supabase Connection Test</h2>\n      <p>This will test the most basic Supabase functionality.</p>\n      \n      <div style={{ marginBottom: '2rem' }}>\n        <button \n          onClick={runBasicTest}\n          disabled={loading}\n          style={{ \n            padding: '0.75rem 1.5rem', \n            backgroundColor: '#007bff', \n            color: 'white', \n            border: 'none', \n            borderRadius: '4px',\n            marginRight: '1rem'\n          }}\n        >\n          {loading ? '🔄 Testing...' : '🧪 Run Basic Test'}\n        </button>\n        \n        <button \n          onClick={testReportsTable}\n          disabled={loading}\n          style={{ \n            padding: '0.75rem 1.5rem', \n            backgroundColor: '#28a745', \n            color: 'white', \n            border: 'none', \n            borderRadius: '4px'\n          }}\n        >\n          {loading ? '🔄 Testing...' : '📊 Test Reports Tables'}\n        </button>\n      </div>\n\n      <div style={{ \n        backgroundColor: '#f8f9fa', \n        border: '1px solid #dee2e6', \n        borderRadius: '4px', \n        padding: '1rem',\n        maxHeight: '500px',\n        overflowY: 'auto'\n      }}>\n        <h4>📋 Test Results:</h4>\n        {results.length === 0 ? (\n          <p style={{ color: '#666', fontStyle: 'italic' }}>Click a test button to start...</p>\n        ) : (\n          <div style={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>\n            {results.map((result, index) => (\n              <div key={index} style={{ marginBottom: '0.5rem', wordBreak: 'break-word' }}>\n                {result}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#e3f2fd', borderRadius: '4px' }}>\n        <h4>📋 Instructions:</h4>\n        <ol>\n          <li><strong>First:</strong> Run the BASIC_SUPABASE_TEST.sql script in Supabase SQL Editor</li>\n          <li><strong>Then:</strong> Click \"Run Basic Test\" to verify connection</li>\n          <li><strong>Finally:</strong> Click \"Test Reports Tables\" to check reports data</li>\n        </ol>\n        \n        <h4>✅ Success Indicators:</h4>\n        <ul>\n          <li>✅ \"Connection successful!\" message</li>\n          <li>✅ Environment variables are \"Set\"</li>\n          <li>✅ Insert and count operations work</li>\n          <li>✅ At least one reports table has data</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default BasicSupabaseTest;\n"], "names": ["BasicSupabaseTest", "results", "setResults", "useState", "loading", "setLoading", "addResult", "message", "prev", "Date", "toLocaleTimeString", "_jsxs", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "children", "_jsx", "marginBottom", "onClick", "async", "data", "error", "supabase", "from", "select", "JSON", "stringify", "length", "err", "supabaseUrl", "process", "supabase<PERSON>ey", "substring", "insert", "toISOString", "count", "head", "disabled", "backgroundColor", "color", "border", "borderRadius", "marginRight", "tablesToTest", "tableName", "sampleData", "sampleError", "limit", "maxHeight", "overflowY", "fontStyle", "fontFamily", "fontSize", "map", "result", "index", "wordBreak", "marginTop"], "sourceRoot": ""}