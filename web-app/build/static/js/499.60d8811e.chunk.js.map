{"version": 3, "file": "static/js/499.60d8811e.chunk.js", "mappings": "wKAEO,SAASA,EAA4BC,GAC1C,OAAOC,EAAAA,EAAAA,IAAqB,kBAAmBD,EACjD,CACA,MACA,GAD4BE,EAAAA,EAAAA,GAAuB,kBAAmB,CAAC,OAAQ,YAAa,QAAS,QAAS,UAAW,a,+DCFzH,MAAMC,EAAQ,CACZ,CAAEC,MAAO,cAAeC,MAAO,QAC/B,CAAED,MAAO,aAAcC,MAAO,gBAC9B,CAAED,MAAO,oBAAqBC,MAAO,MACrC,CAAED,MAAO,MAAOC,MAAO,oBAgBzB,EAb6BC,KAEzBC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,SACxBN,EAAMO,KAAI,CAACC,EAAMC,KAChBC,EAAAA,EAAAA,MAAA,OAAKL,UAAW,kBAAkBI,IAAQH,SAAA,EACxCF,EAAAA,EAAAA,KAAA,MAAAE,SAAKE,EAAKP,SACVG,EAAAA,EAAAA,KAAA,KAAGC,UAAU,aAAYC,SAAEE,EAAKN,UAFcO,M,kECZjD,SAASE,EAAuBd,GACrC,OAAOC,EAAAA,EAAAA,IAAqB,aAAcD,EAC5C,CACA,MACA,GADuBE,EAAAA,EAAAA,GAAuB,aAAc,CAAC,OAAQ,WAAY,YAAa,QAAS,SAAU,WAAY,QAAS,WAAY,eAAgB,uBAAwB,iBAAkB,gBAAiB,UAAW,mB,mICKxO,MAkBA,EAlBoCa,IAA2C,IAA1C,OAAEC,EAAM,QAAEC,EAAO,MAAEb,EAAK,SAAEK,GAAUM,EACvE,OAAKC,GAGHT,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAgBU,QAASD,EAAQR,UAC9CI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,gBAAgBU,QAASC,GAAKA,EAAEC,kBAAkBX,SAAA,EAC/DI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,KAAA,MAAAE,SAAKL,KACLG,EAAAA,EAAAA,KAAA,UAAQC,UAAU,eAAeU,QAASD,EAAQR,SAAC,aAErDF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,aAAYC,SACxBA,SAVW,IAaZ,E,cCpBH,MAeMY,EAAaA,CAACC,EAAgBC,KACzC,MAAMC,EAAOD,EAAcE,MAAKC,GAAKA,EAAEC,KAAOL,IAC9C,QAAOE,IAAQA,EAAKI,QAAgB,EAIzBC,EAAaA,CAACP,EAAgBC,KACjCA,EAAcO,MAAKJ,GAAKA,EAAEE,WAAaN,IAIpCS,EAAiBC,IAC5B,MAAMtB,EAAmC,CAAC,EACpCuB,EAAoB,GAW1B,OAVAD,EAAKE,SAAQC,IACXzB,EAAIyB,EAAKR,IAAM,IAAKQ,EAAM1B,SAAU,GAAI,IAE1CuB,EAAKE,SAAQC,IAC+B,IAADC,EAArCD,EAAKP,UAAYlB,EAAIyB,EAAKP,UACD,QAA3BQ,EAAA1B,EAAIyB,EAAKP,UAAUnB,gBAAQ,IAAA2B,GAA3BA,EAA6BC,KAAK3B,EAAIyB,EAAKR,KAE3CM,EAAMI,KAAK3B,EAAIyB,EAAKR,IACtB,IAEKM,CAAK,EAIDK,EAAsBA,CAACV,EAAkBL,KACpD,IAAIgB,EAAwB,GAC5B,MAAM9B,EAAWc,EAAciB,QAAOd,GAAKA,EAAEE,WAAaA,IAC1D,IAAK,MAAMa,KAAShC,EAClB8B,EAAYF,KAAKI,EAAMd,IACvBY,EAAcA,EAAYG,OAAOJ,EAAoBG,EAAMd,GAAIJ,IAEjE,OAAOgB,CAAW,ECxBPI,EAAqBC,IAChC,MAAM,WACJC,EAAU,cACVC,EAAa,aACbC,EAAY,gBACZC,EAAe,UACfC,EAAS,aACTC,EAAY,aACZC,EAAY,gBACZC,EAAe,WACfC,EAAU,cACVC,EAAa,aACbC,EAAY,SACZC,EAAQ,WACRC,EAAU,oBACVC,EAAmB,mBACnBC,EAAkB,cAClBC,EAAa,UACbC,EAAS,eACTC,EAAc,iBACdC,EAAgB,gBAChBC,EAAe,0BACfC,GACErB,EAEEsB,GAAkBC,EAAAA,EAAAA,cAAYC,UAClCb,GAAa,GACb,IACE,MACMc,SADsBC,EAAAA,EAAAA,KAAQC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,gBACXC,KAAK/D,KAAIgE,IAAG,CAAO/C,GAAI+C,EAAI/C,MAAO+C,EAAIC,WAC9E7B,EAAcuB,EAChB,CAAE,MAAOO,GACPpB,EAAS,+BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,IACC,CAACT,EAAeS,EAAcC,IA+IjC,MAAO,CACLU,kBACAa,iBAzIuBX,UACvB,IAAKnB,IAAcE,EAEjB,YADAK,EAAS,qCAGXD,GAAa,GAEb,QAbuBa,WACvB,MAAMY,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc7C,GAErC,aADsBsD,EAAAA,EAAAA,IAAOD,IACdE,QAAQ,EASGC,CAAiBlC,GAIzC,OAFAO,EAAS,+DACTD,GAAa,GAGfA,GAAa,GACbG,GAAoB,EAAK,EA6HzB0B,oBA1H0BhB,UAAa,IAADiB,EACtC,IAAKpC,IAAcE,EAGf,OAFAK,EAAS,6CACTE,GAAoB,GAGxB,IAAI4B,EAA+B,KAChB,qBAAfjC,GAAqCN,EACvCuC,EAAgBvC,EACQ,qBAAfM,EACTiC,EAAgB,KACPvC,GAA+B,qBAAfM,EACvBiC,EAAgBvC,EACRA,GAA+B,qBAAfM,IACxBiC,EAAgB,MAGpB,MACMC,EAAU,GADGD,EAA4D,QAA/CD,EAAGxC,EAAWpB,MAAKC,GAAKA,EAAEC,KAAO2D,WAAc,IAAAD,OAAA,EAA5CA,EAA8CG,KAAO,iBACvDvC,IAAYwC,QAAQ,OAAQ,KAE7D,IACElC,GAAa,GACbG,GAAoB,GACpB,MAAMgC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcvB,IAC9B0C,KAAMC,EAAeC,MAAOC,GD/GR1F,KAChC,MAAM2F,EAAO3F,EACV4F,MAAM,IACNC,QAAO,CAACC,EAAKC,IAASD,EAAMC,EAAKC,WAAW,IAAI,GAE7CC,EAAQ,CAACC,EAAAA,IAAUC,EAAAA,IAAWC,EAAAA,IAAOC,EAAAA,KACrCC,EAAS,CAAC,UAAW,UAAW,UAAW,UAAW,WAK5D,MAAO,CAAEf,KAHIU,EAAMN,EAAOM,EAAMM,QAGjBd,MAFDa,EAAOX,EAAOW,EAAOC,QAEb,ECoGqCC,CAAkBzD,SAEnE0D,EAAAA,EAAAA,IAAOnB,EAAS,CACpB/D,GAAIsB,EACJ7C,MAAO+C,EACPqC,KAAMD,EACN3D,SAAU0D,EACVwB,aAAa,IAAIC,MAAOC,cACxBrB,KAAMC,EAAcqB,KACpBpB,MAAOC,EACPoB,OAAQ,GACRC,QAAQ,EACRC,OAAQnE,UAGJiB,IAENhB,EAAa,IACbE,EAAgB,IAChBO,GAAmB,GACnBL,EAAc,IACdN,EAAgBC,GAChBQ,EAAW,WAAWN,qCACtBkE,YAAW,IAAM5D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPpB,EAAS,yDACTqB,QAAQC,MAAM,uBAAwBF,EACxC,CAAC,QACCrB,GAAa,EACf,GAqEA+D,eAlEsB9F,IACtBsC,EAAetC,GACf4B,EAAgB5B,EAAKpB,OACrB2D,GAAiB,EAAK,EAgEtBwD,iBA7DuBnD,UACvB,MAAMoD,EAAc3E,EAAWpB,MAAKC,GAAKA,EAAEC,KAAOoB,IAClD,GAAKyE,GAAgBrE,EACrB,IACEI,GAAa,GACb,MAAMmC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcgD,EAAY7F,UAC5C8F,EAAAA,EAAAA,IAAU/B,EAAS,CAAEtF,MAAO+C,EAAc2D,aAAa,IAAIC,MAAOC,sBAClE9C,IACNH,GAAiB,GACjBD,EAAe,MACfV,EAAgB,IAChBK,EAAW,gCACX4D,YAAW,IAAM5D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,GA4CAmE,kBAzCyBpG,IACzB0C,EAAgB1C,GAChB2C,GAA0B,EAAK,EAwC/B0D,oBArC0BvD,UAC1B,GAAKrB,EAAL,CACAQ,GAAa,GACb,IACE,MAAMqE,GAAQC,EAAAA,EAAAA,IAAWrD,EAAAA,IACnBsD,EAAiBxF,EAAoBS,EAAcF,GACnDkF,EAAc,CAAChF,KAAiB+E,GAEtC,IAAK,MAAMnG,KAAMoG,EACfH,EAAMI,QAAOtD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc7C,IACnCiG,EAAMI,QAAOtD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAAS7C,UAE1BiG,EAAMK,eACN/D,IAEND,GAA0B,GAC1BD,EAAgB,MAChBhB,EAAgB,IAChBY,EAAc,MACdC,EAAU,IACVJ,EAAW,yDACX4D,YAAW,IAAM5D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,CA1ByB,CA0BzB,EAWD,E,cC1LI,MC2DP,EA5EkDxC,IAS3C,IAT4C,WACjD8B,EAAU,aACVE,EAAY,aACZmF,EAAY,WACZ7E,EAAU,eACV8E,EAAc,UACdC,EAAS,eACTC,EAAc,gBACdC,GACDvH,EACC,MAAMwH,EAAoB,SAACC,GAAwD,IAArCC,EAAKC,UAAA/B,OAAA,QAAAgC,IAAAD,UAAA,GAAAA,UAAA,GAAG,EACpD,OAAOF,EAAMI,SAAQpH,GAAQ,EAC3BjB,EAAAA,EAAAA,KAAA,UAAsBF,MAAOmB,EAAKG,GAAIkH,MAAO,CAAEC,YAAwB,GAARL,EAAH,MAAoBhI,SAC7E,GAAG,KAAKsI,OAAON,MAAUjH,EAAKpB,SADpBoB,EAAKG,OAGdH,EAAKf,UAAYe,EAAKf,SAASkG,OAAS,EAAI4B,EAAkB/G,EAAKf,SAAUgI,EAAQ,GAAK,KAElG,EAkBA,OACE5H,EAAAA,EAAAA,MAAA,OAAKL,UAAU,gBAAeC,SAAA,EAC5BI,EAAAA,EAAAA,MAAA,UACER,MAAO0C,EACPiG,SApBoB7H,IACxB,MAAM8H,EAAkB9H,EAAE+H,OAAO7I,MACjC6H,EAAae,EAAgB,EAmBzBzI,UAAU,cACV2I,SAAUf,EAAU3H,SAAA,EAEpBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,GAAEI,SAAE2H,EAAY,qBAAuB,gCACpDG,EAAkBxG,EAAcc,QAGnCtC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,4BAA2BC,UACxCI,EAAAA,EAAAA,MAAA,UACER,MAAOgD,EACP2F,SA1BoB7H,IAC1B,MAAMiI,EAAYjI,EAAE+H,OAAO7I,MAC3B8H,EAAeiB,GAEG,qBAAdA,GAAkD,qBAAdA,EACtCf,IACuB,kBAAde,GACTd,GACF,EAmBM9H,UAAU,8BAA6BC,SAAA,EAEvCF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,GAAEI,SAAC,sBACjBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,mBAAmB8I,WAAYpG,EAAatC,SAAC,2BAG1DsC,IACClC,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,EACEF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,mBAAkBI,SAAC,0BAGjCF,EAAAA,EAAAA,KAAA,UACEF,MAAM,gBACN8I,UAAWtH,EAAWkB,EAAcF,IAAexB,EAAW0B,EAAcF,GAAYpC,SACzF,mDAOL,ECzCV,EAnCsDM,IAK/C,IALgD,aACrDgC,EAAY,WACZF,EAAU,WACVyG,EAAU,aACVC,GACDxI,EACC,MAAMyI,EAAmB3G,EAAWpB,MAAKC,GAAKA,EAAEC,KAAOoB,IAEvD,OAAKyG,GAKH3I,EAAAA,EAAAA,MAAA,OAAKL,UAAU,kBAAiBC,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,oBAAkB+I,EAAiBpJ,MAAM,QAC7CS,EAAAA,EAAAA,MAAA,OAAKL,UAAU,eAAcC,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMoI,EAAWE,GAC1BhJ,UAAU,kDACV2I,UAAWpG,EAAatC,SAAA,CAEvBgJ,EAAAA,cAAoBC,EAAAA,KAAoC,iBAE3D7I,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMqI,EAAaxG,GAC5BvC,UAAU,8CACV2I,UAAWpG,EAAatC,SAAA,CAEvBgJ,EAAAA,cAAoBE,EAAAA,KAAqC,0BAnBzD,IAsBD,ECyNV,EAxPwD5I,IAKjD,IAAD6I,EAAA,IALmD,MACvDC,EAAK,MACLjJ,EAAK,SACLkJ,EAAQ,SACRC,GACDhJ,EACC,MAAMiJ,EAAqBA,CAACC,EAAkB5J,EAAe6J,KAC3D,MAAMC,EAAa,IAAKN,EAAMO,SAAW,IACzCD,EAAWF,GAAY,IAAKE,EAAWF,GAAW,CAACC,GAAM7J,GACzDyJ,EAASlJ,EAAO,IAAKiJ,EAAOO,QAASD,GAAa,EAa9CE,EAA4BlJ,IAChC,MAAM,MAAEd,EAAK,KAAEiK,GAASnJ,EAAE+H,OAC1B,IAAIqB,EAAuBlK,EACd,aAATiK,IACFC,EAAmBpJ,EAAE+H,OAA4BsB,SAEnDV,EAASlJ,EAAO,IAAKiJ,EAAOY,aAAcF,GAAkB,EAG9D,OACE1J,EAAAA,EAAAA,MAAA,OAAKL,UAAU,8BAA6BC,SAAA,EAC1CI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,gEAA+DC,SAAA,EAC5EF,EAAAA,EAAAA,KAAA,UAAAE,SAASoJ,EAAMa,OAAS,kBAAyB,KAAGb,EAAMS,KAAK,KAC/DzJ,EAAAA,EAAAA,MAAA,UAAQK,QAASA,IAAM6I,EAASnJ,GAAQJ,UAAU,wBAAuBC,SAAA,CACtEgJ,EAAAA,cAAoBE,EAAAA,KAAqC,iBAG9D9I,EAAAA,EAAAA,MAAA,OAAKL,UAAU,YAAWC,SAAA,EAExBI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,cAAc/J,IAASJ,UAAU,aAAYC,SAAC,YAC9DI,EAAAA,EAAAA,MAAA,UACEc,GAAI,cAAcf,IAClBJ,UAAU,eACVH,MAAOwJ,EAAMS,KACbtB,SAAW7H,GAAM2I,EAASlJ,EAAO,IAC5BiJ,EACHS,KAAMnJ,EAAE+H,OAAO7I,MACf+J,QAAwB,aAAfP,EAAMS,MAAsC,UAAfT,EAAMS,MAAmC,mBAAfT,EAAMS,UAA4B3B,EAAYkB,EAAMO,QACpHQ,YAA4B,YAAff,EAAMS,MAAqC,WAAfT,EAAMS,UAAoB3B,EAAYkB,EAAMe,cACpFnK,SAAA,EAEHF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,OAAMI,SAAC,UACrBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,WAAUI,SAAC,cACzBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,SAAQI,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,OAAMI,SAAC,UACrBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,WAAUI,SAAC,cACzBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,QAAOI,SAAC,iBACtBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,WAAUI,SAAC,uBACzBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,iBAAgBI,SAAC,oBAC/BF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,SAAQI,SAAC,YACvBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,OAAMI,SAAC,iBACrBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,UAASI,SAAC,oBACxBF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,SAAQI,SAAC,kBAI3BI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,eAAe/J,IAASJ,UAAU,aAAYC,SAAC,aAC/DF,EAAAA,EAAAA,KAAA,SACEoB,GAAI,eAAef,IACnB0J,KAAK,OACL9J,UAAU,eACVH,MAAOwJ,EAAMa,MACb1B,SAAW7H,GAAM2I,EAASlJ,EAAO,IAAIiJ,EAAOa,MAAOvJ,EAAE+H,OAAO7I,QAC5DwK,UAAQ,OAIX,CAAC,OAAQ,WAAY,SAAU,QAAQC,SAASjB,EAAMS,QACrDzJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,qBAAqB/J,IAASJ,UAAU,aAAYC,SAAC,mBACrEF,EAAAA,EAAAA,KAAA,SACEoB,GAAI,qBAAqBf,IACzB0J,KAAK,OACL9J,UAAU,eACVH,MAAOwJ,EAAMe,aAAe,GAC5B5B,SAAW7H,GAAM2I,EAASlJ,EAAO,IAAIiJ,EAAOe,YAAazJ,EAAE+H,OAAO7I,aAKxD,WAAfwJ,EAAMS,OACLzJ,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,EACEI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,aAAa/J,IAASJ,UAAU,aAAYC,SAAC,iBAC7DF,EAAAA,EAAAA,KAAA,SACEoB,GAAI,aAAaf,IACjB0J,KAAK,SACL9J,UAAU,eACVH,WAAqBsI,IAAdkB,EAAMkB,IAAoB,GAAKlB,EAAMkB,IAC5C/B,SAAW7H,GAAM2I,EAASlJ,EAAO,IAAIiJ,EAAOkB,IAAwB,KAAnB5J,EAAE+H,OAAO7I,WAAesI,EAAYqC,WAAW7J,EAAE+H,OAAO7I,eAG7GQ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,aAAa/J,IAASJ,UAAU,aAAYC,SAAC,iBAC7DF,EAAAA,EAAAA,KAAA,SACEoB,GAAI,aAAaf,IACjB0J,KAAK,SACL9J,UAAU,eACVH,WAAqBsI,IAAdkB,EAAMoB,IAAoB,GAAKpB,EAAMoB,IAC5CjC,SAAW7H,GAAM2I,EAASlJ,EAAO,IAAIiJ,EAAOoB,IAAwB,KAAnB9J,EAAE+H,OAAO7I,WAAesI,EAAYqC,WAAW7J,EAAE+H,OAAO7I,iBAMhH,CAAC,WAAY,QAAS,kBAAkByK,SAASjB,EAAMS,QACtDzJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,kCAAiCC,SAAA,EAC9CF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,cAChB,QADiCmJ,EAC9CC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAelJ,KAAI,CAACwK,EAAKjB,KACxBpJ,EAAAA,EAAAA,MAAA,OAAoBL,UAAU,mBAAkBC,SAAA,EAC9CF,EAAAA,EAAAA,KAAA,SACE+J,KAAK,OACL9J,UAAU,eACVoK,YAAY,eACZvK,MAAO6K,EAAIR,MACX1B,SAAW7H,GAAM6I,EAAmBC,EAAU9I,EAAE+H,OAAO7I,MAAO,YAEhEE,EAAAA,EAAAA,KAAA,SACE+J,KAAK,OACL9J,UAAU,eACVoK,YAAY,eACZvK,MAAO6K,EAAI7K,MACX2I,SAAW7H,GAAM6I,EAAmBC,EAAU9I,EAAE+H,OAAO7I,MAAO,YAEhEE,EAAAA,EAAAA,KAAA,UAAQ+J,KAAK,SAASpJ,QAASA,IAzHvB+I,KAAsB,IAADkB,EACzC,MAAMhB,EAA0B,QAAhBgB,EAAGtB,EAAMO,eAAO,IAAAe,OAAA,EAAbA,EAAe3I,QAAO,CAAC4I,EAAGC,IAAMA,IAAMpB,IACzDH,EAASlJ,EAAO,IAAKiJ,EAAOO,QAASD,GAAa,EAuHDmB,CAAarB,GAAWzJ,UAAU,yBAAwBC,SAAC,aAfxFwJ,MAoBZ1J,EAAAA,EAAAA,KAAA,UAAQ+J,KAAK,SAASpJ,QAnIdqK,KAChB,MAAMpB,EAAa,IAAKN,EAAMO,SAAW,GAAK,CAAEM,MAAO,GAAIrK,MAAO,KAClEyJ,EAASlJ,EAAO,IAAKiJ,EAAOO,QAASD,GAAa,EAiIA3J,UAAU,2BAA0BC,SAAC,kBAOlF,CAAC,OAAQ,WAAY,SAAU,QAAQqK,SAASjB,EAAMS,QACnDzJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,uBAAuB/J,IAASJ,UAAU,aAAYC,SAAC,qBACvEF,EAAAA,EAAAA,KAAA,SACIoB,GAAI,uBAAuBf,IAC3B0J,KAAqB,WAAfT,EAAMS,KAAoB,SAA0B,SAAfT,EAAMS,KAAkB,OAAS,OAC5E9J,UAAU,eACVH,WAA8BsI,IAAvBkB,EAAMY,aAA6B,GAAKe,OAAO3B,EAAMY,cAC5DzB,SAAUqB,QAKL,aAAfR,EAAMS,MAAsC,WAAfT,EAAMS,QACjCzJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,wBAAuBC,SAAA,EAClCF,EAAAA,EAAAA,KAAA,SACIoB,GAAI,uBAAuBf,IAC3B0J,KAAK,WACL9J,UAAU,mBACVgK,QAASiB,QAAQ5B,EAAMY,cACvBzB,SAAUqB,KAEd9J,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,uBAAuB/J,IAASJ,UAAU,mBAAkBC,SAAC,yBAIpF,CAAC,WAAY,SAASqK,SAASjB,EAAMS,OAAST,EAAMO,SAAWP,EAAMO,QAAQzD,OAAS,IAClF9F,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACxBF,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,uBAAuB/J,IAASJ,UAAU,aAAYC,SAAC,qBACvEI,EAAAA,EAAAA,MAAA,UACIc,GAAI,uBAAuBf,IAC3BJ,UAAU,eACVH,WAA8BsI,IAAvBkB,EAAMY,aAA6B,GAAKe,OAAO3B,EAAMY,cAC5DzB,SAAUqB,EAAyB5J,SAAA,EAEnCF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,GAAEI,SAAC,yBAChBoJ,EAAMO,QAAQ1J,KAAIwK,IAAO3K,EAAAA,EAAAA,KAAA,UAAwBF,MAAO6K,EAAI7K,MAAMI,SAAEyK,EAAIR,OAAlCQ,EAAI7K,eAKvC,mBAAfwJ,EAAMS,OACHzJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACvBF,EAAAA,EAAAA,KAAA,SAAOC,UAAU,aAAYC,SAAC,wCAC9BF,EAAAA,EAAAA,KAAA,SACI+J,KAAK,OACL9J,UAAU,eACVH,MAAOqL,MAAMC,QAAQ9B,EAAMY,cAAgBZ,EAAMY,aAAamB,KAAK,KAAO,GAC1E5C,SAAW7H,GAAM2I,EAASlJ,EAAO,IAAIiJ,EAAOY,aAActJ,EAAE+H,OAAO7I,MAAM2F,MAAM,KAAKtF,KAAImL,GAAKA,EAAEC,SAAQtJ,QAAOqJ,GAAKA,MACnHjB,YAAY,qBAKR,WAAff,EAAMS,OACLzJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,qBAAqB/J,IAASJ,UAAU,aAAYC,SAAC,mBACrEF,EAAAA,EAAAA,KAAA,SACEoB,GAAI,qBAAqBf,IACzB0J,KAAK,OACL9J,UAAU,eACVH,MAAOwJ,EAAMkC,YAAc,GAC3B/C,SAAW7H,GAAM2I,EAASlJ,EAAO,IAAIiJ,EAAOkC,WAAY5K,EAAE+H,OAAO7I,aAKvD,YAAfwJ,EAAMS,OACLzJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,uBAAuB/J,IAASJ,UAAU,aAAYC,SAAC,qBACvEF,EAAAA,EAAAA,KAAA,SACEoB,GAAI,uBAAuBf,IAC3B0J,KAAK,OACL9J,UAAU,eACVH,MAAOwJ,EAAMmC,cAAgB,GAC7BhD,SAAW7H,GAAM2I,EAASlJ,EAAO,IAAIiJ,EAAOmC,aAAc7K,EAAE+H,OAAO7I,cAMvE,CAAC,SAAU,WAAWyK,SAASjB,EAAMS,QACrCzJ,EAAAA,EAAAA,MAAA,OAAKL,UAAU,wBAAuBC,SAAA,EACpCF,EAAAA,EAAAA,KAAA,SACEoB,GAAI,kBAAkBf,IACtB0J,KAAK,WACL9J,UAAU,mBACVgK,UAAWX,EAAMgB,SACjB7B,SAAW7H,GAAM2I,EAASlJ,EAAO,IAAIiJ,EAAOgB,SAAU1J,EAAE+H,OAAOsB,aAEjEjK,EAAAA,EAAAA,KAAA,SAAOoK,QAAS,kBAAkB/J,IAASJ,UAAU,mBAAkBC,SAAC,sBAI1E,EC/LV,EAhD8DM,IASvD,IATwD,WAC7DkL,EAAU,OACV/E,EAAM,WACNgF,EAAU,cACVC,EAAa,cACbC,EAAa,OACbC,EAAM,UACNC,EAAS,QACTC,GACDxL,EACC,OACEF,EAAAA,EAAAA,MAAA,OAAKL,UAAU,kBAAiBC,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,2BAAyBwL,EAAW7L,UAExCG,EAAAA,EAAAA,KAAA,MAAAE,SAAI,yBACHyG,EAAOxG,KAAI,CAACmJ,EAAOjJ,KAClBL,EAAAA,EAAAA,KAACiM,EAAe,CAEd3C,MAAOA,EACPjJ,MAAOA,EACPkJ,SAAUqC,EACVpC,SAAUqC,GAJLvC,EAAMlI,IAAMf,MAQrBC,EAAAA,EAAAA,MAAA,UAAQK,QAASgL,EAAY1L,UAAU,oBAAmBC,SAAA,CACvDgJ,EAAAA,cAAoBgD,EAAAA,KAAoC,iBAG3D5L,EAAAA,EAAAA,MAAA,UACEK,QAASmL,EACT7L,UAAU,4BACV2I,SAAUoD,IAAYN,GAAgC,IAAlB/E,EAAOP,OAAalG,SAAA,CAEvDgJ,EAAAA,cAAoBiD,EAAAA,KAAoC,IAAEH,EAAU,YAAc,8BAGrFhM,EAAAA,EAAAA,KAAA,UACEW,QAASoL,EACT9L,UAAU,8BACV2I,UAAW8C,GAAgC,IAAlB/E,EAAOP,OAAalG,SAC9C,mBAGG,ECuCGkM,EAAwC,CACnD,CAAEtM,MAAO,QAASqK,MAAO,SACzB,CAAErK,MAAO,SAAUqK,MAAO,UAC1B,CAAErK,MAAO,UAAWqK,MAAO,Y,cCxFtB,MCkJP,EA/I0D3J,IAQnD,IARoD,GACzDY,EAAE,MACF+I,EAAK,QACLN,EAAO,eACPwC,EAAc,SACd5D,EAAQ,SACRG,GAAW,EAAK,YAChByB,EAAc,wBACf7J,EACC,MAAOC,EAAQ6L,IAAaC,EAAAA,EAAAA,WAAS,GAC/BC,GAAcC,EAAAA,EAAAA,QAAuB,OAG3CC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAsBC,IACtBJ,EAAYK,UAAYL,EAAYK,QAAQC,SAASF,EAAMjE,SAC7D2D,GAAU,EACZ,EAIF,OADAS,SAASC,iBAAiB,YAAaL,GAChC,KACLI,SAASE,oBAAoB,YAAaN,EAAmB,CAC9D,GACA,IAEH,MA+BMO,EAAgBb,EAAejG,SAAWyD,EAAQzD,QAAUyD,EAAQzD,OAAS,EAC7E+G,EAAkBd,EAAejG,OAAS,GAAKiG,EAAejG,OAASyD,EAAQzD,OAErF,OACE9F,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAO8J,QAAShJ,EAAInB,UAAU,aAAYC,SAAA,CAAEiK,EAAM,QAClD7J,EAAAA,EAAAA,MAAA,OAAKL,UAAU,WAAWmN,IAAKZ,EAAYtM,SAAA,EACzCF,EAAAA,EAAAA,KAAA,UACEoB,GAAIA,EACJnB,UAAW,+DAA8D2I,EAAW,WAAa,IACjGmB,KAAK,SACLpJ,QAASA,KAAOiI,GAAY0D,GAAW7L,GACvCmI,SAAUA,EACVN,MAAO,CACL+E,gBAAiBzE,EAAW,UAAY,QACxC0E,YAAa,WACbpN,UAEFF,EAAAA,EAAAA,KAAA,QAAMC,UAAqC,IAA1BoM,EAAejG,OAAe,aAAe,GAAGlG,SA7BlDqN,MACrB,GAA8B,IAA1BlB,EAAejG,OACjB,OAAOiE,EACF,GAA8B,IAA1BgC,EAAejG,OAAc,CACtC,MAAMoH,EAAiB3D,EAAQ3I,MAAKuM,GAAUA,EAAOrM,KAAOiL,EAAe,KAC3E,OAAqB,OAAdmB,QAAc,IAAdA,OAAc,EAAdA,EAAgB9G,OAAQ2D,CACjC,CACE,MAAO,GAAGgC,EAAejG,iBAC3B,EAsBSmH,OAIJ9M,IAAWmI,IACVtI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,2BAA2BqI,MAAO,CAAEoF,UAAW,QAASC,UAAW,QAASzN,SAAA,CAExF2J,EAAQzD,OAAS,IAChB9F,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,EACEF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,UAC5BI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SACEC,UAAU,mBACV8J,KAAK,WACL3I,GAAI,GAAGA,eACP6I,QAASiD,EACTE,IAAMQ,IACAA,IAAOA,EAAMC,cAAgBV,EAAe,EAElD1E,SA3DIqF,KAClBzB,EAAejG,SAAWyD,EAAQzD,OAEpCqC,EAAS,IAGTA,EAASoB,EAAQ1J,KAAIsN,GAAUA,EAAOrM,KACxC,KAsDgBd,EAAAA,EAAAA,MAAA,SAAOL,UAAU,2BAA2BmK,QAAS,GAAGhJ,eAAgBlB,SAAA,CAAC,eAC1D2J,EAAQzD,OAAO,aAIlCpG,EAAAA,EAAAA,KAAA,MAAIC,UAAU,wBAKjB4J,EAAQ1J,KAAIsN,IACXzN,EAAAA,EAAAA,KAAA,OAAqBC,UAAU,gBAAeC,UAC5CI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBF,EAAAA,EAAAA,KAAA,SACEC,UAAU,mBACV8J,KAAK,WACL3I,GAAI,GAAGA,KAAMqM,EAAOrM,KACpB6I,QAASoC,EAAe9B,SAASkD,EAAOrM,IACxCqH,SAAUA,KAAMsF,OAzFJC,EAyFyBP,EAAOrM,QAxFxDiL,EAAe9B,SAASyD,GAE1BvF,EAAS4D,EAAepK,QAAOb,GAAMA,IAAO4M,KAG5CvF,EAAS,IAAI4D,EAAgB2B,KANHA,KAyFoC,KAElDhO,EAAAA,EAAAA,KAAA,SAAOC,UAAU,mBAAmBmK,QAAS,GAAGhJ,KAAMqM,EAAOrM,KAAKlB,SAC/DuN,EAAO/G,WAVJ+G,EAAOrM,MAgBC,IAAnByI,EAAQzD,SACPpG,EAAAA,EAAAA,KAAA,OAAKC,UAAU,2BAA0BC,UACvCF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,iCAQbmM,EAAejG,OAAS,IACvB9F,EAAAA,EAAAA,MAAA,SAAOL,UAAU,0BAAyBC,SAAA,CACvCmM,EAAejG,OAAO,OAAKyD,EAAQzD,OAAO,iBAG3C,ECyBV,EArKgE5F,IASzD,IAT0D,gBAC/DyN,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,GACDhO,EAEC,MAAM,QAAEiO,EAAO,UAAEC,EAAS,QAAEC,EAAO,QAAE3C,EAAO,MAAEzH,EAAK,QAAEqK,GFbpBC,MACjC,MAAOJ,EAASK,IAAcvC,EAAAA,EAAAA,UAAmB,KAC1CmC,EAAWK,IAAgBxC,EAAAA,EAAAA,UAAqB,KAChDoC,EAASK,IAAczC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASiD,IAAc1C,EAAAA,EAAAA,WAAkB,IACzChI,EAAOtB,IAAYsJ,EAAAA,EAAAA,UAAwB,MAE5C2C,EAAkBrL,UACtB,IACEoL,GAAW,GACXhM,EAAS,MAETqB,QAAQ6K,IAAI,0EAGZ,MAAMC,QAAgBC,EAAAA,EAAcC,qBAEpChL,QAAQ6K,IAAI,sCAAkCC,EAAQhJ,OAAQ,kBAG9D,MAAMmJ,EAAyB,OAAPH,QAAO,IAAPA,OAAO,EAAPA,EACpBjP,KAAIqP,GAAOA,EAAIC,SAChBxN,QAAO,CAACyN,EAAQrP,EAAOsP,IAAUA,EAAMC,QAAQF,KAAYrP,IAC3D4B,QAAQyN,GAAuC,MAAVA,GAAoC,KAAlBA,EAAOnE,SAC9DsE,OAIGC,GAAwC,OAAfP,QAAe,IAAfA,OAAe,EAAfA,EAAiBpP,KAAI4P,IAAU,CAC5D3O,GAAI2O,EAAWC,cAAc9K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzEwB,KAAMqJ,QACD,GAGDE,EAA2B,OAAPb,QAAO,IAAPA,OAAO,EAAPA,EACtBjP,KAAIqP,IAAG,CAAOE,OAAQF,EAAIC,OAAQS,SAAUV,EAAIW,aACjDlO,QAAO,CAACL,EAAMvB,EAAOsP,IACpBA,EAAMS,WAAUC,GAAKA,EAAEX,SAAW9N,EAAK8N,QAAUW,EAAEH,WAAatO,EAAKsO,aAAc7P,IAEpF4B,QAAQL,GACQ,MAAfA,EAAK8N,QAAmC,MAAjB9N,EAAKsO,UACL,KAAvBtO,EAAK8N,OAAOnE,QAA0C,KAAzB3J,EAAKsO,SAAS3E,SAE5CsE,MAAK,CAACS,EAAGC,IAAMD,EAAEZ,OAAOc,cAAcD,EAAEb,SAAWY,EAAEJ,SAASM,cAAcD,EAAEL,YAE3EO,GAA8C,OAAjBR,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmB9P,KAAIyB,IAAI,CAC5DR,GAAIQ,EAAKsO,SAASF,cAAc9K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC5EwB,KAAM9E,EAAKsO,SACXR,OAAQ9N,EAAK8N,aACR,GAGDgB,GAAgC,OAAPtB,QAAO,IAAPA,OAAO,EAAPA,EAC3BnN,QAAOuN,GAAOA,EAAI,gBAAkBA,EAAIC,QAAUD,EAAIW,WACvDhQ,KAAIqP,IAAG,CACNpO,GAAIoO,EAAI,eACR9I,KAAM8I,EAAI,eACVE,OAAQF,EAAIC,QAAU,GACtBS,SAAUV,EAAIW,UAAY,GAC1BQ,WAAYnB,EAAI,qBACX,GAITV,EAAWgB,GACXf,EAAa0B,GACbzB,EAAW0B,EAEb,CAAE,MAAOrM,GACPC,QAAQC,MAAM,8BAAqBF,GACnCpB,EAAS,iDACT6L,EAAW,IACXC,EAAa,IACbC,EAAW,GACb,CAAC,QACCC,GAAW,EACb,GAQF,OAJAvC,EAAAA,EAAAA,YAAU,KACRwC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA3C,UACAzH,QACAqK,QAASM,EACV,EE9EgE0B,GAG3DC,EAAsB5C,EAAgB9N,KAAI2Q,IAAQ,IAAAC,EAAA,OAClB,QADkBA,EACtDtC,EAAQvN,MAAK8P,GAAKA,EAAE5P,KAAO0P,WAAS,IAAAC,OAAA,EAApCA,EAAsCrK,IAAI,IAC1CzE,OAAOiJ,SAEH+F,EAAqBhD,EAAgB7H,OAAS,EAChDsI,EAAUzM,QAAOiO,GAAYW,EAAoBtG,SAAS2F,EAASR,UACnEhB,EAGEwC,EAAwBhD,EAAkB/N,KAAIgR,IAAU,IAAAC,EAAA,OACpB,QADoBA,EAC5D1C,EAAUxN,MAAKmQ,GAAKA,EAAEjQ,KAAO+P,WAAW,IAAAC,OAAA,EAAxCA,EAA0C1K,IAAI,IAC9CzE,OAAOiJ,SAEHoG,EAAmBpD,EAAkB9H,OAAS,EAChDuI,EAAQ1M,QAAOsP,GACbV,EAAoBtG,SAASgH,EAAO7B,SACpCwB,EAAsB3G,SAASgH,EAAOrB,YAExCjC,EAAgB7H,OAAS,EACvBuI,EAAQ1M,QAAOsP,GAAUV,EAAoBtG,SAASgH,EAAO7B,UAC7Df,EAiCN,OA9BAjC,EAAAA,EAAAA,YAAU,KACR,GAAIuB,EAAgB7H,OAAS,EAAG,CAE9B,MAAMoL,EAAiBtD,EAAkBjM,QAAOkP,IAC9C,MAAMjB,EAAWxB,EAAUxN,MAAKmQ,GAAKA,EAAEjQ,KAAO+P,IAC9C,OAAOjB,GAAYW,EAAoBtG,SAAS2F,EAASR,OAAO,IAG9D8B,EAAepL,SAAW8H,EAAkB9H,QAC9CkI,EAAkBkD,EAEtB,IACC,CAACvD,EAAiBC,EAAmBQ,EAAWmC,EAAqBvC,KAExE5B,EAAAA,EAAAA,YAAU,KACR,GAAIwB,EAAkB9H,OAAS,EAAG,CAEhC,MAAMqL,EAAetD,EAAgBlM,QAAOyP,IAC1C,MAAMH,EAAS5C,EAAQzN,MAAKyQ,GAAKA,EAAEvQ,KAAOsQ,IAC1C,OAAOH,GACAV,EAAoBtG,SAASgH,EAAO7B,SACpCwB,EAAsB3G,SAASgH,EAAOrB,SAAS,IAGpDuB,EAAarL,SAAW+H,EAAgB/H,QAC1CmI,EAAgBkD,EAEpB,IACC,CAACvD,EAAmBC,EAAiBQ,EAASkC,EAAqBK,EAAuB3C,KAG3FjO,EAAAA,EAAAA,MAAA,OAAKL,UAAU,iCAAgCC,SAAA,EAC7CF,EAAAA,EAAAA,KAAA,MAAAE,SAAI,yBAEH8L,IACChM,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,UAC/BI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,4BAA2BC,SAAA,EACxCF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wCAAwC2R,KAAK,SAAQ1R,UAClEF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,kBAAiBC,SAAC,iBAC9B,8BAMXqE,IACCjE,EAAAA,EAAAA,MAAA,OAAKL,UAAU,qBAAoBC,SAAA,EACjCF,EAAAA,EAAAA,KAAA,UAAAE,SAAQ,WAAe,IAAEqE,GACzBvE,EAAAA,EAAAA,KAAA,UACEC,UAAU,qCACVU,QAASiO,EAAQ1O,SAClB,cAMH8L,IAAYzH,IACZjE,EAAAA,EAAAA,MAAA,OAAKL,UAAU,MAAKC,SAAA,EAClBF,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBF,EAAAA,EAAAA,KAAC6R,EAAgB,CACfzQ,GAAG,gBACH+I,MAAM,iBACNN,QAAS4E,EACTpC,eAAgB4B,EAChBxF,SAAU4F,EACVzF,SAAUoD,EACV3B,YAAY,4BAIhBrK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBF,EAAAA,EAAAA,KAAC6R,EAAgB,CACfzQ,GAAG,kBACH+I,MAAM,mBACNN,QAASoH,EACT5E,eAAgB6B,EAChBzF,SAAU6F,EACV1F,SAAqC,IAA3BqF,EAAgB7H,QAAgB4F,EAC1C3B,YAAY,8BAIhBrK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBF,EAAAA,EAAAA,KAAC6R,EAAgB,CACfzQ,GAAG,gBACH+I,MAAM,iBACNN,QAASyH,EACTjF,eAAgB8B,EAChB1F,SAAU8F,EACV3F,SAAuC,IAA7BsF,EAAkB9H,QAAgB4F,EAC5C3B,YAAY,4BAIhBrK,EAAAA,EAAAA,KAAA,OAAKC,UAAU,WAAUC,UACvBI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAO8J,QAAQ,mBAAmBnK,UAAU,aAAYC,SAAA,CAAC,sBACrCF,EAAAA,EAAAA,KAAA,QAAMC,UAAU,cAAaC,SAAC,UAElDI,EAAAA,EAAAA,MAAA,UACEc,GAAG,mBACHnB,UAAW,gBAAgBmO,EAAmC,GAAf,cAC/CtO,MAAOsO,EACP3F,SAAW7H,GAAM4N,EAAkB5N,EAAE+H,OAAO7I,OAC5C8I,SAAUoD,EACV1B,UAAQ,EAAApK,SAAA,EAERF,EAAAA,EAAAA,KAAA,UAAQF,MAAM,GAAEI,SAAC,2BAChBkM,EAAmBjM,KAAI2R,IACtB9R,EAAAA,EAAAA,KAAA,UAA8BF,MAAOgS,EAAUhS,MAAMI,SAClD4R,EAAU3H,OADA2H,EAAUhS,aAKzBsO,IACApO,EAAAA,EAAAA,KAAA,OAAKC,UAAU,mBAAkBC,SAAC,4CAQxC,ECyMV,EAzW8B6R,KAAO,IAADC,EAAAC,EAElC,MAAMC,ECf2BC,MACjC,MAAO7P,EAAYC,IAAiBgK,EAAAA,EAAAA,UAAqB,KAClD/J,EAAcC,IAAmB8J,EAAAA,EAAAA,UAAiB,KAClDb,EAAYrI,IAAiBkJ,EAAAA,EAAAA,UAA4B,OACzD5F,EAAQrD,IAAaiJ,EAAAA,EAAAA,UAAsB,KAC3C6F,EAAwBC,IAA6B9F,EAAAA,EAAAA,UAA6B,KAClF1E,EAAW7E,IAAgBuJ,EAAAA,EAAAA,WAAkB,IAC7CP,EAASiD,IAAc1C,EAAAA,EAAAA,WAAS,IAChChI,EAAOtB,IAAYsJ,EAAAA,EAAAA,UAAwB,OAC3C+F,EAASpP,IAAcqJ,EAAAA,EAAAA,UAAwB,OAE/CgG,EAAiBnP,IAAsBmJ,EAAAA,EAAAA,WAAkB,IACzD7J,EAAWC,IAAgB4J,EAAAA,EAAAA,UAAiB,KAC5C3J,EAAcC,IAAmB0J,EAAAA,EAAAA,UAAiB,KAClDiG,EAAkBrP,IAAuBoJ,EAAAA,EAAAA,WAAkB,IAE3DtF,EAAa1D,IAAkBgJ,EAAAA,EAAAA,UAA0B,OACzDkG,EAAejP,IAAoB+I,EAAAA,EAAAA,WAAkB,IAErDmG,EAAcjP,IAAmB8I,EAAAA,EAAAA,UAAwB,OACzDoG,EAAwBjP,IAA6B6I,EAAAA,EAAAA,WAAkB,IAEvEzJ,EAAYC,IAAiBwJ,EAAAA,EAAAA,UAAiB,KAG9CqG,EAAeC,IAAoBtG,EAAAA,EAAAA,WAAS,IAC5CuG,EAAgBC,IAAqBxG,EAAAA,EAAAA,UAAS,KAG9C0B,EAAiB+E,IAAsBzG,EAAAA,EAAAA,UAAmB,KAC1D2B,EAAmB+E,IAAwB1G,EAAAA,EAAAA,UAAmB,KAC9D4B,EAAiB+E,IAAsB3G,EAAAA,EAAAA,UAAmB,KAC1D6B,EAAmB+E,IAAwB5G,EAAAA,EAAAA,UAAiB,IAEnE,MAAO,CAELjK,aACAE,eACAkJ,aACA/E,SACAyL,yBACAvK,YACAmE,UACAzH,QACA+N,UACAC,kBACA7P,YACAE,eACA4P,mBACAvL,cACAwL,gBACAC,eACAC,yBACA7P,aACA8P,gBACAE,iBACA7E,kBACAC,oBACAC,kBACAC,oBAGA7L,gBACAE,kBACAY,gBACAC,YACA+O,4BACArP,eACAiM,aACAhM,WACAC,aACAE,qBACAT,eACAE,kBACAM,sBACAI,iBACAC,mBACAC,kBACAC,4BACAX,gBACA8P,mBACAE,oBACAC,qBACAC,uBACAC,qBACAC,uBACD,EDvEahB,GAKRiB,EAAiBhR,EAAkB,CACvCE,WAAY4P,EAAM5P,WAClBC,cAAe2P,EAAM3P,cACrBC,aAAc0P,EAAM1P,aACpBC,gBAAiByP,EAAMzP,gBACvBC,UAAWwP,EAAMxP,UACjBC,aAAcuP,EAAMvP,aACpBC,aAAcsP,EAAMtP,aACpBC,gBAAiBqP,EAAMrP,gBACvBC,WAAYoP,EAAMpP,WAClBC,cAAemP,EAAMnP,cACrBC,aAAckP,EAAMlP,aACpBC,SAAUiP,EAAMjP,SAChBC,WAAYgP,EAAMhP,WAClBC,oBAAqB+O,EAAM/O,oBAC3BC,mBAAoB8O,EAAM9O,mBAC1BC,cAAe6O,EAAM7O,cACrBC,UAAW4O,EAAM5O,UACjBC,eAAgB2O,EAAM3O,eACtBC,iBAAkB0O,EAAM1O,iBACxBC,gBAAiByO,EAAMzO,gBACvBC,0BAA2BwO,EAAMxO,4BAG7B2P,EThB6BhR,KACnC,MAAM,WACJC,EAAU,aACVE,EAAY,WACZkJ,EAAU,cACVrI,EAAa,OACbsD,EAAM,UACNrD,EAAS,0BACT+O,EAAyB,WACzBpD,EAAU,SACVhM,EAAQ,WACRC,EAAU,kBACV6P,EAAiB,iBACjBF,EAAgB,gBAChB5E,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,mBACjB4E,EAAkB,qBAClBC,EAAoB,mBACpBC,EAAkB,qBAClBC,GACE9Q,EAoTJ,MAAO,CACLiR,wBAnT6B1P,EAAAA,EAAAA,cAAYC,UACzC,GAAK0P,EAAL,CACAjP,QAAQ6K,IAAI,4CAA4CoE,KACxD,IACE,MAAMC,GAAgBrP,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,cAAesP,GACvCE,QAAuB/O,EAAAA,EAAAA,IAAO8O,GACpC,GAAIC,EAAe9O,SAAU,CAC3B,MAAM+O,EAAiBD,EAAerP,OACtCiO,EAA0BqB,EAAe/M,QAAU,IACnDrC,QAAQ6K,IAAI,0BAA2BuE,EAAe/M,OACxD,MACErC,QAAQ6K,IAAI,mDAAmDoE,KAC/DlB,EAA0B,GAE9B,CAAE,MAAOhO,GACPC,QAAQC,MAAM,sCAAuCF,GACrDpB,EAAS,wCACToP,EAA0B,GAC5B,CAjBmB,CAiBnB,GACC,CAACA,EAA2BpP,IAiS7B0Q,gBA/RqB/P,EAAAA,EAAAA,cAAYC,UACjC,GAAK9C,EAAL,CAIAuD,QAAQ6K,IAAI,qCAAqCpO,KACjDkO,GAAW,GACXhM,EAAS,MACT,IAEE,MAAMwB,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASlD,GAC1B6S,QAAgBlP,EAAAA,EAAAA,IAAOD,GAE7B,IAAIL,EAA0B,KAE9B,GAAIwP,EAAQjP,SAEVP,EAAOwP,EAAQxP,YAGf,IACEA,QAAayP,EAAAA,EAAoBF,eAAe5S,EAClD,CAAE,MAAO+S,GACP,CAIJ,GAAI1P,EACFf,EAAce,GACdd,EAAUc,EAAKuC,QAAU,IAEzBqM,EAAmB5O,EAAK6J,kBAAoB7J,EAAK2P,eAAiB,CAAC3P,EAAK2P,gBAAkB,KAC1Fd,EAAqB7O,EAAK8J,oBAAsB9J,EAAK4P,iBAAmB,CAAC5P,EAAK4P,kBAAoB,KAClGd,EAAmB9O,EAAK+J,kBAAoB/J,EAAK6P,eAAiB,CAAC7P,EAAK6P,gBAAkB,KAC1Fd,EAAqB/O,EAAKgK,mBAAqB,QAC1C,CAEL,MAAMnN,EAAOqB,EAAWpB,MAAKC,GAAKA,EAAEC,KAAOL,IAC3CsC,EAAc,CACZjC,GAAIL,EACJlB,OAAW,OAAJoB,QAAI,IAAJA,OAAI,EAAJA,EAAMpB,QAAS,WACtB8G,OAAQ,GACRJ,aAAa,IAAIC,MAAOC,gBAE1BnD,EAAU,IAEV0P,EAAmB,IACnBC,EAAqB,IACrBC,EAAmB,IACnBC,EAAqB,GACvB,CACF,CAAE,MAAO9O,GACPpB,EAAS,sCACTqB,QAAQC,MAAMF,GACdhB,EAAc,MACdC,EAAU,GACZ,CAAC,QACC2L,GAAW,EACb,CAtDA,MAFE3K,QAAQ6K,IAAI,uCAwDd,GACC,CAAC7M,EAAY2M,EAAYhM,EAAUI,EAAeC,EAAW0P,EAAoBC,EAAsBC,EAAoBC,IAqO5He,SAnOeA,KACf,MAAMC,EAAsB,CAC1B/S,GAAI,SAASoF,KAAK4N,QAClBrK,KAAM,OACNI,MAAO,YACPE,YAAa,GACbR,QAAS,GACTS,UAAU,EACVoF,OAAQ,GACRQ,SAAU,GACVqB,OAAQ,IAEVjO,EAAU,IAAIqD,EAAQwN,GAAU,EAwNhCE,oBArN2BC,IAC3BhQ,QAAQ6K,IAAI,mCAAoCmF,GAChD,MAAMH,EAAsB,CAC1B/S,GAAIkT,EAAalT,GACjB2I,KAAMuK,EAAavK,KACnBI,MAAOmK,EAAanK,MACpBE,YAAaiK,EAAajK,YAC1BR,QAASyK,EAAazK,QAAUyK,EAAazK,QAAQ1J,KAAKwK,GACrC,kBAARA,EACF,CAAER,MAAOQ,EAAK7K,MAAO6K,GAErB,CAAER,MAAOQ,EAAIR,MAAOrK,MAAO6K,EAAI7K,cAErCsI,EACLkC,SAAUgK,EAAahK,SACvBJ,aAAcoK,EAAapK,aAC3BM,IAAK8J,EAAa9J,IAClBE,IAAK4J,EAAa5J,IAClBe,kBAAcrD,EACdmM,aAASnM,EACToD,gBAAYpD,EACZoM,gBAAYpM,EACZqM,mBAAerM,EACftI,WAAOsI,GAGT,GAAIzB,EAAOpF,MAAK+H,GAASA,EAAMlI,KAAO+S,EAAS/S,KAI3C,OAHAkD,QAAQoQ,KAAK,iCAAiCP,EAAS/S,yBACvD6B,EAAS,kBAAkBkR,EAAS/S,sDACpC0F,YAAW,IAAM7D,EAAS,OAAO,KAIrCqB,QAAQ6K,IAAI,6BAA8BgF,GAC1C7Q,EAAU,IAAIqD,EAAQwN,IACtBjR,EAAW,gBAAgBiR,EAAShK,iCACpCrD,YAAW,IAAM5D,EAAW,OAAO,IAAK,EAkLxCyR,YA/KkBA,CAACtU,EAAeuU,KAClC,MAAMC,EAAgB,IAAIlO,GAC1BkO,EAAcxU,GAASuU,EACvBtR,EAAUuR,EAAc,EA6KxBC,YA1KmBzU,IACnBiD,EAAUqD,EAAO1E,QAAO,CAAC4I,EAAGC,IAAMA,IAAMzK,IAAO,EA0K/C0U,WAvKiBlR,UACjB,GAAKrB,GAAiBkJ,EAMtB,GAAK0C,EAAL,CAKAa,GAAW,GACX3K,QAAQ6K,IAAI,oDAAqD3M,GACjE8B,QAAQ6K,IAAI,sBAAuBxI,GACnCrC,QAAQ6K,IAAI,oBAAqBf,GAEjC,IAAK,IAADtJ,EACF,MAAMkQ,EAAgBrO,EAAOxG,KAAImJ,IAC/B,MAAM2L,EAAoB,CAAC,EAC3B,IAAK,MAAMtL,KAAOL,OACGlB,IAAfkB,EAAMK,GACRsL,EAAatL,GAAOL,EAAMK,GAE1BsL,EAAatL,GAAO,KAGxB,OAAOsL,CAAY,IAGfC,EAAgC,IACjCxJ,EACHtK,GAAIoB,EACJ3C,OAAkD,QAA3CiF,EAAAxC,EAAWpB,MAAKC,GAAKA,EAAEC,KAAOoB,WAAa,IAAAsC,OAAA,EAA3CA,EAA6CjF,QAAS6L,EAAW7L,MACxE8G,OAAQqO,EACRzO,aAAa,IAAIC,MAAOC,cACxBwH,kBACAC,oBACAC,kBACAC,qBAII+G,EAAe,GAGrBA,EAAarT,MACXwE,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASzB,GAAe0S,GACpCE,OAAM/Q,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAIgR,MAAM,yBAAyBhR,EAAIiR,UAAU,KAK7DH,EAAarT,KACX+R,EAAAA,EAAoB0B,eAAeL,GAChCE,OAAM/Q,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAIgR,MAAM,yBAAyBhR,EAAIiR,UAAU,WAKvDE,QAAQC,IAAIN,GAElB9R,EAAc6R,GACdhS,EAAW,0CACX4D,YAAW,IAAM5D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPC,QAAQC,MAAM,qCAAsCF,GACpDpB,EAAS,sCAAsCoB,aAAegR,MAAQhR,EAAIiR,QAAU,kBACtF,CAAC,QACCrG,GAAW,EACb,CAjEA,MAFEhM,EAAS,+EANTA,EAAS,mDAyEX,EA6FAyS,cA1FoBA,KACpB,IAAKhK,GAAgC,IAAlB/E,EAAOP,OAExB,YADAuP,MAAM,+CAIR,MAAMC,EAAmB,eACjBlK,EAAW7L,qCAEb8G,EAAOxG,KAAImJ,IAAU,IAADsB,EAAAvB,EACpB,IAAIwM,EAAY,GAChB,OAAQvM,EAAMS,MACZ,IAAK,OACL,IAAK,SACL,IAAK,OACL,IAAK,WACH8L,EAAY,gGAEoBvM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8CACnDhB,EAAMS,2CAA2CT,EAAMe,aAAe,OAAOf,EAAMgB,SAAW,WAAa,gDAG9H,MACF,IAAK,WACHuL,EAAY,gGAEoBvM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8DACnChB,EAAMgB,SAAW,WAAa,oDACjChB,EAAMa,wCACjB,QAAbS,EAAAtB,EAAMO,eAAO,IAAAe,OAAA,EAAbA,EAAezK,KAAIsN,GAAU,kBAAkBA,EAAO3N,UAAU2N,EAAOtD,mBAAkBkB,KAAK,MAAO,0EAI7G,MACF,IAAK,WACHwK,EAAY,0HAE8CvM,EAAMlI,OAAOkI,EAAMgB,SAAW,WAAa,iEAC1DhB,EAAMlI,OAAOkI,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,qDAG9F,MACF,IAAK,QACHuL,EAAY,gGAEoBvM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,kCACnD,QAAbjB,EAAAC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAelJ,KAAI,CAACsN,EAAQ3C,IAAM,4HAEqBxB,EAAMlI,WAAWkI,EAAMlI,MAAM0J,aAAa2C,EAAO3N,UAAUwJ,EAAMgB,SAAW,WAAa,mEACvGhB,EAAMlI,MAAM0J,MAAM2C,EAAOtD,kEAEjEkB,KAAK,MAAO,6CAGnB,MACF,IAAK,UACHwK,EAAY,8FAEmBvM,EAAMmC,cAAgB,yNAMrD,MACF,IAAK,SACHoK,EAAY,wEAC2CvM,EAAMkC,YAAc,oCAE3E,MACF,QACEqK,EAAY,8BAA8BvM,EAAMS,WAEpD,OAAO8L,CAAS,IACfxK,KAAK,2BAIZ0H,EAAkB6C,GAClB/C,GAAiB,EAAK,EAYvB,ESnUyBiD,CAAqB,CAC7CxT,WAAY4P,EAAM5P,WAClBE,aAAc0P,EAAM1P,aACpBkJ,WAAYwG,EAAMxG,WAClBrI,cAAe6O,EAAM7O,cACrBsD,OAAQuL,EAAMvL,OACdrD,UAAW4O,EAAM5O,UACjB+O,0BAA2BH,EAAMG,0BACjCpD,WAAYiD,EAAMjD,WAClBhM,SAAUiP,EAAMjP,SAChBC,WAAYgP,EAAMhP,WAClB6P,kBAAmBb,EAAMa,kBACzBF,iBAAkBX,EAAMW,iBACxB5E,gBAAiBiE,EAAMjE,gBACvBC,kBAAmBgE,EAAMhE,kBACzBC,gBAAiB+D,EAAM/D,gBACvBC,kBAAmB8D,EAAM9D,kBACzB4E,mBAAoBd,EAAMc,mBAC1BC,qBAAsBf,EAAMe,qBAC5BC,mBAAoBhB,EAAMgB,mBAC1BC,qBAAsBjB,EAAMiB,wBAI9BzG,EAAAA,EAAAA,YAAU,KACR0G,EAAezP,iBAAiB,GAC/B,KAGH+I,EAAAA,EAAAA,YAAU,KACJwF,EAAM1P,cAAgBlB,EAAW4Q,EAAM1P,aAAc0P,EAAM5P,cAAgBxB,EAAWoR,EAAM1P,aAAc0P,EAAM5P,aAAoC,kBAArB4P,EAAMpP,YACvIuQ,EAAkBM,eAAezB,EAAM1P,cACvC6Q,EAAkBC,uBAAuBpB,EAAM1P,gBACtC0P,EAAM1P,cAAkBlB,EAAW4Q,EAAM1P,aAAc0P,EAAM5P,cAAexB,EAAWoR,EAAM1P,aAAc0P,EAAM5P,aAAqC,kBAArB4P,EAAMpP,WAItIoP,EAAM1P,eAChB0P,EAAM7O,cAAc,MACpB6O,EAAM5O,UAAU,IAChB4O,EAAMG,0BAA0B,IAChCH,EAAMnP,cAAc,MAPpBmP,EAAM7O,cAAc,MACpB6O,EAAM5O,UAAU,IAChB4O,EAAMG,0BAA0B,KAQ9BH,EAAM1P,cAAqC,kBAArB0P,EAAMpP,YAC5BoP,EAAMG,0BAA0B,GACpC,GACC,CAACH,EAAM1P,aAAc0P,EAAM5P,WAAY4P,EAAMpP,aAqEhD,OACE9C,EAAAA,EAAAA,KAAA8I,EAAAA,SAAA,CAAA5I,UACEI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,eAAcC,SAAA,CAC1BgS,EAAM3N,QAASvE,EAAAA,EAAAA,KAAA,OAAKC,UAAU,gBAAeC,SAAEgS,EAAM3N,QACrD2N,EAAMI,UACLtS,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kBAAiBC,SAC7BgS,EAAMI,WAGXtS,EAAAA,EAAAA,KAAA,MAAAE,SAAI,2BAEJF,EAAAA,EAAAA,KAAC+V,EAAY,CACXzT,WAAY4P,EAAM5P,WAClBE,aAAc0P,EAAM1P,aACpBmF,aAhFkB5G,IAGxB,GAFAmR,EAAMzP,gBAAgB1B,GACtBmR,EAAMnP,cAAc,IACfhC,EAGE,CACH,MAAMiV,EAAa1U,EAAWP,EAAQmR,EAAM5P,YACtC2T,EAAanV,EAAWC,EAAQmR,EAAM5P,YACxC0T,IAAcC,IACd/D,EAAM7O,cAAc,MACpB6O,EAAM5O,UAAU,IAExB,MATI4O,EAAM7O,cAAc,MACpB6O,EAAM5O,UAAU,GAQpB,EAoEMR,WAAYoP,EAAMpP,WAClB8E,eAlEoBsO,IAC1BhE,EAAMnP,cAAcmT,EAAO,EAkErBrO,UAAWqK,EAAMrK,UACjBC,eAhEmBqO,KACzBjE,EAAMvP,aAAa,IACnBuP,EAAMrP,gBAAgB,IACtBqP,EAAM9O,oBAAmB,EAAK,EA8DxB2E,gBA3DoBqO,KACtBlE,EAAM1P,cAAgBlB,EAAW4Q,EAAM1P,aAAc0P,EAAM5P,cAAgBxB,EAAWoR,EAAM1P,aAAc0P,EAAM5P,YAClH+Q,EAAkBM,eAAezB,EAAM1P,cAC9B0P,EAAM1P,eACf0P,EAAMjP,SAAS,sFACfiP,EAAM7O,cAAc,MACpB6O,EAAM5O,UAAU,IAClB,IAwDK4O,EAAMK,kBACLvS,EAAAA,EAAAA,KAACqW,EAAK,CACJ5V,OAAQyR,EAAMK,gBACd7R,QAASA,KACPwR,EAAM9O,oBAAmB,GACzB8O,EAAMnP,cAAc,IACpBmP,EAAMvP,aAAa,IACnBuP,EAAMrP,gBAAgB,GAAG,EAE3BhD,MACuB,qBAArBqS,EAAMpP,WAAoC,yBAC1CoP,EAAM1P,cAAqC,qBAArB0P,EAAMpP,WAAoC,4BAAmF,QAAnFkP,EAA4BE,EAAM5P,WAAWpB,MAAKC,GAAKA,EAAEC,KAAO8Q,EAAM1P,sBAAa,IAAAwP,OAAA,EAAvDA,EAAyDnS,SACrJ,oBACDK,UAEDI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,gBAAeC,SAAA,EAC5BF,EAAAA,EAAAA,KAAA,SACE+J,KAAK,OACLM,YAAY,oCACZvK,MAAOoS,EAAMxP,UACb+F,SAAW7H,GAAMsR,EAAMvP,aAAa/B,EAAE+H,OAAO7I,MAAMkQ,cAAc9K,QAAQ,OAAQ,MACjFjF,UAAU,uBAEZD,EAAAA,EAAAA,KAAA,SACE+J,KAAK,OACLM,YAAY,eACZvK,MAAOoS,EAAMtP,aACb6F,SAAW7H,GAAMsR,EAAMrP,gBAAgBjC,EAAE+H,OAAO7I,OAChDG,UAAU,uBAEZK,EAAAA,EAAAA,MAAA,OAAKL,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,UACEW,QAASyS,EAAevO,oBACxB+D,SAAUsJ,EAAMrK,YAAcqK,EAAMxP,YAAcwP,EAAMtP,aACxD3C,UAAU,kBAAiBC,SAE1BgS,EAAMrK,UAAY,cAAgB,6BAErC7H,EAAAA,EAAAA,KAAA,UAAQW,QAASA,KACfuR,EAAM9O,oBAAmB,GACzB8O,EAAMnP,cAAc,IACpBmP,EAAMvP,aAAa,IACnBuP,EAAMrP,gBAAgB,GAAG,EACxB5C,UAAU,oBAAmBC,SAAC,mBASxCgS,EAAM1P,eACLlC,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,GAE0B,kBAArBgS,EAAMpP,YAAkCxB,EAAW4Q,EAAM1P,aAAc0P,EAAM5P,cAAgBxB,EAAWoR,EAAM1P,aAAc0P,EAAM5P,aAAe4P,EAAMxG,cACxJ1L,EAAAA,EAAAA,KAACsW,EAAc,CACb9T,aAAc0P,EAAM1P,aACpBF,WAAY4P,EAAM5P,WAClByG,WAAYqK,EAAerM,eAC3BiC,aAAcoK,EAAejM,oBAKX,kBAArB+K,EAAMpP,YAAkCxB,EAAW4Q,EAAM1P,aAAc0P,EAAM5P,cAAgBxB,EAAWoR,EAAM1P,aAAc0P,EAAM5P,cACjItC,EAAAA,EAAAA,KAACuW,EAAmB,CAClBtI,gBAAiBiE,EAAMjE,gBACvBC,kBAAmBgE,EAAMhE,kBACzBC,gBAAiB+D,EAAM/D,gBACvBC,kBAAmB8D,EAAM9D,kBACzBC,gBA3HeI,IAC3ByD,EAAMc,mBAAmBvE,GAEzByD,EAAMe,qBAAqB,IAC3Bf,EAAMgB,mBAAmB,GAAG,EAwHhB5E,kBArHiBI,IAC7BwD,EAAMe,qBAAqBvE,GAE3BwD,EAAMgB,mBAAmB,GAAG,EAmHhB3E,gBAhHeI,IAC3BuD,EAAMgB,mBAAmBvE,EAAQ,EAgHrBH,kBA7GiBsD,IAC7BI,EAAMiB,qBAAqBrB,EAAU,IAiHP,kBAArBI,EAAMpP,YAAkCxB,EAAW4Q,EAAM1P,aAAc0P,EAAM5P,cAAgBxB,EAAWoR,EAAM1P,aAAc0P,EAAM5P,aAAe4P,EAAMxG,aACtJ1L,EAAAA,EAAAA,KAACwW,EAAkB,CACjB9K,WAAYwG,EAAMxG,WAClB/E,OAAQuL,EAAMvL,OACdgF,WAAY0H,EAAkBa,SAC9BtI,cAAeyH,EAAkBsB,YACjC9I,cAAewH,EAAkByB,YACjChJ,OAAQuH,EAAkB0B,WAC1BhJ,UAAWsH,EAAkBqC,cAC7B1J,QAASkG,EAAMlG,UAKG,kBAArBkG,EAAMpP,cAAoCxB,EAAW4Q,EAAM1P,aAAc0P,EAAM5P,aAAexB,EAAWoR,EAAM1P,aAAc0P,EAAM5P,eAClItC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,wDAAuDC,SAAC,iLAInD,kBAArBgS,EAAMpP,aAAmCxB,EAAW4Q,EAAM1P,aAAc0P,EAAM5P,cAC7EtC,EAAAA,EAAAA,KAAA,OAAKC,UAAU,kDAAiDC,SAAC,6IAOrEgS,EAAM1P,cAAqC,KAArB0P,EAAMpP,aAC5BxC,EAAAA,EAAAA,MAAA,OAAKL,UAAU,gDAA+CC,SAAA,EAC5DF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,+FACHF,EAAAA,EAAAA,KAAA,KAAAE,SAAG,wJAKNgS,EAAMO,eAAiBP,EAAMjL,cAC5B3G,EAAAA,EAAAA,MAAC+V,EAAK,CACJ5V,OAAQyR,EAAMO,cACd/R,QAASA,KACPwR,EAAM1O,kBAAiB,GACvB0O,EAAMrP,gBAAgB,IACtBqP,EAAM3O,eAAe,KAAK,EAE5B1D,MAAO,gBAAgBqS,EAAMjL,YAAYpH,QAAQK,SAAA,EAEjDF,EAAAA,EAAAA,KAAA,SACE+J,KAAK,OACLjK,MAAOoS,EAAMtP,aACb6F,SAAW7H,GAAMsR,EAAMrP,gBAAgBjC,EAAE+H,OAAO7I,OAChDuK,YAAY,mBACZpK,UAAU,uBAEZK,EAAAA,EAAAA,MAAA,OAAKL,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,UACEW,QAASyS,EAAepM,iBACxB/G,UAAU,kBACV2I,SAAUsJ,EAAMrK,YAAcqK,EAAMtP,aAAa2I,OAAOrL,SAEvDgS,EAAMrK,UAAY,cAAgB,kBAErC7H,EAAAA,EAAAA,KAAA,UACEW,QAASA,KACPuR,EAAM1O,kBAAiB,GACvB0O,EAAMrP,gBAAgB,IACtBqP,EAAM3O,eAAe,KAAK,EAE5BtD,UAAU,oBAAmBC,SAC9B,iBAONgS,EAAMS,wBAA0BT,EAAMQ,eACrCpS,EAAAA,EAAAA,MAAC+V,EAAK,CACJ5V,OAAQyR,EAAMS,uBACdjS,QAASA,IAAMwR,EAAMxO,2BAA0B,GAC/C7D,MAAM,mBAAkBK,SAAA,EAExBI,EAAAA,EAAAA,MAAA,KAAAJ,SAAA,CAAG,+CAAoG,QAAxD+R,EAACC,EAAM5P,WAAWpB,MAAKC,GAAKA,EAAEC,KAAO8Q,EAAMQ,sBAAa,IAAAT,OAAA,EAAvDA,EAAyDpS,MAAM,qGAC/GS,EAAAA,EAAAA,MAAA,OAAKL,UAAU,6BAA4BC,SAAA,EACzCF,EAAAA,EAAAA,KAAA,UACEW,QAASyS,EAAehM,oBACxBnH,UAAU,iBACV2I,SAAUsJ,EAAMrK,UAAU3H,SAEzBgS,EAAMrK,UAAY,cAAgB,oBAErC7H,EAAAA,EAAAA,KAAA,UACEW,QAASA,IAAMuR,EAAMxO,2BAA0B,GAC/CzD,UAAU,oBAAmBC,SAC9B,kBAQPF,EAAAA,EAAAA,KAACqW,EAAK,CACJ5V,OAAQyR,EAAMU,cACdlS,QAASA,IAAMwR,EAAMW,kBAAiB,GACtChT,MAAM,eAAcK,UAEpBF,EAAAA,EAAAA,KAAA,OAAKyW,wBAAyB,CAAEC,OAAQxE,EAAMY,wBAGjD,E,iHEpXA,SAAS6D,EAAoBlX,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,EACoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,SAAvD,MCgBMiX,GAAWC,EAAAA,EAAAA,IAAOC,EAAAA,EAAO,CAC7BpQ,KAAM,UACNjH,KAAM,QAFSoX,CAGd,CACDE,SAAU,WAyDZ,EAvD0B7N,EAAAA,YAAiB,SAAc8N,EAAS5J,GAChE,MAAM/K,GAAQ4U,EAAAA,EAAAA,GAAgB,CAC5B5U,MAAO2U,EACPtQ,KAAM,aAEF,UACJzG,EAAS,OACTiX,GAAS,KACNC,GACD9U,EACE+U,EAAa,IACd/U,EACH6U,UAEIG,EA7BkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoBZ,EAAqBU,EAAQ,EAsB1CG,CAAkBJ,GAClC,OAAoBpX,EAAAA,EAAAA,KAAK4W,EAAU,CACjC3W,WAAWwX,EAAAA,EAAAA,GAAKJ,EAAQE,KAAMtX,GAC9ByX,UAAWR,EAAS,OAAI9O,EACxBgF,IAAKA,EACLgK,WAAYA,KACTD,GAEP,IC/CO,SAASQ,EAA2BlY,GACzC,OAAOC,EAAAA,EAAAA,IAAqB,iBAAkBD,EAChD,EAC2BE,EAAAA,EAAAA,GAAuB,iBAAkB,CAAC,SAArE,MCcMiY,GAAkBf,EAAAA,EAAAA,IAAO,MAAO,CACpCnQ,KAAM,iBACNjH,KAAM,QAFgBoX,CAGrB,CACDgB,QAAS,GACT,eAAgB,CACdC,cAAe,MAqDnB,EAlDiC5O,EAAAA,YAAiB,SAAqB8N,EAAS5J,GAC9E,MAAM/K,GAAQ4U,EAAAA,EAAAA,GAAgB,CAC5B5U,MAAO2U,EACPtQ,KAAM,oBAEF,UACJzG,EAAS,UACT8X,EAAY,SACTZ,GACD9U,EACE+U,EAAa,IACd/U,EACH0V,aAEIV,EAhCkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoBI,EAA4BN,EAAQ,EAyBjDG,CAAkBJ,GAClC,OAAoBpX,EAAAA,EAAAA,KAAK4X,EAAiB,CACxCI,GAAID,EACJ9X,WAAWwX,EAAAA,EAAAA,GAAKJ,EAAQE,KAAMtX,GAC9BmX,WAAYA,EACZhK,IAAKA,KACF+J,GAEP,I,0EChDO,SAASc,EAAwBxY,GACtC,OAAOC,EAAAA,EAAAA,IAAqB,cAAeD,EAC7C,EACwBE,EAAAA,EAAAA,GAAuB,cAAe,CAAC,OAAQ,YAAa,QAAS,sBAAuB,UAAW,UAAW,UAAW,oBCArJ,MACA,GAD8BA,EAAAA,EAAAA,GAAuB,oBAAqB,CAAC,OAAQ,eAAgB,QAAS,sBAAuB,WAAY,UAAW,UAAW,aCH9J,SAASuY,GAA8CzY,GAC5D,OAAOC,EAAAA,EAAAA,IAAqB,6BAA8BD,EAC5D,EACuCE,EAAAA,EAAAA,GAAuB,6BAA8B,CAAC,OAAQ,mBAArG,MCgBMwY,IAA8BtB,EAAAA,EAAAA,IAAO,MAAO,CAChDnQ,KAAM,6BACNjH,KAAM,OACN2Y,kBAAmBA,CAAC/V,EAAOgW,KACzB,MAAM,WACJjB,GACE/U,EACJ,MAAO,CAACgW,EAAOd,KAAMH,EAAWkB,gBAAkBD,EAAOC,eAAe,GAPxCzB,CASjC,CACD0B,SAAU,WACVC,MAAO,GACPC,IAAK,MACLC,UAAW,mBACXC,SAAU,CAAC,CACTtW,MAAO7B,IAAA,IAAC,WACN4W,GACD5W,EAAA,OAAK4W,EAAWkB,cAAc,EAC/BhQ,MAAO,CACLkQ,MAAO,OAUPI,GAAuC1P,EAAAA,YAAiB,SAAiC8N,EAAS5J,GACtG,MAAM/K,GAAQ4U,EAAAA,EAAAA,GAAgB,CAC5B5U,MAAO2U,EACPtQ,KAAM,gCAEF,UACJzG,KACGkX,GACD9U,EACEwW,EAAU3P,EAAAA,WAAiB4P,EAAAA,GAC3B1B,EAAa,IACd/U,EACHiW,eAAgBO,EAAQP,gBAEpBjB,EArDkBD,KACxB,MAAM,eACJkB,EAAc,QACdjB,GACED,EACE2B,EAAQ,CACZxB,KAAM,CAAC,OAAQe,GAAkB,mBAEnC,OAAOhB,EAAAA,EAAAA,GAAeyB,EAAOb,GAA+Cb,EAAQ,EA6CpEG,CAAkBJ,GAClC,OAAoBpX,EAAAA,EAAAA,KAAKmY,GAA6B,CACpDlY,WAAWwX,EAAAA,EAAAA,GAAKJ,EAAQE,KAAMtX,GAC9BmX,WAAYA,EACZhK,IAAKA,KACF+J,GAEP,IAuBAyB,GAAwBI,QAAU,0BAClC,YCtDaC,IAAepC,EAAAA,EAAAA,IAAO,MAAO,CACxCnQ,KAAM,cACNjH,KAAM,OACN2Y,kBAzB+BA,CAAC/V,EAAOgW,KACvC,MAAM,WACJjB,GACE/U,EACJ,MAAO,CAACgW,EAAOd,KAAMH,EAAW8B,OAASb,EAAOa,MAAiC,eAA1B9B,EAAW+B,YAA+Bd,EAAOe,oBAAqBhC,EAAWiC,SAAWhB,EAAOgB,SAAUjC,EAAWkB,gBAAkBD,EAAOiB,SAAUlC,EAAWmC,gBAAkBlB,EAAOR,QAAST,EAAWoC,oBAAsBnB,EAAOoB,gBAAgB,GAkB7R5C,EAIzB6C,EAAAA,EAAAA,IAAUlZ,IAAA,IAAC,MACZmZ,GACDnZ,EAAA,MAAM,CACLoZ,QAAS,OACTC,eAAgB,aAChBV,WAAY,SACZZ,SAAU,WACVuB,eAAgB,OAChBC,MAAO,OACPC,UAAW,aACXC,UAAW,OACXtB,SAAU,CAAC,CACTtW,MAAO6X,IAAA,IAAC,WACN9C,GACD8C,EAAA,OAAM9C,EAAWmC,cAAc,EAChCjR,MAAO,CACL6R,WAAY,EACZrC,cAAe,IAEhB,CACDzV,MAAO+X,IAAA,IAAC,WACNhD,GACDgD,EAAA,OAAMhD,EAAWmC,gBAAkBnC,EAAW8B,KAAK,EACpD5Q,MAAO,CACL6R,WAAY,EACZrC,cAAe,IAEhB,CACDzV,MAAOgY,IAAA,IAAC,WACNjD,GACDiD,EAAA,OAAMjD,EAAWmC,iBAAmBnC,EAAWkB,cAAc,EAC9DhQ,MAAO,CACLC,YAAa,GACb+R,aAAc,KAEf,CACDjY,MAAOkY,IAAA,IAAC,WACNnD,GACDmD,EAAA,OAAMnD,EAAWmC,kBAAoBnC,EAAWqC,eAAe,EAChEnR,MAAO,CAGLgS,aAAc,KAEf,CACDjY,MAAOmY,IAAA,IAAC,WACNpD,GACDoD,EAAA,QAAOpD,EAAWqC,eAAe,EAClCnR,MAAO,CACL,CAAC,QAAQmS,EAAsBlD,QAAS,CACtC+C,aAAc,MAGjB,CACDjY,MAAO,CACL8W,WAAY,cAEd7Q,MAAO,CACL6Q,WAAY,eAEb,CACD9W,MAAOqY,IAAA,IAAC,WACNtD,GACDsD,EAAA,OAAKtD,EAAWiC,OAAO,EACxB/Q,MAAO,CACLqS,aAAc,cAAchB,EAAMiB,MAAQjB,GAAOkB,QAAQxB,UACzDyB,eAAgB,gBAEjB,CACDzY,MAAO0Y,IAAA,IAAC,WACN3D,GACD2D,EAAA,OAAK3D,EAAW4D,MAAM,EACvB1S,MAAO,CACL2S,WAAYtB,EAAMuB,YAAYC,OAAO,mBAAoB,CACvDC,SAAUzB,EAAMuB,YAAYE,SAASC,WAEvC,UAAW,CACTvB,eAAgB,OAChBzM,iBAAkBsM,EAAMiB,MAAQjB,GAAOkB,QAAQ3E,OAAOoF,MAEtD,uBAAwB,CACtBjO,gBAAiB,kBAItB,CACDhL,MAAOkZ,IAAA,IAAC,WACNnE,GACDmE,EAAA,OAAKnE,EAAWoC,kBAAkB,EACnClR,MAAO,CAGLgS,aAAc,MAGnB,KACKkB,IAAoB3E,EAAAA,EAAAA,IAAO,KAAM,CACrCnQ,KAAM,cACNjH,KAAM,aAFkBoX,CAGvB,CACD0B,SAAU,aAiPZ,GA3O8BrP,EAAAA,YAAiB,SAAkB8N,EAAS5J,GACxE,MAAM/K,GAAQ4U,EAAAA,EAAAA,GAAgB,CAC5B5U,MAAO2U,EACPtQ,KAAM,iBAEF,WACJyS,EAAa,SACbjZ,SAAUub,EAAY,UACtBxb,EACA8X,UAAW2D,EAAa,WACxBC,EAAa,CAAC,EAAC,gBACfC,EAAkB,CAAC,EAAC,mBACpBC,EAAqB,KACrBC,gBACE7b,UAAW8b,KACRD,GACD,CAAC,EAAC,MACN5C,GAAQ,EAAK,eACbZ,GAAiB,EAAK,eACtBiB,GAAiB,EAAK,QACtBF,GAAU,EAAK,gBACfI,EAAe,UACfuC,EAAY,CAAC,EAAC,MACdjD,EAAQ,CAAC,KACN5B,GACD9U,EACEwW,EAAU3P,EAAAA,WAAiB4P,EAAAA,GAC3BmD,EAAe/S,EAAAA,SAAc,KAAM,CACvCgQ,MAAOA,GAASL,EAAQK,QAAS,EACjCC,aACAb,oBACE,CAACa,EAAYN,EAAQK,MAAOA,EAAOZ,IACjC4D,EAAchT,EAAAA,OAAa,MAC3BhJ,EAAWgJ,EAAAA,SAAeiT,QAAQV,GAGlCjC,EAAqBtZ,EAASkG,SAAUgW,EAAAA,EAAAA,GAAalc,EAASA,EAASkG,OAAS,GAAI,CAAC,4BACrFgR,EAAa,IACd/U,EACH8W,aACAD,MAAO+C,EAAa/C,MACpBZ,iBACAiB,iBACAF,UACAG,sBAEInC,EA5KkBD,KACxB,MAAM,WACJ+B,EAAU,QACV9B,EAAO,MACP6B,EAAK,eACLZ,EAAc,eACdiB,EAAc,QACdF,EAAO,mBACPG,GACEpC,EACE2B,EAAQ,CACZxB,KAAM,CAAC,OAAQ2B,GAAS,SAAUZ,GAAkB,WAAYiB,GAAkB,UAAWF,GAAW,UAA0B,eAAfF,GAA+B,sBAAuBK,GAAsB,mBAC/L6C,UAAW,CAAC,cAEd,OAAO/E,EAAAA,EAAAA,GAAeyB,EAAOd,EAAyBZ,EAAQ,EA8J9CG,CAAkBJ,GAC5BkF,GAAYC,EAAAA,EAAAA,GAAWL,EAAa9O,GACpCoP,EAAOzD,EAAMxB,MAAQoE,EAAWa,MAAQvD,GACxCwD,EAAYT,EAAUzE,MAAQqE,EAAgBrE,MAAQ,CAAC,EACvDmF,EAAiB,CACrBzc,WAAWwX,EAAAA,EAAAA,GAAKJ,EAAQE,KAAMkF,EAAUxc,UAAWA,MAChDkX,GAEL,IAAIwF,EAAYjB,GAAiB,KAGjC,OAAIlC,GAEFmD,EAAaD,EAAe3E,WAAc2D,EAAwBiB,EAAR,MAG/B,OAAvBd,IACgB,OAAdc,EACFA,EAAY,MAC0B,OAA7BD,EAAe3E,YACxB2E,EAAe3E,UAAY,SAGX/X,EAAAA,EAAAA,KAAK8Y,EAAAA,EAAY8D,SAAU,CAC7C9c,MAAOmc,EACP/b,UAAuBI,EAAAA,EAAAA,MAAMkb,GAAmB,CAC9CxD,GAAI6D,EACJ5b,WAAWwX,EAAAA,EAAAA,GAAKJ,EAAQgF,UAAWN,GACnC3O,IAAKkP,EACLlF,WAAYA,KACT0E,EACH5b,SAAU,EAAcF,EAAAA,EAAAA,KAAKwc,EAAM,IAC9BC,OACEI,EAAAA,EAAAA,GAAgBL,IAAS,CAC5BxE,GAAI2E,EACJvF,WAAY,IACPA,KACAqF,EAAUrF,gBAGdsF,EACHxc,SAAUA,IACRA,EAAS4c,aAIC9c,EAAAA,EAAAA,KAAK8Y,EAAAA,EAAY8D,SAAU,CAC7C9c,MAAOmc,EACP/b,UAAuBI,EAAAA,EAAAA,MAAMkc,EAAM,IAC9BC,EACHzE,GAAI2E,EACJvP,IAAKkP,OACAO,EAAAA,EAAAA,GAAgBL,IAAS,CAC5BpF,WAAY,IACPA,KACAqF,EAAUrF,gBAGdsF,EACHxc,SAAU,CAACA,EAAUuZ,IAAgCzZ,EAAAA,EAAAA,KAAK4Y,GAAyB,CACjF1Y,SAAUuZ,QAIlB,I,qCCxPA,MAeMsD,IAAmBlG,EAAAA,EAAAA,IAAO,MAAO,CACrCnQ,KAAM,kBACNjH,KAAM,OACN2Y,kBAAmBA,CAAC/V,EAAOgW,KACzB,MAAM,WACJjB,GACE/U,EACJ,MAAO,CAAC,CACN,CAAC,MAAM2a,GAAAA,EAAoBC,WAAY5E,EAAO4E,SAC7C,CACD,CAAC,MAAMD,GAAAA,EAAoBE,aAAc7E,EAAO6E,WAC/C7E,EAAOd,KAAMH,EAAW+F,OAAS9E,EAAO8E,MAAO/F,EAAW6F,SAAW7F,EAAW8F,WAAa7E,EAAO+E,UAAWhG,EAAW8B,OAASb,EAAOa,MAAM,GAX9HrC,CAatB,CACDwG,KAAM,WACNC,SAAU,EACVC,UAAW,EACXC,aAAc,EACd,CAAC,IAAIC,GAAAA,EAAkBlG,iBAAiByF,GAAAA,EAAoBC,YAAa,CACvErD,QAAS,SAEX,CAAC,IAAI6D,GAAAA,EAAkBlG,iBAAiByF,GAAAA,EAAoBE,cAAe,CACzEtD,QAAS,SAEXjB,SAAU,CAAC,CACTtW,MAAO7B,IAAA,IAAC,WACN4W,GACD5W,EAAA,OAAK4W,EAAW6F,SAAW7F,EAAW8F,SAAS,EAChD5U,MAAO,CACLiV,UAAW,EACXC,aAAc,IAEf,CACDnb,MAAO6X,IAAA,IAAC,WACN9C,GACD8C,EAAA,OAAK9C,EAAW+F,KAAK,EACtB7U,MAAO,CACLC,YAAa,QAiKnB,GA7JkCW,EAAAA,YAAiB,SAAsB8N,EAAS5J,GAChF,MAAM/K,GAAQ4U,EAAAA,EAAAA,GAAgB,CAC5B5U,MAAO2U,EACPtQ,KAAM,qBAEF,SACJxG,EAAQ,UACRD,EAAS,kBACTyd,GAAoB,EAAK,MACzBP,GAAQ,EACRF,QAASU,EAAW,uBACpBC,EACAV,UAAWW,EAAa,yBACxBC,EAAwB,MACxB/E,EAAQ,CAAC,EAAC,UACViD,EAAY,CAAC,KACV7E,GACD9U,GACE,MACJ6W,GACEhQ,EAAAA,WAAiB4P,EAAAA,GACrB,IAAImE,EAAyB,MAAfU,EAAsBA,EAAczd,EAC9Cgd,EAAYW,EAChB,MAAMzG,EAAa,IACd/U,EACHqb,oBACAP,QACAF,UAAWA,EACXC,YAAaA,EACbhE,SAEI7B,EAvFkBD,KACxB,MAAM,QACJC,EAAO,MACP8F,EAAK,QACLF,EAAO,UACPC,EAAS,MACThE,GACE9B,EACE2B,EAAQ,CACZxB,KAAM,CAAC,OAAQ4F,GAAS,QAASjE,GAAS,QAAS+D,GAAWC,GAAa,aAC3ED,QAAS,CAAC,WACVC,UAAW,CAAC,cAEd,OAAO5F,EAAAA,EAAAA,GAAeyB,EAAOvZ,GAAAA,EAA6B6X,EAAQ,EA0ElDG,CAAkBJ,GAC5B2G,EAAyB,CAC7BhF,QACAiD,UAAW,CACTiB,QAASW,EACTV,UAAWY,KACR9B,KAGAgC,EAAUC,IAAiBC,EAAAA,GAAAA,GAAQ,OAAQ,CAChDje,WAAWwX,EAAAA,EAAAA,GAAKJ,EAAQE,KAAMtX,GAC9Bke,YAAapB,GACbgB,uBAAwB,IACnBA,KACA5G,GAELC,aACAhK,SAEKgR,EAAaC,IAAoBH,EAAAA,GAAAA,GAAQ,UAAW,CACzDje,UAAWoX,EAAQ4F,QACnBkB,YAAaG,EAAAA,EACbP,yBACA3G,gBAEKmH,EAAeC,IAAsBN,EAAAA,GAAAA,GAAQ,YAAa,CAC/Dje,UAAWoX,EAAQ6F,UACnBiB,YAAaG,EAAAA,EACbP,yBACA3G,eAkBF,OAhBe,MAAX6F,GAAmBA,EAAQlT,OAASuU,EAAAA,GAAeZ,IACrDT,GAAuBjd,EAAAA,EAAAA,KAAKoe,EAAa,CACvCK,QAASvF,EAAQ,QAAU,QAC3BnB,UAAWsG,GAAkBI,aAAUrW,EAAY,UAChDiW,EACHne,SAAU+c,KAGG,MAAbC,GAAqBA,EAAUnT,OAASuU,EAAAA,GAAeZ,IACzDR,GAAyBld,EAAAA,EAAAA,KAAKue,EAAe,CAC3CE,QAAS,QACTnZ,MAAO,mBACJkZ,EACHte,SAAUgd,MAGM5c,EAAAA,EAAAA,MAAM0d,EAAU,IAC/BC,EACH/d,SAAU,CAAC+c,EAASC,IAExB,I,0BC3IA,MAiBMwB,IAAc7H,EAAAA,EAAAA,IAAO,MAAO,CAChCnQ,KAAM,aACNjH,KAAM,OACN2Y,kBAAmBA,CAAC/V,EAAOgW,KACzB,MAAM,WACJjB,GACE/U,EACJ,MAAO,CAACgW,EAAOd,KAAMH,EAAWuH,UAAYtG,EAAOsG,SAAUtG,EAAOjB,EAAWqH,SAAUrH,EAAWwH,OAASvG,EAAOuG,MAAkC,aAA3BxH,EAAWyH,aAA8BxG,EAAOyG,SAAU1H,EAAW2H,UAAY1G,EAAO0G,SAAU3H,EAAWlX,UAAYmY,EAAO2G,aAAc5H,EAAWlX,UAAuC,aAA3BkX,EAAWyH,aAA8BxG,EAAO4G,qBAA+C,UAAzB7H,EAAW6C,WAAoD,aAA3B7C,EAAWyH,aAA8BxG,EAAO6G,eAAyC,SAAzB9H,EAAW6C,WAAmD,aAA3B7C,EAAWyH,aAA8BxG,EAAO8G,cAAc,GAP3hBtI,EASjB6C,EAAAA,EAAAA,IAAUlZ,IAAA,IAAC,MACZmZ,GACDnZ,EAAA,MAAM,CACL4e,OAAQ,EAERC,WAAY,EACZC,YAAa,EACbC,YAAa,QACbjS,aAAcqM,EAAMiB,MAAQjB,GAAOkB,QAAQxB,QAC3CmG,kBAAmB,OACnB7G,SAAU,CAAC,CACTtW,MAAO,CACLsc,UAAU,GAEZrW,MAAO,CACLiQ,SAAU,WACVkH,OAAQ,EACRC,KAAM,EACN3F,MAAO,SAER,CACD1X,MAAO,CACLuc,OAAO,GAETtW,MAAO,CACLgF,YAAaqM,EAAMiB,KAAO,QAAQjB,EAAMiB,KAAKC,QAAQ8E,0BAA2BC,EAAAA,GAAAA,IAAMjG,EAAMkB,QAAQxB,QAAS,OAE9G,CACDhX,MAAO,CACLoc,QAAS,SAEXnW,MAAO,CACLuX,WAAY,KAEb,CACDxd,MAAO,CACLoc,QAAS,SACTI,YAAa,cAEfvW,MAAO,CACLuX,WAAYlG,EAAMmG,QAAQ,GAC1BC,YAAapG,EAAMmG,QAAQ,KAE5B,CACDzd,MAAO,CACLoc,QAAS,SACTI,YAAa,YAEfvW,MAAO,CACLiV,UAAW5D,EAAMmG,QAAQ,GACzBtC,aAAc7D,EAAMmG,QAAQ,KAE7B,CACDzd,MAAO,CACLwc,YAAa,YAEfvW,MAAO,CACL0X,OAAQ,OACRR,kBAAmB,EACnBS,iBAAkB,SAEnB,CACD5d,MAAO,CACL0c,UAAU,GAEZzW,MAAO,CACL4X,UAAW,UACXF,OAAQ,SAET,CACD3d,MAAO6X,IAAA,IAAC,WACN9C,GACD8C,EAAA,QAAO9C,EAAWlX,QAAQ,EAC3BoI,MAAO,CACLsR,QAAS,OACTK,UAAW,SACXkG,OAAQ,EACRC,eAAgB,QAChBC,gBAAiB,QACjB,sBAAuB,CACrBC,QAAS,KACTJ,UAAW,YAGd,CACD7d,MAAO+X,IAAA,IAAC,WACNhD,GACDgD,EAAA,OAAKhD,EAAWlX,UAAuC,aAA3BkX,EAAWyH,WAA0B,EAClEvW,MAAO,CACL,sBAAuB,CACrByR,MAAO,OACPwG,UAAW,eAAe5G,EAAMiB,MAAQjB,GAAOkB,QAAQxB,UACvD+G,eAAgB,aAGnB,CACD/d,MAAOgY,IAAA,IAAC,WACNjD,GACDiD,EAAA,MAAgC,aAA3BjD,EAAWyH,aAA8BzH,EAAWlX,QAAQ,EAClEoI,MAAO,CACLkY,cAAe,SACf,sBAAuB,CACrBR,OAAQ,OACRS,WAAY,eAAe9G,EAAMiB,MAAQjB,GAAOkB,QAAQxB,UACxDgH,gBAAiB,aAGpB,CACDhe,MAAOkY,IAAA,IAAC,WACNnD,GACDmD,EAAA,MAA8B,UAAzBnD,EAAW6C,WAAoD,aAA3B7C,EAAWyH,WAA0B,EAC/EvW,MAAO,CACL,YAAa,CACXyR,MAAO,OAET,WAAY,CACVA,MAAO,SAGV,CACD1X,MAAOmY,IAAA,IAAC,WACNpD,GACDoD,EAAA,MAA8B,SAAzBpD,EAAW6C,WAAmD,aAA3B7C,EAAWyH,WAA0B,EAC9EvW,MAAO,CACL,YAAa,CACXyR,MAAO,OAET,WAAY,CACVA,MAAO,UAId,KACK2G,IAAiB7J,EAAAA,EAAAA,IAAO,OAAQ,CACpCnQ,KAAM,aACNjH,KAAM,UACN2Y,kBAAmBA,CAAC/V,EAAOgW,KACzB,MAAM,WACJjB,GACE/U,EACJ,MAAO,CAACgW,EAAOsI,QAAoC,aAA3BvJ,EAAWyH,aAA8BxG,EAAOuI,gBAAgB,GAPrE/J,EASpB6C,EAAAA,EAAAA,IAAUgB,IAAA,IAAC,MACZf,GACDe,EAAA,MAAM,CACLd,QAAS,eACTrR,YAAa,QAAQoR,EAAMmG,QAAQ,YACnCxF,aAAc,QAAQX,EAAMmG,QAAQ,YACpCe,WAAY,SACZlI,SAAU,CAAC,CACTtW,MAAO,CACLwc,YAAa,YAEfvW,MAAO,CACL6R,WAAY,QAAQR,EAAMmG,QAAQ,YAClChI,cAAe,QAAQ6B,EAAMmG,QAAQ,eAG1C,KACKgB,GAAuB5X,EAAAA,YAAiB,SAAiB8N,EAAS5J,GACtE,MAAM/K,GAAQ4U,EAAAA,EAAAA,GAAgB,CAC5B5U,MAAO2U,EACPtQ,KAAM,gBAEF,SACJiY,GAAW,EAAK,SAChBze,EAAQ,UACRD,EAAS,YACT4e,EAAc,aAAY,UAC1B9G,GAAY7X,GAA4B,aAAhB2e,EAA6B,MAAQ,MAAI,SACjEE,GAAW,EAAK,MAChBH,GAAQ,EAAK,KACbhN,GAAqB,OAAdmG,EAAqB,iBAAc3P,GAAS,UACnD6R,EAAY,SAAQ,QACpBwE,EAAU,eACPtH,GACD9U,EACE+U,EAAa,IACd/U,EACHsc,WACA5G,YACAgH,WACAH,QACAC,cACAjN,OACAqI,YACAwE,WAEIpH,EAtNkBD,KACxB,MAAM,SACJuH,EAAQ,SACRze,EAAQ,QACRmX,EAAO,SACP0H,EAAQ,MACRH,EAAK,YACLC,EAAW,UACX5E,EAAS,QACTwE,GACErH,EACE2B,EAAQ,CACZxB,KAAM,CAAC,OAAQoH,GAAY,WAAYF,EAASG,GAAS,QAAyB,aAAhBC,GAA8B,WAAYE,GAAY,WAAY7e,GAAY,eAAgBA,GAA4B,aAAhB2e,GAA8B,uBAAsC,UAAd5E,GAAyC,aAAhB4E,GAA8B,iBAAgC,SAAd5E,GAAwC,aAAhB4E,GAA8B,iBACjW8B,QAAS,CAAC,UAA2B,aAAhB9B,GAA8B,oBAErD,OAAOvH,EAAAA,EAAAA,GAAeyB,EAAOxY,GAAAA,EAAwB8W,EAAQ,EAuM7CG,CAAkBJ,GAClC,OAAoBpX,EAAAA,EAAAA,KAAK0e,GAAa,CACpC1G,GAAID,EACJ9X,WAAWwX,EAAAA,EAAAA,GAAKJ,EAAQE,KAAMtX,GAC9B2R,KAAMA,EACNxE,IAAKA,EACLgK,WAAYA,EACZ,mBAA6B,cAATxF,GAAuC,OAAdmG,GAAsC,aAAhB8G,OAA4CzW,EAAdyW,KAC9F1H,EACHjX,SAAUA,GAAwBF,EAAAA,EAAAA,KAAK0gB,GAAgB,CACrDzgB,UAAWoX,EAAQsJ,QACnBvJ,WAAYA,EACZlX,SAAUA,IACP,MAET,IAMI4gB,KACFA,GAAQC,sBAAuB,GAiEjC,YCtSaC,GAAwBA,KACnC,MAAOvS,EAASK,IAAcvC,EAAAA,EAAAA,UAAmB,KAC1CmC,EAAWK,IAAgBxC,EAAAA,EAAAA,UAAqB,KAChDoC,EAASK,IAAczC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASiD,IAAc1C,EAAAA,EAAAA,WAAkB,IACzChI,EAAOtB,IAAYsJ,EAAAA,EAAAA,UAAwB,OAC3C0U,EAAcC,IAAmB3U,EAAAA,EAAAA,UAAiB,IAClD4U,EAAUC,IAAe7U,EAAAA,EAAAA,UAAiB,IAE3C2C,EAAkBrL,UACtB,IACEoL,GAAW,GACXhM,EAAS,MAETqB,QAAQ6K,IAAI,mFAGZ,MAAMkS,QAAsBhS,EAAAA,EAAcC,qBAK1C,GAHAhL,QAAQ6K,IAAI,wDAAoDkS,EAAcjb,OAAQ,WACtF8a,EAAgBG,EAAcjb,QAED,IAAzBib,EAAcjb,OAMhB,OALA9B,QAAQ6K,IAAI,+DACZL,EAAW,IACXC,EAAa,IACbC,EAAW,SACXoS,EAAY,WAKd,MAAME,EAAgB,IAAIC,IAC1BF,EAAc1f,SAAQ4P,IAChBA,EAAO9B,QAAU8B,EAAO9B,OAAOlE,QACjC+V,EAAcE,IAAIjQ,EAAO9B,OAAOlE,OAClC,IAGF,MAAMuE,EAAyB3E,MAAMsW,KAAKH,GACvCzR,OACA1P,KAAI4P,IAAU,CACb3O,GAAI2O,EAAWC,cAAc9K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzEwB,KAAMqJ,MAGVzL,QAAQ6K,IAAI,yDAAgDW,EAAa1J,QAGzE,MAAMsb,EAAkB,IAAIC,IAC5BN,EAAc1f,SAAQ4P,IAChBA,EAAOpB,UAAYoB,EAAOpB,SAAS5E,QAAUgG,EAAO9B,QAAU8B,EAAO9B,OAAOlE,QAC9EmW,EAAgBE,IAAIrQ,EAAOpB,SAAS5E,OAAQgG,EAAO9B,OAAOlE,OAC5D,IAGF,MAAMkF,EAA6BtF,MAAMsW,KAAKC,EAAgBG,WAC3DhS,MAAK,CAAArP,EAAA0Z,KAAA,IAAE5J,GAAE9P,GAAG+P,GAAE2J,EAAA,OAAK5J,EAAEE,cAAcD,EAAE,IACrCpQ,KAAIia,IAAA,IAAE0H,EAAc/R,GAAWqK,EAAA,MAAM,CACpChZ,GAAI0gB,EAAa9R,cAAc9K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC3EwB,KAAMob,EACNpS,OAAQK,EACT,IAEHzL,QAAQ6K,IAAI,2DAAkDsB,EAAerK,QAG7E,MAAMsK,EAAyB2Q,EAC5Bpf,QAAOsP,GAAUA,EAAO,gBAAkBA,EAAO,eAAehG,SAChEpL,KAAIoR,IAAM,CACTnQ,GAAImQ,EAAO,eACX7K,KAAM6K,EAAO,eACb7B,OAAQ6B,EAAO9B,QAAU,GACzBS,SAAUqB,EAAOpB,UAAY,GAC7BQ,WAAYY,EAAO,mBAGvBjN,QAAQ6K,IAAI,yDAAgDuB,EAAatK,QA8C/E,SACEib,EACA5S,EACAC,EACAC,GAQA,GANArK,QAAQ6K,IAAI,wEACZ7K,QAAQ6K,IAAI,oDAA0CkS,EAAcjb,UACpE9B,QAAQ6K,IAAI,0DAAgDV,EAAQrI,UACpE9B,QAAQ6K,IAAI,4DAAkDT,EAAUtI,UACxE9B,QAAQ6K,IAAI,0DAAgDR,EAAQvI,UAEhEuI,EAAQvI,OAAS,EAAG,CAEtB,MAAM2b,EAAcpT,EAAQxO,KAAIwR,GAAKA,EAAEjL,OAAMmJ,OAC7CvL,QAAQ6K,IAAI,8DAAoD4S,EAAY,OAC5Ezd,QAAQ6K,IAAI,6DAAmD4S,EAAYA,EAAY3b,OAAS,OAGhG,MAAM4b,EAA0C,CAAC,EACjDrT,EAAQhN,SAAQ4P,IACd,MAAM0Q,EAAc1Q,EAAO7K,KAAKwb,OAAO,GAAGC,cAC1CH,EAAaC,IAAgBD,EAAaC,IAAgB,GAAK,CAAC,IAGlE3d,QAAQ6K,IAAI,4DACZiT,OAAOC,KAAKL,GAAcnS,OAAOlO,SAAQ2gB,IACvChe,QAAQ6K,IAAI,uCAA6BmT,MAAWN,EAAaM,aAAkB,IAIrF,MAAMC,EAAkB5T,EAAQzN,MAAKyQ,GAAKA,EAAEjL,KAAKsJ,cAAczF,SAAS,sBAClEiY,EAAqB7T,EAAQzN,MAAKyQ,GAAKA,EAAEjL,KAAKsJ,cAAczF,SAAS,yBAsB3E,GApBAjG,QAAQ6K,IAAI,sEAA4DoT,KACxEje,QAAQ6K,IAAI,yEAA+DqT,KAEvED,GACFje,QAAQ6K,IAAI,gEAAsDoT,EAAgB7b,SAEhF8b,GACFle,QAAQ6K,IAAI,mEAAyDqT,EAAmB9b,SAItF+H,EAAQrI,OAAS,IACnB9B,QAAQ6K,IAAI,sDACZV,EAAQ9M,SAAQ+N,IACd,MAAM+S,EAAgB9T,EAAQ1M,QAAO0P,GAAKA,EAAEjC,SAAWA,EAAOhJ,OAC9DpC,QAAQ6K,IAAI,uCAA6BO,EAAOhJ,SAAS+b,EAAcrc,iBAAiB,KAKxFsI,EAAUtI,OAAS,EAAG,CACxB9B,QAAQ6K,IAAI,yEACWT,EAAUvO,KAAI+P,IAAQ,CAC3CxJ,KAAMwJ,EAASxJ,KACfgc,MAAO/T,EAAQ1M,QAAO0P,GAAKA,EAAEzB,WAAaA,EAASxJ,OAAMN,WACvDyJ,MAAK,CAACS,EAAGC,IAAMA,EAAEmS,MAAQpS,EAAEoS,QAAOC,MAAM,EAAG,IAEhChhB,SAAQuO,IACrB5L,QAAQ6K,IAAI,uCAA6Be,EAASxJ,SAASwJ,EAASwS,gBAAgB,GAExF,CACF,CAEApe,QAAQ6K,IAAI,6DACd,CA/GMyT,CAAoBvB,EAAevR,EAAcW,EAAgBC,GAGjE5B,EAAWgB,GACXf,EAAa0B,GACbzB,EAAW0B,GACX0Q,EAAY,uBAEZ9c,QAAQ6K,IAAI,yDAEd,CAAE,MAAO9K,GACPC,QAAQC,MAAM,uCAAmCF,GACjDpB,EAAS,iDACT6L,EAAW,IACXC,EAAa,IACbC,EAAW,IACXkS,EAAgB,GAChBE,EAAY,QACd,CAAC,QACCnS,GAAW,EACb,GAQF,OAJAvC,EAAAA,EAAAA,YAAU,KACRwC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA3C,UACAzH,QACAqK,QAASM,EACT+R,eACAE,WACD,EA4EH,MCwFA,GAvRoC0B,KAClC,MAAM,QACJpU,EAAO,UACPC,EAAS,QACTC,EAAO,QACP3C,EAAO,MACPzH,EAAK,aACL0c,EAAY,SACZE,EAAQ,QACRvS,GACEoS,KAEJ,GAAIhV,EACF,OACE1L,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAAClJ,QAAQ,OAAO4G,cAAc,SAASrH,WAAW,SAAS4J,EAAG,EAAE7iB,SAAA,EAClEF,EAAAA,EAAAA,KAACgjB,EAAAA,EAAgB,CAACC,KAAM,MACxBjjB,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKyE,GAAI,CAAEC,GAAI,GAAIjjB,SAAC,0DAGxCF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,QAAQnZ,MAAM,iBAAiB4d,GAAI,CAAEC,GAAI,GAAIjjB,SAAC,wEAOxE,GAAIqE,EACF,OACEjE,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAACC,EAAG,EAAE7iB,SAAA,EACRF,EAAAA,EAAAA,KAACojB,EAAAA,EAAK,CAACC,SAAS,QAAQH,GAAI,CAAEI,GAAI,GAAIpjB,SACnCqE,KAEHvE,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,QAAOve,SAAC,4FAQlC,MAAMqjB,EAAgD,CAAC,EACvD5U,EAAQhN,SAAQ4P,IACd,MAAM0Q,EAAc1Q,EAAO7K,KAAKwb,OAAO,GAAGC,cAC1CoB,EAAmBtB,IAAgBsB,EAAmBtB,IAAgB,GAAK,CAAC,IAG9E,MAAMuB,EAAoB7U,EAAQxO,KAAIwR,GAAKA,EAAEjL,OAAMmJ,OAC7C0S,EAAkB5T,EAAQzN,MAAKyQ,GAAKA,EAAEjL,KAAKsJ,cAAczF,SAAS,sBAClEiY,EAAqB7T,EAAQzN,MAAKyQ,GAAKA,EAAEjL,KAAKsJ,cAAczF,SAAS,yBAGrEkZ,EAAehV,EAAQtO,KAAIuP,IAAM,CACrChJ,KAAMgJ,EAAOhJ,KACbgc,MAAO/T,EAAQ1M,QAAO0P,GAAKA,EAAEjC,SAAWA,EAAOhJ,OAAMN,WACnDyJ,MAAK,CAACS,EAAGC,IAAMA,EAAEmS,MAAQpS,EAAEoS,QAAOC,MAAM,EAAG,GAGzCe,EAAiBhV,EAAUvO,KAAI+P,IAAQ,CAC3CxJ,KAAMwJ,EAASxJ,KACfgc,MAAO/T,EAAQ1M,QAAO0P,GAAKA,EAAEzB,WAAaA,EAASxJ,OAAMN,WACvDyJ,MAAK,CAACS,EAAGC,IAAMA,EAAEmS,MAAQpS,EAAEoS,QAAOC,MAAM,EAAG,IAE/C,OACEriB,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAACC,EAAG,EAAE7iB,SAAA,EACRF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAzjB,SAAC,+CAItCF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,QAAQnZ,MAAM,iBAAiBse,WAAS,EAAA1jB,SAAC,uKAM7DI,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAAClJ,QAAQ,OAAOiK,IAAK,EAAGX,GAAI,CAAEI,GAAI,EAAGQ,SAAU,QAAS5jB,SAAA,EAC1DF,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOpd,UAC5BF,EAAAA,EAAAA,KAAC+jB,EAAI,CAAA7jB,UACHI,EAAAA,EAAAA,MAAC0jB,EAAW,CAAA9jB,SAAA,EACVF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKnZ,MAAM,UAASpF,SAAC,mBAGzCF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAIve,SACrB+gB,EAAagD,oBAEhBjkB,EAAAA,EAAAA,KAACkkB,EAAAA,EAAI,CACH/Z,MAAOgX,EACP8B,KAAK,QACL3d,MAAO2b,EAAe,IAAO,UAAY,UACzCiC,GAAI,CAAEC,GAAI,aAMlBnjB,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOpd,UAC5BF,EAAAA,EAAAA,KAAC+jB,EAAI,CAAA7jB,UACHI,EAAAA,EAAAA,MAAC0jB,EAAW,CAAA9jB,SAAA,EACVF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKnZ,MAAM,UAASpF,SAAC,aAGzCF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAIve,SACrBuO,EAAQrI,iBAMjBpG,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOpd,UAC5BF,EAAAA,EAAAA,KAAC+jB,EAAI,CAAA7jB,UACHI,EAAAA,EAAAA,MAAC0jB,EAAW,CAAA9jB,SAAA,EACVF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKnZ,MAAM,UAASpF,SAAC,eAGzCF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAIve,SACrBwO,EAAUtI,iBAMnBpG,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOpd,UAC5BF,EAAAA,EAAAA,KAAC+jB,EAAI,CAAA7jB,UACHI,EAAAA,EAAAA,MAAC0jB,EAAW,CAAA9jB,SAAA,EACVF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKnZ,MAAM,UAASpF,SAAC,aAGzCF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAIve,SACrByO,EAAQvI,oBAQnB9F,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAAClJ,QAAQ,OAAOiK,IAAK,EAAGX,GAAI,CAAEY,SAAU,QAAS5jB,SAAA,EACnDF,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOpd,UAC5BI,EAAAA,EAAAA,MAACwW,EAAAA,EAAK,CAACoM,GAAI,CAAEH,EAAG,GAAI7iB,SAAA,EAClBF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAzjB,SAAC,0BAItCI,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIpjB,SAAA,EACjBF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,YAAWve,SAAC,gCAGhCF,EAAAA,EAAAA,KAACkkB,EAAAA,EAAI,CACH/Z,MAAO8W,EAAe,IAAO,aAAU,YACvC3b,MAAO2b,EAAe,IAAO,UAAY,QACzCgC,KAAK,cAIT3iB,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIpjB,SAAA,EACjBF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,YAAWve,SAAC,yBAGhCI,EAAAA,EAAAA,MAACge,EAAAA,EAAU,CAACG,QAAQ,QAAOve,SAAA,CAAC,WACjBsjB,EAAkB,IAAM,MAAM,QAEzCljB,EAAAA,EAAAA,MAACge,EAAAA,EAAU,CAACG,QAAQ,QAAOve,SAAA,CAAC,UAClBsjB,EAAkBA,EAAkBpd,OAAS,IAAM,MAAM,WAIrE9F,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIpjB,SAAA,EACjBF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,YAAWve,SAAC,6BAGhCF,EAAAA,EAAAA,KAACkkB,EAAAA,EAAI,CACH/Z,MAAOoY,EAAkB,aAAU,YACnCjd,MAAOid,EAAkB,UAAY,QACrCU,KAAK,UAENV,IACCjiB,EAAAA,EAAAA,MAACge,EAAAA,EAAU,CAACG,QAAQ,QAAQyE,GAAI,CAAEC,GAAI,GAAIjjB,SAAA,CAAC,IACvCqiB,EAAgB7b,KAAK,WAK7BpG,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIpjB,SAAA,EACjBF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,YAAWve,SAAC,gCAGhCF,EAAAA,EAAAA,KAACkkB,EAAAA,EAAI,CACH/Z,MAAOqY,EAAqB,aAAU,YACtCld,MAAOkd,EAAqB,UAAY,QACxCS,KAAK,UAENT,IACCliB,EAAAA,EAAAA,MAACge,EAAAA,EAAU,CAACG,QAAQ,QAAQyE,GAAI,CAAEC,GAAI,GAAIjjB,SAAA,CAAC,IACvCsiB,EAAmB9b,KAAK,gBAOpC1G,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOpd,UAC5BI,EAAAA,EAAAA,MAACwW,EAAAA,EAAK,CAACoM,GAAI,CAAEH,EAAG,GAAI7iB,SAAA,EAClBF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAzjB,SAAC,yBAGtCF,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACI,GAAI,CAAExV,UAAW,IAAKqJ,SAAU,QAAS7W,SAC3CkiB,OAAOC,KAAKkB,GAAoB1T,OAAO1P,KAAImiB,IAC1ChiB,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAAclJ,QAAQ,OAAOC,eAAe,gBAAgBqJ,GAAI,CAAEI,GAAI,GAAIpjB,SAAA,EAC5EI,EAAAA,EAAAA,MAACge,EAAAA,EAAU,CAACG,QAAQ,QAAOve,SAAA,CAAEoiB,EAAO,QACpChiB,EAAAA,EAAAA,MAACge,EAAAA,EAAU,CAACG,QAAQ,QAAOve,SAAA,CAAEqjB,EAAmBjB,GAAQ,gBAFhDA,gBAUpBhiB,EAAAA,EAAAA,MAACwiB,EAAAA,EAAG,CAAClJ,QAAQ,OAAOiK,IAAK,EAAGX,GAAI,CAAEC,GAAI,EAAGW,SAAU,QAAS5jB,SAAA,EAC1DF,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOpd,UAC5BI,EAAAA,EAAAA,MAACwW,EAAAA,EAAK,CAACoM,GAAI,CAAEH,EAAG,GAAI7iB,SAAA,EAClBF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAzjB,SAAC,mCAGtCF,EAAAA,EAAAA,KAACmkB,EAAAA,EAAI,CAACjL,OAAK,EAAAhZ,SACRujB,EAAatjB,KAAI,CAACuP,EAAQrP,KACzBC,EAAAA,EAAAA,MAAC4I,EAAAA,SAAc,CAAAhJ,SAAA,EACbF,EAAAA,EAAAA,KAACokB,GAAQ,CAAAlkB,UACPF,EAAAA,EAAAA,KAACqkB,GAAY,CACXpH,QAASvN,EAAOhJ,KAChBwW,UAAW,GAAGxN,EAAOgT,oBAGxBriB,EAAQojB,EAAard,OAAS,IAAKpG,EAAAA,EAAAA,KAAC8gB,GAAO,MAPzBpR,EAAOhJ,gBAcpC1G,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOpd,UAC5BI,EAAAA,EAAAA,MAACwW,EAAAA,EAAK,CAACoM,GAAI,CAAEH,EAAG,GAAI7iB,SAAA,EAClBF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAzjB,SAAC,sCAGtCF,EAAAA,EAAAA,KAAC8iB,EAAAA,EAAG,CAACI,GAAI,CAAExV,UAAW,IAAKqJ,SAAU,QAAS7W,UAC5CF,EAAAA,EAAAA,KAACmkB,EAAAA,EAAI,CAACjL,OAAK,EAAAhZ,SACRwjB,EAAevjB,KAAI,CAAC+P,EAAU7P,KAC7BC,EAAAA,EAAAA,MAAC4I,EAAAA,SAAc,CAAAhJ,SAAA,EACbF,EAAAA,EAAAA,KAACokB,GAAQ,CAAAlkB,UACPF,EAAAA,EAAAA,KAACqkB,GAAY,CACXpH,QAAS/M,EAASxJ,KAClBwW,UAAW,GAAGhN,EAASwS,oBAG1BriB,EAAQqjB,EAAetd,OAAS,IAAKpG,EAAAA,EAAAA,KAAC8gB,GAAO,MAP3B5Q,EAASxJ,oBAiBzCua,EAAe,MACd3gB,EAAAA,EAAAA,MAAC8iB,EAAAA,EAAK,CAACC,SAAS,UAAUH,GAAI,CAAEC,GAAI,GAAIjjB,SAAA,EACtCF,EAAAA,EAAAA,KAACse,EAAAA,EAAU,CAACG,QAAQ,KAAIve,SAAC,8DAGzBI,EAAAA,EAAAA,MAACge,EAAAA,EAAU,CAACG,QAAQ,QAAOve,SAAA,CAAC,kCACM+gB,EAAagD,iBAAiB,gKAMhE,EC9OV,GAhD4BK,KAC1B,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,MAEjBC,EAAUC,KADAC,EAAAA,EAAAA,OACepY,EAAAA,EAAAA,UAAc,QACvCqY,EAAgBC,IAAqBtY,EAAAA,EAAAA,WAAkB,GAe9D,OAbAG,EAAAA,EAAAA,YAAU,KACc7I,WACpB,GAAI0gB,EAAa,CACf,MAAMO,GAAU3gB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,YAAasgB,EAAYQ,KAC3CC,QAAiBtgB,EAAAA,EAAAA,IAAOogB,GAC1BE,EAASrgB,UACX+f,EAAYM,EAAS5gB,OAEzB,GAEF6gB,EAAe,GACd,CAACV,KAGFjkB,EAAAA,EAAAA,MAAA,OAAKL,UAAU,sBAAqBC,SAAA,EAClCF,EAAAA,EAAAA,KAACklB,EAAAA,EAAO,CAACT,SAAUA,KACnBnkB,EAAAA,EAAAA,MAAA,OAAKL,UAAU,eAAcC,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,OAAKL,UAAU,aAAYC,SAAA,CAAC,mBAE1BF,EAAAA,EAAAA,KAAA,UACEW,QAASA,IAAMkkB,GAAmBD,GAClCtc,MAAO,CACLuX,WAAY,OACZhI,QAAS,WACTxK,gBAAiBuX,EAAiB,UAAY,UAC9Ctf,MAAO,QACP6a,OAAQ,OACRgF,aAAc,MACdC,OAAQ,UACRC,SAAU,QACVnlB,SAED0kB,EAAiB,mBAAqB,iCAG3C5kB,EAAAA,EAAAA,KAACD,EAAAA,EAAU,IACV6kB,GAAiB5kB,EAAAA,EAAAA,KAAC6iB,GAAiB,KAAM7iB,EAAAA,EAAAA,KAAC+R,EAAW,SAEpD,C", "sources": ["../node_modules/@mui/material/esm/ListItemText/listItemTextClasses.js", "components/shared/StatsCards.tsx", "../node_modules/@mui/material/esm/Divider/dividerClasses.js", "components/shared/Modal.tsx", "components/admin/business/utils/cardUtils.ts", "components/admin/business/hooks/useCardManagement.ts", "components/admin/business/hooks/usePageConfiguration.ts", "components/admin/business/components/CardSelector.tsx", "components/admin/business/components/CardManagement.tsx", "components/admin/business/components/FieldConfigItem.tsx", "components/admin/business/components/PageBuilderContent.tsx", "components/admin/business/types/PageBuilderTypes.ts", "components/admin/business/hooks/useOfficeDataSimple.ts", "components/admin/business/components/CheckboxDropdown.tsx", "components/admin/business/components/ReportConfiguration.tsx", "components/admin/business/PageBuilder.tsx", "components/admin/business/hooks/usePageBuilderState.ts", "../node_modules/@mui/material/esm/Card/cardClasses.js", "../node_modules/@mui/material/esm/Card/Card.js", "../node_modules/@mui/material/esm/CardContent/cardContentClasses.js", "../node_modules/@mui/material/esm/CardContent/CardContent.js", "../node_modules/@mui/material/esm/ListItem/listItemClasses.js", "../node_modules/@mui/material/esm/ListItemButton/listItemButtonClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/listItemSecondaryActionClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/ListItemSecondaryAction.js", "../node_modules/@mui/material/esm/ListItem/ListItem.js", "../node_modules/@mui/material/esm/ListItemText/ListItemText.js", "../node_modules/@mui/material/esm/Divider/Divider.js", "components/admin/business/hooks/useOfficeDataEnhanced.ts", "components/admin/business/OfficeLoadingTest.tsx", "components/admin/AdminPage.tsx"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemTextUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemText', slot);\n}\nconst listItemTextClasses = generateUtilityClasses('MuiListItemText', ['root', 'multiline', 'dense', 'inset', 'primary', 'secondary']);\nexport default listItemTextClasses;", "import React from 'react';\nimport './StatsCards.css';\n\nconst stats = [\n  { title: 'SB Accounts', value: 123456 },\n  { title: 'BD Revenue', value: '₹24,343' },\n  { title: 'No. Aadhaar Trans', value: 1259 },\n  { title: 'PLI', value: '₹99,99,999' }\n];\n\nconst StatsCards: React.FC = () => {\n  return (\n    <div className=\"stats-grid\">\n      {stats.map((stat, index) => (\n        <div className={`stat-card card-${index}`} key={index}>\n          <h3>{stat.title}</h3>\n          <p className=\"stat-value\">{stat.value}</p>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default StatsCards;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDividerUtilityClass(slot) {\n  return generateUtilityClass('MuiDivider', slot);\n}\nconst dividerClasses = generateUtilityClasses('MuiDivider', ['root', 'absolute', 'fullWidth', 'inset', 'middle', 'flexItem', 'light', 'vertical', 'withChildren', 'withChildrenVertical', 'textAlignRight', 'textAlignLeft', 'wrapper', 'wrapperVertical']);\nexport default dividerClasses;", "import React from 'react';\nimport './Modal.css';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n}\n\nconst Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={e => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2>{title}</h2>\n          <button className=\"close-button\" onClick={onClose}>&times;</button>\n        </div>\n        <div className=\"modal-body\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;", "import { <PERSON>a<PERSON>older, FaFile<PERSON>lt, Fa<PERSON>og, FaFolderOpen } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\n// Helper function to generate card style (icon and color)\nexport const generateCardStyle = (title: string) => {\n  const hash = title\n    .split('')\n    .reduce((acc, char) => acc + char.charCodeAt(0), 0);\n  \n  const icons = [FaFolder, FaFileAlt, FaCog, FaFolderOpen]; // Add more icons if needed\n  const colors = ['#FFC107', '#2196F3', '#4CAF50', '#E91E63', '#9C27B0'];\n\n  const icon = icons[hash % icons.length];\n  const color = colors[hash % colors.length];\n  \n  return { icon, color };\n};\n\n// Helper function to check if a card is a main card\nexport const isMainCard = (cardId: string, allCategories: Category[]): boolean => {\n  const card = allCategories.find(c => c.id === cardId);\n  return card ? !card.parentId : false;\n};\n\n// Helper function to check if a card is a leaf card\nexport const isLeafCard = (cardId: string, allCategories: Category[]): boolean => {\n  return !allCategories.some(c => c.parentId === cardId);\n};\n\n// Helper function to organize cards into a tree structure\nexport const organizeCards = (list: Category[]): Category[] => {\n  const map: { [key: string]: Category } = {};\n  const roots: Category[] = [];\n  list.forEach(item => {\n    map[item.id] = { ...item, children: [] }; \n  });\n  list.forEach(item => {\n    if (item.parentId && map[item.parentId]) {\n      map[item.parentId].children?.push(map[item.id]);\n    } else {\n      roots.push(map[item.id]);\n    }\n  });\n  return roots;\n};\n\n// Helper function to get all descendant IDs\nexport const getAllDescendantIds = (parentId: string, allCategories: Category[]): string[] => {\n  let descendants: string[] = [];\n  const children = allCategories.filter(c => c.parentId === parentId);\n  for (const child of children) {\n    descendants.push(child.id);\n    descendants = descendants.concat(getAllDescendantIds(child.id, allCategories));\n  }\n  return descendants;\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc, collection, getDocs, writeBatch, deleteDoc, updateDoc } from 'firebase/firestore';\nimport { Category } from '../types/PageBuilderTypes';\nimport { generateCardStyle, getAllDescendantIds } from '../utils/cardUtils';\n\ninterface UseCardManagementProps {\n  categories: Category[];\n  setCategories: (categories: Category[]) => void;\n  selectedCard: string;\n  setSelectedCard: (card: string) => void;\n  newCardId: string;\n  setNewCardId: (id: string) => void;\n  newCardTitle: string;\n  setNewCardTitle: (title: string) => void;\n  actionType: string;\n  setActionType: (type: string) => void;\n  setIsLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setShowConfirmModal: (show: boolean) => void;\n  setIsAddingNewCard: (adding: boolean) => void;\n  setPageConfig: (config: any) => void;\n  setFields: (fields: any[]) => void;\n  setEditingCard: (card: Category | null) => void;\n  setShowEditModal: (show: boolean) => void;\n  setCardToDelete: (id: string | null) => void;\n  setShowDeleteConfirmModal: (show: boolean) => void;\n}\n\nexport const useCardManagement = (props: UseCardManagementProps) => {\n  const {\n    categories,\n    setCategories,\n    selectedCard,\n    setSelectedCard,\n    newCardId,\n    setNewCardId,\n    newCardTitle,\n    setNewCardTitle,\n    actionType,\n    setActionType,\n    setIsLoading,\n    setError,\n    setSuccess,\n    setShowConfirmModal,\n    setIsAddingNewCard,\n    setPageConfig,\n    setFields,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n  } = props;\n\n  const fetchCategories = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const querySnapshot = await getDocs(collection(db, 'categories'));\n      const fetchedCategories = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Category));\n      setCategories(fetchedCategories);\n    } catch (err) {\n      setError('Failed to fetch categories.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [setCategories, setIsLoading, setError]);\n\n  const checkDuplicateId = async (id: string): Promise<boolean> => {\n    const docRef = doc(db, 'categories', id);\n    const docSnap = await getDoc(docRef);\n    return docSnap.exists();\n  };\n\n  const handleAddNewCard = async () => {\n    if (!newCardId || !newCardTitle) {\n      setError('Report ID and Title are required.');\n      return;\n    }\n    setIsLoading(true);\n    const isDuplicate = await checkDuplicateId(newCardId);\n    if (isDuplicate) {\n      setError('This Report ID already exists. Please use a unique ID.');\n      setIsLoading(false);\n      return;\n    }\n    setIsLoading(false);\n    setShowConfirmModal(true);\n  };\n\n  const handleConfirmCreate = async () => {\n    if (!newCardId || !newCardTitle) {\n        setError('Report ID and Title cannot be empty.');\n        setShowConfirmModal(false);\n        return;\n    }\n    let parentIdToSet: string | null = null;\n    if (actionType === 'createNestedCard' && selectedCard) {\n      parentIdToSet = selectedCard;\n    } else if (actionType === 'addNewCardGlobal') {\n      parentIdToSet = null; \n    } else if (selectedCard && actionType !== 'addNewCardGlobal') {\n        parentIdToSet = selectedCard;\n    } else if (!selectedCard && actionType !== 'createNestedCard') { \n        parentIdToSet = null;\n    }\n\n    const parentPath = parentIdToSet ? categories.find(c => c.id === parentIdToSet)?.path : '/categories';\n    const newPath = `${parentPath}/${newCardId}`.replace(/\\/+/g, '/');\n\n    try {\n      setIsLoading(true);\n      setShowConfirmModal(false);\n      const cardRef = doc(db, 'categories', newCardId);\n      const { icon: generatedIcon, color: generatedColor } = generateCardStyle(newCardTitle);\n      \n      await setDoc(cardRef, {\n        id: newCardId,\n        title: newCardTitle,\n        path: newPath,\n        parentId: parentIdToSet,\n        lastUpdated: new Date().toISOString(),\n        icon: generatedIcon.name,\n        color: generatedColor,\n        fields: [],\n        isPage: true,\n        pageId: newCardId,\n      });\n  \n      await fetchCategories(); \n      \n      setNewCardId('');\n      setNewCardTitle('');\n      setIsAddingNewCard(false);\n      setActionType(''); \n      setSelectedCard(newCardId);\n      setSuccess(`Report \"${newCardTitle}\" has been created successfully!`);\n      setTimeout(() => setSuccess(null), 3000);\n      \n    } catch (err) {\n      setError('Error creating new report. Check console for details.');\n      console.error('Error creating card:', err);\n    } finally {\n      setIsLoading(false); \n    }\n  };\n\n  const handleEditCard = (card: Category) => {\n    setEditingCard(card);\n    setNewCardTitle(card.title);\n    setShowEditModal(true);\n  };\n\n  const handleUpdateCard = async () => {\n    const editingCard = categories.find(c => c.id === selectedCard);\n    if (!editingCard || !newCardTitle) return;\n    try {\n      setIsLoading(true);\n      const cardRef = doc(db, 'categories', editingCard.id);\n      await updateDoc(cardRef, { title: newCardTitle, lastUpdated: new Date().toISOString() });\n      await fetchCategories();\n      setShowEditModal(false);\n      setEditingCard(null);\n      setNewCardTitle('');\n      setSuccess('Report updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to update report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDeleteClick = (cardId: string) => {\n    setCardToDelete(cardId);\n    setShowDeleteConfirmModal(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (!selectedCard) return;\n    setIsLoading(true);\n    try {\n      const batch = writeBatch(db);\n      const allDescendants = getAllDescendantIds(selectedCard, categories);\n      const idsToDelete = [selectedCard, ...allDescendants];\n\n      for (const id of idsToDelete) {\n        batch.delete(doc(db, 'categories', id));\n        batch.delete(doc(db, 'pages', id));\n      }\n      await batch.commit();\n      await fetchCategories();\n\n      setShowDeleteConfirmModal(false);\n      setCardToDelete(null);\n      setSelectedCard('');\n      setPageConfig(null);\n      setFields([]);\n      setSuccess('Report and all its nested items deleted successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to delete report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return {\n    fetchCategories,\n    handleAddNewCard,\n    handleConfirmCreate,\n    handleEditCard,\n    handleUpdateCard,\n    handleDeleteClick,\n    handleConfirmDelete,\n  };\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc } from 'firebase/firestore';\nimport { FormField, PageConfig, Category } from '../types/PageBuilderTypes';\nimport { <PERSON><PERSON>ield as DynamicFormField, FormConfig as DynamicFormConfig, FormFieldOption } from '../../../shared/DynamicForm';\nimport { supabasePageService } from '../services/supabasePageService';\n\ninterface UsePageConfigurationProps {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  setPageConfig: (config: PageConfig | null) => void;\n  fields: FormField[];\n  setFields: (fields: FormField[]) => void;\n  setAvailableDynamicFields: (fields: DynamicFormField[]) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setPreviewContent: (content: string) => void;\n  setIsPreviewOpen: (open: boolean) => void;\n  // New dropdown values - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  // Setters for dropdown values\n  setSelectedRegions: (regions: string[]) => void;\n  setSelectedDivisions: (divisions: string[]) => void;\n  setSelectedOffices: (offices: string[]) => void;\n  setSelectedFrequency: (frequency: string) => void;\n}\n\nexport const usePageConfiguration = (props: UsePageConfigurationProps) => {\n  const {\n    categories,\n    selectedCard,\n    pageConfig,\n    setPageConfig,\n    fields,\n    setFields,\n    setAvailableDynamicFields,\n    setLoading,\n    setError,\n    setSuccess,\n    setPreviewContent,\n    setIsPreviewOpen,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  } = props;\n\n  const fetchDynamicFormFields = useCallback(async (formId: string) => {\n    if (!formId) return;\n    console.log(`Fetching dynamic form fields for formId: ${formId}`);\n    try {\n      const formConfigRef = doc(db, 'formConfigs', formId);\n      const formConfigSnap = await getDoc(formConfigRef);\n      if (formConfigSnap.exists()) {\n        const formConfigData = formConfigSnap.data() as DynamicFormConfig;\n        setAvailableDynamicFields(formConfigData.fields || []);\n        console.log('Fetched dynamic fields:', formConfigData.fields);\n      } else {\n        console.log(`No dynamic form configuration found for formId: ${formId}`);\n        setAvailableDynamicFields([]);\n      }\n    } catch (err) {\n      console.error('Error fetching dynamic form fields:', err);\n      setError('Failed to fetch dynamic form fields.');\n      setAvailableDynamicFields([]);\n    }\n  }, [setAvailableDynamicFields, setError]);\n\n  const loadPageConfig = useCallback(async (cardId: string) => {\n    if (!cardId) {\n      console.log('loadPageConfig called with no cardId');\n      return;\n    }\n    console.log(`loadPageConfig called for cardId: ${cardId}`);\n    setLoading(true);\n    setError(null);\n    try {\n      // Try loading from Firebase first\n      const docRef = doc(db, 'pages', cardId);\n      const docSnap = await getDoc(docRef);\n\n      let data: PageConfig | null = null;\n\n      if (docSnap.exists()) {\n        // Loading from Firebase\n        data = docSnap.data() as PageConfig;\n      } else {\n        // If not found in Firebase, try Supabase\n        try {\n          data = await supabasePageService.loadPageConfig(cardId);\n        } catch (supabaseError) {\n          // Not found in either database, will create new config\n        }\n      }\n\n      if (data) {\n        setPageConfig(data);\n        setFields(data.fields || []);\n        // Load saved dropdown values - handle both old single values and new arrays\n        setSelectedRegions(data.selectedRegions || (data.selectedRegion ? [data.selectedRegion] : []));\n        setSelectedDivisions(data.selectedDivisions || (data.selectedDivision ? [data.selectedDivision] : []));\n        setSelectedOffices(data.selectedOffices || (data.selectedOffice ? [data.selectedOffice] : []));\n        setSelectedFrequency(data.selectedFrequency || '');\n      } else {\n        // Create new page config\n        const card = categories.find(c => c.id === cardId);\n        setPageConfig({\n          id: cardId,\n          title: card?.title || 'New Page',\n          fields: [],\n          lastUpdated: new Date().toISOString(),\n        });\n        setFields([]);\n        // Reset dropdown values for new page\n        setSelectedRegions([]);\n        setSelectedDivisions([]);\n        setSelectedOffices([]);\n        setSelectedFrequency('');\n      }\n    } catch (err) {\n      setError('Failed to load page configuration.');\n      console.error(err);\n      setPageConfig(null);\n      setFields([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [categories, setLoading, setError, setPageConfig, setFields, setSelectedRegions, setSelectedDivisions, setSelectedOffices, setSelectedFrequency]);\n\n  const addField = () => {\n    const newField: FormField = {\n      id: `field_${Date.now()}`,\n      type: 'text',\n      label: 'New Field',\n      placeholder: '',\n      options: [],\n      required: false,\n      region: '',\n      division: '',\n      office: '',\n    };\n    setFields([...fields, newField]);\n  };\n\n  const addFieldFromDynamic = (dynamicField: DynamicFormField) => {\n    console.log('Attempting to add dynamic field:', dynamicField);\n    const newField: FormField = {\n      id: dynamicField.id,\n      type: dynamicField.type,\n      label: dynamicField.label,\n      placeholder: dynamicField.placeholder,\n      options: dynamicField.options ? dynamicField.options.map((opt: string | FormFieldOption) => {\n        if (typeof opt === 'string') {\n          return { label: opt, value: opt };\n        } else {\n          return { label: opt.label, value: opt.value };\n        }\n      }) : undefined,\n      required: dynamicField.required,\n      defaultValue: dynamicField.defaultValue,\n      min: dynamicField.min,\n      max: dynamicField.max,\n      sectionTitle: undefined,\n      columns: undefined,\n      buttonText: undefined,\n      buttonType: undefined,\n      onClickAction: undefined,\n      value: undefined,\n    };\n\n    if (fields.some(field => field.id === newField.id)) {\n        console.warn(`Duplicate field ID detected: \"${newField.id}\". Field not added.`);\n        setError(`Field with ID \"${newField.id}\" already exists in the page configuration.`);\n        setTimeout(() => setError(null), 3000);\n        return;\n    }\n\n    console.log('Adding new field to state:', newField);\n    setFields([...fields, newField]);\n    setSuccess(`Added field \"${newField.label}\" to page configuration.`);\n    setTimeout(() => setSuccess(null), 3000);\n  };\n\n  const updateField = (index: number, updatedField: FormField) => {\n    const updatedFields = [...fields];\n    updatedFields[index] = updatedField;\n    setFields(updatedFields);\n  };\n\n  const removeField = (index: number) => {\n    setFields(fields.filter((_, i) => i !== index));\n  };\n\n  const handleSave = async () => {\n    if (!selectedCard || !pageConfig) {\n      setError('No report selected or page configuration loaded.');\n      return;\n    }\n\n    // Validate that report frequency is selected\n    if (!selectedFrequency) {\n      setError('Report frequency is required. Please select a frequency before saving.');\n      return;\n    }\n\n    setLoading(true);\n    console.log('Attempting to save page configuration for cardId:', selectedCard);\n    console.log('Fields being saved:', fields);\n    console.log('Report frequency:', selectedFrequency);\n\n    try {\n      const cleanedFields = fields.map(field => {\n        const cleanedField: any = {};\n        for (const key in field) {\n          if (field[key] !== undefined) {\n            cleanedField[key] = field[key];\n          } else {\n            cleanedField[key] = null;\n          }\n        }\n        return cleanedField;\n      });\n\n      const updatedPageConfig: PageConfig = {\n        ...pageConfig,\n        id: selectedCard,\n        title: categories.find(c => c.id === selectedCard)?.title || pageConfig.title,\n        fields: cleanedFields,\n        lastUpdated: new Date().toISOString(),\n        selectedRegions,\n        selectedDivisions,\n        selectedOffices,\n        selectedFrequency,\n      };\n\n      // Save to both Firebase and Supabase\n      const savePromises = [];\n\n      // Save to Firebase\n      savePromises.push(\n        setDoc(doc(db, 'pages', selectedCard), updatedPageConfig)\n          .catch(err => {\n            console.error('Firebase save failed:', err);\n            throw new Error(`Firebase save failed: ${err.message}`);\n          })\n      );\n\n      // Save to Supabase\n      savePromises.push(\n        supabasePageService.savePageConfig(updatedPageConfig)\n          .catch(err => {\n            console.error('Supabase save failed:', err);\n            throw new Error(`Supabase save failed: ${err.message}`);\n          })\n      );\n\n      // Wait for both saves to complete\n      await Promise.all(savePromises);\n\n      setPageConfig(updatedPageConfig);\n      setSuccess('Page configuration saved successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n\n    } catch (err) {\n      console.error('Failed to save page configuration:', err);\n      setError(`Failed to save page configuration: ${err instanceof Error ? err.message : 'Unknown error'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePreview = () => {\n    if (!pageConfig || fields.length === 0) {\n      alert('No page configuration or fields to preview.');\n      return;\n    }\n\n    const generatedPreview = `\n      <h1>${pageConfig.title}</h1>\n      <form>\n        ${fields.map(field => {\n          let fieldHtml = '';\n          switch (field.type) {\n            case 'text':\n            case 'number':\n            case 'date':\n            case 'textarea':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <input type=\"${field.type}\" class=\"form-control\" placeholder=\"${field.placeholder || ''}\" ${field.required ? 'required' : ''} />\n                </div>\n              `;\n              break;\n            case 'dropdown':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <select class=\"form-control\" ${field.required ? 'required' : ''}>\n                    <option value=\"\">Select ${field.label}</option>\n                    ${field.options?.map(option => `<option value=\"${option.value}\">${option.label}</option>`).join('') || ''}\n                  </select>\n                </div>\n              `;\n              break;\n            case 'checkbox':\n              fieldHtml = `\n                <div class=\"form-check mb-3\">\n                  <input type=\"checkbox\" class=\"form-check-input\" id=\"${field.id}\" ${field.required ? 'required' : ''} />\n                  <label class=\"form-check-label\" for=\"${field.id}\">${field.label}${field.required ? ' *' : ''}</label>\n                </div>\n              `;\n              break;\n            case 'radio':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  ${field.options?.map((option, i) => `\n                    <div class=\"form-check\">\n                      <input class=\"form-check-input\" type=\"radio\" name=\"${field.id}\" id=\"${field.id}-${i}\" value=\"${option.value}\" ${field.required ? 'required' : ''}>\n                      <label class=\"form-check-label\" for=\"${field.id}-${i}\">${option.label}</label>\n                    </div>\n                  `).join('') || ''}\n                </div>\n              `;\n              break;\n            case 'section':\n              fieldHtml = `\n                <div class=\"card mt-3 mb-3\">\n                  <div class=\"card-header\">${field.sectionTitle || 'Section'}</div>\n                  <div class=\"card-body\">\n                    <p>Fields for this section would appear here in the actual form.</p>\n                  </div>\n                </div>\n              `;\n              break;\n            case 'button':\n              fieldHtml = `\n                <button type=\"button\" class=\"btn btn-primary mt-3\">${field.buttonText || 'Button'}</button>\n              `;\n              break;\n            default:\n              fieldHtml = `<p>Unsupported field type: ${field.type}</p>`;\n          }\n          return fieldHtml;\n        }).join('')}\n      </form>\n    `;\n\n    setPreviewContent(generatedPreview);\n    setIsPreviewOpen(true);\n  };\n\n  return {\n    fetchDynamicFormFields,\n    loadPageConfig,\n    addField,\n    addFieldFromDynamic,\n    updateField,\n    removeField,\n    handleSave,\n    handlePreview,\n  };\n};\n", "import React from 'react';\nimport { Category } from '../types/PageBuilderTypes';\nimport { organizeCards, isLeafCard, isMainCard } from '../utils/cardUtils';\n\ninterface CardSelectorProps {\n  categories: Category[];\n  selectedCard: string;\n  onCardChange: (cardId: string) => void;\n  actionType: string;\n  onActionChange: (action: string) => void;\n  isLoading: boolean;\n  onCreateAction: () => void;\n  onWebPageAction: () => void;\n}\n\nconst CardSelector: React.FC<CardSelectorProps> = ({\n  categories,\n  selectedCard,\n  onCardChange,\n  actionType,\n  onActionChange,\n  isLoading,\n  onCreateAction,\n  onWebPageAction,\n}) => {\n  const renderCardOptions = (cards: Category[], level = 0): React.ReactElement[] => {\n    return cards.flatMap(card => [\n      <option key={card.id} value={card.id} style={{ paddingLeft: `${level * 20}px` }}>\n        {`${'--'.repeat(level)} ${card.title}`}\n      </option>,\n      ...(card.children && card.children.length > 0 ? renderCardOptions(card.children, level + 1) : []),\n    ]);\n  };\n\n  const handleCardChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newSelectedCard = e.target.value;\n    onCardChange(newSelectedCard);\n  };\n\n  const handleActionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newAction = e.target.value;\n    onActionChange(newAction);\n    \n    if (newAction === 'createNestedCard' || newAction === 'addNewCardGlobal') {\n      onCreateAction();\n    } else if (newAction === 'createWebPage') {\n      onWebPageAction();\n    }\n  };\n\n  return (\n    <div className=\"card-selector\">\n      <select\n        value={selectedCard}\n        onChange={handleCardChange}\n        className=\"form-select\"\n        disabled={isLoading}\n      >\n        <option value=\"\">{isLoading ? 'Loading Reports...' : 'Select or Create New Report'}</option>\n        {renderCardOptions(organizeCards(categories))}\n      </select>\n\n      <div className=\"action-dropdown-container\">\n        <select\n          value={actionType}\n          onChange={handleActionChange}\n          className=\"form-select action-dropdown\"\n        >\n          <option value=\"\">Select Action...</option>\n          <option value=\"addNewCardGlobal\" disabled={!!selectedCard}>\n            Create New Main Report\n          </option>\n          {selectedCard && (\n            <>\n              <option value=\"createNestedCard\">\n                Create Nested Report\n              </option>\n              <option\n                value=\"createWebPage\"\n                disabled={!isLeafCard(selectedCard, categories) || isMainCard(selectedCard, categories)}\n              >\n                Create/Edit Web Page for this Report\n              </option>\n            </>\n          )}\n        </select>\n      </div>\n    </div>\n  );\n};\n\nexport default CardSelector;\n", "import React from 'react';\nimport { FaEdit, FaTrash } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\ninterface CardManagementProps {\n  selectedCard: string;\n  categories: Category[];\n  onEditCard: (card: Category) => void;\n  onDeleteCard: (cardId: string) => void;\n}\n\nconst CardManagement: React.FC<CardManagementProps> = ({\n  selectedCard,\n  categories,\n  onEditCard,\n  onDeleteCard,\n}) => {\n  const selectedCategory = categories.find(c => c.id === selectedCard);\n\n  if (!selectedCategory) {\n    return null;\n  }\n\n  return (\n    <div className=\"card-management\">\n      <h3>Report Details: \"{selectedCategory.title}\"</h3>\n      <div className=\"card-actions\">\n        <button\n          onClick={() => onEditCard(selectedCategory)}\n          className=\"edit-button btn btn-outline-primary btn-sm me-2\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaEdit as React.ComponentType<any>)} Edit Name\n        </button>\n        <button\n          onClick={() => onDeleteCard(selectedCard)}\n          className=\"delete-button btn btn-outline-danger btn-sm\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaTrash as React.ComponentType<any>)} Delete Report\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CardManagement;\n", "import React from 'react';\nimport { FaTrash } from 'react-icons/fa';\nimport { FormField } from '../types/PageBuilderTypes';\n\ninterface FieldConfigItemProps {\n  field: FormField;\n  index: number;\n  onUpdate: (index: number, field: FormField) => void;\n  onRemove: (index: number) => void;\n}\n\nconst FieldConfigItem: React.FC<FieldConfigItemProps> = ({\n  field,\n  index,\n  onUpdate,\n  onRemove,\n}) => {\n  const handleOptionChange = (optIndex: number, value: string, key: 'label' | 'value') => {\n    const newOptions = [...(field.options || [])];\n    newOptions[optIndex] = { ...newOptions[optIndex], [key]: value };\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const addOption = () => {\n    const newOptions = [...(field.options || []), { label: '', value: '' }];\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const removeOption = (optIndex: number) => {\n    const newOptions = field.options?.filter((_, i) => i !== optIndex);\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const handleDefaultValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { value, type } = e.target;\n    let newDefaultValue: any = value;\n    if (type === 'checkbox') {\n      newDefaultValue = (e.target as HTMLInputElement).checked;\n    }\n    onUpdate(index, { ...field, defaultValue: newDefaultValue });\n  };\n\n  return (\n    <div className=\"field-config-item card mb-3\">\n      <div className=\"card-header d-flex justify-content-between align-items-center\">\n        <strong>{field.label || 'Unnamed Field'}</strong> ({field.type})\n        <button onClick={() => onRemove(index)} className=\"btn btn-danger btn-sm\">\n          {React.createElement(FaTrash as React.ComponentType<any>)} Remove\n        </button>\n      </div>\n      <div className=\"card-body\">\n        {/* Field Type Selector */}\n        <div className=\"form-group\">\n          <label htmlFor={`field-type-${index}`} className=\"form-label\">Type: </label>\n          <select\n            id={`field-type-${index}`}\n            className=\"form-control\"\n            value={field.type}\n            onChange={(e) => onUpdate(index, {\n              ...field, \n              type: e.target.value as FormField['type'], \n              options: field.type !== 'dropdown' && field.type !== 'radio' && field.type !== 'checkbox-group' ? undefined : field.options, \n              placeholder: field.type === 'section' || field.type === 'button' ? undefined : field.placeholder \n            })}\n          >\n            <option value=\"text\">Text</option>\n            <option value=\"textarea\">Textarea</option>\n            <option value=\"number\">Number</option>\n            <option value=\"date\">Date</option>\n            <option value=\"dropdown\">Dropdown</option>\n            <option value=\"radio\">Radio Group</option>\n            <option value=\"checkbox\">Checkbox (Single)</option>\n            <option value=\"checkbox-group\">Checkbox Group</option>\n            <option value=\"switch\">Switch</option>\n            <option value=\"file\">File Upload</option>\n            <option value=\"section\">Section Header</option>\n            <option value=\"button\">Button</option>\n          </select>\n        </div>\n\n        <div className=\"form-group\">\n          <label htmlFor={`field-label-${index}`} className=\"form-label\">Label: </label>\n          <input\n            id={`field-label-${index}`}\n            type=\"text\"\n            className=\"form-control\"\n            value={field.label}\n            onChange={(e) => onUpdate(index, {...field, label: e.target.value})}\n            required\n          />\n        </div>\n\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-placeholder-${index}`} className=\"form-label\">Placeholder: </label>\n            <input\n              id={`field-placeholder-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.placeholder || ''}\n              onChange={(e) => onUpdate(index, {...field, placeholder: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'number' && (\n          <>\n            <div className=\"form-group\">\n              <label htmlFor={`field-min-${index}`} className=\"form-label\">Min Value: </label>\n              <input\n                id={`field-min-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.min === undefined ? '' : field.min}\n                onChange={(e) => onUpdate(index, {...field, min: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor={`field-max-${index}`} className=\"form-label\">Max Value: </label>\n              <input\n                id={`field-max-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.max === undefined ? '' : field.max}\n                onChange={(e) => onUpdate(index, {...field, max: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n          </>\n        )}\n\n        {['dropdown', 'radio', 'checkbox-group'].includes(field.type) && (\n          <div className=\"form-group field-options-config\">\n            <label className=\"form-label\">Options: </label>\n            {field.options?.map((opt, optIndex) => (\n              <div key={optIndex} className=\"input-group mb-2\">\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Label\"\n                  value={opt.label}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'label')}\n                />\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Value\"\n                  value={opt.value}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'value')}\n                />\n                <button type=\"button\" onClick={() => removeOption(optIndex)} className=\"btn btn-outline-danger\">\n                  Remove\n                </button>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addOption} className=\"btn btn-secondary btn-sm\">\n              Add Option\n            </button>\n          </div>\n        )}\n\n        {/* Default Value - Type specific handling */}\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n            <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <input\n                    id={`field-default-value-${index}`}\n                    type={field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n            </div>\n        )}\n\n        {(field.type === 'checkbox' || field.type === 'switch') && (\n            <div className=\"form-group form-check\">\n                <input\n                    id={`field-default-value-${index}`}\n                    type=\"checkbox\"\n                    className=\"form-check-input\"\n                    checked={Boolean(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n                <label htmlFor={`field-default-value-${index}`} className=\"form-check-label\">Default Checked: </label>\n            </div>\n        )}\n\n        {['dropdown', 'radio'].includes(field.type) && field.options && field.options.length > 0 && (\n             <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <select\n                    id={`field-default-value-${index}`}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                >\n                    <option value=\"\">-- Select Default --</option>\n                    {field.options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}\n                </select>\n            </div>\n        )}\n\n        {field.type === 'checkbox-group' && (\n            <div className=\"form-group\">\n                <label className=\"form-label\">Default Values (comma-separated): </label>\n                <input\n                    type=\"text\"\n                    className=\"form-control\"\n                    value={Array.isArray(field.defaultValue) ? field.defaultValue.join(',') : ''}\n                    onChange={(e) => onUpdate(index, {...field, defaultValue: e.target.value.split(',').map(s => s.trim()).filter(s => s)})}\n                    placeholder=\"value1,value2\"\n                />\n            </div>\n        )}\n\n        {field.type === 'button' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-button-text-${index}`} className=\"form-label\">Button Text: </label>\n            <input\n              id={`field-button-text-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.buttonText || ''}\n              onChange={(e) => onUpdate(index, {...field, buttonText: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'section' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-section-title-${index}`} className=\"form-label\">Section Title: </label>\n            <input\n              id={`field-section-title-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.sectionTitle || ''}\n              onChange={(e) => onUpdate(index, {...field, sectionTitle: e.target.value})}\n            />\n          </div>\n        )}\n\n        {/* Required Checkbox (excluding button and section) */}\n        {!['button', 'section'].includes(field.type) && (\n          <div className=\"form-group form-check\">\n            <input\n              id={`field-required-${index}`}\n              type=\"checkbox\"\n              className=\"form-check-input\"\n              checked={!!field.required}\n              onChange={(e) => onUpdate(index, {...field, required: e.target.checked})}\n            />\n            <label htmlFor={`field-required-${index}`} className=\"form-check-label\"> Required</label>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FieldConfigItem;\n", "import React from 'react';\nimport { FaPlus, FaSave } from 'react-icons/fa';\nimport { FormField, PageConfig } from '../types/PageBuilderTypes';\nimport FieldConfigItem from './FieldConfigItem';\n\ninterface PageBuilderContentProps {\n  pageConfig: PageConfig;\n  fields: FormField[];\n  onAddField: () => void;\n  onUpdateField: (index: number, field: FormField) => void;\n  onRemoveField: (index: number) => void;\n  onSave: () => void;\n  onPreview: () => void;\n  loading: boolean;\n}\n\nconst PageBuilderContent: React.FC<PageBuilderContentProps> = ({\n  pageConfig,\n  fields,\n  onAddField,\n  onUpdateField,\n  onRemoveField,\n  onSave,\n  onPreview,\n  loading,\n}) => {\n  return (\n    <div className=\"builder-content\">\n      <h4>Page Configuration for: {pageConfig.title}</h4>\n      \n      <h5>Current Page Fields:</h5>\n      {fields.map((field, index) => (\n        <FieldConfigItem\n          key={field.id || index}\n          field={field}\n          index={index}\n          onUpdate={onUpdateField}\n          onRemove={onRemoveField}\n        />\n      ))}\n      \n      <button onClick={onAddField} className=\"btn btn-info mt-3\">\n        {React.createElement(FaPlus as React.ComponentType<any>)} Add Field\n      </button>\n      \n      <button \n        onClick={onSave} \n        className=\"btn btn-success mt-3 ms-2\" \n        disabled={loading || !pageConfig || fields.length === 0}\n      >\n        {React.createElement(FaSave as React.ComponentType<any>)} {loading ? 'Saving...' : 'Save Page Configuration'}\n      </button>\n      \n      <button \n        onClick={onPreview} \n        className=\"btn btn-secondary mt-3 ms-2\" \n        disabled={!pageConfig || fields.length === 0}\n      >\n        Preview Page\n      </button>\n    </div>\n  );\n};\n\nexport default PageBuilderContent;\n", "// Interfaces for PageBuilder component\n\nexport interface FormFieldOption {\n  label: string;\n  value: string;\n}\n\n// NOTE: This FormField interface is for the PageBuilder's internal state\n// and represents the configuration being built for a specific 'page'.\n// It's slightly different from the DynamicFormField used by the DynamicForm component.\nexport interface FormField {\n  id: string;\n  type: 'text' | 'textarea' | 'number' | 'date' | 'dropdown' | 'radio' | 'checkbox' | 'checkbox-group' | 'section' | 'button' | 'file' | 'switch';\n  label: string;\n  placeholder?: string;\n  region?: string;\n  division?: string;\n  office?: string;\n  options?: FormFieldOption[]; // For dropdown, radio, checkbox\n  required?: boolean;\n  value?: any; // Current value of the field (might not be used in builder, but kept for consistency)\n  // For section type\n  sectionTitle?: string;\n  columns?: number; // Number of columns for fields within the section\n  // For button type\n  buttonText?: string;\n  buttonType?: string;\n  onClickAction?: string;\n  defaultValue?: any;\n  min?: number;\n  max?: number;\n  [key: string]: any; // Add this index signature\n}\n\nexport interface PageConfig {\n  id: string;\n  title: string;\n  fields: FormField[];\n  lastUpdated: string;\n  isPage?: boolean; // New field\n  pageId?: string;\n  // Report configuration - updated to support both old single values and new arrays\n  selectedRegion?: string; // Keep for backward compatibility\n  selectedDivision?: string; // Keep for backward compatibility\n  selectedOffice?: string; // Keep for backward compatibility\n  selectedRegions?: string[]; // New array-based selections\n  selectedDivisions?: string[]; // New array-based selections\n  selectedOffices?: string[]; // New array-based selections\n  selectedFrequency?: string;\n}\n\nexport interface Category {\n  id: string;\n  title: string;\n  path: string; // e.g., /categories/parent-id/child-id\n  parentId: string | null;\n  children?: Category[];\n  icon?: string; // Icon name (e.g., 'FaFolder')\n  color?: string; // Color for the icon/card\n  fields?: FormField[]; // If storing form fields directly on category for some reason\n  lastUpdated?: string;\n  isPage: boolean; // New field\n  pageId: string; \n}\n\nexport interface PageBuilderState {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  fields: FormField[];\n  availableDynamicFields: any[];\n  isLoading: boolean;\n  loading: boolean;\n  error: string | null;\n  success: string | null;\n  isAddingNewCard: boolean;\n  newCardId: string;\n  newCardTitle: string;\n  showConfirmModal: boolean;\n  editingCard: Category | null;\n  showEditModal: boolean;\n  cardToDelete: string | null;\n  showDeleteConfirmModal: boolean;\n  actionType: string;\n  isPreviewOpen: boolean;\n  previewContent: string;\n  // New dropdown states - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n}\n\n// Report frequency options\nexport interface ReportFrequency {\n  value: string;\n  label: string;\n}\n\nexport const REPORT_FREQUENCIES: ReportFrequency[] = [\n  { value: 'daily', label: 'Daily' },\n  { value: 'weekly', label: 'Weekly' },\n  { value: 'monthly', label: 'Monthly' }\n];\n\n// Location hierarchy interfaces - matching Supabase table structure\nexport interface Region {\n  id: string;\n  name: string;\n}\n\nexport interface Division {\n  id: string;\n  name: string;\n  region: string; // matches the Region column in Supabase\n}\n\nexport interface Office {\n  id: string; // Now uses office name instead of facility ID\n  name: string;\n  region: string; // matches the Region column in Supabase\n  division: string; // matches the Division column in Supabase\n  facilityId?: string; // Keep facility ID for reference/mapping\n}\n\n// Supabase office record interface (matches actual table structure)\nexport interface SupabaseOfficeRecord {\n  'Facility ID': string;\n  Region: string;\n  Division: string;\n  'Office name': string;\n}\n", "import { useState, useEffect } from 'react';\nimport { supabase } from '../../../../config/supabaseClient';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n}\n\nexport const useOfficeDataSimple = (): UseOfficeDataReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataSimple: Fetching with enhanced pagination...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allData = await OfficeService.fetchAllOfficeData();\n\n      console.log('✅ useOfficeDataSimple: Fetched', allData.length, 'office records');\n\n      // Process regions exactly like SQL: SELECT DISTINCT \"Region\" FROM offices ORDER BY \"Region\"\n      const distinctRegions = allData\n        ?.map(row => row.Region)\n        .filter((region, index, array) => array.indexOf(region) === index)\n        .filter((region): region is string => region != null && region.trim() !== '') // Type guard to ensure string\n        .sort();\n\n      // Process regions successfully\n\n      const regionsArray: Region[] = distinctRegions?.map(regionName => ({\n        id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: regionName,\n      })) || [];\n\n      // Process divisions exactly like SQL: SELECT DISTINCT \"Region\", \"Division\" FROM offices ORDER BY \"Region\", \"Division\"\n      const distinctDivisions = allData\n        ?.map(row => ({ region: row.Region, division: row.Division }))\n        .filter((item, index, array) =>\n          array.findIndex(x => x.region === item.region && x.division === item.division) === index\n        )\n        .filter((item): item is { region: string; division: string } =>\n          item.region != null && item.division != null &&\n          item.region.trim() !== '' && item.division.trim() !== ''\n        )\n        .sort((a, b) => a.region.localeCompare(b.region) || a.division.localeCompare(b.division));\n\n      const divisionsArray: Division[] = distinctDivisions?.map(item => ({\n        id: item.division.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: item.division,\n        region: item.region,\n      })) || [];\n\n      // Process all offices - USE OFFICE NAME AS ID instead of Facility ID\n      const officesArray: Office[] = allData\n        ?.filter(row => row['Office name'] && row.Region && row.Division)\n        .map(row => ({\n          id: row['Office name'], // ✅ FIXED: Use office name as ID for form targeting\n          name: row['Office name'],\n          region: row.Region || '',\n          division: row.Division || '',\n          facilityId: row['Office name'], // Use office name as facility ID for consistency\n        })) || [];\n\n      // Data processing completed successfully\n\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n\n    } catch (err) {\n      console.error('🚨 SIMPLE: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n  };\n};\n", "import React, { useState, useRef, useEffect } from 'react';\n\ninterface Option {\n  id: string;\n  name: string;\n}\n\ninterface CheckboxDropdownProps {\n  id: string;\n  label: string;\n  options: Option[];\n  selectedValues: string[];\n  onChange: (values: string[]) => void;\n  disabled?: boolean;\n  placeholder?: string;\n}\n\nconst CheckboxDropdown: React.FC<CheckboxDropdownProps> = ({\n  id,\n  label,\n  options,\n  selectedValues,\n  onChange,\n  disabled = false,\n  placeholder = \"-- Select Options --\"\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleCheckboxChange = (optionId: string) => {\n    if (selectedValues.includes(optionId)) {\n      // Remove from selection\n      onChange(selectedValues.filter(id => id !== optionId));\n    } else {\n      // Add to selection\n      onChange([...selectedValues, optionId]);\n    }\n  };\n\n  const handleSelectAll = () => {\n    if (selectedValues.length === options.length) {\n      // Deselect all\n      onChange([]);\n    } else {\n      // Select all\n      onChange(options.map(option => option.id));\n    }\n  };\n\n  const getDisplayText = () => {\n    if (selectedValues.length === 0) {\n      return placeholder;\n    } else if (selectedValues.length === 1) {\n      const selectedOption = options.find(option => option.id === selectedValues[0]);\n      return selectedOption?.name || placeholder;\n    } else {\n      return `${selectedValues.length} selected`;\n    }\n  };\n\n  const isAllSelected = selectedValues.length === options.length && options.length > 0;\n  const isIndeterminate = selectedValues.length > 0 && selectedValues.length < options.length;\n\n  return (\n    <div className=\"form-group\">\n      <label htmlFor={id} className=\"form-label\">{label}:</label>\n      <div className=\"dropdown\" ref={dropdownRef}>\n        <button\n          id={id}\n          className={`btn btn-outline-secondary dropdown-toggle w-100 text-start ${disabled ? 'disabled' : ''}`}\n          type=\"button\"\n          onClick={() => !disabled && setIsOpen(!isOpen)}\n          disabled={disabled}\n          style={{ \n            backgroundColor: disabled ? '#e9ecef' : 'white',\n            borderColor: '#ced4da'\n          }}\n        >\n          <span className={selectedValues.length === 0 ? 'text-muted' : ''}>\n            {getDisplayText()}\n          </span>\n        </button>\n        \n        {isOpen && !disabled && (\n          <div className=\"dropdown-menu show w-100\" style={{ maxHeight: '300px', overflowY: 'auto' }}>\n            {/* Select All Option */}\n            {options.length > 1 && (\n              <>\n                <div className=\"dropdown-item\">\n                  <div className=\"form-check\">\n                    <input\n                      className=\"form-check-input\"\n                      type=\"checkbox\"\n                      id={`${id}-select-all`}\n                      checked={isAllSelected}\n                      ref={(input) => {\n                        if (input) input.indeterminate = isIndeterminate;\n                      }}\n                      onChange={handleSelectAll}\n                    />\n                    <label className=\"form-check-label fw-bold\" htmlFor={`${id}-select-all`}>\n                      Select All ({options.length})\n                    </label>\n                  </div>\n                </div>\n                <hr className=\"dropdown-divider\" />\n              </>\n            )}\n            \n            {/* Individual Options */}\n            {options.map(option => (\n              <div key={option.id} className=\"dropdown-item\">\n                <div className=\"form-check\">\n                  <input\n                    className=\"form-check-input\"\n                    type=\"checkbox\"\n                    id={`${id}-${option.id}`}\n                    checked={selectedValues.includes(option.id)}\n                    onChange={() => handleCheckboxChange(option.id)}\n                  />\n                  <label className=\"form-check-label\" htmlFor={`${id}-${option.id}`}>\n                    {option.name}\n                  </label>\n                </div>\n              </div>\n            ))}\n            \n            {options.length === 0 && (\n              <div className=\"dropdown-item text-muted\">\n                <em>No options available</em>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n      \n      {/* Selected count indicator */}\n      {selectedValues.length > 0 && (\n        <small className=\"text-muted mt-1 d-block\">\n          {selectedValues.length} of {options.length} selected\n        </small>\n      )}\n    </div>\n  );\n};\n\nexport default CheckboxDropdown;\n", "import React, { useEffect } from 'react';\nimport { REPORT_FREQUENCIES } from '../types/PageBuilderTypes';\nimport { useOfficeDataSimple as useOfficeData } from '../hooks/useOfficeDataSimple';\nimport CheckboxDropdown from './CheckboxDropdown';\n\ninterface ReportConfigurationProps {\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  onRegionsChange: (regions: string[]) => void;\n  onDivisionsChange: (divisions: string[]) => void;\n  onOfficesChange: (offices: string[]) => void;\n  onFrequencyChange: (frequency: string) => void;\n}\n\nconst ReportConfiguration: React.FC<ReportConfigurationProps> = ({\n  selectedRegions,\n  selectedDivisions,\n  selectedOffices,\n  selectedFrequency,\n  onRegionsChange,\n  onDivisionsChange,\n  onOfficesChange,\n  onFrequencyChange,\n}) => {\n  // Use custom hook to fetch office data from Supabase\n  const { regions, divisions, offices, loading, error, refetch } = useOfficeData();\n\n  // Filter divisions based on selected regions\n  const selectedRegionNames = selectedRegions.map(regionId =>\n    regions.find(r => r.id === regionId)?.name\n  ).filter(Boolean);\n\n  const availableDivisions = selectedRegions.length > 0\n    ? divisions.filter(division => selectedRegionNames.includes(division.region))\n    : divisions; // Show all divisions if no regions selected\n\n  // Filter offices based on selected divisions\n  const selectedDivisionNames = selectedDivisions.map(divisionId =>\n    divisions.find(d => d.id === divisionId)?.name\n  ).filter(Boolean);\n\n  const availableOffices = selectedDivisions.length > 0\n    ? offices.filter(office =>\n        selectedRegionNames.includes(office.region) &&\n        selectedDivisionNames.includes(office.division)\n      )\n    : selectedRegions.length > 0\n      ? offices.filter(office => selectedRegionNames.includes(office.region))\n      : offices; // Show all offices if no filters applied\n\n  // Reset dependent selections when parent selections change\n  useEffect(() => {\n    if (selectedRegions.length > 0) {\n      // Remove divisions that don't belong to selected regions\n      const validDivisions = selectedDivisions.filter(divisionId => {\n        const division = divisions.find(d => d.id === divisionId);\n        return division && selectedRegionNames.includes(division.region);\n      });\n\n      if (validDivisions.length !== selectedDivisions.length) {\n        onDivisionsChange(validDivisions);\n      }\n    }\n  }, [selectedRegions, selectedDivisions, divisions, selectedRegionNames, onDivisionsChange]);\n\n  useEffect(() => {\n    if (selectedDivisions.length > 0) {\n      // Remove offices that don't belong to selected regions/divisions\n      const validOffices = selectedOffices.filter(officeId => {\n        const office = offices.find(o => o.id === officeId);\n        return office &&\n               selectedRegionNames.includes(office.region) &&\n               selectedDivisionNames.includes(office.division);\n      });\n\n      if (validOffices.length !== selectedOffices.length) {\n        onOfficesChange(validOffices);\n      }\n    }\n  }, [selectedDivisions, selectedOffices, offices, selectedRegionNames, selectedDivisionNames, onOfficesChange]);\n\n  return (\n    <div className=\"report-configuration mt-3 mb-3\">\n      <h5>Report Configuration</h5>\n\n      {loading && (\n        <div className=\"alert alert-info\">\n          <div className=\"d-flex align-items-center\">\n            <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n            Loading office data...\n          </div>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"alert alert-danger\">\n          <strong>Error:</strong> {error}\n          <button\n            className=\"btn btn-sm btn-outline-danger ms-2\"\n            onClick={refetch}\n          >\n            Retry\n          </button>\n        </div>\n      )}\n\n      {!loading && !error && (\n        <div className=\"row\">\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"region-select\"\n              label=\"Select Regions\"\n              options={regions}\n              selectedValues={selectedRegions}\n              onChange={onRegionsChange}\n              disabled={loading}\n              placeholder=\"-- Select Regions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"division-select\"\n              label=\"Select Divisions\"\n              options={availableDivisions}\n              selectedValues={selectedDivisions}\n              onChange={onDivisionsChange}\n              disabled={selectedRegions.length === 0 || loading}\n              placeholder=\"-- Select Divisions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"office-select\"\n              label=\"Select Offices\"\n              options={availableOffices}\n              selectedValues={selectedOffices}\n              onChange={onOfficesChange}\n              disabled={selectedDivisions.length === 0 || loading}\n              placeholder=\"-- Select Offices --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <div className=\"form-group\">\n              <label htmlFor=\"frequency-select\" className=\"form-label\">\n                Report Frequency: <span className=\"text-danger\">*</span>\n              </label>\n              <select\n                id=\"frequency-select\"\n                className={`form-select ${!selectedFrequency ? 'is-invalid' : ''}`}\n                value={selectedFrequency}\n                onChange={(e) => onFrequencyChange(e.target.value)}\n                disabled={loading}\n                required\n              >\n                <option value=\"\">-- Select Frequency --</option>\n                {REPORT_FREQUENCIES.map(frequency => (\n                  <option key={frequency.value} value={frequency.value}>\n                    {frequency.label}\n                  </option>\n                ))}\n              </select>\n              {!selectedFrequency && (\n                <div className=\"invalid-feedback\">\n                  Report frequency is required.\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ReportConfiguration;\n", "import React, { useEffect } from 'react';\nimport Modal from '../../shared/Modal';\nimport './PageBuilder.css';\n\n// Import refactored components and hooks\nimport { usePageBuilderState } from './hooks/usePageBuilderState';\nimport { useCardManagement } from './hooks/useCardManagement';\nimport { usePageConfiguration } from './hooks/usePageConfiguration';\nimport CardSelector from './components/CardSelector';\nimport CardManagement from './components/CardManagement';\nimport PageBuilderContent from './components/PageBuilderContent';\nimport ReportConfiguration from './components/ReportConfiguration';\n// Debug components removed - Supabase integration working\nimport { isLeafCard, isMainCard } from './utils/cardUtils';\n\n// All interfaces and utilities are now imported from separate files\n\nconst PageBuilder: React.FC = () => {\n  // Use custom hooks for state management\n  const state = usePageBuilderState();\n\n  // Debug mode removed - using working SQL-based implementation\n\n  // Initialize custom hooks\n  const cardManagement = useCardManagement({\n    categories: state.categories,\n    setCategories: state.setCategories,\n    selectedCard: state.selectedCard,\n    setSelectedCard: state.setSelectedCard,\n    newCardId: state.newCardId,\n    setNewCardId: state.setNewCardId,\n    newCardTitle: state.newCardTitle,\n    setNewCardTitle: state.setNewCardTitle,\n    actionType: state.actionType,\n    setActionType: state.setActionType,\n    setIsLoading: state.setIsLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setShowConfirmModal: state.setShowConfirmModal,\n    setIsAddingNewCard: state.setIsAddingNewCard,\n    setPageConfig: state.setPageConfig,\n    setFields: state.setFields,\n    setEditingCard: state.setEditingCard,\n    setShowEditModal: state.setShowEditModal,\n    setCardToDelete: state.setCardToDelete,\n    setShowDeleteConfirmModal: state.setShowDeleteConfirmModal,\n  });\n\n  const pageConfiguration = usePageConfiguration({\n    categories: state.categories,\n    selectedCard: state.selectedCard,\n    pageConfig: state.pageConfig,\n    setPageConfig: state.setPageConfig,\n    fields: state.fields,\n    setFields: state.setFields,\n    setAvailableDynamicFields: state.setAvailableDynamicFields,\n    setLoading: state.setLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setPreviewContent: state.setPreviewContent,\n    setIsPreviewOpen: state.setIsPreviewOpen,\n    selectedRegions: state.selectedRegions,\n    selectedDivisions: state.selectedDivisions,\n    selectedOffices: state.selectedOffices,\n    selectedFrequency: state.selectedFrequency,\n    setSelectedRegions: state.setSelectedRegions,\n    setSelectedDivisions: state.setSelectedDivisions,\n    setSelectedOffices: state.setSelectedOffices,\n    setSelectedFrequency: state.setSelectedFrequency,\n  });\n\n  // Initialize data on component mount\n  useEffect(() => {\n    cardManagement.fetchCategories();\n  }, []);\n\n  // Handle card and action changes\n  useEffect(() => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.actionType === 'createWebPage') {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n      pageConfiguration.fetchDynamicFormFields(state.selectedCard);\n    } else if (state.selectedCard && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && state.actionType === 'createWebPage') {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n    } else if (!state.selectedCard) {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n      state.setActionType('');\n    }\n\n    if (state.selectedCard && state.actionType !== 'createWebPage') {\n        state.setAvailableDynamicFields([]);\n    }\n  }, [state.selectedCard, state.categories, state.actionType]);\n\n  // Event handlers for UI interactions\n  const handleCardChange = (cardId: string) => {\n    state.setSelectedCard(cardId);\n    state.setActionType('');\n    if (!cardId) {\n        state.setPageConfig(null);\n        state.setFields([]);\n    } else {\n        const cardIsLeaf = isLeafCard(cardId, state.categories);\n        const cardIsMain = isMainCard(cardId, state.categories);\n        if(!cardIsLeaf || cardIsMain) {\n            state.setPageConfig(null);\n            state.setFields([]);\n        }\n    }\n  };\n\n  const handleActionChange = (action: string) => {\n    state.setActionType(action);\n  };\n\n  const handleCreateAction = () => {\n    state.setNewCardId('');\n    state.setNewCardTitle('');\n    state.setIsAddingNewCard(true);\n  };\n\n  const handleWebPageAction = () => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories)) {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n    } else if (state.selectedCard) {\n      state.setError('Web page can only be created/edited for a final nested report (not a main report).');\n      state.setPageConfig(null);\n      state.setFields([]);\n    }\n  };\n\n  // Event handlers for report configuration dropdowns - updated for arrays\n  const handleRegionsChange = (regions: string[]) => {\n    state.setSelectedRegions(regions);\n    // Reset dependent dropdowns when regions change\n    state.setSelectedDivisions([]);\n    state.setSelectedOffices([]);\n  };\n\n  const handleDivisionsChange = (divisions: string[]) => {\n    state.setSelectedDivisions(divisions);\n    // Reset dependent dropdown when divisions change\n    state.setSelectedOffices([]);\n  };\n\n  const handleOfficesChange = (offices: string[]) => {\n    state.setSelectedOffices(offices);\n  };\n\n  const handleFrequencyChange = (frequency: string) => {\n    state.setSelectedFrequency(frequency);\n  };\n\n  // All card management functions are now handled by the useCardManagement hook\n\n  // All page builder functions are now handled by the usePageConfiguration hook\n\n  // All rendering functions are now handled by separate components\n  \n  // All field rendering is now handled by the FieldConfigItem component\n\n  return (\n    <>\n      <div className=\"page-builder\">\n        {state.error && <div className=\"error-message\">{state.error}</div>}\n        {state.success && (\n          <div className=\"success-message\">\n            {state.success}\n          </div>\n        )}\n        <h2>Report & Page Builder</h2>\n\n        <CardSelector\n          categories={state.categories}\n          selectedCard={state.selectedCard}\n          onCardChange={handleCardChange}\n          actionType={state.actionType}\n          onActionChange={handleActionChange}\n          isLoading={state.isLoading}\n          onCreateAction={handleCreateAction}\n          onWebPageAction={handleWebPageAction}\n        />\n\n        {/* Modal for adding/creating new card */}\n        {state.isAddingNewCard && (\n          <Modal\n            isOpen={state.isAddingNewCard}\n            onClose={() => {\n              state.setIsAddingNewCard(false);\n              state.setActionType('');\n              state.setNewCardId('');\n              state.setNewCardTitle('');\n            }}\n            title={\n              state.actionType === 'addNewCardGlobal' ? \"Create New Main Report\" :\n              state.selectedCard && state.actionType === 'createNestedCard' ? `Add Nested Report under \"${state.categories.find(c => c.id === state.selectedCard)?.title}\"` :\n              \"Create New Report\"\n            }\n          >\n            <div className=\"new-card-form\">\n              <input\n                type=\"text\"\n                placeholder=\"Report ID (e.g., 'new-report-id')\"\n                value={state.newCardId}\n                onChange={(e) => state.setNewCardId(e.target.value.toLowerCase().replace(/\\s+/g, '-'))}\n                className=\"form-control mb-2\"\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Report Title\"\n                value={state.newCardTitle}\n                onChange={(e) => state.setNewCardTitle(e.target.value)}\n                className=\"form-control mb-2\"\n              />\n              <div className=\"form-buttons modal-buttons\">\n                <button\n                  onClick={cardManagement.handleConfirmCreate}\n                  disabled={state.isLoading || !state.newCardId || !state.newCardTitle}\n                  className=\"btn btn-primary\"\n                >\n                  {state.isLoading ? 'Creating...' : 'Confirm & Create Report'}\n                </button>\n                <button onClick={() => {\n                  state.setIsAddingNewCard(false);\n                  state.setActionType('');\n                  state.setNewCardId('');\n                  state.setNewCardTitle('');\n                }} className=\"btn btn-secondary\">\n                  Cancel\n                </button>\n              </div>\n            </div>\n          </Modal>\n        )}\n\n        {/* Conditional Rendering for Card Management OR Page Builder OR Warnings */}\n        {state.selectedCard && (\n          <>\n            {/* Card Management Section */}\n            {!(state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig) && (\n              <CardManagement\n                selectedCard={state.selectedCard}\n                categories={state.categories}\n                onEditCard={cardManagement.handleEditCard}\n                onDeleteCard={cardManagement.handleDeleteClick}\n              />\n            )}\n\n            {/* Report Configuration Dropdowns */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && (\n              <ReportConfiguration\n                selectedRegions={state.selectedRegions}\n                selectedDivisions={state.selectedDivisions}\n                selectedOffices={state.selectedOffices}\n                selectedFrequency={state.selectedFrequency}\n                onRegionsChange={handleRegionsChange}\n                onDivisionsChange={handleDivisionsChange}\n                onOfficesChange={handleOfficesChange}\n                onFrequencyChange={handleFrequencyChange}\n              />\n            )}\n\n            {/* Page Builder Content */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig && (\n              <PageBuilderContent\n                pageConfig={state.pageConfig}\n                fields={state.fields}\n                onAddField={pageConfiguration.addField}\n                onUpdateField={pageConfiguration.updateField}\n                onRemoveField={pageConfiguration.removeField}\n                onSave={pageConfiguration.handleSave}\n                onPreview={pageConfiguration.handlePreview}\n                loading={state.loading}\n              />\n            )}\n\n            {/* Warning Messages */}\n            {state.actionType === 'createWebPage' && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && (\n              <div className=\"warning-message mt-3 p-2 bg-warning text-dark rounded\">\n                Page configuration is only available for final nested reports (which are not main reports). Please select an appropriate nested report to configure its page, or create one.\n              </div>\n            )}\n            {state.actionType !== 'createWebPage' && !isLeafCard(state.selectedCard, state.categories) && (\n              <div className=\"info-message mt-3 p-2 bg-info text-dark rounded\">\n                This is a parent report. You can create nested reports under it or select an existing nested report to manage or configure its page.\n              </div>\n            )}\n          </>\n        )}\n\n        {!state.selectedCard && state.actionType === '' && (\n          <div className=\"info-message mt-3 p-3 bg-light border rounded\">\n            <p>Select a report from the dropdown to manage it or configure its web page (if applicable).</p>\n            <p>If no reports exist, or to create a new top-level report, choose \"Create New Main Report\" from the action dropdown after clearing any selection.</p>\n          </div>\n        )}\n\n        {/* Modals for Edit and Delete Confirmation */}\n        {state.showEditModal && state.editingCard && (\n          <Modal\n            isOpen={state.showEditModal}\n            onClose={() => {\n              state.setShowEditModal(false);\n              state.setNewCardTitle('');\n              state.setEditingCard(null);\n            }}\n            title={`Edit Report: ${state.editingCard.title}`}\n          >\n            <input\n              type=\"text\"\n              value={state.newCardTitle}\n              onChange={(e) => state.setNewCardTitle(e.target.value)}\n              placeholder=\"New Report Title\"\n              className=\"form-control mb-2\"\n            />\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleUpdateCard}\n                className=\"btn btn-primary\"\n                disabled={state.isLoading || !state.newCardTitle.trim()}\n              >\n                {state.isLoading ? 'Updating...' : 'Update Title'}\n              </button>\n              <button\n                onClick={() => {\n                  state.setShowEditModal(false);\n                  state.setNewCardTitle('');\n                  state.setEditingCard(null);\n                }}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {state.showDeleteConfirmModal && state.cardToDelete && (\n          <Modal\n            isOpen={state.showDeleteConfirmModal}\n            onClose={() => state.setShowDeleteConfirmModal(false)}\n            title=\"Confirm Deletion\"\n          >\n            <p>Are you sure you want to delete the report \"{state.categories.find(c => c.id === state.cardToDelete)?.title}\" and ALL its nested reports and associated page configurations? This action cannot be undone.</p>\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleConfirmDelete}\n                className=\"btn btn-danger\"\n                disabled={state.isLoading}\n              >\n                {state.isLoading ? 'Deleting...' : 'Confirm Delete'}\n              </button>\n              <button\n                onClick={() => state.setShowDeleteConfirmModal(false)}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {/* Preview Modal */}\n        <Modal\n          isOpen={state.isPreviewOpen}\n          onClose={() => state.setIsPreviewOpen(false)}\n          title=\"Page Preview\"\n        >\n          <div dangerouslySetInnerHTML={{ __html: state.previewContent }} />\n        </Modal>\n      </div>\n    </>\n  );\n};\n\nexport default PageBuilder;\n\n\n", "import { useState } from 'react';\nimport { Category, FormField, PageConfig } from '../types/PageBuilderTypes';\nimport { FormField as DynamicFormField } from '../../../shared/DynamicForm';\n\nexport const usePageBuilderState = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [selectedCard, setSelectedCard] = useState<string>('');\n  const [pageConfig, setPageConfig] = useState<PageConfig | null>(null);\n  const [fields, setFields] = useState<FormField[]>([]);\n  const [availableDynamicFields, setAvailableDynamicFields] = useState<DynamicFormField[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const [isAddingNewCard, setIsAddingNewCard] = useState<boolean>(false);\n  const [newCardId, setNewCardId] = useState<string>('');\n  const [newCardTitle, setNewCardTitle] = useState<string>('');\n  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);\n\n  const [editingCard, setEditingCard] = useState<Category | null>(null);\n  const [showEditModal, setShowEditModal] = useState<boolean>(false);\n\n  const [cardToDelete, setCardToDelete] = useState<string | null>(null);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<boolean>(false);\n\n  const [actionType, setActionType] = useState<string>('');\n\n  // State for Preview Modal\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false);\n  const [previewContent, setPreviewContent] = useState('');\n\n  // New dropdown states - updated to arrays for multiple selections\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\n  const [selectedDivisions, setSelectedDivisions] = useState<string[]>([]);\n  const [selectedOffices, setSelectedOffices] = useState<string[]>([]);\n  const [selectedFrequency, setSelectedFrequency] = useState<string>('');\n\n  return {\n    // State values\n    categories,\n    selectedCard,\n    pageConfig,\n    fields,\n    availableDynamicFields,\n    isLoading,\n    loading,\n    error,\n    success,\n    isAddingNewCard,\n    newCardId,\n    newCardTitle,\n    showConfirmModal,\n    editingCard,\n    showEditModal,\n    cardToDelete,\n    showDeleteConfirmModal,\n    actionType,\n    isPreviewOpen,\n    previewContent,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n\n    // State setters\n    setCategories,\n    setSelectedCard,\n    setPageConfig,\n    setFields,\n    setAvailableDynamicFields,\n    setIsLoading,\n    setLoading,\n    setError,\n    setSuccess,\n    setIsAddingNewCard,\n    setNewCardId,\n    setNewCardTitle,\n    setShowConfirmModal,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n    setActionType,\n    setIsPreviewOpen,\n    setPreviewContent,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  };\n};\n", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root']);\nexport default cardClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getCardUtilityClass } from \"./cardClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardUtilityClass, classes);\n};\nconst CardRoot = styled(Paper, {\n  name: 'MuiCard',\n  slot: 'Root'\n})({\n  overflow: 'hidden'\n});\nconst Card = /*#__PURE__*/React.forwardRef(function Card(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCard'\n  });\n  const {\n    className,\n    raised = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    raised\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardRoot, {\n    className: clsx(classes.root, className),\n    elevation: raised ? 8 : undefined,\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Card.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the card will use raised styling.\n   * @default false\n   */\n  raised: chainPropTypes(PropTypes.bool, props => {\n    if (props.raised && props.variant === 'outlined') {\n      return new Error('MUI: Combining `raised={true}` with `variant=\"outlined\"` has no effect.');\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Card;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardContentUtilityClass(slot) {\n  return generateUtilityClass('MuiCardContent', slot);\n}\nconst cardContentClasses = generateUtilityClasses('MuiCardContent', ['root']);\nexport default cardContentClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getCardContentUtilityClass } from \"./cardContentClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardContentUtilityClass, classes);\n};\nconst CardContentRoot = styled('div', {\n  name: 'MuiCardContent',\n  slot: 'Root'\n})({\n  padding: 16,\n  '&:last-child': {\n    paddingBottom: 24\n  }\n});\nconst CardContent = /*#__PURE__*/React.forwardRef(function CardContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardContent'\n  });\n  const {\n    className,\n    component = 'div',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardContentRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardContent;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemUtilityClass(slot) {\n  return generateUtilityClass('MuiListItem', slot);\n}\nconst listItemClasses = generateUtilityClasses('MuiListItem', ['root', 'container', 'dense', 'alignItemsFlexStart', 'divider', 'gutters', 'padding', 'secondaryAction']);\nexport default listItemClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemButton', slot);\n}\nconst listItemButtonClasses = generateUtilityClasses('MuiListItemButton', ['root', 'focusVisible', 'dense', 'alignItemsFlexStart', 'disabled', 'divider', 'gutters', 'selected']);\nexport default listItemButtonClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemSecondaryActionClassesUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemSecondaryAction', slot);\n}\nconst listItemSecondaryActionClasses = generateUtilityClasses('MuiListItemSecondaryAction', ['root', 'disableGutters']);\nexport default listItemSecondaryActionClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemSecondaryActionClassesUtilityClass } from \"./listItemSecondaryActionClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disableGutters,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getListItemSecondaryActionClassesUtilityClass, classes);\n};\nconst ListItemSecondaryActionRoot = styled('div', {\n  name: 'MuiListItemSecondaryAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.disableGutters && styles.disableGutters];\n  }\n})({\n  position: 'absolute',\n  right: 16,\n  top: '50%',\n  transform: 'translateY(-50%)',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.disableGutters,\n    style: {\n      right: 0\n    }\n  }]\n});\n\n/**\n * Must be used as the last child of ListItem to function properly.\n *\n * @deprecated Use the `secondaryAction` prop in the `ListItem` component instead. This component will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n */\nconst ListItemSecondaryAction = /*#__PURE__*/React.forwardRef(function ListItemSecondaryAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemSecondaryAction'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const ownerState = {\n    ...props,\n    disableGutters: context.disableGutters\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemSecondaryActionRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemSecondaryAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or selection control.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nListItemSecondaryAction.muiName = 'ListItemSecondaryAction';\nexport default ListItemSecondaryAction;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport isMuiElement from \"../utils/isMuiElement.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemUtilityClass } from \"./listItemClasses.js\";\nimport { listItemButtonClasses } from \"../ListItemButton/index.js\";\nimport ListItemSecondaryAction from \"../ListItemSecondaryAction/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.dense && styles.dense, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart, ownerState.divider && styles.divider, !ownerState.disableGutters && styles.gutters, !ownerState.disablePadding && styles.padding, ownerState.hasSecondaryAction && styles.secondaryAction];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes,\n    dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  } = ownerState;\n  const slots = {\n    root: ['root', dense && 'dense', !disableGutters && 'gutters', !disablePadding && 'padding', divider && 'divider', alignItems === 'flex-start' && 'alignItemsFlexStart', hasSecondaryAction && 'secondaryAction'],\n    container: ['container']\n  };\n  return composeClasses(slots, getListItemUtilityClass, classes);\n};\nexport const ListItemRoot = styled('div', {\n  name: 'MuiListItem',\n  slot: 'Root',\n  overridesResolver\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'flex-start',\n  alignItems: 'center',\n  position: 'relative',\n  textDecoration: 'none',\n  width: '100%',\n  boxSizing: 'border-box',\n  textAlign: 'left',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding,\n    style: {\n      paddingTop: 8,\n      paddingBottom: 8\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && ownerState.dense,\n    style: {\n      paddingTop: 4,\n      paddingBottom: 4\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !ownerState.disableGutters,\n    style: {\n      paddingLeft: 16,\n      paddingRight: 16\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !!ownerState.secondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.secondaryAction,\n    style: {\n      [`& > .${listItemButtonClasses.root}`]: {\n        paddingRight: 48\n      }\n    }\n  }, {\n    props: {\n      alignItems: 'flex-start'\n    },\n    style: {\n      alignItems: 'flex-start'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.divider,\n    style: {\n      borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,\n      backgroundClip: 'padding-box'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.button,\n    style: {\n      transition: theme.transitions.create('background-color', {\n        duration: theme.transitions.duration.shortest\n      }),\n      '&:hover': {\n        textDecoration: 'none',\n        backgroundColor: (theme.vars || theme).palette.action.hover,\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: 'transparent'\n        }\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hasSecondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }]\n})));\nconst ListItemContainer = styled('li', {\n  name: 'MuiListItem',\n  slot: 'Container'\n})({\n  position: 'relative'\n});\n\n/**\n * Uses an additional container component if `ListItemSecondaryAction` is the last child.\n */\nconst ListItem = /*#__PURE__*/React.forwardRef(function ListItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItem'\n  });\n  const {\n    alignItems = 'center',\n    children: childrenProp,\n    className,\n    component: componentProp,\n    components = {},\n    componentsProps = {},\n    ContainerComponent = 'li',\n    ContainerProps: {\n      className: ContainerClassName,\n      ...ContainerProps\n    } = {},\n    dense = false,\n    disableGutters = false,\n    disablePadding = false,\n    divider = false,\n    secondaryAction,\n    slotProps = {},\n    slots = {},\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const childContext = React.useMemo(() => ({\n    dense: dense || context.dense || false,\n    alignItems,\n    disableGutters\n  }), [alignItems, context.dense, dense, disableGutters]);\n  const listItemRef = React.useRef(null);\n  const children = React.Children.toArray(childrenProp);\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  const hasSecondaryAction = children.length && isMuiElement(children[children.length - 1], ['ListItemSecondaryAction']);\n  const ownerState = {\n    ...props,\n    alignItems,\n    dense: childContext.dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleRef = useForkRef(listItemRef, ref);\n  const Root = slots.root || components.Root || ListItemRoot;\n  const rootProps = slotProps.root || componentsProps.root || {};\n  const componentProps = {\n    className: clsx(classes.root, rootProps.className, className),\n    ...other\n  };\n  let Component = componentProp || 'li';\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  if (hasSecondaryAction) {\n    // Use div by default.\n    Component = !componentProps.component && !componentProp ? 'div' : Component;\n\n    // Avoid nesting of li > li.\n    if (ContainerComponent === 'li') {\n      if (Component === 'li') {\n        Component = 'div';\n      } else if (componentProps.component === 'li') {\n        componentProps.component = 'div';\n      }\n    }\n    return /*#__PURE__*/_jsx(ListContext.Provider, {\n      value: childContext,\n      children: /*#__PURE__*/_jsxs(ListItemContainer, {\n        as: ContainerComponent,\n        className: clsx(classes.container, ContainerClassName),\n        ref: handleRef,\n        ownerState: ownerState,\n        ...ContainerProps,\n        children: [/*#__PURE__*/_jsx(Root, {\n          ...rootProps,\n          ...(!isHostComponent(Root) && {\n            as: Component,\n            ownerState: {\n              ...ownerState,\n              ...rootProps.ownerState\n            }\n          }),\n          ...componentProps,\n          children: children\n        }), children.pop()]\n      })\n    });\n  }\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsxs(Root, {\n      ...rootProps,\n      as: Component,\n      ref: handleRef,\n      ...(!isHostComponent(Root) && {\n        ownerState: {\n          ...ownerState,\n          ...rootProps.ownerState\n        }\n      }),\n      ...componentProps,\n      children: [children, secondaryAction && /*#__PURE__*/_jsx(ListItemSecondaryAction, {\n        children: secondaryAction\n      })]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Defines the `align-items` style property.\n   * @default 'center'\n   */\n  alignItems: PropTypes.oneOf(['center', 'flex-start']),\n  /**\n   * The content of the component if a `ListItemSecondaryAction` is used it must\n   * be the last child.\n   */\n  children: chainPropTypes(PropTypes.node, props => {\n    const children = React.Children.toArray(props.children);\n\n    // React.Children.toArray(props.children).findLastIndex(isListItemSecondaryAction)\n    let secondaryActionIndex = -1;\n    for (let i = children.length - 1; i >= 0; i -= 1) {\n      const child = children[i];\n      if (isMuiElement(child, ['ListItemSecondaryAction'])) {\n        secondaryActionIndex = i;\n        break;\n      }\n    }\n\n    //  is ListItemSecondaryAction the last child of ListItem\n    if (secondaryActionIndex !== -1 && secondaryActionIndex !== children.length - 1) {\n      return new Error('MUI: You used an element after ListItemSecondaryAction. ' + 'For ListItem to detect that it has a secondary action ' + 'you must pass it as the last child to ListItem.');\n    }\n    return null;\n  }),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The container component used when a `ListItemSecondaryAction` is the last child.\n   * @default 'li'\n   * @deprecated Use the `component` or `slots.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerComponent: elementTypeAcceptingRef,\n  /**\n   * Props applied to the container component if used.\n   * @default {}\n   * @deprecated Use the `slotProps.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerProps: PropTypes.object,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used.\n   * The prop defaults to the value inherited from the parent List component.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * If `true`, all padding is removed.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * If `true`, a 1px light border is added to the bottom of the list item.\n   * @default false\n   */\n  divider: PropTypes.bool,\n  /**\n   * The element to display at the end of ListItem.\n   */\n  secondaryAction: PropTypes.node,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItem;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography, { typographyClasses } from \"../Typography/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport listItemTextClasses, { getListItemTextUtilityClass } from \"./listItemTextClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    inset,\n    primary,\n    secondary,\n    dense\n  } = ownerState;\n  const slots = {\n    root: ['root', inset && 'inset', dense && 'dense', primary && secondary && 'multiline'],\n    primary: ['primary'],\n    secondary: ['secondary']\n  };\n  return composeClasses(slots, getListItemTextUtilityClass, classes);\n};\nconst ListItemTextRoot = styled('div', {\n  name: 'MuiListItemText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${listItemTextClasses.primary}`]: styles.primary\n    }, {\n      [`& .${listItemTextClasses.secondary}`]: styles.secondary\n    }, styles.root, ownerState.inset && styles.inset, ownerState.primary && ownerState.secondary && styles.multiline, ownerState.dense && styles.dense];\n  }\n})({\n  flex: '1 1 auto',\n  minWidth: 0,\n  marginTop: 4,\n  marginBottom: 4,\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.primary})`]: {\n    display: 'block'\n  },\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.secondary})`]: {\n    display: 'block'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.primary && ownerState.secondary,\n    style: {\n      marginTop: 6,\n      marginBottom: 6\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.inset,\n    style: {\n      paddingLeft: 56\n    }\n  }]\n});\nconst ListItemText = /*#__PURE__*/React.forwardRef(function ListItemText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemText'\n  });\n  const {\n    children,\n    className,\n    disableTypography = false,\n    inset = false,\n    primary: primaryProp,\n    primaryTypographyProps,\n    secondary: secondaryProp,\n    secondaryTypographyProps,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const {\n    dense\n  } = React.useContext(ListContext);\n  let primary = primaryProp != null ? primaryProp : children;\n  let secondary = secondaryProp;\n  const ownerState = {\n    ...props,\n    disableTypography,\n    inset,\n    primary: !!primary,\n    secondary: !!secondary,\n    dense\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      primary: primaryTypographyProps,\n      secondary: secondaryTypographyProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: ListItemTextRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    ref\n  });\n  const [PrimarySlot, primarySlotProps] = useSlot('primary', {\n    className: classes.primary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SecondarySlot, secondarySlotProps] = useSlot('secondary', {\n    className: classes.secondary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  if (primary != null && primary.type !== Typography && !disableTypography) {\n    primary = /*#__PURE__*/_jsx(PrimarySlot, {\n      variant: dense ? 'body2' : 'body1',\n      component: primarySlotProps?.variant ? undefined : 'span',\n      ...primarySlotProps,\n      children: primary\n    });\n  }\n  if (secondary != null && secondary.type !== Typography && !disableTypography) {\n    secondary = /*#__PURE__*/_jsx(SecondarySlot, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      ...secondarySlotProps,\n      children: secondary\n    });\n  }\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [primary, secondary]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Alias for the `primary` prop.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the children won't be wrapped by a Typography component.\n   * This can be useful to render an alternative Typography variant by wrapping\n   * the `children` (or `primary`) text, and optional `secondary` text\n   * with the Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * If `true`, the children are indented.\n   * This should be used if there is no left avatar or left icon.\n   * @default false\n   */\n  inset: PropTypes.bool,\n  /**\n   * The main content element.\n   */\n  primary: PropTypes.node,\n  /**\n   * These props will be forwarded to the primary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.primary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  primaryTypographyProps: PropTypes.object,\n  /**\n   * The secondary content element.\n   */\n  secondary: PropTypes.node,\n  /**\n   * These props will be forwarded to the secondary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.secondary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  secondaryTypographyProps: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    primary: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    secondary: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    primary: PropTypes.elementType,\n    root: PropTypes.elementType,\n    secondary: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemText;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDividerUtilityClass } from \"./dividerClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    absolute,\n    children,\n    classes,\n    flexItem,\n    light,\n    orientation,\n    textAlign,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', absolute && 'absolute', variant, light && 'light', orientation === 'vertical' && 'vertical', flexItem && 'flexItem', children && 'withChildren', children && orientation === 'vertical' && 'withChildrenVertical', textAlign === 'right' && orientation !== 'vertical' && 'textAlignRight', textAlign === 'left' && orientation !== 'vertical' && 'textAlignLeft'],\n    wrapper: ['wrapper', orientation === 'vertical' && 'wrapperVertical']\n  };\n  return composeClasses(slots, getDividerUtilityClass, classes);\n};\nconst DividerRoot = styled('div', {\n  name: 'MuiDivider',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.absolute && styles.absolute, styles[ownerState.variant], ownerState.light && styles.light, ownerState.orientation === 'vertical' && styles.vertical, ownerState.flexItem && styles.flexItem, ownerState.children && styles.withChildren, ownerState.children && ownerState.orientation === 'vertical' && styles.withChildrenVertical, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && styles.textAlignRight, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && styles.textAlignLeft];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 0,\n  // Reset browser default style.\n  flexShrink: 0,\n  borderWidth: 0,\n  borderStyle: 'solid',\n  borderColor: (theme.vars || theme).palette.divider,\n  borderBottomWidth: 'thin',\n  variants: [{\n    props: {\n      absolute: true\n    },\n    style: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      width: '100%'\n    }\n  }, {\n    props: {\n      light: true\n    },\n    style: {\n      borderColor: theme.vars ? `rgba(${theme.vars.palette.dividerChannel} / 0.08)` : alpha(theme.palette.divider, 0.08)\n    }\n  }, {\n    props: {\n      variant: 'inset'\n    },\n    style: {\n      marginLeft: 72\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'horizontal'\n    },\n    style: {\n      marginLeft: theme.spacing(2),\n      marginRight: theme.spacing(2)\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'vertical'\n    },\n    style: {\n      marginTop: theme.spacing(1),\n      marginBottom: theme.spacing(1)\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      height: '100%',\n      borderBottomWidth: 0,\n      borderRightWidth: 'thin'\n    }\n  }, {\n    props: {\n      flexItem: true\n    },\n    style: {\n      alignSelf: 'stretch',\n      height: 'auto'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.children,\n    style: {\n      display: 'flex',\n      textAlign: 'center',\n      border: 0,\n      borderTopStyle: 'solid',\n      borderLeftStyle: 'solid',\n      '&::before, &::after': {\n        content: '\"\"',\n        alignSelf: 'center'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.children && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before, &::after': {\n        width: '100%',\n        borderTop: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderTopStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.orientation === 'vertical' && ownerState.children,\n    style: {\n      flexDirection: 'column',\n      '&::before, &::after': {\n        height: '100%',\n        borderLeft: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderLeftStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '90%'\n      },\n      '&::after': {\n        width: '10%'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '10%'\n      },\n      '&::after': {\n        width: '90%'\n      }\n    }\n  }]\n})));\nconst DividerWrapper = styled('span', {\n  name: 'MuiDivider',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.wrapper, ownerState.orientation === 'vertical' && styles.wrapperVertical];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-block',\n  paddingLeft: `calc(${theme.spacing(1)} * 1.2)`,\n  paddingRight: `calc(${theme.spacing(1)} * 1.2)`,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      paddingTop: `calc(${theme.spacing(1)} * 1.2)`,\n      paddingBottom: `calc(${theme.spacing(1)} * 1.2)`\n    }\n  }]\n})));\nconst Divider = /*#__PURE__*/React.forwardRef(function Divider(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDivider'\n  });\n  const {\n    absolute = false,\n    children,\n    className,\n    orientation = 'horizontal',\n    component = children || orientation === 'vertical' ? 'div' : 'hr',\n    flexItem = false,\n    light = false,\n    role = component !== 'hr' ? 'separator' : undefined,\n    textAlign = 'center',\n    variant = 'fullWidth',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    absolute,\n    component,\n    flexItem,\n    light,\n    orientation,\n    role,\n    textAlign,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DividerRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    role: role,\n    ref: ref,\n    ownerState: ownerState,\n    \"aria-orientation\": role === 'separator' && (component !== 'hr' || orientation === 'vertical') ? orientation : undefined,\n    ...other,\n    children: children ? /*#__PURE__*/_jsx(DividerWrapper, {\n      className: classes.wrapper,\n      ownerState: ownerState,\n      children: children\n    }) : null\n  });\n});\n\n/**\n * The following flag is used to ensure that this component isn't tabbable i.e.\n * does not get highlight/focus inside of MUI List.\n */\nif (Divider) {\n  Divider.muiSkipListHighlight = true;\n}\nprocess.env.NODE_ENV !== \"production\" ? Divider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Absolutely position the element.\n   * @default false\n   */\n  absolute: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, a vertical divider will have the correct height when used in flex container.\n   * (By default, a vertical divider will have a calculated height of `0px` if it is the child of a flex container.)\n   * @default false\n   */\n  flexItem: PropTypes.bool,\n  /**\n   * If `true`, the divider will have a lighter color.\n   * @default false\n   * @deprecated Use <Divider sx={{ opacity: 0.6 }} /> (or any opacity or color) instead. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  light: PropTypes.bool,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The text alignment.\n   * @default 'center'\n   */\n  textAlign: PropTypes.oneOf(['center', 'left', 'right']),\n  /**\n   * The variant to use.\n   * @default 'fullWidth'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['fullWidth', 'inset', 'middle']), PropTypes.string])\n} : void 0;\nexport default Divider;", "import { useState, useEffect } from 'react';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataEnhancedReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n  totalRecords: number;\n  approach: string;\n}\n\n/**\n * Enhanced office data hook with comprehensive pagination\n * Mirrors the successful Flutter implementation to overcome 1000-record limit\n */\nexport const useOfficeDataEnhanced = (): UseOfficeDataEnhancedReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [totalRecords, setTotalRecords] = useState<number>(0);\n  const [approach, setApproach] = useState<string>('');\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataEnhanced: Starting comprehensive office data fetch...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allOfficeData = await OfficeService.fetchAllOfficeData();\n      \n      console.log('✅ useOfficeDataEnhanced: Fetched office records:', allOfficeData.length, 'records');\n      setTotalRecords(allOfficeData.length);\n\n      if (allOfficeData.length === 0) {\n        console.log('⚠️ useOfficeDataEnhanced: No office records found');\n        setRegions([]);\n        setDivisions([]);\n        setOffices([]);\n        setApproach('no-data');\n        return;\n      }\n\n      // Process regions - get unique regions\n      const uniqueRegions = new Set<string>();\n      allOfficeData.forEach(office => {\n        if (office.Region && office.Region.trim()) {\n          uniqueRegions.add(office.Region.trim());\n        }\n      });\n\n      const regionsArray: Region[] = Array.from(uniqueRegions)\n        .sort()\n        .map(regionName => ({\n          id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed regions:', regionsArray.length);\n\n      // Process divisions - get unique divisions with their regions\n      const uniqueDivisions = new Map<string, string>();\n      allOfficeData.forEach(office => {\n        if (office.Division && office.Division.trim() && office.Region && office.Region.trim()) {\n          uniqueDivisions.set(office.Division.trim(), office.Region.trim());\n        }\n      });\n\n      const divisionsArray: Division[] = Array.from(uniqueDivisions.entries())\n        .sort(([a], [b]) => a.localeCompare(b))\n        .map(([divisionName, regionName]) => ({\n          id: divisionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: divisionName,\n          region: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed divisions:', divisionsArray.length);\n\n      // Process offices - use office name as ID for consistency\n      const officesArray: Office[] = allOfficeData\n        .filter(office => office['Office name'] && office['Office name'].trim())\n        .map(office => ({\n          id: office['Office name'], // Use office name as ID for form targeting\n          name: office['Office name'],\n          region: office.Region || '',\n          division: office.Division || '',\n          facilityId: office['Office name'], // Keep for reference\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed offices:', officesArray.length);\n\n      // Log comprehensive statistics\n      logOfficeStatistics(allOfficeData, regionsArray, divisionsArray, officesArray);\n\n      // Set the processed data\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n      setApproach('enhanced-pagination');\n\n      console.log('✅ useOfficeDataEnhanced: Data processing complete');\n\n    } catch (err) {\n      console.error('❌ useOfficeDataEnhanced: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n      setTotalRecords(0);\n      setApproach('error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n    totalRecords,\n    approach,\n  };\n};\n\n/**\n * Log comprehensive statistics about the processed office data\n */\nfunction logOfficeStatistics(\n  allOfficeData: any[],\n  regions: Region[],\n  divisions: Division[],\n  offices: Office[]\n): void {\n  console.log('📊 useOfficeDataEnhanced: === COMPREHENSIVE STATISTICS ===');\n  console.log(`📊 useOfficeDataEnhanced: Raw records: ${allOfficeData.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed regions: ${regions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed divisions: ${divisions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed offices: ${offices.length}`);\n\n  if (offices.length > 0) {\n    // Alphabetical range\n    const sortedNames = offices.map(o => o.name).sort();\n    console.log(`📊 useOfficeDataEnhanced: Office range - First: \"${sortedNames[0]}\"`);\n    console.log(`📊 useOfficeDataEnhanced: Office range - Last: \"${sortedNames[sortedNames.length - 1]}\"`);\n\n    // Letter distribution\n    const letterCounts: { [key: string]: number } = {};\n    offices.forEach(office => {\n      const firstLetter = office.name.charAt(0).toUpperCase();\n      letterCounts[firstLetter] = (letterCounts[firstLetter] || 0) + 1;\n    });\n\n    console.log('📊 useOfficeDataEnhanced: Letter distribution:');\n    Object.keys(letterCounts).sort().forEach(letter => {\n      console.log(`📊 useOfficeDataEnhanced: ${letter}: ${letterCounts[letter]} offices`);\n    });\n\n    // Check for specific offices\n    const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n    const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n    \n    console.log(`📊 useOfficeDataEnhanced: Contains \"Tirupur division\": ${!!tirupurDivision}`);\n    console.log(`📊 useOfficeDataEnhanced: Contains \"Coimbatore division\": ${!!coimbatoreDivision}`);\n\n    if (tirupurDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Tirupur division: \"${tirupurDivision.name}\"`);\n    }\n    if (coimbatoreDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Coimbatore division: \"${coimbatoreDivision.name}\"`);\n    }\n\n    // Region breakdown\n    if (regions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Regions found:');\n      regions.forEach(region => {\n        const regionOffices = offices.filter(o => o.region === region.name);\n        console.log(`📊 useOfficeDataEnhanced: ${region.name}: ${regionOffices.length} offices`);\n      });\n    }\n\n    // Division breakdown\n    if (divisions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Top 10 divisions by office count:');\n      const divisionCounts = divisions.map(division => ({\n        name: division.name,\n        count: offices.filter(o => o.division === division.name).length\n      })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n      divisionCounts.forEach(division => {\n        console.log(`📊 useOfficeDataEnhanced: ${division.name}: ${division.count} offices`);\n      });\n    }\n  }\n\n  console.log('📊 useOfficeDataEnhanced: === END STATISTICS ===');\n}\n\nexport default useOfficeDataEnhanced;\n", "import React from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n  Chip,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport { useOfficeDataEnhanced } from './hooks/useOfficeDataEnhanced';\n\n/**\n * Test component to verify enhanced office loading functionality\n * This component displays comprehensive statistics about the loaded office data\n */\nconst OfficeLoadingTest: React.FC = () => {\n  const {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    totalRecords,\n    approach,\n    refetch\n  } = useOfficeDataEnhanced();\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" flexDirection=\"column\" alignItems=\"center\" p={4}>\n        <CircularProgress size={60} />\n        <Typography variant=\"h6\" sx={{ mt: 2 }}>\n          Loading office data with comprehensive pagination...\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n          This may take a moment as we fetch ALL records from the database\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={4}>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n        <Typography variant=\"body2\">\n          Failed to load office data. Please check the console for detailed error information.\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Calculate statistics\n  const letterDistribution: { [key: string]: number } = {};\n  offices.forEach(office => {\n    const firstLetter = office.name.charAt(0).toUpperCase();\n    letterDistribution[firstLetter] = (letterDistribution[firstLetter] || 0) + 1;\n  });\n\n  const sortedOfficeNames = offices.map(o => o.name).sort();\n  const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n  const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n\n  // Top regions by office count\n  const regionCounts = regions.map(region => ({\n    name: region.name,\n    count: offices.filter(o => o.region === region.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 5);\n\n  // Top divisions by office count\n  const divisionCounts = divisions.map(division => ({\n    name: division.name,\n    count: offices.filter(o => o.division === division.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n  return (\n    <Box p={4}>\n      <Typography variant=\"h4\" gutterBottom>\n        Office Loading Test - Enhanced Pagination\n      </Typography>\n      \n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\n        This test verifies that the enhanced office loading system can fetch ALL records from the Supabase database,\n        overcoming the default 1000-record pagination limit.\n      </Typography>\n\n      {/* Summary Cards */}\n      <Box display=\"flex\" gap={3} sx={{ mb: 4, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Total Records\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalRecords.toLocaleString()}\n              </Typography>\n              <Chip\n                label={approach}\n                size=\"small\"\n                color={totalRecords > 1000 ? \"success\" : \"warning\"}\n                sx={{ mt: 1 }}\n              />\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Regions\n              </Typography>\n              <Typography variant=\"h4\">\n                {regions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Divisions\n              </Typography>\n              <Typography variant=\"h4\">\n                {divisions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Offices\n              </Typography>\n              <Typography variant=\"h4\">\n                {offices.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n      </Box>\n\n      {/* Verification Results */}\n      <Box display=\"flex\" gap={3} sx={{ flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Verification Results\n            </Typography>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Records exceed 1000 limit:\n              </Typography>\n              <Chip\n                label={totalRecords > 1000 ? \"✅ YES\" : \"❌ NO\"}\n                color={totalRecords > 1000 ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Alphabetical Range:\n              </Typography>\n              <Typography variant=\"body2\">\n                First: \"{sortedOfficeNames[0] || 'N/A'}\"\n              </Typography>\n              <Typography variant=\"body2\">\n                Last: \"{sortedOfficeNames[sortedOfficeNames.length - 1] || 'N/A'}\"\n              </Typography>\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Tirupur Division Found:\n              </Typography>\n              <Chip\n                label={tirupurDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={tirupurDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {tirupurDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{tirupurDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Coimbatore Division Found:\n              </Typography>\n              <Chip\n                label={coimbatoreDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={coimbatoreDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {coimbatoreDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{coimbatoreDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Letter Distribution\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {Object.keys(letterDistribution).sort().map(letter => (\n                <Box key={letter} display=\"flex\" justifyContent=\"space-between\" sx={{ mb: 1 }}>\n                  <Typography variant=\"body2\">{letter}:</Typography>\n                  <Typography variant=\"body2\">{letterDistribution[letter]} offices</Typography>\n                </Box>\n              ))}\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      <Box display=\"flex\" gap={3} sx={{ mt: 3, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 5 Regions by Office Count\n            </Typography>\n            <List dense>\n              {regionCounts.map((region, index) => (\n                <React.Fragment key={region.name}>\n                  <ListItem>\n                    <ListItemText\n                      primary={region.name}\n                      secondary={`${region.count} offices`}\n                    />\n                  </ListItem>\n                  {index < regionCounts.length - 1 && <Divider />}\n                </React.Fragment>\n              ))}\n            </List>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 10 Divisions by Office Count\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              <List dense>\n                {divisionCounts.map((division, index) => (\n                  <React.Fragment key={division.name}>\n                    <ListItem>\n                      <ListItemText\n                        primary={division.name}\n                        secondary={`${division.count} offices`}\n                      />\n                    </ListItem>\n                    {index < divisionCounts.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      {/* Success Message */}\n      {totalRecords > 1000 && (\n        <Alert severity=\"success\" sx={{ mt: 3 }}>\n          <Typography variant=\"h6\">\n            🎉 Success! Enhanced Office Loading is Working\n          </Typography>\n          <Typography variant=\"body2\">\n            The system successfully loaded {totalRecords.toLocaleString()} office records, \n            which exceeds the default 1000-record Supabase limit. This confirms that the \n            comprehensive pagination solution is working correctly.\n          </Typography>\n        </Alert>\n      )}\n    </Box>\n  );\n};\n\nexport default OfficeLoadingTest;\n", "import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Sidebar from '../shared/Sidebar';\nimport StatsCards from '../shared/StatsCards';\nimport PageBuilder from './business/PageBuilder';\nimport OfficeLoadingTest from './business/OfficeLoadingTest';\n\nconst AdminPage: React.FC = () => {\n  const { currentUser } = useAuth();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState<any>(null);\n  const [showOfficeTest, setShowOfficeTest] = useState<boolean>(false);\n\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        const userRef = doc(db, 'employees', currentUser.uid);\n        const userSnap = await getDoc(userRef);\n        if (userSnap.exists()) {\n          setUserData(userSnap.data());\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n      <div className=\"main-content\">\n        <div className=\"page-title\">\n          Admin Dashboard\n          <button\n            onClick={() => setShowOfficeTest(!showOfficeTest)}\n            style={{\n              marginLeft: '20px',\n              padding: '8px 16px',\n              backgroundColor: showOfficeTest ? '#dc3545' : '#007bff',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {showOfficeTest ? 'Hide Office Test' : 'Show Office Loading Test'}\n          </button>\n        </div>\n        <StatsCards />\n        {showOfficeTest ? <OfficeLoadingTest /> : <PageBuilder />}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPage;"], "names": ["getListItemTextUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "stats", "title", "value", "StatsCards", "_jsx", "className", "children", "map", "stat", "index", "_jsxs", "getDividerUtilityClass", "_ref", "isOpen", "onClose", "onClick", "e", "stopPropagation", "isMainCard", "cardId", "allCategories", "card", "find", "c", "id", "parentId", "isLeafCard", "some", "organizeCards", "list", "roots", "for<PERSON>ach", "item", "_map$item$parentId$ch", "push", "getAllDescendantIds", "descendants", "filter", "child", "concat", "useCardManagement", "props", "categories", "setCategories", "selected<PERSON><PERSON>", "setSelectedCard", "newCardId", "setNewCardId", "newCardTitle", "setNewCardTitle", "actionType", "setActionType", "setIsLoading", "setError", "setSuccess", "setShowConfirmModal", "setIsAddingNewCard", "setPageConfig", "setFields", "setEditingCard", "setShowEditModal", "setCardToDelete", "setShowDeleteConfirmModal", "fetchCategories", "useCallback", "async", "fetchedCategories", "getDocs", "collection", "db", "docs", "doc", "data", "err", "console", "error", "handleAddNewCard", "doc<PERSON>ef", "getDoc", "exists", "checkDuplicateId", "handleConfirmCreate", "_categories$find", "parentIdToSet", "newPath", "path", "replace", "cardRef", "icon", "generatedIcon", "color", "generatedColor", "hash", "split", "reduce", "acc", "char", "charCodeAt", "icons", "FaFolder", "FaFileAlt", "FaCog", "FaFolderOpen", "colors", "length", "generateCardStyle", "setDoc", "lastUpdated", "Date", "toISOString", "name", "fields", "isPage", "pageId", "setTimeout", "handleEditCard", "handleUpdateCard", "editingCard", "updateDoc", "handleDeleteClick", "handleConfirmDelete", "batch", "writeBatch", "allDescendants", "idsToDelete", "delete", "commit", "onCardChange", "onActionChange", "isLoading", "onCreateAction", "onWebPageAction", "renderCardOptions", "cards", "level", "arguments", "undefined", "flatMap", "style", "paddingLeft", "repeat", "onChange", "newSelectedCard", "target", "disabled", "newAction", "_Fragment", "onEditCard", "onDeleteCard", "selectedCate<PERSON><PERSON>", "React", "FaEdit", "FaTrash", "_field$options2", "field", "onUpdate", "onRemove", "handleOptionChange", "optIndex", "key", "newOptions", "options", "handleDefaultValueChange", "type", "newDefaultValue", "checked", "defaultValue", "label", "htmlFor", "placeholder", "required", "includes", "min", "parseFloat", "max", "opt", "_field$options", "_", "i", "removeOption", "addOption", "String", "Boolean", "Array", "isArray", "join", "s", "trim", "buttonText", "sectionTitle", "pageConfig", "onAddField", "onUpdateField", "onRemoveField", "onSave", "onPreview", "loading", "FieldConfigItem", "FaPlus", "FaSave", "REPORT_FREQUENCIES", "<PERSON><PERSON><PERSON><PERSON>", "setIsOpen", "useState", "dropdownRef", "useRef", "useEffect", "handleClickOutside", "event", "current", "contains", "document", "addEventListener", "removeEventListener", "isAllSelected", "isIndeterminate", "ref", "backgroundColor", "borderColor", "getDisplayText", "selectedOption", "option", "maxHeight", "overflowY", "input", "indeterminate", "handleSelectAll", "handleCheckboxChange", "optionId", "selectedRegions", "selectedDivisions", "selectedOffices", "selectedFrequency", "onRegionsChange", "onDivisionsChange", "onOfficesChange", "onFrequencyChange", "regions", "divisions", "offices", "refetch", "useOfficeDataSimple", "setRegions", "setDivisions", "setOffices", "setLoading", "fetchOfficeData", "log", "allData", "OfficeService", "fetchAllOfficeData", "distinctRegions", "row", "Region", "region", "array", "indexOf", "sort", "regionsArray", "regionName", "toLowerCase", "distinctDivisions", "division", "Division", "findIndex", "x", "a", "b", "localeCompare", "divisionsArray", "officesArray", "facilityId", "useOfficeData", "selectedRegionNames", "regionId", "_regions$find", "r", "availableDivisions", "selectedDivisionNames", "divisionId", "_divisions$find", "d", "availableOffices", "office", "validDivisions", "validOffices", "officeId", "o", "role", "CheckboxDropdown", "frequency", "PageBuilder", "_state$categories$fin", "_state$categories$fin2", "state", "usePageBuilderState", "availableDynamicFields", "setAvailableDynamicFields", "success", "isAddingNewCard", "showConfirmModal", "showEditModal", "cardToDelete", "showDeleteConfirmModal", "isPreviewOpen", "setIsPreviewOpen", "previewContent", "setPreviewContent", "setSelectedRegions", "setSelectedDivisions", "setSelectedOffices", "setSelectedFrequency", "cardManagement", "pageConfiguration", "fetchDynamicFormFields", "formId", "formConfigRef", "formConfigSnap", "formConfigData", "loadPageConfig", "docSnap", "supabasePageService", "supabaseError", "selectedRegion", "selectedDivision", "selectedOffice", "addField", "newField", "now", "addFieldFromDynamic", "dynamicField", "columns", "buttonType", "onClickAction", "warn", "updateField", "updatedField", "<PERSON><PERSON><PERSON>s", "removeField", "handleSave", "cleanedFields", "cleanedField", "updatedPageConfig", "savePromises", "catch", "Error", "message", "savePageConfig", "Promise", "all", "handlePreview", "alert", "generatedPreview", "fieldHtml", "usePageConfiguration", "CardSelector", "cardIsLeaf", "cardIsMain", "action", "handleCreateAction", "handleWebPageAction", "Modal", "CardManagement", "ReportConfiguration", "PageBuilderContent", "dangerouslySetInnerHTML", "__html", "getCardUtilityClass", "CardRoot", "styled", "Paper", "overflow", "inProps", "useDefaultProps", "raised", "other", "ownerState", "classes", "composeClasses", "root", "useUtilityClasses", "clsx", "elevation", "getCardContentUtilityClass", "CardContentRoot", "padding", "paddingBottom", "component", "as", "getListItemUtilityClass", "getListItemSecondaryActionClassesUtilityClass", "ListItemSecondaryActionRoot", "overridesResolver", "styles", "disableGutters", "position", "right", "top", "transform", "variants", "ListItemSecondaryAction", "context", "ListContext", "slots", "mui<PERSON><PERSON>", "ListItemRoot", "dense", "alignItems", "alignItemsFlexStart", "divider", "gutters", "disablePadding", "hasSecondaryAction", "secondaryAction", "memoTheme", "theme", "display", "justifyContent", "textDecoration", "width", "boxSizing", "textAlign", "_ref2", "paddingTop", "_ref3", "_ref4", "paddingRight", "_ref5", "_ref6", "listItemButtonClasses", "_ref7", "borderBottom", "vars", "palette", "backgroundClip", "_ref8", "button", "transition", "transitions", "create", "duration", "shortest", "hover", "_ref9", "ListItemContainer", "childrenProp", "componentProp", "components", "componentsProps", "ContainerComponent", "ContainerProps", "ContainerClassName", "slotProps", "childContext", "listItemRef", "toArray", "isMuiElement", "container", "handleRef", "useForkRef", "Root", "rootProps", "componentProps", "Component", "Provider", "isHostComponent", "pop", "ListItemTextRoot", "listItemTextClasses", "primary", "secondary", "inset", "multiline", "flex", "min<PERSON><PERSON><PERSON>", "marginTop", "marginBottom", "typographyClasses", "disableTypography", "primaryProp", "primaryTypographyProps", "secondaryProp", "secondaryTypographyProps", "externalForwardedProps", "RootSlot", "rootSlotProps", "useSlot", "elementType", "PrimarySlot", "primarySlotProps", "Typography", "SecondarySlot", "secondarySlotProps", "variant", "DividerRoot", "absolute", "light", "orientation", "vertical", "flexItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withChildrenVertical", "textAlignRight", "textAlignLeft", "margin", "flexShrink", "borderWidth", "borderStyle", "borderBottomWidth", "bottom", "left", "dividerChannel", "alpha", "marginLeft", "spacing", "marginRight", "height", "borderRightWidth", "alignSelf", "border", "borderTopStyle", "borderLeftStyle", "content", "borderTop", "flexDirection", "borderLeft", "DividerWrapper", "wrapper", "wrapperVertical", "whiteSpace", "Divider", "muiSkipListHighlight", "useOfficeDataEnhanced", "totalRecords", "setTotalRecords", "approach", "setApproach", "allOfficeData", "uniqueRegions", "Set", "add", "from", "uniqueDivisions", "Map", "set", "entries", "divisionName", "sortedNames", "letterCounts", "firstLetter", "char<PERSON>t", "toUpperCase", "Object", "keys", "letter", "tirupurDivision", "coimbatoreDivision", "regionOffices", "count", "slice", "logOfficeStatistics", "OfficeLoadingTest", "Box", "p", "CircularProgress", "size", "sx", "mt", "<PERSON><PERSON>", "severity", "mb", "letterDistribution", "sortedOfficeNames", "regionCounts", "divisionCounts", "gutterBottom", "paragraph", "gap", "flexWrap", "Card", "<PERSON><PERSON><PERSON><PERSON>", "toLocaleString", "Chip", "List", "ListItem", "ListItemText", "AdminPage", "currentUser", "useAuth", "userData", "setUserData", "useNavigate", "showOfficeTest", "setShowOfficeTest", "userRef", "uid", "userSnap", "fetchUserData", "Sidebar", "borderRadius", "cursor", "fontSize"], "sourceRoot": ""}