"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[541],{1244:(e,t,o)=>{o.d(t,{A:()=>h});var r=o(5043),a=o(8387),n=o(1807),i=o(8128),s=o(8301),l=o(4799),d=o(9857),c=o(6061);function p(e){return(0,c.Ay)("MuiCard",e)}(0,d.A)("MuiCard",["root"]);var u=o(579);const m=(0,i.Ay)(l.A,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),h=r.forwardRef((function(e,t){const o=(0,s.b)({props:e,name:"MuiCard"}),{className:r,raised:i=!1,...l}=o,d={...o,raised:i},c=(e=>{const{classes:t}=e;return(0,n.A)({root:["root"]},p,t)})(d);return(0,u.jsx)(m,{className:(0,a.A)(c.root,r),elevation:i?8:void 0,ref:t,ownerState:d,...l})}))},1977:(e,t,o)=>{o.d(t,{A:()=>i,b:()=>n});var r=o(9857),a=o(6061);function n(e){return(0,a.Ay)("MuiListItemText",e)}const i=(0,r.A)("MuiListItemText",["root","multiline","dense","inset","primary","secondary"])},2541:(e,t,o)=>{o.r(t),o.d(t,{default:()=>xe});var r=o(5043),a=o(5472),n=o(2073),i=o(9066),s=o(1103),l=o(3969),d=o(1244),c=o(6128),p=o(5895),u=o(2775),m=o(8387),h=o(1807),x=o(8128),g=o(8301),y=o(9857),f=o(6061);function v(e){return(0,f.Ay)("MuiTableContainer",e)}(0,y.A)("MuiTableContainer",["root"]);var b=o(579);const A=(0,x.Ay)("div",{name:"MuiTableContainer",slot:"Root"})({width:"100%",overflowX:"auto"}),j=r.forwardRef((function(e,t){const o=(0,g.b)({props:e,name:"MuiTableContainer"}),{className:r,component:a="div",...n}=o,i={...o,component:a},s=(e=>{const{classes:t}=e;return(0,h.A)({root:["root"]},v,t)})(i);return(0,b.jsx)(A,{ref:t,as:a,className:(0,m.A)(s.root,r),ownerState:i,...n})}));var w=o(4799);const C=r.createContext();var M=o(1612);function k(e){return(0,f.Ay)("MuiTable",e)}(0,y.A)("MuiTable",["root","stickyHeader"]);const S=(0,x.Ay)("table",{name:"MuiTable",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.stickyHeader&&t.stickyHeader]}})((0,M.A)((e=>{let{theme:t}=e;return{display:"table",width:"100%",borderCollapse:"collapse",borderSpacing:0,"& caption":{...t.typography.body2,padding:t.spacing(2),color:(t.vars||t).palette.text.secondary,textAlign:"left",captionSide:"bottom"},variants:[{props:e=>{let{ownerState:t}=e;return t.stickyHeader},style:{borderCollapse:"separate"}}]}}))),R="table",$=r.forwardRef((function(e,t){const o=(0,g.b)({props:e,name:"MuiTable"}),{className:a,component:n=R,padding:i="normal",size:s="medium",stickyHeader:l=!1,...d}=o,c={...o,component:n,padding:i,size:s,stickyHeader:l},p=(e=>{const{classes:t,stickyHeader:o}=e,r={root:["root",o&&"stickyHeader"]};return(0,h.A)(r,k,t)})(c),u=r.useMemo((()=>({padding:i,size:s,stickyHeader:l})),[i,s,l]);return(0,b.jsx)(C.Provider,{value:u,children:(0,b.jsx)(S,{as:n,role:n===R?null:"table",ref:t,className:(0,m.A)(p.root,a),ownerState:c,...d})})}));const T=r.createContext();function N(e){return(0,f.Ay)("MuiTableHead",e)}(0,y.A)("MuiTableHead",["root"]);const H=(0,x.Ay)("thead",{name:"MuiTableHead",slot:"Root"})({display:"table-header-group"}),W={variant:"head"},z="thead",O=r.forwardRef((function(e,t){const o=(0,g.b)({props:e,name:"MuiTableHead"}),{className:r,component:a=z,...n}=o,i={...o,component:a},s=(e=>{const{classes:t}=e;return(0,h.A)({root:["root"]},N,t)})(i);return(0,b.jsx)(T.Provider,{value:W,children:(0,b.jsx)(H,{as:a,className:(0,m.A)(s.root,r),ref:t,role:a===z?null:"rowgroup",ownerState:i,...n})})}));var I=o(9826);function B(e){return(0,f.Ay)("MuiTableRow",e)}const E=(0,y.A)("MuiTableRow",["root","selected","hover","head","footer"]),L=(0,x.Ay)("tr",{name:"MuiTableRow",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.head&&t.head,o.footer&&t.footer]}})((0,M.A)((e=>{let{theme:t}=e;return{color:"inherit",display:"table-row",verticalAlign:"middle",outline:0,[`&.${E.hover}:hover`]:{backgroundColor:(t.vars||t).palette.action.hover},[`&.${E.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,I.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,I.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity)}}}}))),D="tr",_=r.forwardRef((function(e,t){const o=(0,g.b)({props:e,name:"MuiTableRow"}),{className:a,component:n=D,hover:i=!1,selected:s=!1,...l}=o,d=r.useContext(T),c={...o,component:n,hover:i,selected:s,head:d&&"head"===d.variant,footer:d&&"footer"===d.variant},p=(e=>{const{classes:t,selected:o,hover:r,head:a,footer:n}=e,i={root:["root",o&&"selected",r&&"hover",a&&"head",n&&"footer"]};return(0,h.A)(i,B,t)})(c);return(0,b.jsx)(L,{as:n,ref:t,className:(0,m.A)(p.root,a),role:n===D?null:"row",ownerState:c,...l})})),U=_;var X=o(7194);function G(e){return(0,f.Ay)("MuiTableCell",e)}const V=(0,y.A)("MuiTableCell",["root","head","body","footer","sizeSmall","sizeMedium","paddingCheckbox","paddingNone","alignLeft","alignCenter","alignRight","alignJustify","stickyHeader"]),P=(0,x.Ay)("td",{name:"MuiTableCell",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,t[o.variant],t[`size${(0,X.A)(o.size)}`],"normal"!==o.padding&&t[`padding${(0,X.A)(o.padding)}`],"inherit"!==o.align&&t[`align${(0,X.A)(o.align)}`],o.stickyHeader&&t.stickyHeader]}})((0,M.A)((e=>{let{theme:t}=e;return{...t.typography.body2,display:"table-cell",verticalAlign:"inherit",borderBottom:t.vars?`1px solid ${t.vars.palette.TableCell.border}`:`1px solid\n    ${"light"===t.palette.mode?(0,I.a)((0,I.X4)(t.palette.divider,1),.88):(0,I.e$)((0,I.X4)(t.palette.divider,1),.68)}`,textAlign:"left",padding:16,variants:[{props:{variant:"head"},style:{color:(t.vars||t).palette.text.primary,lineHeight:t.typography.pxToRem(24),fontWeight:t.typography.fontWeightMedium}},{props:{variant:"body"},style:{color:(t.vars||t).palette.text.primary}},{props:{variant:"footer"},style:{color:(t.vars||t).palette.text.secondary,lineHeight:t.typography.pxToRem(21),fontSize:t.typography.pxToRem(12)}},{props:{size:"small"},style:{padding:"6px 16px",[`&.${V.paddingCheckbox}`]:{width:24,padding:"0 12px 0 16px","& > *":{padding:0}}}},{props:{padding:"checkbox"},style:{width:48,padding:"0 0 0 4px"}},{props:{padding:"none"},style:{padding:0}},{props:{align:"left"},style:{textAlign:"left"}},{props:{align:"center"},style:{textAlign:"center"}},{props:{align:"right"},style:{textAlign:"right",flexDirection:"row-reverse"}},{props:{align:"justify"},style:{textAlign:"justify"}},{props:e=>{let{ownerState:t}=e;return t.stickyHeader},style:{position:"sticky",top:0,zIndex:2,backgroundColor:(t.vars||t).palette.background.default}}]}}))),F=r.forwardRef((function(e,t){const o=(0,g.b)({props:e,name:"MuiTableCell"}),{align:a="inherit",className:n,component:i,padding:s,scope:l,size:d,sortDirection:c,variant:p,...u}=o,x=r.useContext(C),y=r.useContext(T),f=y&&"head"===y.variant;let v;v=i||(f?"th":"td");let A=l;"td"===v?A=void 0:!A&&f&&(A="col");const j=p||y&&y.variant,w={...o,align:a,component:v,padding:s||(x&&x.padding?x.padding:"normal"),size:d||(x&&x.size?x.size:"medium"),sortDirection:c,stickyHeader:"head"===j&&x&&x.stickyHeader,variant:j},M=(e=>{const{classes:t,variant:o,align:r,padding:a,size:n,stickyHeader:i}=e,s={root:["root",o,i&&"stickyHeader","inherit"!==r&&`align${(0,X.A)(r)}`,"normal"!==a&&`padding${(0,X.A)(a)}`,`size${(0,X.A)(n)}`]};return(0,h.A)(s,G,t)})(w);let k=null;return c&&(k="asc"===c?"ascending":"descending"),(0,b.jsx)(P,{as:v,ref:t,className:(0,m.A)(M.root,n),"aria-sort":k,scope:A,ownerState:w,...u})})),J=F;function K(e){return(0,f.Ay)("MuiTableBody",e)}(0,y.A)("MuiTableBody",["root"]);const Z=(0,x.Ay)("tbody",{name:"MuiTableBody",slot:"Root"})({display:"table-row-group"}),q={variant:"body"},Q="tbody",Y=r.forwardRef((function(e,t){const o=(0,g.b)({props:e,name:"MuiTableBody"}),{className:r,component:a=Q,...n}=o,i={...o,component:a},s=(e=>{const{classes:t}=e;return(0,h.A)({root:["root"]},K,t)})(i);return(0,b.jsx)(T.Provider,{value:q,children:(0,b.jsx)(Z,{className:(0,m.A)(s.root,r),as:a,ref:t,role:a===Q?null:"rowgroup",ownerState:i,...n})})}));var ee=o(256),te=o(7434),oe=o(2466),re=o(6685),ae=o(7018),ne=o(4418),ie=o(8348),se=o(4171);const le=(0,y.A)("MuiListItemIcon",["root","alignItemsFlexStart"]);var de=o(1977);function ce(e){return(0,f.Ay)("MuiMenuItem",e)}const pe=(0,y.A)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),ue=(0,x.Ay)(ae.A,{shouldForwardProp:e=>(0,oe.A)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,o.dense&&t.dense,o.divider&&t.divider,!o.disableGutters&&t.gutters]}})((0,M.A)((e=>{let{theme:t}=e;return{...t.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${pe.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,I.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${pe.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,I.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${pe.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,I.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,I.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${pe.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${pe.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity},[`& + .${se.A.root}`]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},[`& + .${se.A.inset}`]:{marginLeft:52},[`& .${de.A.root}`]:{marginTop:0,marginBottom:0},[`& .${de.A.inset}`]:{paddingLeft:36},[`& .${le.root}`]:{minWidth:36},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"}},{props:e=>{let{ownerState:t}=e;return!t.dense},style:{[t.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:e=>{let{ownerState:t}=e;return t.dense},style:{minHeight:32,paddingTop:4,paddingBottom:4,...t.typography.body2,[`& .${le.root} svg`]:{fontSize:"1.25rem"}}}]}}))),me=r.forwardRef((function(e,t){const o=(0,g.b)({props:e,name:"MuiMenuItem"}),{autoFocus:a=!1,component:n="li",dense:i=!1,divider:s=!1,disableGutters:l=!1,focusVisibleClassName:d,role:c="menuitem",tabIndex:p,className:u,...x}=o,y=r.useContext(re.A),f=r.useMemo((()=>({dense:i||y.dense||!1,disableGutters:l})),[y.dense,i,l]),v=r.useRef(null);(0,ne.A)((()=>{a&&v.current&&v.current.focus()}),[a]);const A={...o,dense:f.dense,divider:s,disableGutters:l},j=(e=>{const{disabled:t,dense:o,divider:r,disableGutters:a,selected:n,classes:i}=e,s={root:["root",o&&"dense",t&&"disabled",!a&&"gutters",r&&"divider",n&&"selected"]},l=(0,h.A)(s,ce,i);return{...i,...l}})(o),w=(0,ie.A)(v,t);let C;return o.disabled||(C=void 0!==p?p:-1),(0,b.jsx)(re.A.Provider,{value:f,children:(0,b.jsx)(ue,{ref:w,role:c,tabIndex:C,component:n,focusVisibleClassName:(0,m.A)(j.focusVisible,d),className:(0,m.A)(j.root,u),...x,ownerState:A,classes:j})})}));var he=o(2097);const xe=()=>{const{currentUser:e}=(0,i.A)(),[t,o]=(0,r.useState)([]),[m,h]=(0,r.useState)(""),[x,g]=(0,r.useState)(null),[y,f]=(0,r.useState)({}),[v,A]=(0,r.useState)({});(0,r.useEffect)((()=>{(async()=>{if(e){const t=(0,a.H9)(n.db,"employees",e.uid),o=await(0,a.x7)(t);o.exists()&&g(o.data())}})(),C()}),[e]);const C=async()=>{try{const e=(0,a.rJ)(n.db,"employees"),t=(0,a.P)(e),r=(await(0,a.GG)(t)).docs.map((e=>({id:e.id,...e.data()})));o(r)}catch(e){console.error("Error fetching users:",e)}},M=t.filter((e=>{var t,o,r;return(null===(t=e.employeeId)||void 0===t?void 0:t.toLowerCase().includes(m.toLowerCase()))||(null===(o=e.name)||void 0===o?void 0:o.toLowerCase().includes(m.toLowerCase()))||(null===(r=e.email)||void 0===r?void 0:r.toLowerCase().includes(m.toLowerCase()))}));return(0,b.jsxs)("div",{className:"dashboard-container",children:[(0,b.jsx)(s.A,{userData:x}),(0,b.jsx)("div",{className:"main-content",children:(0,b.jsxs)(l.A,{sx:{p:3},children:[(0,b.jsxs)(l.A,{sx:{mb:3},children:[(0,b.jsx)("h1",{style:{margin:0,marginBottom:"8px",color:"#1976d2",fontSize:"2rem",fontWeight:"bold"},children:"Master Admin Panel"}),(0,b.jsx)("p",{style:{margin:0,color:"#666",fontSize:"1rem"},children:"Manage user roles and permissions for all employees"})]}),(0,b.jsxs)(l.A,{sx:{display:"flex",gap:2,mb:3,flexWrap:"wrap"},children:[(0,b.jsx)(d.A,{sx:{minWidth:200,flex:1},children:(0,b.jsxs)(c.A,{children:[(0,b.jsx)(p.A,{color:"textSecondary",gutterBottom:!0,children:"Total Employees"}),(0,b.jsx)(p.A,{variant:"h4",component:"div",color:"primary",children:t.length})]})}),(0,b.jsx)(d.A,{sx:{minWidth:200,flex:1},children:(0,b.jsxs)(c.A,{children:[(0,b.jsx)(p.A,{color:"textSecondary",gutterBottom:!0,children:"Filtered Results"}),(0,b.jsx)(p.A,{variant:"h4",component:"div",color:"secondary",children:M.length})]})}),(0,b.jsx)(d.A,{sx:{minWidth:200,flex:1},children:(0,b.jsxs)(c.A,{children:[(0,b.jsx)(p.A,{color:"textSecondary",gutterBottom:!0,children:"Admin Users"}),(0,b.jsx)(p.A,{variant:"h4",component:"div",color:"success.main",children:t.filter((e=>"admin"===e.role||"master_admin"===e.role)).length})]})}),(0,b.jsx)(d.A,{sx:{minWidth:200,flex:1},children:(0,b.jsxs)(c.A,{children:[(0,b.jsx)(p.A,{color:"textSecondary",gutterBottom:!0,children:"Regular Users"}),(0,b.jsx)(p.A,{variant:"h4",component:"div",color:"info.main",children:t.filter((e=>"user"===e.role||!e.role)).length})]})})]}),(0,b.jsx)(u.A,{fullWidth:!0,label:"Search by Employee ID, Name, or Email",variant:"outlined",value:m,onChange:e=>h(e.target.value),sx:{mb:3},placeholder:"Enter Employee ID, Full Name, or Email to search..."}),(0,b.jsx)(j,{component:w.A,sx:{boxShadow:3},children:(0,b.jsxs)($,{sx:{minWidth:650},children:[(0,b.jsx)(O,{sx:{backgroundColor:"#f5f5f5"},children:(0,b.jsxs)(U,{children:[(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"Employee ID"}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"Employee Name"}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"Email"}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"Office Name"}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"Division Name"}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"Designation"}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"Current Role"}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"New Role"}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"Actions"}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"#1976d2"},children:"Status"})]})}),(0,b.jsx)(Y,{children:M.map(((e,r)=>(0,b.jsxs)(U,{sx:{"&:nth-of-type(odd)":{backgroundColor:"#fafafa"},"&:hover":{backgroundColor:"#e3f2fd"}},children:[(0,b.jsx)(J,{sx:{fontFamily:"monospace",fontWeight:"bold"},children:e.employeeId}),(0,b.jsx)(J,{sx:{fontWeight:"bold",color:"primary.main"},children:e.name||"N/A"}),(0,b.jsx)(J,{sx:{color:"text.secondary"},children:e.email}),(0,b.jsx)(J,{children:e.officeName}),(0,b.jsx)(J,{children:e.divisionName}),(0,b.jsx)(J,{children:e.designation}),(0,b.jsx)(J,{children:(0,b.jsx)(l.A,{sx:{display:"inline-block",px:2,py:.5,borderRadius:1,fontSize:"0.875rem",fontWeight:"bold",backgroundColor:"master_admin"===e.role?"#e8f5e9":"admin"===e.role?"#fff3e0":"user"===e.role?"#e3f2fd":"#f5f5f5",color:"master_admin"===e.role?"#2e7d32":"admin"===e.role?"#f57c00":"user"===e.role?"#1976d2":"#666"},children:"master_admin"===e.role?"Master Admin":"admin"===e.role?"Admin":"user"===e.role?"User":"No Role"})}),(0,b.jsx)(J,{children:(0,b.jsx)(ee.A,{fullWidth:!0,size:"small",children:(0,b.jsxs)(te.A,{value:y[e.id]||"",onChange:t=>{const o=t.target.value;f((t=>({...t,[e.id]:o})))},displayEmpty:!0,disabled:"updating"===v[e.id],children:[(0,b.jsx)(me,{value:"",children:"Select Role"}),(0,b.jsx)(me,{value:"user",children:"User"}),(0,b.jsx)(me,{value:"admin",children:"Admin"}),(0,b.jsx)(me,{value:"master_admin",children:"Master Admin"})]})})}),(0,b.jsx)(J,{children:(0,b.jsx)(he.A,{variant:"contained",color:"primary",disabled:!y[e.id]||"updating"===v[e.id],onClick:()=>(async(e,r)=>{try{A((t=>({...t,[e]:"updating"})));const i=(0,a.H9)(n.db,"employees",e);if(!(await(0,a.x7)(i)).exists())throw new Error("User document not found");await(0,a.mZ)(i,{role:r});const s=await(0,a.x7)(i),l=s.data();if(!s.exists()||(null===l||void 0===l?void 0:l.role)!==r)throw new Error("Role update verification failed");o(t.map((t=>t.id===e?{...t,...l,role:r}:t))),await C(),f((t=>{const o={...t};return delete o[e],o})),A((t=>({...t,[e]:"success"}))),setTimeout((()=>{A((t=>{const o={...t};return delete o[e],o}))}),3e3)}catch(i){console.error("Error updating user role:",i),A((t=>({...t,[e]:"error"}))),setTimeout((()=>{A((t=>{const o={...t};return delete o[e],o}))}),3e3)}})(e.id,y[e.id]),children:"updating"===v[e.id]?"Updating...":"Update"})}),(0,b.jsxs)(J,{children:["success"===v[e.id]&&(0,b.jsx)(l.A,{sx:{color:"success.main"},children:"Role updated successfully!"}),"error"===v[e.id]&&(0,b.jsx)(l.A,{sx:{color:"error.main"},children:"Update failed"})]})]},e.id)))})]})})]})})]})}},4171:(e,t,o)=>{o.d(t,{A:()=>i,K:()=>n});var r=o(9857),a=o(6061);function n(e){return(0,a.Ay)("MuiDivider",e)}const i=(0,r.A)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},6128:(e,t,o)=>{o.d(t,{A:()=>m});var r=o(5043),a=o(8387),n=o(1807),i=o(8128),s=o(8301),l=o(9857),d=o(6061);function c(e){return(0,d.Ay)("MuiCardContent",e)}(0,l.A)("MuiCardContent",["root"]);var p=o(579);const u=(0,i.Ay)("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),m=r.forwardRef((function(e,t){const o=(0,s.b)({props:e,name:"MuiCardContent"}),{className:r,component:i="div",...l}=o,d={...o,component:i},m=(e=>{const{classes:t}=e;return(0,n.A)({root:["root"]},c,t)})(d);return(0,p.jsx)(u,{as:i,className:(0,a.A)(m.root,r),ownerState:d,ref:t,...l})}))}}]);
//# sourceMappingURL=541.aadf3ba5.chunk.js.map