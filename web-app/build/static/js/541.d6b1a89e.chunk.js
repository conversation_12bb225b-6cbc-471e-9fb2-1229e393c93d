"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[541],{1977:(e,t,a)=>{a.d(t,{A:()=>n,b:()=>i});var r=a(9857),o=a(6061);function i(e){return(0,o.Ay)("MuiListItemText",e)}const n=(0,r.A)("MuiListItemText",["root","multiline","dense","inset","primary","secondary"])},2541:(e,t,a)=>{a.r(t),a.d(t,{default:()=>ue});var r=a(5043),o=a(5472),i=a(2073),n=a(9066),s=a(1103),l=a(3969),d=a(2775),c=a(8387),p=a(1807),u=a(8128),m=a(8301),y=a(9857),h=a(6061);function v(e){return(0,h.Ay)("MuiTableContainer",e)}(0,y.A)("MuiTableContainer",["root"]);var g=a(579);const b=(0,u.Ay)("div",{name:"MuiTableContainer",slot:"Root"})({width:"100%",overflowX:"auto"}),x=r.forwardRef((function(e,t){const a=(0,m.b)({props:e,name:"MuiTableContainer"}),{className:r,component:o="div",...i}=a,n={...a,component:o},s=(e=>{const{classes:t}=e;return(0,p.A)({root:["root"]},v,t)})(n);return(0,g.jsx)(b,{ref:t,as:o,className:(0,c.A)(s.root,r),ownerState:n,...i})}));var f=a(4799);const A=r.createContext();var w=a(1612);function C(e){return(0,h.Ay)("MuiTable",e)}(0,y.A)("MuiTable",["root","stickyHeader"]);const j=(0,u.Ay)("table",{name:"MuiTable",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.root,a.stickyHeader&&t.stickyHeader]}})((0,w.A)((e=>{let{theme:t}=e;return{display:"table",width:"100%",borderCollapse:"collapse",borderSpacing:0,"& caption":{...t.typography.body2,padding:t.spacing(2),color:(t.vars||t).palette.text.secondary,textAlign:"left",captionSide:"bottom"},variants:[{props:e=>{let{ownerState:t}=e;return t.stickyHeader},style:{borderCollapse:"separate"}}]}}))),M="table",k=r.forwardRef((function(e,t){const a=(0,m.b)({props:e,name:"MuiTable"}),{className:o,component:i=M,padding:n="normal",size:s="medium",stickyHeader:l=!1,...d}=a,u={...a,component:i,padding:n,size:s,stickyHeader:l},y=(e=>{const{classes:t,stickyHeader:a}=e,r={root:["root",a&&"stickyHeader"]};return(0,p.A)(r,C,t)})(u),h=r.useMemo((()=>({padding:n,size:s,stickyHeader:l})),[n,s,l]);return(0,g.jsx)(A.Provider,{value:h,children:(0,g.jsx)(j,{as:i,role:i===M?null:"table",ref:t,className:(0,c.A)(y.root,o),ownerState:u,...d})})}));const $=r.createContext();function T(e){return(0,h.Ay)("MuiTableHead",e)}(0,y.A)("MuiTableHead",["root"]);const R=(0,u.Ay)("thead",{name:"MuiTableHead",slot:"Root"})({display:"table-header-group"}),S={variant:"head"},H="thead",N=r.forwardRef((function(e,t){const a=(0,m.b)({props:e,name:"MuiTableHead"}),{className:r,component:o=H,...i}=a,n={...a,component:o},s=(e=>{const{classes:t}=e;return(0,p.A)({root:["root"]},T,t)})(n);return(0,g.jsx)($.Provider,{value:S,children:(0,g.jsx)(R,{as:o,className:(0,c.A)(s.root,r),ref:t,role:o===H?null:"rowgroup",ownerState:n,...i})})}));var O=a(9826);function z(e){return(0,h.Ay)("MuiTableRow",e)}const I=(0,y.A)("MuiTableRow",["root","selected","hover","head","footer"]),D=(0,u.Ay)("tr",{name:"MuiTableRow",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.root,a.head&&t.head,a.footer&&t.footer]}})((0,w.A)((e=>{let{theme:t}=e;return{color:"inherit",display:"table-row",verticalAlign:"middle",outline:0,[`&.${I.hover}:hover`]:{backgroundColor:(t.vars||t).palette.action.hover},[`&.${I.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,O.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),"&:hover":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,O.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity)}}}}))),B="tr",L=r.forwardRef((function(e,t){const a=(0,m.b)({props:e,name:"MuiTableRow"}),{className:o,component:i=B,hover:n=!1,selected:s=!1,...l}=a,d=r.useContext($),u={...a,component:i,hover:n,selected:s,head:d&&"head"===d.variant,footer:d&&"footer"===d.variant},y=(e=>{const{classes:t,selected:a,hover:r,head:o,footer:i}=e,n={root:["root",a&&"selected",r&&"hover",o&&"head",i&&"footer"]};return(0,p.A)(n,z,t)})(u);return(0,g.jsx)(D,{as:i,ref:t,className:(0,c.A)(y.root,o),role:i===B?null:"row",ownerState:u,...l})})),X=L;var E=a(7194);function G(e){return(0,h.Ay)("MuiTableCell",e)}const V=(0,y.A)("MuiTableCell",["root","head","body","footer","sizeSmall","sizeMedium","paddingCheckbox","paddingNone","alignLeft","alignCenter","alignRight","alignJustify","stickyHeader"]),_=(0,u.Ay)("td",{name:"MuiTableCell",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.root,t[a.variant],t[`size${(0,E.A)(a.size)}`],"normal"!==a.padding&&t[`padding${(0,E.A)(a.padding)}`],"inherit"!==a.align&&t[`align${(0,E.A)(a.align)}`],a.stickyHeader&&t.stickyHeader]}})((0,w.A)((e=>{let{theme:t}=e;return{...t.typography.body2,display:"table-cell",verticalAlign:"inherit",borderBottom:t.vars?`1px solid ${t.vars.palette.TableCell.border}`:`1px solid\n    ${"light"===t.palette.mode?(0,O.a)((0,O.X4)(t.palette.divider,1),.88):(0,O.e$)((0,O.X4)(t.palette.divider,1),.68)}`,textAlign:"left",padding:16,variants:[{props:{variant:"head"},style:{color:(t.vars||t).palette.text.primary,lineHeight:t.typography.pxToRem(24),fontWeight:t.typography.fontWeightMedium}},{props:{variant:"body"},style:{color:(t.vars||t).palette.text.primary}},{props:{variant:"footer"},style:{color:(t.vars||t).palette.text.secondary,lineHeight:t.typography.pxToRem(21),fontSize:t.typography.pxToRem(12)}},{props:{size:"small"},style:{padding:"6px 16px",[`&.${V.paddingCheckbox}`]:{width:24,padding:"0 12px 0 16px","& > *":{padding:0}}}},{props:{padding:"checkbox"},style:{width:48,padding:"0 0 0 4px"}},{props:{padding:"none"},style:{padding:0}},{props:{align:"left"},style:{textAlign:"left"}},{props:{align:"center"},style:{textAlign:"center"}},{props:{align:"right"},style:{textAlign:"right",flexDirection:"row-reverse"}},{props:{align:"justify"},style:{textAlign:"justify"}},{props:e=>{let{ownerState:t}=e;return t.stickyHeader},style:{position:"sticky",top:0,zIndex:2,backgroundColor:(t.vars||t).palette.background.default}}]}}))),P=r.forwardRef((function(e,t){const a=(0,m.b)({props:e,name:"MuiTableCell"}),{align:o="inherit",className:i,component:n,padding:s,scope:l,size:d,sortDirection:u,variant:y,...h}=a,v=r.useContext(A),b=r.useContext($),x=b&&"head"===b.variant;let f;f=n||(x?"th":"td");let w=l;"td"===f?w=void 0:!w&&x&&(w="col");const C=y||b&&b.variant,j={...a,align:o,component:f,padding:s||(v&&v.padding?v.padding:"normal"),size:d||(v&&v.size?v.size:"medium"),sortDirection:u,stickyHeader:"head"===C&&v&&v.stickyHeader,variant:C},M=(e=>{const{classes:t,variant:a,align:r,padding:o,size:i,stickyHeader:n}=e,s={root:["root",a,n&&"stickyHeader","inherit"!==r&&`align${(0,E.A)(r)}`,"normal"!==o&&`padding${(0,E.A)(o)}`,`size${(0,E.A)(i)}`]};return(0,p.A)(s,G,t)})(j);let k=null;return u&&(k="asc"===u?"ascending":"descending"),(0,g.jsx)(_,{as:f,ref:t,className:(0,c.A)(M.root,i),"aria-sort":k,scope:w,ownerState:j,...h})})),U=P;function W(e){return(0,h.Ay)("MuiTableBody",e)}(0,y.A)("MuiTableBody",["root"]);const F=(0,u.Ay)("tbody",{name:"MuiTableBody",slot:"Root"})({display:"table-row-group"}),J={variant:"body"},K="tbody",Z=r.forwardRef((function(e,t){const a=(0,m.b)({props:e,name:"MuiTableBody"}),{className:r,component:o=K,...i}=a,n={...a,component:o},s=(e=>{const{classes:t}=e;return(0,p.A)({root:["root"]},W,t)})(n);return(0,g.jsx)($.Provider,{value:J,children:(0,g.jsx)(F,{className:(0,c.A)(s.root,r),as:o,ref:t,role:o===K?null:"rowgroup",ownerState:n,...i})})}));var q=a(256),Q=a(7434),Y=a(2466),ee=a(6685),te=a(7018),ae=a(4418),re=a(8348),oe=a(4171);const ie=(0,y.A)("MuiListItemIcon",["root","alignItemsFlexStart"]);var ne=a(1977);function se(e){return(0,h.Ay)("MuiMenuItem",e)}const le=(0,y.A)("MuiMenuItem",["root","focusVisible","dense","disabled","divider","gutters","selected"]),de=(0,u.Ay)(te.A,{shouldForwardProp:e=>(0,Y.A)(e)||"classes"===e,name:"MuiMenuItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:a}=e;return[t.root,a.dense&&t.dense,a.divider&&t.divider,!a.disableGutters&&t.gutters]}})((0,w.A)((e=>{let{theme:t}=e;return{...t.typography.body1,display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",minHeight:48,paddingTop:6,paddingBottom:6,boxSizing:"border-box",whiteSpace:"nowrap","&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${le.selected}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,O.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${le.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,O.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}},[`&.${le.selected}:hover`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,O.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,O.X4)(t.palette.primary.main,t.palette.action.selectedOpacity)}},[`&.${le.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},[`&.${le.disabled}`]:{opacity:(t.vars||t).palette.action.disabledOpacity},[`& + .${oe.A.root}`]:{marginTop:t.spacing(1),marginBottom:t.spacing(1)},[`& + .${oe.A.inset}`]:{marginLeft:52},[`& .${ne.A.root}`]:{marginTop:0,marginBottom:0},[`& .${ne.A.inset}`]:{paddingLeft:36},[`& .${ie.root}`]:{minWidth:36},variants:[{props:e=>{let{ownerState:t}=e;return!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"}},{props:e=>{let{ownerState:t}=e;return!t.dense},style:{[t.breakpoints.up("sm")]:{minHeight:"auto"}}},{props:e=>{let{ownerState:t}=e;return t.dense},style:{minHeight:32,paddingTop:4,paddingBottom:4,...t.typography.body2,[`& .${ie.root} svg`]:{fontSize:"1.25rem"}}}]}}))),ce=r.forwardRef((function(e,t){const a=(0,m.b)({props:e,name:"MuiMenuItem"}),{autoFocus:o=!1,component:i="li",dense:n=!1,divider:s=!1,disableGutters:l=!1,focusVisibleClassName:d,role:u="menuitem",tabIndex:y,className:h,...v}=a,b=r.useContext(ee.A),x=r.useMemo((()=>({dense:n||b.dense||!1,disableGutters:l})),[b.dense,n,l]),f=r.useRef(null);(0,ae.A)((()=>{o&&f.current&&f.current.focus()}),[o]);const A={...a,dense:x.dense,divider:s,disableGutters:l},w=(e=>{const{disabled:t,dense:a,divider:r,disableGutters:o,selected:i,classes:n}=e,s={root:["root",a&&"dense",t&&"disabled",!o&&"gutters",r&&"divider",i&&"selected"]},l=(0,p.A)(s,se,n);return{...n,...l}})(a),C=(0,re.A)(f,t);let j;return a.disabled||(j=void 0!==y?y:-1),(0,g.jsx)(ee.A.Provider,{value:x,children:(0,g.jsx)(de,{ref:C,role:u,tabIndex:j,component:i,focusVisibleClassName:(0,c.A)(w.focusVisible,d),className:(0,c.A)(w.root,h),...v,ownerState:A,classes:w})})}));var pe=a(2097);const ue=()=>{const{currentUser:e}=(0,n.A)(),[t,a]=(0,r.useState)([]),[c,p]=(0,r.useState)(""),[u,m]=(0,r.useState)(null),[y,h]=(0,r.useState)({}),[v,b]=(0,r.useState)({});(0,r.useEffect)((()=>{(async()=>{if(e){const t=(0,o.H9)(i.db,"employees",e.uid),a=await(0,o.x7)(t);a.exists()&&m(a.data())}})(),A()}),[e]);const A=async()=>{try{const e=(0,o.rJ)(i.db,"employees"),t=(0,o.P)(e),r=(await(0,o.GG)(t)).docs.map((e=>({id:e.id,...e.data()})));a(r)}catch(e){console.error("Error fetching users:",e)}},w=t.filter((e=>{var t;return null===(t=e.employeeId)||void 0===t?void 0:t.toLowerCase().includes(c.toLowerCase())}));return(0,g.jsxs)("div",{className:"dashboard-container",children:[(0,g.jsx)(s.A,{userData:u}),(0,g.jsx)("div",{className:"main-content",children:(0,g.jsxs)(l.A,{sx:{p:3},children:[(0,g.jsx)(d.A,{fullWidth:!0,label:"Search by Employee ID",variant:"outlined",value:c,onChange:e=>p(e.target.value),sx:{mb:3}}),(0,g.jsx)(x,{component:f.A,children:(0,g.jsxs)(k,{children:[(0,g.jsx)(N,{children:(0,g.jsxs)(X,{children:[(0,g.jsx)(U,{children:"Employee ID"}),(0,g.jsx)(U,{children:"Office Name"}),(0,g.jsx)(U,{children:"Division Name"}),(0,g.jsx)(U,{children:"Designation"}),(0,g.jsx)(U,{children:"Current Role"}),(0,g.jsx)(U,{children:"New Role"}),(0,g.jsx)(U,{children:"Actions"}),(0,g.jsx)(U,{children:"Status"})]})}),(0,g.jsx)(Z,{children:w.map((e=>(0,g.jsxs)(X,{children:[(0,g.jsx)(U,{children:e.employeeId}),(0,g.jsx)(U,{children:e.officeName}),(0,g.jsx)(U,{children:e.divisionName}),(0,g.jsx)(U,{children:e.designation}),(0,g.jsx)(U,{children:e.role||"No Role"}),(0,g.jsx)(U,{children:(0,g.jsx)(q.A,{fullWidth:!0,size:"small",children:(0,g.jsxs)(Q.A,{value:y[e.id]||"",onChange:t=>{const a=t.target.value;h((t=>({...t,[e.id]:a})))},displayEmpty:!0,disabled:"updating"===v[e.id],children:[(0,g.jsx)(ce,{value:"",children:"Select Role"}),(0,g.jsx)(ce,{value:"user",children:"User"}),(0,g.jsx)(ce,{value:"admin",children:"Admin"}),(0,g.jsx)(ce,{value:"master_admin",children:"Master Admin"})]})})}),(0,g.jsx)(U,{children:(0,g.jsx)(pe.A,{variant:"contained",color:"primary",disabled:!y[e.id]||"updating"===v[e.id],onClick:()=>(async(e,r)=>{try{b((t=>({...t,[e]:"updating"})));const n=(0,o.H9)(i.db,"employees",e);if(!(await(0,o.x7)(n)).exists())throw new Error("User document not found");await(0,o.mZ)(n,{role:r});const s=await(0,o.x7)(n),l=s.data();if(!s.exists()||(null===l||void 0===l?void 0:l.role)!==r)throw new Error("Role update verification failed");a(t.map((t=>t.id===e?{...t,...l,role:r}:t))),await A(),h((t=>{const a={...t};return delete a[e],a})),b((t=>({...t,[e]:"success"}))),setTimeout((()=>{b((t=>{const a={...t};return delete a[e],a}))}),3e3)}catch(n){console.error("Error updating user role:",n),b((t=>({...t,[e]:"error"}))),setTimeout((()=>{b((t=>{const a={...t};return delete a[e],a}))}),3e3)}})(e.id,y[e.id]),children:"updating"===v[e.id]?"Updating...":"Update"})}),(0,g.jsxs)(U,{children:["success"===v[e.id]&&(0,g.jsx)(l.A,{sx:{color:"success.main"},children:"Role updated successfully!"}),"error"===v[e.id]&&(0,g.jsx)(l.A,{sx:{color:"error.main"},children:"Update failed"})]})]},e.id)))})]})})]})})]})}},4171:(e,t,a)=>{a.d(t,{A:()=>n,K:()=>i});var r=a(9857),o=a(6061);function i(e){return(0,o.Ay)("MuiDivider",e)}const n=(0,r.A)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])}}]);
//# sourceMappingURL=541.d6b1a89e.chunk.js.map