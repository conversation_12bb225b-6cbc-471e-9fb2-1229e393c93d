"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[625],{1244:(e,t,i)=>{i.d(t,{A:()=>m});var a=i(5043),n=i(8387),s=i(1807),o=i(8128),r=i(8301),l=i(4799),d=i(9857),c=i(6061);function u(e){return(0,c.Ay)("MuiCard",e)}(0,d.A)("MuiCard",["root"]);var p=i(579);const h=(0,o.Ay)(l.A,{name:"MuiCard",slot:"Root"})({overflow:"hidden"}),m=a.forwardRef((function(e,t){const i=(0,r.b)({props:e,name:"MuiCard"}),{className:a,raised:o=!1,...l}=i,d={...i,raised:o},c=(e=>{const{classes:t}=e;return(0,s.A)({root:["root"]},u,t)})(d);return(0,p.jsx)(h,{className:(0,n.A)(c.root,a),elevation:o?8:void 0,ref:t,ownerState:d,...l})}))},1977:(e,t,i)=>{i.d(t,{A:()=>o,b:()=>s});var a=i(9857),n=i(6061);function s(e){return(0,n.Ay)("MuiListItemText",e)}const o=(0,a.A)("MuiListItemText",["root","multiline","dense","inset","primary","secondary"])},3393:(e,t,i)=>{i.d(t,{A:()=>s});i(5043),i(9482);var a=i(579);const n=[{title:"SB Accounts",value:123456},{title:"BD Revenue",value:"\u20b924,343"},{title:"No. Aadhaar Trans",value:1259},{title:"PLI",value:"\u20b999,99,999"}],s=()=>(0,a.jsx)("div",{className:"stats-grid",children:n.map(((e,t)=>(0,a.jsxs)("div",{className:`stat-card card-${t}`,children:[(0,a.jsx)("h3",{children:e.title}),(0,a.jsx)("p",{className:"stat-value",children:e.value})]},t)))})},4171:(e,t,i)=>{i.d(t,{A:()=>o,K:()=>s});var a=i(9857),n=i(6061);function s(e){return(0,n.Ay)("MuiDivider",e)}const o=(0,a.A)("MuiDivider",["root","absolute","fullWidth","inset","middle","flexItem","light","vertical","withChildren","withChildrenVertical","textAlignRight","textAlignLeft","wrapper","wrapperVertical"])},6128:(e,t,i)=>{i.d(t,{A:()=>h});var a=i(5043),n=i(8387),s=i(1807),o=i(8128),r=i(8301),l=i(9857),d=i(6061);function c(e){return(0,d.Ay)("MuiCardContent",e)}(0,l.A)("MuiCardContent",["root"]);var u=i(579);const p=(0,o.Ay)("div",{name:"MuiCardContent",slot:"Root"})({padding:16,"&:last-child":{paddingBottom:24}}),h=a.forwardRef((function(e,t){const i=(0,r.b)({props:e,name:"MuiCardContent"}),{className:a,component:o="div",...l}=i,d={...i,component:o},h=(e=>{const{classes:t}=e;return(0,s.A)({root:["root"]},c,t)})(d);return(0,u.jsx)(p,{as:o,className:(0,n.A)(h.root,a),ownerState:d,ref:t,...l})}))},8625:(e,t,i)=>{i.r(t),i.d(t,{default:()=>ve});var a=i(5043),n=i(9002),s=i(5472),o=i(2073),r=i(9066),l=i(1103),d=i(3393),c=i(579);const u=e=>{let{isOpen:t,onClose:i,title:a,children:n}=e;return t?(0,c.jsx)("div",{className:"modal-overlay",onClick:i,children:(0,c.jsxs)("div",{className:"modal-content",onClick:e=>e.stopPropagation(),children:[(0,c.jsxs)("div",{className:"modal-header",children:[(0,c.jsx)("h2",{children:a}),(0,c.jsx)("button",{className:"close-button",onClick:i,children:"\xd7"})]}),(0,c.jsx)("div",{className:"modal-body",children:n})]})}):null};var p=i(3204);const h=(e,t)=>{const i=t.find((t=>t.id===e));return!!i&&!i.parentId},m=(e,t)=>!t.some((t=>t.parentId===e)),f=e=>{const t={},i=[];return e.forEach((e=>{t[e.id]={...e,children:[]}})),e.forEach((e=>{var a;e.parentId&&t[e.parentId]?null===(a=t[e.parentId].children)||void 0===a||a.push(t[e.id]):i.push(t[e.id])})),i},g=(e,t)=>{let i=[];const a=t.filter((t=>t.parentId===e));for(const n of a)i.push(n.id),i=i.concat(g(n.id,t));return i},v=e=>{const{categories:t,setCategories:i,selectedCard:n,setSelectedCard:r,newCardId:l,setNewCardId:d,newCardTitle:c,setNewCardTitle:u,actionType:h,setActionType:m,setIsLoading:f,setError:v,setSuccess:x,setShowConfirmModal:b,setIsAddingNewCard:y,setPageConfig:C,setFields:j,setEditingCard:w,setShowEditModal:A,setCardToDelete:S,setShowDeleteConfirmModal:N}=e,D=(0,a.useCallback)((async()=>{f(!0);try{const e=(await(0,s.GG)((0,s.rJ)(o.db,"categories"))).docs.map((e=>({id:e.id,...e.data()})));i(e)}catch(e){v("Failed to fetch categories."),console.error(e)}finally{f(!1)}}),[i,f,v]);return{fetchCategories:D,handleAddNewCard:async()=>{if(!l||!c)return void v("Report ID and Title are required.");f(!0);if(await(async e=>{const t=(0,s.H9)(o.db,"categories",e);return(await(0,s.x7)(t)).exists()})(l))return v("This Report ID already exists. Please use a unique ID."),void f(!1);f(!1),b(!0)},handleConfirmCreate:async()=>{var e;if(!l||!c)return v("Report ID and Title cannot be empty."),void b(!1);let i=null;"createNestedCard"===h&&n?i=n:"addNewCardGlobal"===h?i=null:n&&"addNewCardGlobal"!==h?i=n:n||"createNestedCard"===h||(i=null);const a=`${i?null===(e=t.find((e=>e.id===i)))||void 0===e?void 0:e.path:"/categories"}/${l}`.replace(/\/+/g,"/");try{f(!0),b(!1);const e=(0,s.H9)(o.db,"categories",l),{icon:t,color:n}=(e=>{const t=e.split("").reduce(((e,t)=>e+t.charCodeAt(0)),0),i=[p.M1W,p.t69,p.Pcn,p.g1V],a=["#FFC107","#2196F3","#4CAF50","#E91E63","#9C27B0"];return{icon:i[t%i.length],color:a[t%a.length]}})(c);await(0,s.BN)(e,{id:l,title:c,path:a,parentId:i,lastUpdated:(new Date).toISOString(),icon:t.name,color:n,fields:[],isPage:!0,pageId:l}),await D(),d(""),u(""),y(!1),m(""),r(l),x(`Report "${c}" has been created successfully!`),setTimeout((()=>x(null)),3e3)}catch(g){v("Error creating new report. Check console for details."),console.error("Error creating card:",g)}finally{f(!1)}},handleEditCard:e=>{w(e),u(e.title),A(!0)},handleUpdateCard:async()=>{const e=t.find((e=>e.id===n));if(e&&c)try{f(!0);const t=(0,s.H9)(o.db,"categories",e.id);await(0,s.mZ)(t,{title:c,lastUpdated:(new Date).toISOString()}),await D(),A(!1),w(null),u(""),x("Report updated successfully!"),setTimeout((()=>x(null)),3e3)}catch(i){v("Failed to update report."),console.error(i)}finally{f(!1)}},handleDeleteClick:e=>{S(e),N(!0)},handleConfirmDelete:async()=>{if(n){f(!0);try{const e=(0,s.wP)(o.db),i=g(n,t),a=[n,...i];for(const t of a)e.delete((0,s.H9)(o.db,"categories",t)),e.delete((0,s.H9)(o.db,"pages",t));await e.commit(),await D(),N(!1),S(null),r(""),C(null),j([]),x("Report and all its nested items deleted successfully!"),setTimeout((()=>x(null)),3e3)}catch(e){v("Failed to delete report."),console.error(e)}finally{f(!1)}}}}};var x=i(8883);const b=e=>{let{categories:t,selectedCard:i,onCardChange:a,actionType:n,onActionChange:s,isLoading:o,onCreateAction:r,onWebPageAction:l}=e;const d=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e.flatMap((e=>[(0,c.jsx)("option",{value:e.id,style:{paddingLeft:20*t+"px"},children:`${"--".repeat(t)} ${e.title}`},e.id),...e.children&&e.children.length>0?d(e.children,t+1):[]]))};return(0,c.jsxs)("div",{className:"card-selector",children:[(0,c.jsxs)("select",{value:i,onChange:e=>{const t=e.target.value;a(t)},className:"form-select",disabled:o,children:[(0,c.jsx)("option",{value:"",children:o?"Loading Reports...":"Select or Create New Report"}),d(f(t))]}),(0,c.jsx)("div",{className:"action-dropdown-container",children:(0,c.jsxs)("select",{value:n,onChange:e=>{const t=e.target.value;s(t),"createNestedCard"===t||"addNewCardGlobal"===t?r():"createWebPage"===t&&l()},className:"form-select action-dropdown",children:[(0,c.jsx)("option",{value:"",children:"Select Action..."}),(0,c.jsx)("option",{value:"addNewCardGlobal",disabled:!!i,children:"Create New Main Report"}),i&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("option",{value:"createNestedCard",children:"Create Nested Report"}),(0,c.jsx)("option",{value:"createWebPage",disabled:!m(i,t)||h(i,t),children:"Create/Edit Web Page for this Report"})]})]})})]})},y=e=>{let{selectedCard:t,categories:i,onEditCard:n,onDeleteCard:s}=e;const o=i.find((e=>e.id===t));return o?(0,c.jsxs)("div",{className:"card-management",children:[(0,c.jsxs)("h3",{children:['Report Details: "',o.title,'"']}),(0,c.jsxs)("div",{className:"card-actions",children:[(0,c.jsxs)("button",{onClick:()=>n(o),className:"edit-button btn btn-outline-primary btn-sm me-2",disabled:!t,children:[a.createElement(p.uO9)," Edit Name"]}),(0,c.jsxs)("button",{onClick:()=>s(t),className:"delete-button btn btn-outline-danger btn-sm",disabled:!t,children:[a.createElement(p.qbC)," Delete Report"]})]})]}):null},C=e=>{var t;let{field:i,index:n,onUpdate:s,onRemove:o}=e;const r=(e,t,a)=>{const o=[...i.options||[]];o[e]={...o[e],[a]:t},s(n,{...i,options:o})},l=e=>{const{value:t,type:a}=e.target;let o=t;"checkbox"===a&&(o=e.target.checked),s(n,{...i,defaultValue:o})};return(0,c.jsxs)("div",{className:"field-config-item card mb-3",children:[(0,c.jsxs)("div",{className:"card-header d-flex justify-content-between align-items-center",children:[(0,c.jsx)("strong",{children:i.label||"Unnamed Field"})," (",i.type,")",(0,c.jsxs)("button",{onClick:()=>o(n),className:"btn btn-danger btn-sm",children:[a.createElement(p.qbC)," Remove"]})]}),(0,c.jsxs)("div",{className:"card-body",children:[(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{htmlFor:`field-type-${n}`,className:"form-label",children:"Type: "}),(0,c.jsxs)("select",{id:`field-type-${n}`,className:"form-control",value:i.type,onChange:e=>s(n,{...i,type:e.target.value,options:"dropdown"!==i.type&&"radio"!==i.type&&"checkbox-group"!==i.type?void 0:i.options,placeholder:"section"===i.type||"button"===i.type?void 0:i.placeholder}),children:[(0,c.jsx)("option",{value:"text",children:"Text"}),(0,c.jsx)("option",{value:"textarea",children:"Textarea"}),(0,c.jsx)("option",{value:"number",children:"Number"}),(0,c.jsx)("option",{value:"date",children:"Date"}),(0,c.jsx)("option",{value:"dropdown",children:"Dropdown"}),(0,c.jsx)("option",{value:"radio",children:"Radio Group"}),(0,c.jsx)("option",{value:"checkbox",children:"Checkbox (Single)"}),(0,c.jsx)("option",{value:"checkbox-group",children:"Checkbox Group"}),(0,c.jsx)("option",{value:"switch",children:"Switch"}),(0,c.jsx)("option",{value:"file",children:"File Upload"}),(0,c.jsx)("option",{value:"section",children:"Section Header"}),(0,c.jsx)("option",{value:"button",children:"Button"})]})]}),(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{htmlFor:`field-label-${n}`,className:"form-label",children:"Label: "}),(0,c.jsx)("input",{id:`field-label-${n}`,type:"text",className:"form-control",value:i.label,onChange:e=>s(n,{...i,label:e.target.value}),required:!0})]}),["text","textarea","number","date"].includes(i.type)&&(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{htmlFor:`field-placeholder-${n}`,className:"form-label",children:"Placeholder: "}),(0,c.jsx)("input",{id:`field-placeholder-${n}`,type:"text",className:"form-control",value:i.placeholder||"",onChange:e=>s(n,{...i,placeholder:e.target.value})})]}),"number"===i.type&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{htmlFor:`field-min-${n}`,className:"form-label",children:"Min Value: "}),(0,c.jsx)("input",{id:`field-min-${n}`,type:"number",className:"form-control",value:void 0===i.min?"":i.min,onChange:e=>s(n,{...i,min:""===e.target.value?void 0:parseFloat(e.target.value)})})]}),(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{htmlFor:`field-max-${n}`,className:"form-label",children:"Max Value: "}),(0,c.jsx)("input",{id:`field-max-${n}`,type:"number",className:"form-control",value:void 0===i.max?"":i.max,onChange:e=>s(n,{...i,max:""===e.target.value?void 0:parseFloat(e.target.value)})})]})]}),["dropdown","radio","checkbox-group"].includes(i.type)&&(0,c.jsxs)("div",{className:"form-group field-options-config",children:[(0,c.jsx)("label",{className:"form-label",children:"Options: "}),null===(t=i.options)||void 0===t?void 0:t.map(((e,t)=>(0,c.jsxs)("div",{className:"input-group mb-2",children:[(0,c.jsx)("input",{type:"text",className:"form-control",placeholder:"Option Label",value:e.label,onChange:e=>r(t,e.target.value,"label")}),(0,c.jsx)("input",{type:"text",className:"form-control",placeholder:"Option Value",value:e.value,onChange:e=>r(t,e.target.value,"value")}),(0,c.jsx)("button",{type:"button",onClick:()=>(e=>{var t;const a=null===(t=i.options)||void 0===t?void 0:t.filter(((t,i)=>i!==e));s(n,{...i,options:a})})(t),className:"btn btn-outline-danger",children:"Remove"})]},t))),(0,c.jsx)("button",{type:"button",onClick:()=>{const e=[...i.options||[],{label:"",value:""}];s(n,{...i,options:e})},className:"btn btn-secondary btn-sm",children:"Add Option"})]}),["text","textarea","number","date"].includes(i.type)&&(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{htmlFor:`field-default-value-${n}`,className:"form-label",children:"Default Value: "}),(0,c.jsx)("input",{id:`field-default-value-${n}`,type:"number"===i.type?"number":"date"===i.type?"date":"text",className:"form-control",value:void 0===i.defaultValue?"":String(i.defaultValue),onChange:l})]}),("checkbox"===i.type||"switch"===i.type)&&(0,c.jsxs)("div",{className:"form-group form-check",children:[(0,c.jsx)("input",{id:`field-default-value-${n}`,type:"checkbox",className:"form-check-input",checked:Boolean(i.defaultValue),onChange:l}),(0,c.jsx)("label",{htmlFor:`field-default-value-${n}`,className:"form-check-label",children:"Default Checked: "})]}),["dropdown","radio"].includes(i.type)&&i.options&&i.options.length>0&&(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{htmlFor:`field-default-value-${n}`,className:"form-label",children:"Default Value: "}),(0,c.jsxs)("select",{id:`field-default-value-${n}`,className:"form-control",value:void 0===i.defaultValue?"":String(i.defaultValue),onChange:l,children:[(0,c.jsx)("option",{value:"",children:"-- Select Default --"}),i.options.map((e=>(0,c.jsx)("option",{value:e.value,children:e.label},e.value)))]})]}),"checkbox-group"===i.type&&(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{className:"form-label",children:"Default Values (comma-separated): "}),(0,c.jsx)("input",{type:"text",className:"form-control",value:Array.isArray(i.defaultValue)?i.defaultValue.join(","):"",onChange:e=>s(n,{...i,defaultValue:e.target.value.split(",").map((e=>e.trim())).filter((e=>e))}),placeholder:"value1,value2"})]}),"button"===i.type&&(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{htmlFor:`field-button-text-${n}`,className:"form-label",children:"Button Text: "}),(0,c.jsx)("input",{id:`field-button-text-${n}`,type:"text",className:"form-control",value:i.buttonText||"",onChange:e=>s(n,{...i,buttonText:e.target.value})})]}),"section"===i.type&&(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsx)("label",{htmlFor:`field-section-title-${n}`,className:"form-label",children:"Section Title: "}),(0,c.jsx)("input",{id:`field-section-title-${n}`,type:"text",className:"form-control",value:i.sectionTitle||"",onChange:e=>s(n,{...i,sectionTitle:e.target.value})})]}),!["button","section"].includes(i.type)&&(0,c.jsxs)("div",{className:"form-group form-check",children:[(0,c.jsx)("input",{id:`field-required-${n}`,type:"checkbox",className:"form-check-input",checked:!!i.required,onChange:e=>s(n,{...i,required:e.target.checked})}),(0,c.jsx)("label",{htmlFor:`field-required-${n}`,className:"form-check-label",children:" Required"})]})]})]})},j=e=>{let{pageConfig:t,fields:i,onAddField:n,onUpdateField:s,onRemoveField:o,onSave:r,onPreview:l,loading:d}=e;return(0,c.jsxs)("div",{className:"builder-content",children:[(0,c.jsxs)("h4",{children:["Page Configuration for: ",t.title]}),(0,c.jsx)("h5",{children:"Current Page Fields:"}),i.map(((e,t)=>(0,c.jsx)(C,{field:e,index:t,onUpdate:s,onRemove:o},e.id||t))),(0,c.jsxs)("button",{onClick:n,className:"btn btn-info mt-3",children:[a.createElement(p.OiG)," Add Field"]}),(0,c.jsxs)("button",{onClick:r,className:"btn btn-success mt-3 ms-2",disabled:d||!t||0===i.length,children:[a.createElement(p.dIn)," ",d?"Saving...":"Save Page Configuration"]}),(0,c.jsx)("button",{onClick:l,className:"btn btn-secondary mt-3 ms-2",disabled:!t||0===i.length,children:"Preview Page"})]})},w=[{value:"daily",label:"Daily"},{value:"weekly",label:"Weekly"},{value:"monthly",label:"Monthly"}];var A=i(4540);const S=e=>{let{id:t,label:i,options:n,selectedValues:s,onChange:o,disabled:r=!1,placeholder:l="-- Select Options --"}=e;const[d,u]=(0,a.useState)(!1),p=(0,a.useRef)(null);(0,a.useEffect)((()=>{const e=e=>{p.current&&!p.current.contains(e.target)&&u(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}}),[]);const h=s.length===n.length&&n.length>0,m=s.length>0&&s.length<n.length;return(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsxs)("label",{htmlFor:t,className:"form-label",children:[i,":"]}),(0,c.jsxs)("div",{className:"dropdown",ref:p,children:[(0,c.jsx)("button",{id:t,className:"btn btn-outline-secondary dropdown-toggle w-100 text-start "+(r?"disabled":""),type:"button",onClick:()=>!r&&u(!d),disabled:r,style:{backgroundColor:r?"#e9ecef":"white",borderColor:"#ced4da"},children:(0,c.jsx)("span",{className:0===s.length?"text-muted":"",children:(()=>{if(0===s.length)return l;if(1===s.length){const e=n.find((e=>e.id===s[0]));return(null===e||void 0===e?void 0:e.name)||l}return`${s.length} selected`})()})}),d&&!r&&(0,c.jsxs)("div",{className:"dropdown-menu show w-100",style:{maxHeight:"300px",overflowY:"auto"},children:[n.length>1&&(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("div",{className:"dropdown-item",children:(0,c.jsxs)("div",{className:"form-check",children:[(0,c.jsx)("input",{className:"form-check-input",type:"checkbox",id:`${t}-select-all`,checked:h,ref:e=>{e&&(e.indeterminate=m)},onChange:()=>{s.length===n.length?o([]):o(n.map((e=>e.id)))}}),(0,c.jsxs)("label",{className:"form-check-label fw-bold",htmlFor:`${t}-select-all`,children:["Select All (",n.length,")"]})]})}),(0,c.jsx)("hr",{className:"dropdown-divider"})]}),n.map((e=>(0,c.jsx)("div",{className:"dropdown-item",children:(0,c.jsxs)("div",{className:"form-check",children:[(0,c.jsx)("input",{className:"form-check-input",type:"checkbox",id:`${t}-${e.id}`,checked:s.includes(e.id),onChange:()=>{return t=e.id,void(s.includes(t)?o(s.filter((e=>e!==t))):o([...s,t]));var t}}),(0,c.jsx)("label",{className:"form-check-label",htmlFor:`${t}-${e.id}`,children:e.name})]})},e.id))),0===n.length&&(0,c.jsx)("div",{className:"dropdown-item text-muted",children:(0,c.jsx)("em",{children:"No options available"})})]})]}),s.length>0&&(0,c.jsxs)("small",{className:"text-muted mt-1 d-block",children:[s.length," of ",n.length," selected"]})]})},N=e=>{let{selectedRegions:t,selectedDivisions:i,selectedOffices:n,selectedFrequency:s,onRegionsChange:o,onDivisionsChange:r,onOfficesChange:l,onFrequencyChange:d}=e;const{regions:u,divisions:p,offices:h,loading:m,error:f,refetch:g}=(()=>{const[e,t]=(0,a.useState)([]),[i,n]=(0,a.useState)([]),[s,o]=(0,a.useState)([]),[r,l]=(0,a.useState)(!0),[d,c]=(0,a.useState)(null),u=async()=>{try{l(!0),c(null),console.log("\ud83c\udfe2 useOfficeDataSimple: Fetching with enhanced pagination...");const e=await A.A.fetchAllOfficeData();console.log("\u2705 useOfficeDataSimple: Fetched",e.length,"office records");const i=null===e||void 0===e?void 0:e.map((e=>e.Region)).filter(((e,t,i)=>i.indexOf(e)===t)).filter((e=>null!=e&&""!==e.trim())).sort(),a=(null===i||void 0===i?void 0:i.map((e=>({id:e.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""),name:e}))))||[],s=null===e||void 0===e?void 0:e.map((e=>({region:e.Region,division:e.Division}))).filter(((e,t,i)=>i.findIndex((t=>t.region===e.region&&t.division===e.division))===t)).filter((e=>null!=e.region&&null!=e.division&&""!==e.region.trim()&&""!==e.division.trim())).sort(((e,t)=>e.region.localeCompare(t.region)||e.division.localeCompare(t.division))),r=(null===s||void 0===s?void 0:s.map((e=>({id:e.division.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""),name:e.division,region:e.region}))))||[],d=(null===e||void 0===e?void 0:e.filter((e=>e["Office name"]&&e.Region&&e.Division)).map((e=>({id:e["Office name"],name:e["Office name"],region:e.Region||"",division:e.Division||"",facilityId:e["Office name"]}))))||[];t(a),n(r),o(d)}catch(e){console.error("\ud83d\udea8 SIMPLE: Error:",e),c("Failed to load office data. Please try again."),t([]),n([]),o([])}finally{l(!1)}};return(0,a.useEffect)((()=>{u()}),[]),{regions:e,divisions:i,offices:s,loading:r,error:d,refetch:u}})(),v=t.map((e=>{var t;return null===(t=u.find((t=>t.id===e)))||void 0===t?void 0:t.name})).filter(Boolean),x=t.length>0?p.filter((e=>v.includes(e.region))):p,b=i.map((e=>{var t;return null===(t=p.find((t=>t.id===e)))||void 0===t?void 0:t.name})).filter(Boolean),y=i.length>0?h.filter((e=>v.includes(e.region)&&b.includes(e.division))):t.length>0?h.filter((e=>v.includes(e.region))):h;return(0,a.useEffect)((()=>{if(t.length>0){const e=i.filter((e=>{const t=p.find((t=>t.id===e));return t&&v.includes(t.region)}));e.length!==i.length&&r(e)}}),[t,i,p,v,r]),(0,a.useEffect)((()=>{if(i.length>0){const e=n.filter((e=>{const t=h.find((t=>t.id===e));return t&&v.includes(t.region)&&b.includes(t.division)}));e.length!==n.length&&l(e)}}),[i,n,h,v,b,l]),(0,c.jsxs)("div",{className:"report-configuration mt-3 mb-3",children:[(0,c.jsx)("h5",{children:"Report Configuration"}),m&&(0,c.jsx)("div",{className:"alert alert-info",children:(0,c.jsxs)("div",{className:"d-flex align-items-center",children:[(0,c.jsx)("div",{className:"spinner-border spinner-border-sm me-2",role:"status",children:(0,c.jsx)("span",{className:"visually-hidden",children:"Loading..."})}),"Loading office data..."]})}),f&&(0,c.jsxs)("div",{className:"alert alert-danger",children:[(0,c.jsx)("strong",{children:"Error:"})," ",f,(0,c.jsx)("button",{className:"btn btn-sm btn-outline-danger ms-2",onClick:g,children:"Retry"})]}),!m&&!f&&(0,c.jsxs)("div",{className:"row",children:[(0,c.jsx)("div",{className:"col-md-3",children:(0,c.jsx)(S,{id:"region-select",label:"Select Regions",options:u,selectedValues:t,onChange:o,disabled:m,placeholder:"-- Select Regions --"})}),(0,c.jsx)("div",{className:"col-md-3",children:(0,c.jsx)(S,{id:"division-select",label:"Select Divisions",options:x,selectedValues:i,onChange:r,disabled:0===t.length||m,placeholder:"-- Select Divisions --"})}),(0,c.jsx)("div",{className:"col-md-3",children:(0,c.jsx)(S,{id:"office-select",label:"Select Offices",options:y,selectedValues:n,onChange:l,disabled:0===i.length||m,placeholder:"-- Select Offices --"})}),(0,c.jsx)("div",{className:"col-md-3",children:(0,c.jsxs)("div",{className:"form-group",children:[(0,c.jsxs)("label",{htmlFor:"frequency-select",className:"form-label",children:["Report Frequency: ",(0,c.jsx)("span",{className:"text-danger",children:"*"})]}),(0,c.jsxs)("select",{id:"frequency-select",className:"form-select "+(s?"":"is-invalid"),value:s,onChange:e=>d(e.target.value),disabled:m,required:!0,children:[(0,c.jsx)("option",{value:"",children:"-- Select Frequency --"}),w.map((e=>(0,c.jsx)("option",{value:e.value,children:e.label},e.value)))]}),!s&&(0,c.jsx)("div",{className:"invalid-feedback",children:"Report frequency is required."})]})})]})]})},D=()=>{var e,t;const i=(()=>{const[e,t]=(0,a.useState)([]),[i,n]=(0,a.useState)(""),[s,o]=(0,a.useState)(null),[r,l]=(0,a.useState)([]),[d,c]=(0,a.useState)([]),[u,p]=(0,a.useState)(!1),[h,m]=(0,a.useState)(!1),[f,g]=(0,a.useState)(null),[v,x]=(0,a.useState)(null),[b,y]=(0,a.useState)(!1),[C,j]=(0,a.useState)(""),[w,A]=(0,a.useState)(""),[S,N]=(0,a.useState)(!1),[D,T]=(0,a.useState)(null),[F,R]=(0,a.useState)(!1),[$,k]=(0,a.useState)(null),[I,E]=(0,a.useState)(!1),[P,O]=(0,a.useState)(""),[M,L]=(0,a.useState)(!1),[q,W]=(0,a.useState)(""),[U,B]=(0,a.useState)([]),[V,G]=(0,a.useState)([]),[H,z]=(0,a.useState)([]),[_,Y]=(0,a.useState)("");return{categories:e,selectedCard:i,pageConfig:s,fields:r,availableDynamicFields:d,isLoading:u,loading:h,error:f,success:v,isAddingNewCard:b,newCardId:C,newCardTitle:w,showConfirmModal:S,editingCard:D,showEditModal:F,cardToDelete:$,showDeleteConfirmModal:I,actionType:P,isPreviewOpen:M,previewContent:q,selectedRegions:U,selectedDivisions:V,selectedOffices:H,selectedFrequency:_,setCategories:t,setSelectedCard:n,setPageConfig:o,setFields:l,setAvailableDynamicFields:c,setIsLoading:p,setLoading:m,setError:g,setSuccess:x,setIsAddingNewCard:y,setNewCardId:j,setNewCardTitle:A,setShowConfirmModal:N,setEditingCard:T,setShowEditModal:R,setCardToDelete:k,setShowDeleteConfirmModal:E,setActionType:O,setIsPreviewOpen:L,setPreviewContent:W,setSelectedRegions:B,setSelectedDivisions:G,setSelectedOffices:z,setSelectedFrequency:Y}})(),n=v({categories:i.categories,setCategories:i.setCategories,selectedCard:i.selectedCard,setSelectedCard:i.setSelectedCard,newCardId:i.newCardId,setNewCardId:i.setNewCardId,newCardTitle:i.newCardTitle,setNewCardTitle:i.setNewCardTitle,actionType:i.actionType,setActionType:i.setActionType,setIsLoading:i.setIsLoading,setError:i.setError,setSuccess:i.setSuccess,setShowConfirmModal:i.setShowConfirmModal,setIsAddingNewCard:i.setIsAddingNewCard,setPageConfig:i.setPageConfig,setFields:i.setFields,setEditingCard:i.setEditingCard,setShowEditModal:i.setShowEditModal,setCardToDelete:i.setCardToDelete,setShowDeleteConfirmModal:i.setShowDeleteConfirmModal}),r=(e=>{const{categories:t,selectedCard:i,pageConfig:n,setPageConfig:r,fields:l,setFields:d,setAvailableDynamicFields:c,setLoading:u,setError:p,setSuccess:h,setPreviewContent:m,setIsPreviewOpen:f,selectedRegions:g,selectedDivisions:v,selectedOffices:b,selectedFrequency:y,setSelectedRegions:C,setSelectedDivisions:j,setSelectedOffices:w,setSelectedFrequency:A}=e;return{fetchDynamicFormFields:(0,a.useCallback)((async e=>{if(e){console.log(`Fetching dynamic form fields for formId: ${e}`);try{const t=(0,s.H9)(o.db,"formConfigs",e),i=await(0,s.x7)(t);if(i.exists()){const e=i.data();c(e.fields||[]),console.log("Fetched dynamic fields:",e.fields)}else console.log(`No dynamic form configuration found for formId: ${e}`),c([])}catch(t){console.error("Error fetching dynamic form fields:",t),p("Failed to fetch dynamic form fields."),c([])}}}),[c,p]),loadPageConfig:(0,a.useCallback)((async e=>{if(e){console.log(`loadPageConfig called for cardId: ${e}`),u(!0),p(null);try{const a=(0,s.H9)(o.db,"pages",e),n=await(0,s.x7)(a);let l=null;if(n.exists())l=n.data();else try{l=await x.Z.loadPageConfig(e)}catch(i){}if(l)r(l),d(l.fields||[]),C(l.selectedRegions||(l.selectedRegion?[l.selectedRegion]:[])),j(l.selectedDivisions||(l.selectedDivision?[l.selectedDivision]:[])),w(l.selectedOffices||(l.selectedOffice?[l.selectedOffice]:[])),A(l.selectedFrequency||"");else{const i=t.find((t=>t.id===e));r({id:e,title:(null===i||void 0===i?void 0:i.title)||"New Page",fields:[],lastUpdated:(new Date).toISOString()}),d([]),C([]),j([]),w([]),A("")}}catch(a){p("Failed to load page configuration."),console.error(a),r(null),d([])}finally{u(!1)}}else console.log("loadPageConfig called with no cardId")}),[t,u,p,r,d,C,j,w,A]),addField:()=>{const e={id:`field_${Date.now()}`,type:"text",label:"New Field",placeholder:"",options:[],required:!1,region:"",division:"",office:""};d([...l,e])},addFieldFromDynamic:e=>{console.log("Attempting to add dynamic field:",e);const t={id:e.id,type:e.type,label:e.label,placeholder:e.placeholder,options:e.options?e.options.map((e=>"string"===typeof e?{label:e,value:e}:{label:e.label,value:e.value})):void 0,required:e.required,defaultValue:e.defaultValue,min:e.min,max:e.max,sectionTitle:void 0,columns:void 0,buttonText:void 0,buttonType:void 0,onClickAction:void 0,value:void 0};if(l.some((e=>e.id===t.id)))return console.warn(`Duplicate field ID detected: "${t.id}". Field not added.`),p(`Field with ID "${t.id}" already exists in the page configuration.`),void setTimeout((()=>p(null)),3e3);console.log("Adding new field to state:",t),d([...l,t]),h(`Added field "${t.label}" to page configuration.`),setTimeout((()=>h(null)),3e3)},updateField:(e,t)=>{const i=[...l];i[e]=t,d(i)},removeField:e=>{d(l.filter(((t,i)=>i!==e)))},handleSave:async()=>{if(i&&n)if(y){u(!0),console.log("Attempting to save page configuration for cardId:",i),console.log("Fields being saved:",l),console.log("Report frequency:",y);try{var e;const a=l.map((e=>{const t={};for(const i in e)void 0!==e[i]?t[i]=e[i]:t[i]=null;return t})),d={...n,id:i,title:(null===(e=t.find((e=>e.id===i)))||void 0===e?void 0:e.title)||n.title,fields:a,lastUpdated:(new Date).toISOString(),selectedRegions:g,selectedDivisions:v,selectedOffices:b,selectedFrequency:y},c=[];c.push((0,s.BN)((0,s.H9)(o.db,"pages",i),d).catch((e=>{throw console.error("Firebase save failed:",e),new Error(`Firebase save failed: ${e.message}`)}))),c.push(x.Z.savePageConfig(d).catch((e=>{throw console.error("Supabase save failed:",e),new Error(`Supabase save failed: ${e.message}`)}))),await Promise.all(c),r(d),h("Page configuration saved successfully!"),setTimeout((()=>h(null)),3e3)}catch(a){console.error("Failed to save page configuration:",a),p(`Failed to save page configuration: ${a instanceof Error?a.message:"Unknown error"}`)}finally{u(!1)}}else p("Report frequency is required. Please select a frequency before saving.");else p("No report selected or page configuration loaded.")},handlePreview:()=>{if(!n||0===l.length)return void alert("No page configuration or fields to preview.");const e=`\n      <h1>${n.title}</h1>\n      <form>\n        ${l.map((e=>{var t,i;let a="";switch(e.type){case"text":case"number":case"date":case"textarea":a=`\n                <div class="form-group mb-3">\n                  <label class="form-label">${e.label}${e.required?" *":""}</label>\n                  <input type="${e.type}" class="form-control" placeholder="${e.placeholder||""}" ${e.required?"required":""} />\n                </div>\n              `;break;case"dropdown":a=`\n                <div class="form-group mb-3">\n                  <label class="form-label">${e.label}${e.required?" *":""}</label>\n                  <select class="form-control" ${e.required?"required":""}>\n                    <option value="">Select ${e.label}</option>\n                    ${(null===(t=e.options)||void 0===t?void 0:t.map((e=>`<option value="${e.value}">${e.label}</option>`)).join(""))||""}\n                  </select>\n                </div>\n              `;break;case"checkbox":a=`\n                <div class="form-check mb-3">\n                  <input type="checkbox" class="form-check-input" id="${e.id}" ${e.required?"required":""} />\n                  <label class="form-check-label" for="${e.id}">${e.label}${e.required?" *":""}</label>\n                </div>\n              `;break;case"radio":a=`\n                <div class="form-group mb-3">\n                  <label class="form-label">${e.label}${e.required?" *":""}</label>\n                  ${(null===(i=e.options)||void 0===i?void 0:i.map(((t,i)=>`\n                    <div class="form-check">\n                      <input class="form-check-input" type="radio" name="${e.id}" id="${e.id}-${i}" value="${t.value}" ${e.required?"required":""}>\n                      <label class="form-check-label" for="${e.id}-${i}">${t.label}</label>\n                    </div>\n                  `)).join(""))||""}\n                </div>\n              `;break;case"section":a=`\n                <div class="card mt-3 mb-3">\n                  <div class="card-header">${e.sectionTitle||"Section"}</div>\n                  <div class="card-body">\n                    <p>Fields for this section would appear here in the actual form.</p>\n                  </div>\n                </div>\n              `;break;case"button":a=`\n                <button type="button" class="btn btn-primary mt-3">${e.buttonText||"Button"}</button>\n              `;break;default:a=`<p>Unsupported field type: ${e.type}</p>`}return a})).join("")}\n      </form>\n    `;m(e),f(!0)}}})({categories:i.categories,selectedCard:i.selectedCard,pageConfig:i.pageConfig,setPageConfig:i.setPageConfig,fields:i.fields,setFields:i.setFields,setAvailableDynamicFields:i.setAvailableDynamicFields,setLoading:i.setLoading,setError:i.setError,setSuccess:i.setSuccess,setPreviewContent:i.setPreviewContent,setIsPreviewOpen:i.setIsPreviewOpen,selectedRegions:i.selectedRegions,selectedDivisions:i.selectedDivisions,selectedOffices:i.selectedOffices,selectedFrequency:i.selectedFrequency,setSelectedRegions:i.setSelectedRegions,setSelectedDivisions:i.setSelectedDivisions,setSelectedOffices:i.setSelectedOffices,setSelectedFrequency:i.setSelectedFrequency});(0,a.useEffect)((()=>{n.fetchCategories()}),[]),(0,a.useEffect)((()=>{i.selectedCard&&m(i.selectedCard,i.categories)&&!h(i.selectedCard,i.categories)&&"createWebPage"===i.actionType?(r.loadPageConfig(i.selectedCard),r.fetchDynamicFormFields(i.selectedCard)):!i.selectedCard||m(i.selectedCard,i.categories)&&!h(i.selectedCard,i.categories)||"createWebPage"!==i.actionType?i.selectedCard||(i.setPageConfig(null),i.setFields([]),i.setAvailableDynamicFields([]),i.setActionType("")):(i.setPageConfig(null),i.setFields([]),i.setAvailableDynamicFields([])),i.selectedCard&&"createWebPage"!==i.actionType&&i.setAvailableDynamicFields([])}),[i.selectedCard,i.categories,i.actionType]);return(0,c.jsx)(c.Fragment,{children:(0,c.jsxs)("div",{className:"page-builder",children:[i.error&&(0,c.jsx)("div",{className:"error-message",children:i.error}),i.success&&(0,c.jsx)("div",{className:"success-message",children:i.success}),(0,c.jsx)("h2",{children:"Report & Page Builder"}),(0,c.jsx)(b,{categories:i.categories,selectedCard:i.selectedCard,onCardChange:e=>{if(i.setSelectedCard(e),i.setActionType(""),e){const t=m(e,i.categories),a=h(e,i.categories);t&&!a||(i.setPageConfig(null),i.setFields([]))}else i.setPageConfig(null),i.setFields([])},actionType:i.actionType,onActionChange:e=>{i.setActionType(e)},isLoading:i.isLoading,onCreateAction:()=>{i.setNewCardId(""),i.setNewCardTitle(""),i.setIsAddingNewCard(!0)},onWebPageAction:()=>{i.selectedCard&&m(i.selectedCard,i.categories)&&!h(i.selectedCard,i.categories)?r.loadPageConfig(i.selectedCard):i.selectedCard&&(i.setError("Web page can only be created/edited for a final nested report (not a main report)."),i.setPageConfig(null),i.setFields([]))}}),i.isAddingNewCard&&(0,c.jsx)(u,{isOpen:i.isAddingNewCard,onClose:()=>{i.setIsAddingNewCard(!1),i.setActionType(""),i.setNewCardId(""),i.setNewCardTitle("")},title:"addNewCardGlobal"===i.actionType?"Create New Main Report":i.selectedCard&&"createNestedCard"===i.actionType?`Add Nested Report under "${null===(e=i.categories.find((e=>e.id===i.selectedCard)))||void 0===e?void 0:e.title}"`:"Create New Report",children:(0,c.jsxs)("div",{className:"new-card-form",children:[(0,c.jsx)("input",{type:"text",placeholder:"Report ID (e.g., 'new-report-id')",value:i.newCardId,onChange:e=>i.setNewCardId(e.target.value.toLowerCase().replace(/\s+/g,"-")),className:"form-control mb-2"}),(0,c.jsx)("input",{type:"text",placeholder:"Report Title",value:i.newCardTitle,onChange:e=>i.setNewCardTitle(e.target.value),className:"form-control mb-2"}),(0,c.jsxs)("div",{className:"form-buttons modal-buttons",children:[(0,c.jsx)("button",{onClick:n.handleConfirmCreate,disabled:i.isLoading||!i.newCardId||!i.newCardTitle,className:"btn btn-primary",children:i.isLoading?"Creating...":"Confirm & Create Report"}),(0,c.jsx)("button",{onClick:()=>{i.setIsAddingNewCard(!1),i.setActionType(""),i.setNewCardId(""),i.setNewCardTitle("")},className:"btn btn-secondary",children:"Cancel"})]})]})}),i.selectedCard&&(0,c.jsxs)(c.Fragment,{children:[!("createWebPage"===i.actionType&&m(i.selectedCard,i.categories)&&!h(i.selectedCard,i.categories)&&i.pageConfig)&&(0,c.jsx)(y,{selectedCard:i.selectedCard,categories:i.categories,onEditCard:n.handleEditCard,onDeleteCard:n.handleDeleteClick}),"createWebPage"===i.actionType&&m(i.selectedCard,i.categories)&&!h(i.selectedCard,i.categories)&&(0,c.jsx)(N,{selectedRegions:i.selectedRegions,selectedDivisions:i.selectedDivisions,selectedOffices:i.selectedOffices,selectedFrequency:i.selectedFrequency,onRegionsChange:e=>{i.setSelectedRegions(e),i.setSelectedDivisions([]),i.setSelectedOffices([])},onDivisionsChange:e=>{i.setSelectedDivisions(e),i.setSelectedOffices([])},onOfficesChange:e=>{i.setSelectedOffices(e)},onFrequencyChange:e=>{i.setSelectedFrequency(e)}}),"createWebPage"===i.actionType&&m(i.selectedCard,i.categories)&&!h(i.selectedCard,i.categories)&&i.pageConfig&&(0,c.jsx)(j,{pageConfig:i.pageConfig,fields:i.fields,onAddField:r.addField,onUpdateField:r.updateField,onRemoveField:r.removeField,onSave:r.handleSave,onPreview:r.handlePreview,loading:i.loading}),"createWebPage"===i.actionType&&(!m(i.selectedCard,i.categories)||h(i.selectedCard,i.categories))&&(0,c.jsx)("div",{className:"warning-message mt-3 p-2 bg-warning text-dark rounded",children:"Page configuration is only available for final nested reports (which are not main reports). Please select an appropriate nested report to configure its page, or create one."}),"createWebPage"!==i.actionType&&!m(i.selectedCard,i.categories)&&(0,c.jsx)("div",{className:"info-message mt-3 p-2 bg-info text-dark rounded",children:"This is a parent report. You can create nested reports under it or select an existing nested report to manage or configure its page."})]}),!i.selectedCard&&""===i.actionType&&(0,c.jsxs)("div",{className:"info-message mt-3 p-3 bg-light border rounded",children:[(0,c.jsx)("p",{children:"Select a report from the dropdown to manage it or configure its web page (if applicable)."}),(0,c.jsx)("p",{children:'If no reports exist, or to create a new top-level report, choose "Create New Main Report" from the action dropdown after clearing any selection.'})]}),i.showEditModal&&i.editingCard&&(0,c.jsxs)(u,{isOpen:i.showEditModal,onClose:()=>{i.setShowEditModal(!1),i.setNewCardTitle(""),i.setEditingCard(null)},title:`Edit Report: ${i.editingCard.title}`,children:[(0,c.jsx)("input",{type:"text",value:i.newCardTitle,onChange:e=>i.setNewCardTitle(e.target.value),placeholder:"New Report Title",className:"form-control mb-2"}),(0,c.jsxs)("div",{className:"form-buttons modal-buttons",children:[(0,c.jsx)("button",{onClick:n.handleUpdateCard,className:"btn btn-primary",disabled:i.isLoading||!i.newCardTitle.trim(),children:i.isLoading?"Updating...":"Update Title"}),(0,c.jsx)("button",{onClick:()=>{i.setShowEditModal(!1),i.setNewCardTitle(""),i.setEditingCard(null)},className:"btn btn-secondary",children:"Cancel"})]})]}),i.showDeleteConfirmModal&&i.cardToDelete&&(0,c.jsxs)(u,{isOpen:i.showDeleteConfirmModal,onClose:()=>i.setShowDeleteConfirmModal(!1),title:"Confirm Deletion",children:[(0,c.jsxs)("p",{children:['Are you sure you want to delete the report "',null===(t=i.categories.find((e=>e.id===i.cardToDelete)))||void 0===t?void 0:t.title,'" and ALL its nested reports and associated page configurations? This action cannot be undone.']}),(0,c.jsxs)("div",{className:"form-buttons modal-buttons",children:[(0,c.jsx)("button",{onClick:n.handleConfirmDelete,className:"btn btn-danger",disabled:i.isLoading,children:i.isLoading?"Deleting...":"Confirm Delete"}),(0,c.jsx)("button",{onClick:()=>i.setShowDeleteConfirmModal(!1),className:"btn btn-secondary",children:"Cancel"})]})]}),(0,c.jsx)(u,{isOpen:i.isPreviewOpen,onClose:()=>i.setIsPreviewOpen(!1),title:"Page Preview",children:(0,c.jsx)("div",{dangerouslySetInnerHTML:{__html:i.previewContent}})})]})})};var T=i(3969),F=i(172),R=i(5895),$=i(1449),k=i(1244),I=i(6128),E=i(3759),P=i(4799),O=i(5315),M=i(8387),L=i(1807),q=i(7731),W=i(8128),U=i(1612),B=i(8301),V=i(4761),G=i(8348),H=i(6685),z=i(9857),_=i(6061);function Y(e){return(0,_.Ay)("MuiListItem",e)}(0,z.A)("MuiListItem",["root","container","dense","alignItemsFlexStart","divider","gutters","padding","secondaryAction"]);const Z=(0,z.A)("MuiListItemButton",["root","focusVisible","dense","alignItemsFlexStart","disabled","divider","gutters","selected"]);function K(e){return(0,_.Ay)("MuiListItemSecondaryAction",e)}(0,z.A)("MuiListItemSecondaryAction",["root","disableGutters"]);const J=(0,W.Ay)("div",{name:"MuiListItemSecondaryAction",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:i}=e;return[t.root,i.disableGutters&&t.disableGutters]}})({position:"absolute",right:16,top:"50%",transform:"translateY(-50%)",variants:[{props:e=>{let{ownerState:t}=e;return t.disableGutters},style:{right:0}}]}),X=a.forwardRef((function(e,t){const i=(0,B.b)({props:e,name:"MuiListItemSecondaryAction"}),{className:n,...s}=i,o=a.useContext(H.A),r={...i,disableGutters:o.disableGutters},l=(e=>{const{disableGutters:t,classes:i}=e,a={root:["root",t&&"disableGutters"]};return(0,L.A)(a,K,i)})(r);return(0,c.jsx)(J,{className:(0,M.A)(l.root,n),ownerState:r,ref:t,...s})}));X.muiName="ListItemSecondaryAction";const Q=X,ee=(0,W.Ay)("div",{name:"MuiListItem",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:i}=e;return[t.root,i.dense&&t.dense,"flex-start"===i.alignItems&&t.alignItemsFlexStart,i.divider&&t.divider,!i.disableGutters&&t.gutters,!i.disablePadding&&t.padding,i.hasSecondaryAction&&t.secondaryAction]}})((0,U.A)((e=>{let{theme:t}=e;return{display:"flex",justifyContent:"flex-start",alignItems:"center",position:"relative",textDecoration:"none",width:"100%",boxSizing:"border-box",textAlign:"left",variants:[{props:e=>{let{ownerState:t}=e;return!t.disablePadding},style:{paddingTop:8,paddingBottom:8}},{props:e=>{let{ownerState:t}=e;return!t.disablePadding&&t.dense},style:{paddingTop:4,paddingBottom:4}},{props:e=>{let{ownerState:t}=e;return!t.disablePadding&&!t.disableGutters},style:{paddingLeft:16,paddingRight:16}},{props:e=>{let{ownerState:t}=e;return!t.disablePadding&&!!t.secondaryAction},style:{paddingRight:48}},{props:e=>{let{ownerState:t}=e;return!!t.secondaryAction},style:{[`& > .${Z.root}`]:{paddingRight:48}}},{props:{alignItems:"flex-start"},style:{alignItems:"flex-start"}},{props:e=>{let{ownerState:t}=e;return t.divider},style:{borderBottom:`1px solid ${(t.vars||t).palette.divider}`,backgroundClip:"padding-box"}},{props:e=>{let{ownerState:t}=e;return t.button},style:{transition:t.transitions.create("background-color",{duration:t.transitions.duration.shortest}),"&:hover":{textDecoration:"none",backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}}}},{props:e=>{let{ownerState:t}=e;return t.hasSecondaryAction},style:{paddingRight:48}}]}}))),te=(0,W.Ay)("li",{name:"MuiListItem",slot:"Container"})({position:"relative"}),ie=a.forwardRef((function(e,t){const i=(0,B.b)({props:e,name:"MuiListItem"}),{alignItems:n="center",children:s,className:o,component:r,components:l={},componentsProps:d={},ContainerComponent:u="li",ContainerProps:{className:p,...h}={},dense:m=!1,disableGutters:f=!1,disablePadding:g=!1,divider:v=!1,secondaryAction:x,slotProps:b={},slots:y={},...C}=i,j=a.useContext(H.A),w=a.useMemo((()=>({dense:m||j.dense||!1,alignItems:n,disableGutters:f})),[n,j.dense,m,f]),A=a.useRef(null),S=a.Children.toArray(s),N=S.length&&(0,V.A)(S[S.length-1],["ListItemSecondaryAction"]),D={...i,alignItems:n,dense:w.dense,disableGutters:f,disablePadding:g,divider:v,hasSecondaryAction:N},T=(e=>{const{alignItems:t,classes:i,dense:a,disableGutters:n,disablePadding:s,divider:o,hasSecondaryAction:r}=e,l={root:["root",a&&"dense",!n&&"gutters",!s&&"padding",o&&"divider","flex-start"===t&&"alignItemsFlexStart",r&&"secondaryAction"],container:["container"]};return(0,L.A)(l,Y,i)})(D),F=(0,G.A)(A,t),R=y.root||l.Root||ee,$=b.root||d.root||{},k={className:(0,M.A)(T.root,$.className,o),...C};let I=r||"li";return N?(I=k.component||r?I:"div","li"===u&&("li"===I?I="div":"li"===k.component&&(k.component="div")),(0,c.jsx)(H.A.Provider,{value:w,children:(0,c.jsxs)(te,{as:u,className:(0,M.A)(T.container,p),ref:F,ownerState:D,...h,children:[(0,c.jsx)(R,{...$,...!(0,q.A)(R)&&{as:I,ownerState:{...D,...$.ownerState}},...k,children:S}),S.pop()]})})):(0,c.jsx)(H.A.Provider,{value:w,children:(0,c.jsxs)(R,{...$,as:I,ref:F,...!(0,q.A)(R)&&{ownerState:{...D,...$.ownerState}},...k,children:[S,x&&(0,c.jsx)(Q,{children:x})]})})}));var ae=i(3491),ne=i(1977),se=i(9905);const oe=(0,W.Ay)("div",{name:"MuiListItemText",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:i}=e;return[{[`& .${ne.A.primary}`]:t.primary},{[`& .${ne.A.secondary}`]:t.secondary},t.root,i.inset&&t.inset,i.primary&&i.secondary&&t.multiline,i.dense&&t.dense]}})({flex:"1 1 auto",minWidth:0,marginTop:4,marginBottom:4,[`.${ae.A.root}:where(& .${ne.A.primary})`]:{display:"block"},[`.${ae.A.root}:where(& .${ne.A.secondary})`]:{display:"block"},variants:[{props:e=>{let{ownerState:t}=e;return t.primary&&t.secondary},style:{marginTop:6,marginBottom:6}},{props:e=>{let{ownerState:t}=e;return t.inset},style:{paddingLeft:56}}]}),re=a.forwardRef((function(e,t){const i=(0,B.b)({props:e,name:"MuiListItemText"}),{children:n,className:s,disableTypography:o=!1,inset:r=!1,primary:l,primaryTypographyProps:d,secondary:u,secondaryTypographyProps:p,slots:h={},slotProps:m={},...f}=i,{dense:g}=a.useContext(H.A);let v=null!=l?l:n,x=u;const b={...i,disableTypography:o,inset:r,primary:!!v,secondary:!!x,dense:g},y=(e=>{const{classes:t,inset:i,primary:a,secondary:n,dense:s}=e,o={root:["root",i&&"inset",s&&"dense",a&&n&&"multiline"],primary:["primary"],secondary:["secondary"]};return(0,L.A)(o,ne.b,t)})(b),C={slots:h,slotProps:{primary:d,secondary:p,...m}},[j,w]=(0,se.A)("root",{className:(0,M.A)(y.root,s),elementType:oe,externalForwardedProps:{...C,...f},ownerState:b,ref:t}),[A,S]=(0,se.A)("primary",{className:y.primary,elementType:R.A,externalForwardedProps:C,ownerState:b}),[N,D]=(0,se.A)("secondary",{className:y.secondary,elementType:R.A,externalForwardedProps:C,ownerState:b});return null==v||v.type===R.A||o||(v=(0,c.jsx)(A,{variant:g?"body2":"body1",component:S?.variant?void 0:"span",...S,children:v})),null==x||x.type===R.A||o||(x=(0,c.jsx)(N,{variant:"body2",color:"textSecondary",...D,children:x})),(0,c.jsxs)(j,{...w,children:[v,x]})}));var le=i(9826),de=i(4171);const ce=(0,W.Ay)("div",{name:"MuiDivider",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:i}=e;return[t.root,i.absolute&&t.absolute,t[i.variant],i.light&&t.light,"vertical"===i.orientation&&t.vertical,i.flexItem&&t.flexItem,i.children&&t.withChildren,i.children&&"vertical"===i.orientation&&t.withChildrenVertical,"right"===i.textAlign&&"vertical"!==i.orientation&&t.textAlignRight,"left"===i.textAlign&&"vertical"!==i.orientation&&t.textAlignLeft]}})((0,U.A)((e=>{let{theme:t}=e;return{margin:0,flexShrink:0,borderWidth:0,borderStyle:"solid",borderColor:(t.vars||t).palette.divider,borderBottomWidth:"thin",variants:[{props:{absolute:!0},style:{position:"absolute",bottom:0,left:0,width:"100%"}},{props:{light:!0},style:{borderColor:t.vars?`rgba(${t.vars.palette.dividerChannel} / 0.08)`:(0,le.X4)(t.palette.divider,.08)}},{props:{variant:"inset"},style:{marginLeft:72}},{props:{variant:"middle",orientation:"horizontal"},style:{marginLeft:t.spacing(2),marginRight:t.spacing(2)}},{props:{variant:"middle",orientation:"vertical"},style:{marginTop:t.spacing(1),marginBottom:t.spacing(1)}},{props:{orientation:"vertical"},style:{height:"100%",borderBottomWidth:0,borderRightWidth:"thin"}},{props:{flexItem:!0},style:{alignSelf:"stretch",height:"auto"}},{props:e=>{let{ownerState:t}=e;return!!t.children},style:{display:"flex",textAlign:"center",border:0,borderTopStyle:"solid",borderLeftStyle:"solid","&::before, &::after":{content:'""',alignSelf:"center"}}},{props:e=>{let{ownerState:t}=e;return t.children&&"vertical"!==t.orientation},style:{"&::before, &::after":{width:"100%",borderTop:`thin solid ${(t.vars||t).palette.divider}`,borderTopStyle:"inherit"}}},{props:e=>{let{ownerState:t}=e;return"vertical"===t.orientation&&t.children},style:{flexDirection:"column","&::before, &::after":{height:"100%",borderLeft:`thin solid ${(t.vars||t).palette.divider}`,borderLeftStyle:"inherit"}}},{props:e=>{let{ownerState:t}=e;return"right"===t.textAlign&&"vertical"!==t.orientation},style:{"&::before":{width:"90%"},"&::after":{width:"10%"}}},{props:e=>{let{ownerState:t}=e;return"left"===t.textAlign&&"vertical"!==t.orientation},style:{"&::before":{width:"10%"},"&::after":{width:"90%"}}}]}}))),ue=(0,W.Ay)("span",{name:"MuiDivider",slot:"Wrapper",overridesResolver:(e,t)=>{const{ownerState:i}=e;return[t.wrapper,"vertical"===i.orientation&&t.wrapperVertical]}})((0,U.A)((e=>{let{theme:t}=e;return{display:"inline-block",paddingLeft:`calc(${t.spacing(1)} * 1.2)`,paddingRight:`calc(${t.spacing(1)} * 1.2)`,whiteSpace:"nowrap",variants:[{props:{orientation:"vertical"},style:{paddingTop:`calc(${t.spacing(1)} * 1.2)`,paddingBottom:`calc(${t.spacing(1)} * 1.2)`}}]}}))),pe=a.forwardRef((function(e,t){const i=(0,B.b)({props:e,name:"MuiDivider"}),{absolute:a=!1,children:n,className:s,orientation:o="horizontal",component:r=(n||"vertical"===o?"div":"hr"),flexItem:l=!1,light:d=!1,role:u=("hr"!==r?"separator":void 0),textAlign:p="center",variant:h="fullWidth",...m}=i,f={...i,absolute:a,component:r,flexItem:l,light:d,orientation:o,role:u,textAlign:p,variant:h},g=(e=>{const{absolute:t,children:i,classes:a,flexItem:n,light:s,orientation:o,textAlign:r,variant:l}=e,d={root:["root",t&&"absolute",l,s&&"light","vertical"===o&&"vertical",n&&"flexItem",i&&"withChildren",i&&"vertical"===o&&"withChildrenVertical","right"===r&&"vertical"!==o&&"textAlignRight","left"===r&&"vertical"!==o&&"textAlignLeft"],wrapper:["wrapper","vertical"===o&&"wrapperVertical"]};return(0,L.A)(d,de.K,a)})(f);return(0,c.jsx)(ce,{as:r,className:(0,M.A)(g.root,s),role:u,ref:t,ownerState:f,"aria-orientation":"separator"!==u||"hr"===r&&"vertical"!==o?void 0:o,...m,children:n?(0,c.jsx)(ue,{className:g.wrapper,ownerState:f,children:n}):null})}));pe&&(pe.muiSkipListHighlight=!0);const he=pe,me=()=>{const[e,t]=(0,a.useState)([]),[i,n]=(0,a.useState)([]),[s,o]=(0,a.useState)([]),[r,l]=(0,a.useState)(!0),[d,c]=(0,a.useState)(null),[u,p]=(0,a.useState)(0),[h,m]=(0,a.useState)(""),f=async()=>{try{l(!0),c(null),console.log("\ud83c\udfe2 useOfficeDataEnhanced: Starting comprehensive office data fetch...");const e=await A.A.fetchAllOfficeData();if(console.log("\u2705 useOfficeDataEnhanced: Fetched office records:",e.length,"records"),p(e.length),0===e.length)return console.log("\u26a0\ufe0f useOfficeDataEnhanced: No office records found"),t([]),n([]),o([]),void m("no-data");const i=new Set;e.forEach((e=>{e.Region&&e.Region.trim()&&i.add(e.Region.trim())}));const a=Array.from(i).sort().map((e=>({id:e.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""),name:e})));console.log("\ud83d\udcca useOfficeDataEnhanced: Processed regions:",a.length);const s=new Map;e.forEach((e=>{e.Division&&e.Division.trim()&&e.Region&&e.Region.trim()&&s.set(e.Division.trim(),e.Region.trim())}));const r=Array.from(s.entries()).sort(((e,t)=>{let[i]=e,[a]=t;return i.localeCompare(a)})).map((e=>{let[t,i]=e;return{id:t.toLowerCase().replace(/\s+/g,"-").replace(/[^a-z0-9-]/g,""),name:t,region:i}}));console.log("\ud83d\udcca useOfficeDataEnhanced: Processed divisions:",r.length);const d=e.filter((e=>e["Office name"]&&e["Office name"].trim())).map((e=>({id:e["Office name"],name:e["Office name"],region:e.Region||"",division:e.Division||"",facilityId:e["Office name"]})));console.log("\ud83d\udcca useOfficeDataEnhanced: Processed offices:",d.length),function(e,t,i,a){if(console.log("\ud83d\udcca useOfficeDataEnhanced: === COMPREHENSIVE STATISTICS ==="),console.log(`\ud83d\udcca useOfficeDataEnhanced: Raw records: ${e.length}`),console.log(`\ud83d\udcca useOfficeDataEnhanced: Processed regions: ${t.length}`),console.log(`\ud83d\udcca useOfficeDataEnhanced: Processed divisions: ${i.length}`),console.log(`\ud83d\udcca useOfficeDataEnhanced: Processed offices: ${a.length}`),a.length>0){const e=a.map((e=>e.name)).sort();console.log(`\ud83d\udcca useOfficeDataEnhanced: Office range - First: "${e[0]}"`),console.log(`\ud83d\udcca useOfficeDataEnhanced: Office range - Last: "${e[e.length-1]}"`);const n={};a.forEach((e=>{const t=e.name.charAt(0).toUpperCase();n[t]=(n[t]||0)+1})),console.log("\ud83d\udcca useOfficeDataEnhanced: Letter distribution:"),Object.keys(n).sort().forEach((e=>{console.log(`\ud83d\udcca useOfficeDataEnhanced: ${e}: ${n[e]} offices`)}));const s=a.find((e=>e.name.toLowerCase().includes("tirupur division"))),o=a.find((e=>e.name.toLowerCase().includes("coimbatore division")));if(console.log(`\ud83d\udcca useOfficeDataEnhanced: Contains "Tirupur division": ${!!s}`),console.log(`\ud83d\udcca useOfficeDataEnhanced: Contains "Coimbatore division": ${!!o}`),s&&console.log(`\ud83d\udcca useOfficeDataEnhanced: Found Tirupur division: "${s.name}"`),o&&console.log(`\ud83d\udcca useOfficeDataEnhanced: Found Coimbatore division: "${o.name}"`),t.length>0&&(console.log("\ud83d\udcca useOfficeDataEnhanced: Regions found:"),t.forEach((e=>{const t=a.filter((t=>t.region===e.name));console.log(`\ud83d\udcca useOfficeDataEnhanced: ${e.name}: ${t.length} offices`)}))),i.length>0){console.log("\ud83d\udcca useOfficeDataEnhanced: Top 10 divisions by office count:");i.map((e=>({name:e.name,count:a.filter((t=>t.division===e.name)).length}))).sort(((e,t)=>t.count-e.count)).slice(0,10).forEach((e=>{console.log(`\ud83d\udcca useOfficeDataEnhanced: ${e.name}: ${e.count} offices`)}))}}console.log("\ud83d\udcca useOfficeDataEnhanced: === END STATISTICS ===")}(e,a,r,d),t(a),n(r),o(d),m("enhanced-pagination"),console.log("\u2705 useOfficeDataEnhanced: Data processing complete")}catch(e){console.error("\u274c useOfficeDataEnhanced: Error:",e),c("Failed to load office data. Please try again."),t([]),n([]),o([]),p(0),m("error")}finally{l(!1)}};return(0,a.useEffect)((()=>{f()}),[]),{regions:e,divisions:i,offices:s,loading:r,error:d,refetch:f,totalRecords:u,approach:h}};const fe=()=>{const{regions:e,divisions:t,offices:i,loading:n,error:s,totalRecords:o,approach:r,refetch:l}=me();if(n)return(0,c.jsxs)(T.A,{display:"flex",flexDirection:"column",alignItems:"center",p:4,children:[(0,c.jsx)(F.A,{size:60}),(0,c.jsx)(R.A,{variant:"h6",sx:{mt:2},children:"Loading office data with comprehensive pagination..."}),(0,c.jsx)(R.A,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"This may take a moment as we fetch ALL records from the database"})]});if(s)return(0,c.jsxs)(T.A,{p:4,children:[(0,c.jsx)($.A,{severity:"error",sx:{mb:2},children:s}),(0,c.jsx)(R.A,{variant:"body2",children:"Failed to load office data. Please check the console for detailed error information."})]});const d={};i.forEach((e=>{const t=e.name.charAt(0).toUpperCase();d[t]=(d[t]||0)+1}));const u=i.map((e=>e.name)).sort(),p=i.find((e=>e.name.toLowerCase().includes("tirupur division"))),h=i.find((e=>e.name.toLowerCase().includes("coimbatore division"))),m=e.map((e=>({name:e.name,count:i.filter((t=>t.region===e.name)).length}))).sort(((e,t)=>t.count-e.count)).slice(0,5),f=t.map((e=>({name:e.name,count:i.filter((t=>t.division===e.name)).length}))).sort(((e,t)=>t.count-e.count)).slice(0,10);return(0,c.jsxs)(T.A,{p:4,children:[(0,c.jsx)(R.A,{variant:"h4",gutterBottom:!0,children:"Office Loading Test - Enhanced Pagination"}),(0,c.jsx)(R.A,{variant:"body1",color:"text.secondary",paragraph:!0,children:"This test verifies that the enhanced office loading system can fetch ALL records from the Supabase database, overcoming the default 1000-record pagination limit."}),(0,c.jsxs)(T.A,{display:"flex",gap:3,sx:{mb:4,flexWrap:"wrap"},children:[(0,c.jsx)(T.A,{flex:"1",minWidth:"250px",children:(0,c.jsx)(k.A,{children:(0,c.jsxs)(I.A,{children:[(0,c.jsx)(R.A,{variant:"h6",color:"primary",children:"Total Records"}),(0,c.jsx)(R.A,{variant:"h4",children:o.toLocaleString()}),(0,c.jsx)(E.A,{label:r,size:"small",color:o>1e3?"success":"warning",sx:{mt:1}})]})})}),(0,c.jsx)(T.A,{flex:"1",minWidth:"250px",children:(0,c.jsx)(k.A,{children:(0,c.jsxs)(I.A,{children:[(0,c.jsx)(R.A,{variant:"h6",color:"primary",children:"Regions"}),(0,c.jsx)(R.A,{variant:"h4",children:e.length})]})})}),(0,c.jsx)(T.A,{flex:"1",minWidth:"250px",children:(0,c.jsx)(k.A,{children:(0,c.jsxs)(I.A,{children:[(0,c.jsx)(R.A,{variant:"h6",color:"primary",children:"Divisions"}),(0,c.jsx)(R.A,{variant:"h4",children:t.length})]})})}),(0,c.jsx)(T.A,{flex:"1",minWidth:"250px",children:(0,c.jsx)(k.A,{children:(0,c.jsxs)(I.A,{children:[(0,c.jsx)(R.A,{variant:"h6",color:"primary",children:"Offices"}),(0,c.jsx)(R.A,{variant:"h4",children:i.length})]})})})]}),(0,c.jsxs)(T.A,{display:"flex",gap:3,sx:{flexWrap:"wrap"},children:[(0,c.jsx)(T.A,{flex:"1",minWidth:"400px",children:(0,c.jsxs)(P.A,{sx:{p:3},children:[(0,c.jsx)(R.A,{variant:"h6",gutterBottom:!0,children:"Verification Results"}),(0,c.jsxs)(T.A,{sx:{mb:2},children:[(0,c.jsx)(R.A,{variant:"subtitle2",children:"Records exceed 1000 limit:"}),(0,c.jsx)(E.A,{label:o>1e3?"\u2705 YES":"\u274c NO",color:o>1e3?"success":"error",size:"small"})]}),(0,c.jsxs)(T.A,{sx:{mb:2},children:[(0,c.jsx)(R.A,{variant:"subtitle2",children:"Alphabetical Range:"}),(0,c.jsxs)(R.A,{variant:"body2",children:['First: "',u[0]||"N/A",'"']}),(0,c.jsxs)(R.A,{variant:"body2",children:['Last: "',u[u.length-1]||"N/A",'"']})]}),(0,c.jsxs)(T.A,{sx:{mb:2},children:[(0,c.jsx)(R.A,{variant:"subtitle2",children:"Tirupur Division Found:"}),(0,c.jsx)(E.A,{label:p?"\u2705 YES":"\u274c NO",color:p?"success":"error",size:"small"}),p&&(0,c.jsxs)(R.A,{variant:"body2",sx:{mt:1},children:['"',p.name,'"']})]}),(0,c.jsxs)(T.A,{sx:{mb:2},children:[(0,c.jsx)(R.A,{variant:"subtitle2",children:"Coimbatore Division Found:"}),(0,c.jsx)(E.A,{label:h?"\u2705 YES":"\u274c NO",color:h?"success":"error",size:"small"}),h&&(0,c.jsxs)(R.A,{variant:"body2",sx:{mt:1},children:['"',h.name,'"']})]})]})}),(0,c.jsx)(T.A,{flex:"1",minWidth:"400px",children:(0,c.jsxs)(P.A,{sx:{p:3},children:[(0,c.jsx)(R.A,{variant:"h6",gutterBottom:!0,children:"Letter Distribution"}),(0,c.jsx)(T.A,{sx:{maxHeight:300,overflow:"auto"},children:Object.keys(d).sort().map((e=>(0,c.jsxs)(T.A,{display:"flex",justifyContent:"space-between",sx:{mb:1},children:[(0,c.jsxs)(R.A,{variant:"body2",children:[e,":"]}),(0,c.jsxs)(R.A,{variant:"body2",children:[d[e]," offices"]})]},e)))})]})})]}),(0,c.jsxs)(T.A,{display:"flex",gap:3,sx:{mt:3,flexWrap:"wrap"},children:[(0,c.jsx)(T.A,{flex:"1",minWidth:"400px",children:(0,c.jsxs)(P.A,{sx:{p:3},children:[(0,c.jsx)(R.A,{variant:"h6",gutterBottom:!0,children:"Top 5 Regions by Office Count"}),(0,c.jsx)(O.A,{dense:!0,children:m.map(((e,t)=>(0,c.jsxs)(a.Fragment,{children:[(0,c.jsx)(ie,{children:(0,c.jsx)(re,{primary:e.name,secondary:`${e.count} offices`})}),t<m.length-1&&(0,c.jsx)(he,{})]},e.name)))})]})}),(0,c.jsx)(T.A,{flex:"1",minWidth:"400px",children:(0,c.jsxs)(P.A,{sx:{p:3},children:[(0,c.jsx)(R.A,{variant:"h6",gutterBottom:!0,children:"Top 10 Divisions by Office Count"}),(0,c.jsx)(T.A,{sx:{maxHeight:300,overflow:"auto"},children:(0,c.jsx)(O.A,{dense:!0,children:f.map(((e,t)=>(0,c.jsxs)(a.Fragment,{children:[(0,c.jsx)(ie,{children:(0,c.jsx)(re,{primary:e.name,secondary:`${e.count} offices`})}),t<f.length-1&&(0,c.jsx)(he,{})]},e.name)))})})]})})]}),o>1e3&&(0,c.jsxs)($.A,{severity:"success",sx:{mt:3},children:[(0,c.jsx)(R.A,{variant:"h6",children:"\ud83c\udf89 Success! Enhanced Office Loading is Working"}),(0,c.jsxs)(R.A,{variant:"body2",children:["The system successfully loaded ",o.toLocaleString()," office records, which exceeds the default 1000-record Supabase limit. This confirms that the comprehensive pagination solution is working correctly."]})]})]})},ge=()=>{const[e,t]=(0,a.useState)(!1),[i,n]=(0,a.useState)(""),[r,l]=(0,a.useState)("");return(0,c.jsxs)("div",{style:{padding:"20px",margin:"20px 0",border:"2px solid #007bff",borderRadius:"8px",backgroundColor:"#f8f9fa"},children:[(0,c.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"12px",marginBottom:"15px"},children:[a.createElement(p.dv1,{size:24,color:"#007bff"}),(0,c.jsx)("h3",{style:{margin:0,color:"#007bff"},children:"Add MMU Category"})]}),(0,c.jsx)("p",{style:{marginBottom:"15px",color:"#495057"},children:"Click the button below to add the MMU (Mail Motor Unit) category to both web and mobile data entry sections. This will create a new top-level category for vehicle management and logistics forms."}),r&&(0,c.jsx)("div",{style:{padding:"10px",marginBottom:"15px",backgroundColor:"#f8d7da",color:"#721c24",border:"1px solid #f5c6cb",borderRadius:"4px"},children:r}),i&&(0,c.jsx)("div",{style:{padding:"10px",marginBottom:"15px",backgroundColor:"#d4edda",color:"#155724",border:"1px solid #c3e6cb",borderRadius:"4px"},children:i}),(0,c.jsxs)("button",{onClick:async()=>{t(!0),l(""),n("");try{const e={id:"mmu",title:"MMU",parentId:"",isPage:!1,pageId:null,lastUpdated:new Date,description:"Mail Motor Unit - Vehicle management and logistics",order:9,icon:"truck"};await(0,s.BN)((0,s.H9)(o.db,"pages","mmu"),e),await(0,s.BN)((0,s.H9)(o.db,"categories","mmu"),{id:"mmu",name:"MMU",icon:"fatruck",parentId:"",isPage:!1,pageId:null,lastUpdated:new Date,description:"Mail Motor Unit - Vehicle management and logistics",order:9}),n("\u2705 MMU category successfully added! You can now see it in both web and mobile data entry sections."),setTimeout((()=>{window.location.reload()}),2e3)}catch(e){console.error("Error adding MMU category:",e),l("\u274c Failed to add MMU category. Please try again.")}finally{t(!1)}},disabled:e,style:{padding:"12px 24px",backgroundColor:e?"#6c757d":"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:e?"not-allowed":"pointer",fontSize:"16px",fontWeight:"bold",display:"flex",alignItems:"center",gap:"8px"},children:[a.createElement(p.dv1,{size:16}),e?"Adding MMU Category...":"Add MMU Category to Database"]}),(0,c.jsxs)("div",{style:{marginTop:"15px",fontSize:"14px",color:"#6c757d"},children:[(0,c.jsx)("strong",{children:"What this will do:"}),(0,c.jsxs)("ul",{style:{marginTop:"8px",paddingLeft:"20px"},children:[(0,c.jsx)("li",{children:"Add MMU category to Firebase database"}),(0,c.jsx)("li",{children:"Make MMU appear in web admin panel"}),(0,c.jsx)("li",{children:"Make MMU appear in mobile app data entry"}),(0,c.jsx)("li",{children:"Enable creation of MMU-related forms"})]})]})]})},ve=()=>{const{currentUser:e}=(0,r.A)(),[t,i]=((0,n.Zp)(),(0,a.useState)(null)),[u,p]=(0,a.useState)(!1);return(0,a.useEffect)((()=>{(async()=>{if(e){const t=(0,s.H9)(o.db,"employees",e.uid),a=await(0,s.x7)(t);a.exists()&&i(a.data())}})()}),[e]),(0,c.jsxs)("div",{className:"dashboard-container",children:[(0,c.jsx)(l.A,{userData:t}),(0,c.jsxs)("div",{className:"main-content",children:[(0,c.jsxs)("div",{className:"page-title",children:["Admin Dashboard",(0,c.jsx)("button",{onClick:()=>p(!u),style:{marginLeft:"20px",padding:"8px 16px",backgroundColor:u?"#dc3545":"#007bff",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"14px"},children:u?"Hide Office Test":"Show Office Loading Test"})]}),(0,c.jsx)(d.A,{}),(0,c.jsx)(ge,{}),u?(0,c.jsx)(fe,{}):(0,c.jsx)(D,{})]})]})}}}]);
//# sourceMappingURL=625.9d939921.chunk.js.map