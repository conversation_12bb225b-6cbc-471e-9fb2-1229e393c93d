{"version": 3, "file": "static/js/625.9d939921.chunk.js", "mappings": "4NAEO,SAASA,EAAoBC,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,EACoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,S,aCOvD,MASMC,GAAWC,EAAAA,EAAAA,IAAOC,EAAAA,EAAO,CAC7BC,KAAM,UACNN,KAAM,QAFSI,CAGd,CACDG,SAAU,WAyDZ,EAvD0BC,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,aAEF,UACJO,EAAS,OACTC,GAAS,KACNC,GACDJ,EACEK,EAAa,IACdL,EACHG,UAEIG,EA7BkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoBpB,EAAqBkB,EAAQ,EAsB1CG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKlB,EAAU,CACjCU,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BU,UAAWT,EAAS,OAAIU,EACxBd,IAAKA,EACLM,WAAYA,KACTD,GAEP,G,kEC/CO,SAASU,EAA4BzB,GAC1C,OAAOC,EAAAA,EAAAA,IAAqB,kBAAmBD,EACjD,CACA,MACA,GAD4BE,EAAAA,EAAAA,GAAuB,kBAAmB,CAAC,OAAQ,YAAa,QAAS,QAAS,UAAW,a,+DCFzH,MAAMwB,EAAQ,CACZ,CAAEC,MAAO,cAAeC,MAAO,QAC/B,CAAED,MAAO,aAAcC,MAAO,gBAC9B,CAAED,MAAO,oBAAqBC,MAAO,MACrC,CAAED,MAAO,MAAOC,MAAO,oBAgBzB,EAb6BC,KAEzBR,EAAAA,EAAAA,KAAA,OAAKR,UAAU,aAAYiB,SACxBJ,EAAMK,KAAI,CAACC,EAAMC,KAChBC,EAAAA,EAAAA,MAAA,OAAKrB,UAAW,kBAAkBoB,IAAQH,SAAA,EACxCT,EAAAA,EAAAA,KAAA,MAAAS,SAAKE,EAAKL,SACVN,EAAAA,EAAAA,KAAA,KAAGR,UAAU,aAAYiB,SAAEE,EAAKJ,UAFcK,M,kECZjD,SAASE,EAAuBnC,GACrC,OAAOC,EAAAA,EAAAA,IAAqB,aAAcD,EAC5C,CACA,MACA,GADuBE,EAAAA,EAAAA,GAAuB,aAAc,CAAC,OAAQ,WAAY,YAAa,QAAS,SAAU,WAAY,QAAS,WAAY,eAAgB,uBAAwB,iBAAkB,gBAAiB,UAAW,mB,4GCHjO,SAASkC,EAA2BpC,GACzC,OAAOC,EAAAA,EAAAA,IAAqB,iBAAkBD,EAChD,EAC2BE,EAAAA,EAAAA,GAAuB,iBAAkB,CAAC,S,aCKrE,MASMmC,GAAkBjC,EAAAA,EAAAA,IAAO,MAAO,CACpCE,KAAM,iBACNN,KAAM,QAFgBI,CAGrB,CACDkC,QAAS,GACT,eAAgB,CACdC,cAAe,MAqDnB,EAlDiC/B,EAAAA,YAAiB,SAAqBC,EAASC,GAC9E,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,oBAEF,UACJO,EAAS,UACT2B,EAAY,SACTzB,GACDJ,EACEK,EAAa,IACdL,EACH6B,aAEIvB,EAhCkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoBiB,EAA4BnB,EAAQ,EAyBjDG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKgB,EAAiB,CACxCI,GAAID,EACJ3B,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BG,WAAYA,EACZN,IAAKA,KACFK,GAEP,G,mICxCA,MAkBA,EAlBoC2B,IAA2C,IAA1C,OAAEC,EAAM,QAAEC,EAAO,MAAEjB,EAAK,SAAEG,GAAUY,EACvE,OAAKC,GAGHtB,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAgBgC,QAASD,EAAQd,UAC9CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAgBgC,QAASC,GAAKA,EAAEC,kBAAkBjB,SAAA,EAC/DI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BT,EAAAA,EAAAA,KAAA,MAAAS,SAAKH,KACLN,EAAAA,EAAAA,KAAA,UAAQR,UAAU,eAAegC,QAASD,EAAQd,SAAC,aAErDT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,aAAYiB,SACxBA,SAVW,IAaZ,E,cCpBH,MAeMkB,EAAaA,CAACC,EAAgBC,KACzC,MAAMC,EAAOD,EAAcE,MAAKC,GAAKA,EAAEC,KAAOL,IAC9C,QAAOE,IAAQA,EAAKI,QAAgB,EAIzBC,EAAaA,CAACP,EAAgBC,KACjCA,EAAcO,MAAKJ,GAAKA,EAAEE,WAAaN,IAIpCS,EAAiBC,IAC5B,MAAM5B,EAAmC,CAAC,EACpC6B,EAAoB,GAW1B,OAVAD,EAAKE,SAAQC,IACX/B,EAAI+B,EAAKR,IAAM,IAAKQ,EAAMhC,SAAU,GAAI,IAE1C6B,EAAKE,SAAQC,IAC+B,IAADC,EAArCD,EAAKP,UAAYxB,EAAI+B,EAAKP,UACD,QAA3BQ,EAAAhC,EAAI+B,EAAKP,UAAUzB,gBAAQ,IAAAiC,GAA3BA,EAA6BC,KAAKjC,EAAI+B,EAAKR,KAE3CM,EAAMI,KAAKjC,EAAI+B,EAAKR,IACtB,IAEKM,CAAK,EAIDK,EAAsBA,CAACV,EAAkBL,KACpD,IAAIgB,EAAwB,GAC5B,MAAMpC,EAAWoB,EAAciB,QAAOd,GAAKA,EAAEE,WAAaA,IAC1D,IAAK,MAAMa,KAAStC,EAClBoC,EAAYF,KAAKI,EAAMd,IACvBY,EAAcA,EAAYG,OAAOJ,EAAoBG,EAAMd,GAAIJ,IAEjE,OAAOgB,CAAW,ECxBPI,EAAqB3D,IAChC,MAAM,WACJ4D,EAAU,cACVC,EAAa,aACbC,EAAY,gBACZC,EAAe,UACfC,EAAS,aACTC,EAAY,aACZC,EAAY,gBACZC,EAAe,WACfC,EAAU,cACVC,EAAa,aACbC,EAAY,SACZC,EAAQ,WACRC,EAAU,oBACVC,EAAmB,mBACnBC,EAAkB,cAClBC,EAAa,UACbC,EAAS,eACTC,EAAc,iBACdC,EAAgB,gBAChBC,EAAe,0BACfC,GACEhF,EAEEiF,GAAkBC,EAAAA,EAAAA,cAAYC,UAClCb,GAAa,GACb,IACE,MACMc,SADsBC,EAAAA,EAAAA,KAAQC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,gBACXC,KAAKpE,KAAIqE,IAAG,CAAO9C,GAAI8C,EAAI9C,MAAO8C,EAAIC,WAC9E7B,EAAcuB,EAChB,CAAE,MAAOO,GACPpB,EAAS,+BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,IACC,CAACT,EAAeS,EAAcC,IA+IjC,MAAO,CACLU,kBACAa,iBAzIuBX,UACvB,IAAKnB,IAAcE,EAEjB,YADAK,EAAS,qCAGXD,GAAa,GAEb,QAbuBa,WACvB,MAAMY,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc5C,GAErC,aADsBqD,EAAAA,EAAAA,IAAOD,IACdE,QAAQ,EASGC,CAAiBlC,GAIzC,OAFAO,EAAS,+DACTD,GAAa,GAGfA,GAAa,GACbG,GAAoB,EAAK,EA6HzB0B,oBA1H0BhB,UAAa,IAADiB,EACtC,IAAKpC,IAAcE,EAGf,OAFAK,EAAS,6CACTE,GAAoB,GAGxB,IAAI4B,EAA+B,KAChB,qBAAfjC,GAAqCN,EACvCuC,EAAgBvC,EACQ,qBAAfM,EACTiC,EAAgB,KACPvC,GAA+B,qBAAfM,EACvBiC,EAAgBvC,EACRA,GAA+B,qBAAfM,IACxBiC,EAAgB,MAGpB,MACMC,EAAU,GADGD,EAA4D,QAA/CD,EAAGxC,EAAWnB,MAAKC,GAAKA,EAAEC,KAAO0D,WAAc,IAAAD,OAAA,EAA5CA,EAA8CG,KAAO,iBACvDvC,IAAYwC,QAAQ,OAAQ,KAE7D,IACElC,GAAa,GACbG,GAAoB,GACpB,MAAMgC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcvB,IAC9B0C,KAAMC,EAAeC,MAAOC,GD/GR7F,KAChC,MAAM8F,EAAO9F,EACV+F,MAAM,IACNC,QAAO,CAACC,EAAKC,IAASD,EAAMC,EAAKC,WAAW,IAAI,GAE7CC,EAAQ,CAACC,EAAAA,IAAUC,EAAAA,IAAWC,EAAAA,IAAOC,EAAAA,KACrCC,EAAS,CAAC,UAAW,UAAW,UAAW,UAAW,WAK5D,MAAO,CAAEf,KAHIU,EAAMN,EAAOM,EAAMM,QAGjBd,MAFDa,EAAOX,EAAOW,EAAOC,QAEb,ECoGqCC,CAAkBzD,SAEnE0D,EAAAA,EAAAA,IAAOnB,EAAS,CACpB9D,GAAIqB,EACJhD,MAAOkD,EACPqC,KAAMD,EACN1D,SAAUyD,EACVwB,aAAa,IAAIC,MAAOC,cACxBrB,KAAMC,EAAchH,KACpBiH,MAAOC,EACPmB,OAAQ,GACRC,QAAQ,EACRC,OAAQlE,UAGJiB,IAENhB,EAAa,IACbE,EAAgB,IAChBO,GAAmB,GACnBL,EAAc,IACdN,EAAgBC,GAChBQ,EAAW,WAAWN,qCACtBiE,YAAW,IAAM3D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPpB,EAAS,yDACTqB,QAAQC,MAAM,uBAAwBF,EACxC,CAAC,QACCrB,GAAa,EACf,GAqEA8D,eAlEsB5F,IACtBqC,EAAerC,GACf2B,EAAgB3B,EAAKxB,OACrB8D,GAAiB,EAAK,EAgEtBuD,iBA7DuBlD,UACvB,MAAMmD,EAAc1E,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,IAClD,GAAKwE,GAAgBpE,EACrB,IACEI,GAAa,GACb,MAAMmC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc+C,EAAY3F,UAC5C4F,EAAAA,EAAAA,IAAU9B,EAAS,CAAEzF,MAAOkD,EAAc2D,aAAa,IAAIC,MAAOC,sBAClE9C,IACNH,GAAiB,GACjBD,EAAe,MACfV,EAAgB,IAChBK,EAAW,gCACX2D,YAAW,IAAM3D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,GA4CAkE,kBAzCyBlG,IACzByC,EAAgBzC,GAChB0C,GAA0B,EAAK,EAwC/ByD,oBArC0BtD,UAC1B,GAAKrB,EAAL,CACAQ,GAAa,GACb,IACE,MAAMoE,GAAQC,EAAAA,EAAAA,IAAWpD,EAAAA,IACnBqD,EAAiBtF,EAAoBQ,EAAcF,GACnDiF,EAAc,CAAC/E,KAAiB8E,GAEtC,IAAK,MAAMjG,KAAMkG,EACfH,EAAMI,QAAOrD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc5C,IACnC+F,EAAMI,QAAOrD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAAS5C,UAE1B+F,EAAMK,eACN9D,IAEND,GAA0B,GAC1BD,EAAgB,MAChBhB,EAAgB,IAChBY,EAAc,MACdC,EAAU,IACVJ,EAAW,yDACX2D,YAAW,IAAM3D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,CA1ByB,CA0BzB,EAWD,E,cC1LI,MC2DP,EA5EkDvC,IAS3C,IAT4C,WACjD6B,EAAU,aACVE,EAAY,aACZkF,EAAY,WACZ5E,EAAU,eACV6E,EAAc,UACdC,EAAS,eACTC,EAAc,gBACdC,GACDrH,EACC,MAAMsH,EAAoB,SAACC,GAAwD,IAArCC,EAAKC,UAAA9B,OAAA,QAAA7G,IAAA2I,UAAA,GAAAA,UAAA,GAAG,EACpD,OAAOF,EAAMG,SAAQjH,GAAQ,EAC3B9B,EAAAA,EAAAA,KAAA,UAAsBO,MAAOuB,EAAKG,GAAI+G,MAAO,CAAEC,YAAwB,GAARJ,EAAH,MAAoBpI,SAC7E,GAAG,KAAKyI,OAAOL,MAAU/G,EAAKxB,SADpBwB,EAAKG,OAGdH,EAAKrB,UAAYqB,EAAKrB,SAASuG,OAAS,EAAI2B,EAAkB7G,EAAKrB,SAAUoI,EAAQ,GAAK,KAElG,EAkBA,OACEhI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAeiB,SAAA,EAC5BI,EAAAA,EAAAA,MAAA,UACEN,MAAO6C,EACP+F,SApBoB1H,IACxB,MAAM2H,EAAkB3H,EAAE4H,OAAO9I,MACjC+H,EAAac,EAAgB,EAmBzB5J,UAAU,cACV8J,SAAUd,EAAU/H,SAAA,EAEpBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAE+H,EAAY,qBAAuB,gCACpDG,EAAkBtG,EAAca,QAGnClD,EAAAA,EAAAA,KAAA,OAAKR,UAAU,4BAA2BiB,UACxCI,EAAAA,EAAAA,MAAA,UACEN,MAAOmD,EACPyF,SA1BoB1H,IAC1B,MAAM8H,EAAY9H,EAAE4H,OAAO9I,MAC3BgI,EAAegB,GAEG,qBAAdA,GAAkD,qBAAdA,EACtCd,IACuB,kBAAdc,GACTb,GACF,EAmBMlJ,UAAU,8BAA6BiB,SAAA,EAEvCT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,sBACjBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,mBAAmB+I,WAAYlG,EAAa3C,SAAC,2BAG1D2C,IACCvC,EAAAA,EAAAA,MAAA2I,EAAAA,SAAA,CAAA/I,SAAA,EACET,EAAAA,EAAAA,KAAA,UAAQO,MAAM,mBAAkBE,SAAC,0BAGjCT,EAAAA,EAAAA,KAAA,UACEO,MAAM,gBACN+I,UAAWnH,EAAWiB,EAAcF,IAAevB,EAAWyB,EAAcF,GAAYzC,SACzF,mDAOL,ECzCV,EAnCsDY,IAK/C,IALgD,aACrD+B,EAAY,WACZF,EAAU,WACVuG,EAAU,aACVC,GACDrI,EACC,MAAMsI,EAAmBzG,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,IAEvD,OAAKuG,GAKH9I,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kBAAiBiB,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,oBAAkBkJ,EAAiBrJ,MAAM,QAC7CO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,UACEW,QAASA,IAAMiI,EAAWE,GAC1BnK,UAAU,kDACV8J,UAAWlG,EAAa3C,SAAA,CAEvBtB,EAAAA,cAAoByK,EAAAA,KAAoC,iBAE3D/I,EAAAA,EAAAA,MAAA,UACEW,QAASA,IAAMkI,EAAatG,GAC5B5D,UAAU,8CACV8J,UAAWlG,EAAa3C,SAAA,CAEvBtB,EAAAA,cAAoB0K,EAAAA,KAAqC,0BAnBzD,IAsBD,ECyNV,EAxPwDxI,IAKjD,IAADyI,EAAA,IALmD,MACvDC,EAAK,MACLnJ,EAAK,SACLoJ,EAAQ,SACRC,GACD5I,EACC,MAAM6I,EAAqBA,CAACC,EAAkB5J,EAAe6J,KAC3D,MAAMC,EAAa,IAAKN,EAAMO,SAAW,IACzCD,EAAWF,GAAY,IAAKE,EAAWF,GAAW,CAACC,GAAM7J,GACzDyJ,EAASpJ,EAAO,IAAKmJ,EAAOO,QAASD,GAAa,EAa9CE,EAA4B9I,IAChC,MAAM,MAAElB,EAAK,KAAEiK,GAAS/I,EAAE4H,OAC1B,IAAIoB,EAAuBlK,EACd,aAATiK,IACFC,EAAmBhJ,EAAE4H,OAA4BqB,SAEnDV,EAASpJ,EAAO,IAAKmJ,EAAOY,aAAcF,GAAkB,EAG9D,OACE5J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,8BAA6BiB,SAAA,EAC1CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gEAA+DiB,SAAA,EAC5ET,EAAAA,EAAAA,KAAA,UAAAS,SAASsJ,EAAMa,OAAS,kBAAyB,KAAGb,EAAMS,KAAK,KAC/D3J,EAAAA,EAAAA,MAAA,UAAQW,QAASA,IAAMyI,EAASrJ,GAAQpB,UAAU,wBAAuBiB,SAAA,CACtEtB,EAAAA,cAAoB0K,EAAAA,KAAqC,iBAG9DhJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,YAAWiB,SAAA,EAExBI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,cAAcjK,IAASpB,UAAU,aAAYiB,SAAC,YAC9DI,EAAAA,EAAAA,MAAA,UACEoB,GAAI,cAAcrB,IAClBpB,UAAU,eACVe,MAAOwJ,EAAMS,KACbrB,SAAW1H,GAAMuI,EAASpJ,EAAO,IAC5BmJ,EACHS,KAAM/I,EAAE4H,OAAO9I,MACf+J,QAAwB,aAAfP,EAAMS,MAAsC,UAAfT,EAAMS,MAAmC,mBAAfT,EAAMS,UAA4BrK,EAAY4J,EAAMO,QACpHQ,YAA4B,YAAff,EAAMS,MAAqC,WAAfT,EAAMS,UAAoBrK,EAAY4J,EAAMe,cACpFrK,SAAA,EAEHT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,UACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,cACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,YACvBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,UACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,cACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,QAAOE,SAAC,iBACtBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,uBACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,iBAAgBE,SAAC,oBAC/BT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,YACvBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,iBACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,UAASE,SAAC,oBACxBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,kBAI3BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,eAAejK,IAASpB,UAAU,aAAYiB,SAAC,aAC/DT,EAAAA,EAAAA,KAAA,SACEiC,GAAI,eAAerB,IACnB4J,KAAK,OACLhL,UAAU,eACVe,MAAOwJ,EAAMa,MACbzB,SAAW1H,GAAMuI,EAASpJ,EAAO,IAAImJ,EAAOa,MAAOnJ,EAAE4H,OAAO9I,QAC5DwK,UAAQ,OAIX,CAAC,OAAQ,WAAY,SAAU,QAAQC,SAASjB,EAAMS,QACrD3J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,qBAAqBjK,IAASpB,UAAU,aAAYiB,SAAC,mBACrET,EAAAA,EAAAA,KAAA,SACEiC,GAAI,qBAAqBrB,IACzB4J,KAAK,OACLhL,UAAU,eACVe,MAAOwJ,EAAMe,aAAe,GAC5B3B,SAAW1H,GAAMuI,EAASpJ,EAAO,IAAImJ,EAAOe,YAAarJ,EAAE4H,OAAO9I,aAKxD,WAAfwJ,EAAMS,OACL3J,EAAAA,EAAAA,MAAA2I,EAAAA,SAAA,CAAA/I,SAAA,EACEI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,aAAajK,IAASpB,UAAU,aAAYiB,SAAC,iBAC7DT,EAAAA,EAAAA,KAAA,SACEiC,GAAI,aAAarB,IACjB4J,KAAK,SACLhL,UAAU,eACVe,WAAqBJ,IAAd4J,EAAMkB,IAAoB,GAAKlB,EAAMkB,IAC5C9B,SAAW1H,GAAMuI,EAASpJ,EAAO,IAAImJ,EAAOkB,IAAwB,KAAnBxJ,EAAE4H,OAAO9I,WAAeJ,EAAY+K,WAAWzJ,EAAE4H,OAAO9I,eAG7GM,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,aAAajK,IAASpB,UAAU,aAAYiB,SAAC,iBAC7DT,EAAAA,EAAAA,KAAA,SACEiC,GAAI,aAAarB,IACjB4J,KAAK,SACLhL,UAAU,eACVe,WAAqBJ,IAAd4J,EAAMoB,IAAoB,GAAKpB,EAAMoB,IAC5ChC,SAAW1H,GAAMuI,EAASpJ,EAAO,IAAImJ,EAAOoB,IAAwB,KAAnB1J,EAAE4H,OAAO9I,WAAeJ,EAAY+K,WAAWzJ,EAAE4H,OAAO9I,iBAMhH,CAAC,WAAY,QAAS,kBAAkByK,SAASjB,EAAMS,QACtD3J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kCAAiCiB,SAAA,EAC9CT,EAAAA,EAAAA,KAAA,SAAOR,UAAU,aAAYiB,SAAC,cAChB,QADiCqJ,EAC9CC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAepJ,KAAI,CAAC0K,EAAKjB,KACxBtJ,EAAAA,EAAAA,MAAA,OAAoBrB,UAAU,mBAAkBiB,SAAA,EAC9CT,EAAAA,EAAAA,KAAA,SACEwK,KAAK,OACLhL,UAAU,eACVsL,YAAY,eACZvK,MAAO6K,EAAIR,MACXzB,SAAW1H,GAAMyI,EAAmBC,EAAU1I,EAAE4H,OAAO9I,MAAO,YAEhEP,EAAAA,EAAAA,KAAA,SACEwK,KAAK,OACLhL,UAAU,eACVsL,YAAY,eACZvK,MAAO6K,EAAI7K,MACX4I,SAAW1H,GAAMyI,EAAmBC,EAAU1I,EAAE4H,OAAO9I,MAAO,YAEhEP,EAAAA,EAAAA,KAAA,UAAQwK,KAAK,SAAShJ,QAASA,IAzHvB2I,KAAsB,IAADkB,EACzC,MAAMhB,EAA0B,QAAhBgB,EAAGtB,EAAMO,eAAO,IAAAe,OAAA,EAAbA,EAAevI,QAAO,CAACwI,EAAGC,IAAMA,IAAMpB,IACzDH,EAASpJ,EAAO,IAAKmJ,EAAOO,QAASD,GAAa,EAuHDmB,CAAarB,GAAW3K,UAAU,yBAAwBiB,SAAC,aAfxF0J,MAoBZnK,EAAAA,EAAAA,KAAA,UAAQwK,KAAK,SAAShJ,QAnIdiK,KAChB,MAAMpB,EAAa,IAAKN,EAAMO,SAAW,GAAK,CAAEM,MAAO,GAAIrK,MAAO,KAClEyJ,EAASpJ,EAAO,IAAKmJ,EAAOO,QAASD,GAAa,EAiIA7K,UAAU,2BAA0BiB,SAAC,kBAOlF,CAAC,OAAQ,WAAY,SAAU,QAAQuK,SAASjB,EAAMS,QACnD3J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACvBT,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,uBAAuBjK,IAASpB,UAAU,aAAYiB,SAAC,qBACvET,EAAAA,EAAAA,KAAA,SACIiC,GAAI,uBAAuBrB,IAC3B4J,KAAqB,WAAfT,EAAMS,KAAoB,SAA0B,SAAfT,EAAMS,KAAkB,OAAS,OAC5EhL,UAAU,eACVe,WAA8BJ,IAAvB4J,EAAMY,aAA6B,GAAKe,OAAO3B,EAAMY,cAC5DxB,SAAUoB,QAKL,aAAfR,EAAMS,MAAsC,WAAfT,EAAMS,QACjC3J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,wBAAuBiB,SAAA,EAClCT,EAAAA,EAAAA,KAAA,SACIiC,GAAI,uBAAuBrB,IAC3B4J,KAAK,WACLhL,UAAU,mBACVkL,QAASiB,QAAQ5B,EAAMY,cACvBxB,SAAUoB,KAEdvK,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,uBAAuBjK,IAASpB,UAAU,mBAAkBiB,SAAC,yBAIpF,CAAC,WAAY,SAASuK,SAASjB,EAAMS,OAAST,EAAMO,SAAWP,EAAMO,QAAQtD,OAAS,IAClFnG,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACxBT,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,uBAAuBjK,IAASpB,UAAU,aAAYiB,SAAC,qBACvEI,EAAAA,EAAAA,MAAA,UACIoB,GAAI,uBAAuBrB,IAC3BpB,UAAU,eACVe,WAA8BJ,IAAvB4J,EAAMY,aAA6B,GAAKe,OAAO3B,EAAMY,cAC5DxB,SAAUoB,EAAyB9J,SAAA,EAEnCT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,yBAChBsJ,EAAMO,QAAQ5J,KAAI0K,IAAOpL,EAAAA,EAAAA,KAAA,UAAwBO,MAAO6K,EAAI7K,MAAME,SAAE2K,EAAIR,OAAlCQ,EAAI7K,eAKvC,mBAAfwJ,EAAMS,OACH3J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACvBT,EAAAA,EAAAA,KAAA,SAAOR,UAAU,aAAYiB,SAAC,wCAC9BT,EAAAA,EAAAA,KAAA,SACIwK,KAAK,OACLhL,UAAU,eACVe,MAAOqL,MAAMC,QAAQ9B,EAAMY,cAAgBZ,EAAMY,aAAamB,KAAK,KAAO,GAC1E3C,SAAW1H,GAAMuI,EAASpJ,EAAO,IAAImJ,EAAOY,aAAclJ,EAAE4H,OAAO9I,MAAM8F,MAAM,KAAK3F,KAAIqL,GAAKA,EAAEC,SAAQlJ,QAAOiJ,GAAKA,MACnHjB,YAAY,qBAKR,WAAff,EAAMS,OACL3J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,qBAAqBjK,IAASpB,UAAU,aAAYiB,SAAC,mBACrET,EAAAA,EAAAA,KAAA,SACEiC,GAAI,qBAAqBrB,IACzB4J,KAAK,OACLhL,UAAU,eACVe,MAAOwJ,EAAMkC,YAAc,GAC3B9C,SAAW1H,GAAMuI,EAASpJ,EAAO,IAAImJ,EAAOkC,WAAYxK,EAAE4H,OAAO9I,aAKvD,YAAfwJ,EAAMS,OACL3J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,uBAAuBjK,IAASpB,UAAU,aAAYiB,SAAC,qBACvET,EAAAA,EAAAA,KAAA,SACEiC,GAAI,uBAAuBrB,IAC3B4J,KAAK,OACLhL,UAAU,eACVe,MAAOwJ,EAAMmC,cAAgB,GAC7B/C,SAAW1H,GAAMuI,EAASpJ,EAAO,IAAImJ,EAAOmC,aAAczK,EAAE4H,OAAO9I,cAMvE,CAAC,SAAU,WAAWyK,SAASjB,EAAMS,QACrC3J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,wBAAuBiB,SAAA,EACpCT,EAAAA,EAAAA,KAAA,SACEiC,GAAI,kBAAkBrB,IACtB4J,KAAK,WACLhL,UAAU,mBACVkL,UAAWX,EAAMgB,SACjB5B,SAAW1H,GAAMuI,EAASpJ,EAAO,IAAImJ,EAAOgB,SAAUtJ,EAAE4H,OAAOqB,aAEjE1K,EAAAA,EAAAA,KAAA,SAAO6K,QAAS,kBAAkBjK,IAASpB,UAAU,mBAAkBiB,SAAC,sBAI1E,EC/LV,EAhD8DY,IASvD,IATwD,WAC7D8K,EAAU,OACV7E,EAAM,WACN8E,EAAU,cACVC,EAAa,cACbC,EAAa,OACbC,EAAM,UACNC,EAAS,QACTC,GACDpL,EACC,OACER,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kBAAiBiB,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,2BAAyB0L,EAAW7L,UAExCN,EAAAA,EAAAA,KAAA,MAAAS,SAAI,yBACH6G,EAAO5G,KAAI,CAACqJ,EAAOnJ,KAClBZ,EAAAA,EAAAA,KAAC0M,EAAe,CAEd3C,MAAOA,EACPnJ,MAAOA,EACPoJ,SAAUqC,EACVpC,SAAUqC,GAJLvC,EAAM9H,IAAMrB,MAQrBC,EAAAA,EAAAA,MAAA,UAAQW,QAAS4K,EAAY5M,UAAU,oBAAmBiB,SAAA,CACvDtB,EAAAA,cAAoBwN,EAAAA,KAAoC,iBAG3D9L,EAAAA,EAAAA,MAAA,UACEW,QAAS+K,EACT/M,UAAU,4BACV8J,SAAUmD,IAAYN,GAAgC,IAAlB7E,EAAON,OAAavG,SAAA,CAEvDtB,EAAAA,cAAoByN,EAAAA,KAAoC,IAAEH,EAAU,YAAc,8BAGrFzM,EAAAA,EAAAA,KAAA,UACEwB,QAASgL,EACThN,UAAU,8BACV8J,UAAW6C,GAAgC,IAAlB7E,EAAON,OAAavG,SAC9C,mBAGG,ECuCGoM,EAAwC,CACnD,CAAEtM,MAAO,QAASqK,MAAO,SACzB,CAAErK,MAAO,SAAUqK,MAAO,UAC1B,CAAErK,MAAO,UAAWqK,MAAO,Y,cCxFtB,MCkJP,EA/I0DvJ,IAQnD,IARoD,GACzDY,EAAE,MACF2I,EAAK,QACLN,EAAO,eACPwC,EAAc,SACd3D,EAAQ,SACRG,GAAW,EAAK,YAChBwB,EAAc,wBACfzJ,EACC,MAAOC,EAAQyL,IAAaC,EAAAA,EAAAA,WAAS,GAC/BC,GAAcC,EAAAA,EAAAA,QAAuB,OAG3CC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAsBC,IACtBJ,EAAYK,UAAYL,EAAYK,QAAQC,SAASF,EAAMhE,SAC7D0D,GAAU,EACZ,EAIF,OADAS,SAASC,iBAAiB,YAAaL,GAChC,KACLI,SAASE,oBAAoB,YAAaN,EAAmB,CAC9D,GACA,IAEH,MA+BMO,EAAgBb,EAAe9F,SAAWsD,EAAQtD,QAAUsD,EAAQtD,OAAS,EAC7E4G,EAAkBd,EAAe9F,OAAS,GAAK8F,EAAe9F,OAASsD,EAAQtD,OAErF,OACEnG,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAOgK,QAAS5I,EAAIzC,UAAU,aAAYiB,SAAA,CAAEmK,EAAM,QAClD/J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,WAAWH,IAAK4N,EAAYxM,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEiC,GAAIA,EACJzC,UAAW,+DAA8D8J,EAAW,WAAa,IACjGkB,KAAK,SACLhJ,QAASA,KAAO8H,GAAYyD,GAAWzL,GACvCgI,SAAUA,EACVN,MAAO,CACL6E,gBAAiBvE,EAAW,UAAY,QACxCwE,YAAa,WACbrN,UAEFT,EAAAA,EAAAA,KAAA,QAAMR,UAAqC,IAA1BsN,EAAe9F,OAAe,aAAe,GAAGvG,SA7BlDsN,MACrB,GAA8B,IAA1BjB,EAAe9F,OACjB,OAAO8D,EACF,GAA8B,IAA1BgC,EAAe9F,OAAc,CACtC,MAAMgH,EAAiB1D,EAAQvI,MAAKkM,GAAUA,EAAOhM,KAAO6K,EAAe,KAC3E,OAAqB,OAAdkB,QAAc,IAAdA,OAAc,EAAdA,EAAgB/O,OAAQ6L,CACjC,CACE,MAAO,GAAGgC,EAAe9F,iBAC3B,EAsBS+G,OAIJzM,IAAWgI,IACVzI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,2BAA2BwJ,MAAO,CAAEkF,UAAW,QAASC,UAAW,QAAS1N,SAAA,CAExF6J,EAAQtD,OAAS,IAChBnG,EAAAA,EAAAA,MAAA2I,EAAAA,SAAA,CAAA/I,SAAA,EACET,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeiB,UAC5BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SACER,UAAU,mBACVgL,KAAK,WACLvI,GAAI,GAAGA,eACPyI,QAASiD,EACTtO,IAAM+O,IACAA,IAAOA,EAAMC,cAAgBT,EAAe,EAElDzE,SA3DImF,KAClBxB,EAAe9F,SAAWsD,EAAQtD,OAEpCmC,EAAS,IAGTA,EAASmB,EAAQ5J,KAAIuN,GAAUA,EAAOhM,KACxC,KAsDgBpB,EAAAA,EAAAA,MAAA,SAAOrB,UAAU,2BAA2BqL,QAAS,GAAG5I,eAAgBxB,SAAA,CAAC,eAC1D6J,EAAQtD,OAAO,aAIlChH,EAAAA,EAAAA,KAAA,MAAIR,UAAU,wBAKjB8K,EAAQ5J,KAAIuN,IACXjO,EAAAA,EAAAA,KAAA,OAAqBR,UAAU,gBAAeiB,UAC5CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SACER,UAAU,mBACVgL,KAAK,WACLvI,GAAI,GAAGA,KAAMgM,EAAOhM,KACpByI,QAASoC,EAAe9B,SAASiD,EAAOhM,IACxCkH,SAAUA,KAAMoF,OAzFJC,EAyFyBP,EAAOhM,QAxFxD6K,EAAe9B,SAASwD,GAE1BrF,EAAS2D,EAAehK,QAAOb,GAAMA,IAAOuM,KAG5CrF,EAAS,IAAI2D,EAAgB0B,KANHA,KAyFoC,KAElDxO,EAAAA,EAAAA,KAAA,SAAOR,UAAU,mBAAmBqL,QAAS,GAAG5I,KAAMgM,EAAOhM,KAAKxB,SAC/DwN,EAAOhP,WAVJgP,EAAOhM,MAgBC,IAAnBqI,EAAQtD,SACPhH,EAAAA,EAAAA,KAAA,OAAKR,UAAU,2BAA0BiB,UACvCT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,iCAQbqM,EAAe9F,OAAS,IACvBnG,EAAAA,EAAAA,MAAA,SAAOrB,UAAU,0BAAyBiB,SAAA,CACvCqM,EAAe9F,OAAO,OAAKsD,EAAQtD,OAAO,iBAG3C,ECyBV,EArKgE3F,IASzD,IAT0D,gBAC/DoN,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,GACD3N,EAEC,MAAM,QAAE4N,EAAO,UAAEC,EAAS,QAAEC,EAAO,QAAE1C,EAAO,MAAEtH,EAAK,QAAEiK,GFbpBC,MACjC,MAAOJ,EAASK,IAActC,EAAAA,EAAAA,UAAmB,KAC1CkC,EAAWK,IAAgBvC,EAAAA,EAAAA,UAAqB,KAChDmC,EAASK,IAAcxC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAkB,IACzC7H,EAAOtB,IAAYmJ,EAAAA,EAAAA,UAAwB,MAE5C0C,EAAkBjL,UACtB,IACEgL,GAAW,GACX5L,EAAS,MAETqB,QAAQyK,IAAI,0EAGZ,MAAMC,QAAgBC,EAAAA,EAAcC,qBAEpC5K,QAAQyK,IAAI,sCAAkCC,EAAQ5I,OAAQ,kBAG9D,MAAM+I,EAAyB,OAAPH,QAAO,IAAPA,OAAO,EAAPA,EACpBlP,KAAIsP,GAAOA,EAAIC,SAChBnN,QAAO,CAACoN,EAAQtP,EAAOuP,IAAUA,EAAMC,QAAQF,KAAYtP,IAC3DkC,QAAQoN,GAAuC,MAAVA,GAAoC,KAAlBA,EAAOlE,SAC9DqE,OAIGC,GAAwC,OAAfP,QAAe,IAAfA,OAAe,EAAfA,EAAiBrP,KAAI6P,IAAU,CAC5DtO,GAAIsO,EAAWC,cAAc1K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzE7G,KAAMsR,QACD,GAGDE,EAA2B,OAAPb,QAAO,IAAPA,OAAO,EAAPA,EACtBlP,KAAIsP,IAAG,CAAOE,OAAQF,EAAIC,OAAQS,SAAUV,EAAIW,aACjD7N,QAAO,CAACL,EAAM7B,EAAOuP,IACpBA,EAAMS,WAAUC,GAAKA,EAAEX,SAAWzN,EAAKyN,QAAUW,EAAEH,WAAajO,EAAKiO,aAAc9P,IAEpFkC,QAAQL,GACQ,MAAfA,EAAKyN,QAAmC,MAAjBzN,EAAKiO,UACL,KAAvBjO,EAAKyN,OAAOlE,QAA0C,KAAzBvJ,EAAKiO,SAAS1E,SAE5CqE,MAAK,CAACS,EAAGC,IAAMD,EAAEZ,OAAOc,cAAcD,EAAEb,SAAWY,EAAEJ,SAASM,cAAcD,EAAEL,YAE3EO,GAA8C,OAAjBR,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmB/P,KAAI+B,IAAI,CAC5DR,GAAIQ,EAAKiO,SAASF,cAAc1K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC5E7G,KAAMwD,EAAKiO,SACXR,OAAQzN,EAAKyN,aACR,GAGDgB,GAAgC,OAAPtB,QAAO,IAAPA,OAAO,EAAPA,EAC3B9M,QAAOkN,GAAOA,EAAI,gBAAkBA,EAAIC,QAAUD,EAAIW,WACvDjQ,KAAIsP,IAAG,CACN/N,GAAI+N,EAAI,eACR/Q,KAAM+Q,EAAI,eACVE,OAAQF,EAAIC,QAAU,GACtBS,SAAUV,EAAIW,UAAY,GAC1BQ,WAAYnB,EAAI,qBACX,GAITV,EAAWgB,GACXf,EAAa0B,GACbzB,EAAW0B,EAEb,CAAE,MAAOjM,GACPC,QAAQC,MAAM,8BAAqBF,GACnCpB,EAAS,iDACTyL,EAAW,IACXC,EAAa,IACbC,EAAW,GACb,CAAC,QACCC,GAAW,EACb,GAQF,OAJAtC,EAAAA,EAAAA,YAAU,KACRuC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA1C,UACAtH,QACAiK,QAASM,EACV,EE9EgE0B,GAG3DC,EAAsB5C,EAAgB/N,KAAI4Q,IAAQ,IAAAC,EAAA,OAClB,QADkBA,EACtDtC,EAAQlN,MAAKyP,GAAKA,EAAEvP,KAAOqP,WAAS,IAAAC,OAAA,EAApCA,EAAsCtS,IAAI,IAC1C6D,OAAO6I,SAEH8F,EAAqBhD,EAAgBzH,OAAS,EAChDkI,EAAUpM,QAAO4N,GAAYW,EAAoBrG,SAAS0F,EAASR,UACnEhB,EAGEwC,EAAwBhD,EAAkBhO,KAAIiR,IAAU,IAAAC,EAAA,OACpB,QADoBA,EAC5D1C,EAAUnN,MAAK8P,GAAKA,EAAE5P,KAAO0P,WAAW,IAAAC,OAAA,EAAxCA,EAA0C3S,IAAI,IAC9C6D,OAAO6I,SAEHmG,EAAmBpD,EAAkB1H,OAAS,EAChDmI,EAAQrM,QAAOiP,GACbV,EAAoBrG,SAAS+G,EAAO7B,SACpCwB,EAAsB1G,SAAS+G,EAAOrB,YAExCjC,EAAgBzH,OAAS,EACvBmI,EAAQrM,QAAOiP,GAAUV,EAAoBrG,SAAS+G,EAAO7B,UAC7Df,EAiCN,OA9BAhC,EAAAA,EAAAA,YAAU,KACR,GAAIsB,EAAgBzH,OAAS,EAAG,CAE9B,MAAMgL,EAAiBtD,EAAkB5L,QAAO6O,IAC9C,MAAMjB,EAAWxB,EAAUnN,MAAK8P,GAAKA,EAAE5P,KAAO0P,IAC9C,OAAOjB,GAAYW,EAAoBrG,SAAS0F,EAASR,OAAO,IAG9D8B,EAAehL,SAAW0H,EAAkB1H,QAC9C8H,EAAkBkD,EAEtB,IACC,CAACvD,EAAiBC,EAAmBQ,EAAWmC,EAAqBvC,KAExE3B,EAAAA,EAAAA,YAAU,KACR,GAAIuB,EAAkB1H,OAAS,EAAG,CAEhC,MAAMiL,EAAetD,EAAgB7L,QAAOoP,IAC1C,MAAMH,EAAS5C,EAAQpN,MAAKoQ,GAAKA,EAAElQ,KAAOiQ,IAC1C,OAAOH,GACAV,EAAoBrG,SAAS+G,EAAO7B,SACpCwB,EAAsB1G,SAAS+G,EAAOrB,SAAS,IAGpDuB,EAAajL,SAAW2H,EAAgB3H,QAC1C+H,EAAgBkD,EAEpB,IACC,CAACvD,EAAmBC,EAAiBQ,EAASkC,EAAqBK,EAAuB3C,KAG3FlO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,iCAAgCiB,SAAA,EAC7CT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,yBAEHgM,IACCzM,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBiB,UAC/BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,4BAA2BiB,SAAA,EACxCT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,wCAAwC4S,KAAK,SAAQ3R,UAClET,EAAAA,EAAAA,KAAA,QAAMR,UAAU,kBAAiBiB,SAAC,iBAC9B,8BAMX0E,IACCtE,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,qBAAoBiB,SAAA,EACjCT,EAAAA,EAAAA,KAAA,UAAAS,SAAQ,WAAe,IAAE0E,GACzBnF,EAAAA,EAAAA,KAAA,UACER,UAAU,qCACVgC,QAAS4N,EAAQ3O,SAClB,cAMHgM,IAAYtH,IACZtE,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,MAAKiB,SAAA,EAClBT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAACqS,EAAgB,CACfpQ,GAAG,gBACH2I,MAAM,iBACNN,QAAS2E,EACTnC,eAAgB2B,EAChBtF,SAAU0F,EACVvF,SAAUmD,EACV3B,YAAY,4BAIhB9K,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAACqS,EAAgB,CACfpQ,GAAG,kBACH2I,MAAM,mBACNN,QAASmH,EACT3E,eAAgB4B,EAChBvF,SAAU2F,EACVxF,SAAqC,IAA3BmF,EAAgBzH,QAAgByF,EAC1C3B,YAAY,8BAIhB9K,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAACqS,EAAgB,CACfpQ,GAAG,gBACH2I,MAAM,iBACNN,QAASwH,EACThF,eAAgB6B,EAChBxF,SAAU4F,EACVzF,SAAuC,IAA7BoF,EAAkB1H,QAAgByF,EAC5C3B,YAAY,4BAIhB9K,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAOgK,QAAQ,mBAAmBrL,UAAU,aAAYiB,SAAA,CAAC,sBACrCT,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaiB,SAAC,UAElDI,EAAAA,EAAAA,MAAA,UACEoB,GAAG,mBACHzC,UAAW,gBAAgBoP,EAAmC,GAAf,cAC/CrO,MAAOqO,EACPzF,SAAW1H,GAAMuN,EAAkBvN,EAAE4H,OAAO9I,OAC5C+I,SAAUmD,EACV1B,UAAQ,EAAAtK,SAAA,EAERT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,2BAChBoM,EAAmBnM,KAAI4R,IACtBtS,EAAAA,EAAAA,KAAA,UAA8BO,MAAO+R,EAAU/R,MAAME,SAClD6R,EAAU1H,OADA0H,EAAU/R,aAKzBqO,IACA5O,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBiB,SAAC,4CAQxC,ECyMV,EAzW8B8R,KAAO,IAADC,EAAAC,EAElC,MAAMC,ECf2BC,MACjC,MAAOzP,EAAYC,IAAiB6J,EAAAA,EAAAA,UAAqB,KAClD5J,EAAcC,IAAmB2J,EAAAA,EAAAA,UAAiB,KAClDb,EAAYlI,IAAiB+I,EAAAA,EAAAA,UAA4B,OACzD1F,EAAQpD,IAAa8I,EAAAA,EAAAA,UAAsB,KAC3C4F,EAAwBC,IAA6B7F,EAAAA,EAAAA,UAA6B,KAClFxE,EAAW5E,IAAgBoJ,EAAAA,EAAAA,WAAkB,IAC7CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAS,IAChC7H,EAAOtB,IAAYmJ,EAAAA,EAAAA,UAAwB,OAC3C8F,EAAShP,IAAckJ,EAAAA,EAAAA,UAAwB,OAE/C+F,EAAiB/O,IAAsBgJ,EAAAA,EAAAA,WAAkB,IACzD1J,EAAWC,IAAgByJ,EAAAA,EAAAA,UAAiB,KAC5CxJ,EAAcC,IAAmBuJ,EAAAA,EAAAA,UAAiB,KAClDgG,EAAkBjP,IAAuBiJ,EAAAA,EAAAA,WAAkB,IAE3DpF,EAAazD,IAAkB6I,EAAAA,EAAAA,UAA0B,OACzDiG,EAAe7O,IAAoB4I,EAAAA,EAAAA,WAAkB,IAErDkG,EAAc7O,IAAmB2I,EAAAA,EAAAA,UAAwB,OACzDmG,EAAwB7O,IAA6B0I,EAAAA,EAAAA,WAAkB,IAEvEtJ,EAAYC,IAAiBqJ,EAAAA,EAAAA,UAAiB,KAG9CoG,EAAeC,IAAoBrG,EAAAA,EAAAA,WAAS,IAC5CsG,EAAgBC,IAAqBvG,EAAAA,EAAAA,UAAS,KAG9CyB,EAAiB+E,IAAsBxG,EAAAA,EAAAA,UAAmB,KAC1D0B,EAAmB+E,IAAwBzG,EAAAA,EAAAA,UAAmB,KAC9D2B,EAAiB+E,IAAsB1G,EAAAA,EAAAA,UAAmB,KAC1D4B,EAAmB+E,IAAwB3G,EAAAA,EAAAA,UAAiB,IAEnE,MAAO,CAEL9J,aACAE,eACA+I,aACA7E,SACAsL,yBACApK,YACAiE,UACAtH,QACA2N,UACAC,kBACAzP,YACAE,eACAwP,mBACApL,cACAqL,gBACAC,eACAC,yBACAzP,aACA0P,gBACAE,iBACA7E,kBACAC,oBACAC,kBACAC,oBAGAzL,gBACAE,kBACAY,gBACAC,YACA2O,4BACAjP,eACA6L,aACA5L,WACAC,aACAE,qBACAT,eACAE,kBACAM,sBACAI,iBACAC,mBACAC,kBACAC,4BACAX,gBACA0P,mBACAE,oBACAC,qBACAC,uBACAC,qBACAC,uBACD,EDvEahB,GAKRiB,EAAiB3Q,EAAkB,CACvCC,WAAYwP,EAAMxP,WAClBC,cAAeuP,EAAMvP,cACrBC,aAAcsP,EAAMtP,aACpBC,gBAAiBqP,EAAMrP,gBACvBC,UAAWoP,EAAMpP,UACjBC,aAAcmP,EAAMnP,aACpBC,aAAckP,EAAMlP,aACpBC,gBAAiBiP,EAAMjP,gBACvBC,WAAYgP,EAAMhP,WAClBC,cAAe+O,EAAM/O,cACrBC,aAAc8O,EAAM9O,aACpBC,SAAU6O,EAAM7O,SAChBC,WAAY4O,EAAM5O,WAClBC,oBAAqB2O,EAAM3O,oBAC3BC,mBAAoB0O,EAAM1O,mBAC1BC,cAAeyO,EAAMzO,cACrBC,UAAWwO,EAAMxO,UACjBC,eAAgBuO,EAAMvO,eACtBC,iBAAkBsO,EAAMtO,iBACxBC,gBAAiBqO,EAAMrO,gBACvBC,0BAA2BoO,EAAMpO,4BAG7BuP,EThB6BvU,KACnC,MAAM,WACJ4D,EAAU,aACVE,EAAY,WACZ+I,EAAU,cACVlI,EAAa,OACbqD,EAAM,UACNpD,EAAS,0BACT2O,EAAyB,WACzBpD,EAAU,SACV5L,EAAQ,WACRC,EAAU,kBACVyP,EAAiB,iBACjBF,EAAgB,gBAChB5E,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,mBACjB4E,EAAkB,qBAClBC,EAAoB,mBACpBC,EAAkB,qBAClBC,GACErU,EAoTJ,MAAO,CACLwU,wBAnT6BtP,EAAAA,EAAAA,cAAYC,UACzC,GAAKsP,EAAL,CACA7O,QAAQyK,IAAI,4CAA4CoE,KACxD,IACE,MAAMC,GAAgBjP,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,cAAekP,GACvCE,QAAuB3O,EAAAA,EAAAA,IAAO0O,GACpC,GAAIC,EAAe1O,SAAU,CAC3B,MAAM2O,EAAiBD,EAAejP,OACtC6N,EAA0BqB,EAAe5M,QAAU,IACnDpC,QAAQyK,IAAI,0BAA2BuE,EAAe5M,OACxD,MACEpC,QAAQyK,IAAI,mDAAmDoE,KAC/DlB,EAA0B,GAE9B,CAAE,MAAO5N,GACPC,QAAQC,MAAM,sCAAuCF,GACrDpB,EAAS,wCACTgP,EAA0B,GAC5B,CAjBmB,CAiBnB,GACC,CAACA,EAA2BhP,IAiS7BsQ,gBA/RqB3P,EAAAA,EAAAA,cAAYC,UACjC,GAAK7C,EAAL,CAIAsD,QAAQyK,IAAI,qCAAqC/N,KACjD6N,GAAW,GACX5L,EAAS,MACT,IAEE,MAAMwB,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASjD,GAC1BwS,QAAgB9O,EAAAA,EAAAA,IAAOD,GAE7B,IAAIL,EAA0B,KAE9B,GAAIoP,EAAQ7O,SAEVP,EAAOoP,EAAQpP,YAGf,IACEA,QAAaqP,EAAAA,EAAoBF,eAAevS,EAClD,CAAE,MAAO0S,GACP,CAIJ,GAAItP,EACFf,EAAce,GACdd,EAAUc,EAAKsC,QAAU,IAEzBkM,EAAmBxO,EAAKyJ,kBAAoBzJ,EAAKuP,eAAiB,CAACvP,EAAKuP,gBAAkB,KAC1Fd,EAAqBzO,EAAK0J,oBAAsB1J,EAAKwP,iBAAmB,CAACxP,EAAKwP,kBAAoB,KAClGd,EAAmB1O,EAAK2J,kBAAoB3J,EAAKyP,eAAiB,CAACzP,EAAKyP,gBAAkB,KAC1Fd,EAAqB3O,EAAK4J,mBAAqB,QAC1C,CAEL,MAAM9M,EAAOoB,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOL,IAC3CqC,EAAc,CACZhC,GAAIL,EACJtB,OAAW,OAAJwB,QAAI,IAAJA,OAAI,EAAJA,EAAMxB,QAAS,WACtBgH,OAAQ,GACRH,aAAa,IAAIC,MAAOC,gBAE1BnD,EAAU,IAEVsP,EAAmB,IACnBC,EAAqB,IACrBC,EAAmB,IACnBC,EAAqB,GACvB,CACF,CAAE,MAAO1O,GACPpB,EAAS,sCACTqB,QAAQC,MAAMF,GACdhB,EAAc,MACdC,EAAU,GACZ,CAAC,QACCuL,GAAW,EACb,CAtDA,MAFEvK,QAAQyK,IAAI,uCAwDd,GACC,CAACzM,EAAYuM,EAAY5L,EAAUI,EAAeC,EAAWsP,EAAoBC,EAAsBC,EAAoBC,IAqO5He,SAnOeA,KACf,MAAMC,EAAsB,CAC1B1S,GAAI,SAASmF,KAAKwN,QAClBpK,KAAM,OACNI,MAAO,YACPE,YAAa,GACbR,QAAS,GACTS,UAAU,EACVmF,OAAQ,GACRQ,SAAU,GACVqB,OAAQ,IAEV7N,EAAU,IAAIoD,EAAQqN,GAAU,EAwNhCE,oBArN2BC,IAC3B5P,QAAQyK,IAAI,mCAAoCmF,GAChD,MAAMH,EAAsB,CAC1B1S,GAAI6S,EAAa7S,GACjBuI,KAAMsK,EAAatK,KACnBI,MAAOkK,EAAalK,MACpBE,YAAagK,EAAahK,YAC1BR,QAASwK,EAAaxK,QAAUwK,EAAaxK,QAAQ5J,KAAK0K,GACrC,kBAARA,EACF,CAAER,MAAOQ,EAAK7K,MAAO6K,GAErB,CAAER,MAAOQ,EAAIR,MAAOrK,MAAO6K,EAAI7K,cAErCJ,EACL4K,SAAU+J,EAAa/J,SACvBJ,aAAcmK,EAAanK,aAC3BM,IAAK6J,EAAa7J,IAClBE,IAAK2J,EAAa3J,IAClBe,kBAAc/L,EACd4U,aAAS5U,EACT8L,gBAAY9L,EACZ6U,gBAAY7U,EACZ8U,mBAAe9U,EACfI,WAAOJ,GAGT,GAAImH,EAAOlF,MAAK2H,GAASA,EAAM9H,KAAO0S,EAAS1S,KAI3C,OAHAiD,QAAQgQ,KAAK,iCAAiCP,EAAS1S,yBACvD4B,EAAS,kBAAkB8Q,EAAS1S,sDACpCwF,YAAW,IAAM5D,EAAS,OAAO,KAIrCqB,QAAQyK,IAAI,6BAA8BgF,GAC1CzQ,EAAU,IAAIoD,EAAQqN,IACtB7Q,EAAW,gBAAgB6Q,EAAS/J,iCACpCnD,YAAW,IAAM3D,EAAW,OAAO,IAAK,EAkLxCqR,YA/KkBA,CAACvU,EAAewU,KAClC,MAAMC,EAAgB,IAAI/N,GAC1B+N,EAAczU,GAASwU,EACvBlR,EAAUmR,EAAc,EA6KxBC,YA1KmB1U,IACnBsD,EAAUoD,EAAOxE,QAAO,CAACwI,EAAGC,IAAMA,IAAM3K,IAAO,EA0K/C2U,WAvKiB9Q,UACjB,GAAKrB,GAAiB+I,EAMtB,GAAKyC,EAAL,CAKAa,GAAW,GACXvK,QAAQyK,IAAI,oDAAqDvM,GACjE8B,QAAQyK,IAAI,sBAAuBrI,GACnCpC,QAAQyK,IAAI,oBAAqBf,GAEjC,IAAK,IAADlJ,EACF,MAAM8P,EAAgBlO,EAAO5G,KAAIqJ,IAC/B,MAAM0L,EAAoB,CAAC,EAC3B,IAAK,MAAMrL,KAAOL,OACG5J,IAAf4J,EAAMK,GACRqL,EAAarL,GAAOL,EAAMK,GAE1BqL,EAAarL,GAAO,KAGxB,OAAOqL,CAAY,IAGfC,EAAgC,IACjCvJ,EACHlK,GAAImB,EACJ9C,OAAkD,QAA3CoF,EAAAxC,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,WAAa,IAAAsC,OAAA,EAA3CA,EAA6CpF,QAAS6L,EAAW7L,MACxEgH,OAAQkO,EACRrO,aAAa,IAAIC,MAAOC,cACxBoH,kBACAC,oBACAC,kBACAC,qBAII+G,EAAe,GAGrBA,EAAahT,MACXuE,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASzB,GAAesS,GACpCE,OAAM3Q,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAI4Q,MAAM,yBAAyB5Q,EAAI6Q,UAAU,KAK7DH,EAAahT,KACX0R,EAAAA,EAAoB0B,eAAeL,GAChCE,OAAM3Q,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAI4Q,MAAM,yBAAyB5Q,EAAI6Q,UAAU,WAKvDE,QAAQC,IAAIN,GAElB1R,EAAcyR,GACd5R,EAAW,0CACX2D,YAAW,IAAM3D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPC,QAAQC,MAAM,qCAAsCF,GACpDpB,EAAS,sCAAsCoB,aAAe4Q,MAAQ5Q,EAAI6Q,QAAU,kBACtF,CAAC,QACCrG,GAAW,EACb,CAjEA,MAFE5L,EAAS,+EANTA,EAAS,mDAyEX,EA6FAqS,cA1FoBA,KACpB,IAAK/J,GAAgC,IAAlB7E,EAAON,OAExB,YADAmP,MAAM,+CAIR,MAAMC,EAAmB,eACjBjK,EAAW7L,qCAEbgH,EAAO5G,KAAIqJ,IAAU,IAADsB,EAAAvB,EACpB,IAAIuM,EAAY,GAChB,OAAQtM,EAAMS,MACZ,IAAK,OACL,IAAK,SACL,IAAK,OACL,IAAK,WACH6L,EAAY,gGAEoBtM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8CACnDhB,EAAMS,2CAA2CT,EAAMe,aAAe,OAAOf,EAAMgB,SAAW,WAAa,gDAG9H,MACF,IAAK,WACHsL,EAAY,gGAEoBtM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8DACnChB,EAAMgB,SAAW,WAAa,oDACjChB,EAAMa,wCACjB,QAAbS,EAAAtB,EAAMO,eAAO,IAAAe,OAAA,EAAbA,EAAe3K,KAAIuN,GAAU,kBAAkBA,EAAO1N,UAAU0N,EAAOrD,mBAAkBkB,KAAK,MAAO,0EAI7G,MACF,IAAK,WACHuK,EAAY,0HAE8CtM,EAAM9H,OAAO8H,EAAMgB,SAAW,WAAa,iEAC1DhB,EAAM9H,OAAO8H,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,qDAG9F,MACF,IAAK,QACHsL,EAAY,gGAEoBtM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,kCACnD,QAAbjB,EAAAC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAepJ,KAAI,CAACuN,EAAQ1C,IAAM,4HAEqBxB,EAAM9H,WAAW8H,EAAM9H,MAAMsJ,aAAa0C,EAAO1N,UAAUwJ,EAAMgB,SAAW,WAAa,mEACvGhB,EAAM9H,MAAMsJ,MAAM0C,EAAOrD,kEAEjEkB,KAAK,MAAO,6CAGnB,MACF,IAAK,UACHuK,EAAY,8FAEmBtM,EAAMmC,cAAgB,yNAMrD,MACF,IAAK,SACHmK,EAAY,wEAC2CtM,EAAMkC,YAAc,oCAE3E,MACF,QACEoK,EAAY,8BAA8BtM,EAAMS,WAEpD,OAAO6L,CAAS,IACfvK,KAAK,2BAIZyH,EAAkB6C,GAClB/C,GAAiB,EAAK,EAYvB,ESnUyBiD,CAAqB,CAC7CpT,WAAYwP,EAAMxP,WAClBE,aAAcsP,EAAMtP,aACpB+I,WAAYuG,EAAMvG,WAClBlI,cAAeyO,EAAMzO,cACrBqD,OAAQoL,EAAMpL,OACdpD,UAAWwO,EAAMxO,UACjB2O,0BAA2BH,EAAMG,0BACjCpD,WAAYiD,EAAMjD,WAClB5L,SAAU6O,EAAM7O,SAChBC,WAAY4O,EAAM5O,WAClByP,kBAAmBb,EAAMa,kBACzBF,iBAAkBX,EAAMW,iBACxB5E,gBAAiBiE,EAAMjE,gBACvBC,kBAAmBgE,EAAMhE,kBACzBC,gBAAiB+D,EAAM/D,gBACvBC,kBAAmB8D,EAAM9D,kBACzB4E,mBAAoBd,EAAMc,mBAC1BC,qBAAsBf,EAAMe,qBAC5BC,mBAAoBhB,EAAMgB,mBAC1BC,qBAAsBjB,EAAMiB,wBAI9BxG,EAAAA,EAAAA,YAAU,KACRyG,EAAerP,iBAAiB,GAC/B,KAGH4I,EAAAA,EAAAA,YAAU,KACJuF,EAAMtP,cAAgBjB,EAAWuQ,EAAMtP,aAAcsP,EAAMxP,cAAgBvB,EAAW+Q,EAAMtP,aAAcsP,EAAMxP,aAAoC,kBAArBwP,EAAMhP,YACvImQ,EAAkBM,eAAezB,EAAMtP,cACvCyQ,EAAkBC,uBAAuBpB,EAAMtP,gBACtCsP,EAAMtP,cAAkBjB,EAAWuQ,EAAMtP,aAAcsP,EAAMxP,cAAevB,EAAW+Q,EAAMtP,aAAcsP,EAAMxP,aAAqC,kBAArBwP,EAAMhP,WAItIgP,EAAMtP,eAChBsP,EAAMzO,cAAc,MACpByO,EAAMxO,UAAU,IAChBwO,EAAMG,0BAA0B,IAChCH,EAAM/O,cAAc,MAPpB+O,EAAMzO,cAAc,MACpByO,EAAMxO,UAAU,IAChBwO,EAAMG,0BAA0B,KAQ9BH,EAAMtP,cAAqC,kBAArBsP,EAAMhP,YAC5BgP,EAAMG,0BAA0B,GACpC,GACC,CAACH,EAAMtP,aAAcsP,EAAMxP,WAAYwP,EAAMhP,aAqEhD,OACE1D,EAAAA,EAAAA,KAAAwJ,EAAAA,SAAA,CAAA/I,UACEI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,CAC1BiS,EAAMvN,QAASnF,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeiB,SAAEiS,EAAMvN,QACrDuN,EAAMI,UACL9S,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kBAAiBiB,SAC7BiS,EAAMI,WAGX9S,EAAAA,EAAAA,KAAA,MAAAS,SAAI,2BAEJT,EAAAA,EAAAA,KAACuW,EAAY,CACXrT,WAAYwP,EAAMxP,WAClBE,aAAcsP,EAAMtP,aACpBkF,aAhFkB1G,IAGxB,GAFA8Q,EAAMrP,gBAAgBzB,GACtB8Q,EAAM/O,cAAc,IACf/B,EAGE,CACH,MAAM4U,EAAarU,EAAWP,EAAQ8Q,EAAMxP,YACtCuT,EAAa9U,EAAWC,EAAQ8Q,EAAMxP,YACxCsT,IAAcC,IACd/D,EAAMzO,cAAc,MACpByO,EAAMxO,UAAU,IAExB,MATIwO,EAAMzO,cAAc,MACpByO,EAAMxO,UAAU,GAQpB,EAoEMR,WAAYgP,EAAMhP,WAClB6E,eAlEoBmO,IAC1BhE,EAAM/O,cAAc+S,EAAO,EAkErBlO,UAAWkK,EAAMlK,UACjBC,eAhEmBkO,KACzBjE,EAAMnP,aAAa,IACnBmP,EAAMjP,gBAAgB,IACtBiP,EAAM1O,oBAAmB,EAAK,EA8DxB0E,gBA3DoBkO,KACtBlE,EAAMtP,cAAgBjB,EAAWuQ,EAAMtP,aAAcsP,EAAMxP,cAAgBvB,EAAW+Q,EAAMtP,aAAcsP,EAAMxP,YAClH2Q,EAAkBM,eAAezB,EAAMtP,cAC9BsP,EAAMtP,eACfsP,EAAM7O,SAAS,sFACf6O,EAAMzO,cAAc,MACpByO,EAAMxO,UAAU,IAClB,IAwDKwO,EAAMK,kBACL/S,EAAAA,EAAAA,KAAC6W,EAAK,CACJvV,OAAQoR,EAAMK,gBACdxR,QAASA,KACPmR,EAAM1O,oBAAmB,GACzB0O,EAAM/O,cAAc,IACpB+O,EAAMnP,aAAa,IACnBmP,EAAMjP,gBAAgB,GAAG,EAE3BnD,MACuB,qBAArBoS,EAAMhP,WAAoC,yBAC1CgP,EAAMtP,cAAqC,qBAArBsP,EAAMhP,WAAoC,4BAAmF,QAAnF8O,EAA4BE,EAAMxP,WAAWnB,MAAKC,GAAKA,EAAEC,KAAOyQ,EAAMtP,sBAAa,IAAAoP,OAAA,EAAvDA,EAAyDlS,SACrJ,oBACDG,UAEDI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAeiB,SAAA,EAC5BT,EAAAA,EAAAA,KAAA,SACEwK,KAAK,OACLM,YAAY,oCACZvK,MAAOmS,EAAMpP,UACb6F,SAAW1H,GAAMiR,EAAMnP,aAAa9B,EAAE4H,OAAO9I,MAAMiQ,cAAc1K,QAAQ,OAAQ,MACjFtG,UAAU,uBAEZQ,EAAAA,EAAAA,KAAA,SACEwK,KAAK,OACLM,YAAY,eACZvK,MAAOmS,EAAMlP,aACb2F,SAAW1H,GAAMiR,EAAMjP,gBAAgBhC,EAAE4H,OAAO9I,OAChDf,UAAU,uBAEZqB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEwB,QAASoS,EAAenO,oBACxB6D,SAAUoJ,EAAMlK,YAAckK,EAAMpP,YAAcoP,EAAMlP,aACxDhE,UAAU,kBAAiBiB,SAE1BiS,EAAMlK,UAAY,cAAgB,6BAErCxI,EAAAA,EAAAA,KAAA,UAAQwB,QAASA,KACfkR,EAAM1O,oBAAmB,GACzB0O,EAAM/O,cAAc,IACpB+O,EAAMnP,aAAa,IACnBmP,EAAMjP,gBAAgB,GAAG,EACxBjE,UAAU,oBAAmBiB,SAAC,mBASxCiS,EAAMtP,eACLvC,EAAAA,EAAAA,MAAA2I,EAAAA,SAAA,CAAA/I,SAAA,GAE0B,kBAArBiS,EAAMhP,YAAkCvB,EAAWuQ,EAAMtP,aAAcsP,EAAMxP,cAAgBvB,EAAW+Q,EAAMtP,aAAcsP,EAAMxP,aAAewP,EAAMvG,cACxJnM,EAAAA,EAAAA,KAAC8W,EAAc,CACb1T,aAAcsP,EAAMtP,aACpBF,WAAYwP,EAAMxP,WAClBuG,WAAYmK,EAAelM,eAC3BgC,aAAckK,EAAe9L,oBAKX,kBAArB4K,EAAMhP,YAAkCvB,EAAWuQ,EAAMtP,aAAcsP,EAAMxP,cAAgBvB,EAAW+Q,EAAMtP,aAAcsP,EAAMxP,cACjIlD,EAAAA,EAAAA,KAAC+W,EAAmB,CAClBtI,gBAAiBiE,EAAMjE,gBACvBC,kBAAmBgE,EAAMhE,kBACzBC,gBAAiB+D,EAAM/D,gBACvBC,kBAAmB8D,EAAM9D,kBACzBC,gBA3HeI,IAC3ByD,EAAMc,mBAAmBvE,GAEzByD,EAAMe,qBAAqB,IAC3Bf,EAAMgB,mBAAmB,GAAG,EAwHhB5E,kBArHiBI,IAC7BwD,EAAMe,qBAAqBvE,GAE3BwD,EAAMgB,mBAAmB,GAAG,EAmHhB3E,gBAhHeI,IAC3BuD,EAAMgB,mBAAmBvE,EAAQ,EAgHrBH,kBA7GiBsD,IAC7BI,EAAMiB,qBAAqBrB,EAAU,IAiHP,kBAArBI,EAAMhP,YAAkCvB,EAAWuQ,EAAMtP,aAAcsP,EAAMxP,cAAgBvB,EAAW+Q,EAAMtP,aAAcsP,EAAMxP,aAAewP,EAAMvG,aACtJnM,EAAAA,EAAAA,KAACgX,EAAkB,CACjB7K,WAAYuG,EAAMvG,WAClB7E,OAAQoL,EAAMpL,OACd8E,WAAYyH,EAAkBa,SAC9BrI,cAAewH,EAAkBsB,YACjC7I,cAAeuH,EAAkByB,YACjC/I,OAAQsH,EAAkB0B,WAC1B/I,UAAWqH,EAAkBqC,cAC7BzJ,QAASiG,EAAMjG,UAKG,kBAArBiG,EAAMhP,cAAoCvB,EAAWuQ,EAAMtP,aAAcsP,EAAMxP,aAAevB,EAAW+Q,EAAMtP,aAAcsP,EAAMxP,eAClIlD,EAAAA,EAAAA,KAAA,OAAKR,UAAU,wDAAuDiB,SAAC,iLAInD,kBAArBiS,EAAMhP,aAAmCvB,EAAWuQ,EAAMtP,aAAcsP,EAAMxP,cAC7ElD,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kDAAiDiB,SAAC,6IAOrEiS,EAAMtP,cAAqC,KAArBsP,EAAMhP,aAC5B7C,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gDAA+CiB,SAAA,EAC5DT,EAAAA,EAAAA,KAAA,KAAAS,SAAG,+FACHT,EAAAA,EAAAA,KAAA,KAAAS,SAAG,wJAKNiS,EAAMO,eAAiBP,EAAM9K,cAC5B/G,EAAAA,EAAAA,MAACgW,EAAK,CACJvV,OAAQoR,EAAMO,cACd1R,QAASA,KACPmR,EAAMtO,kBAAiB,GACvBsO,EAAMjP,gBAAgB,IACtBiP,EAAMvO,eAAe,KAAK,EAE5B7D,MAAO,gBAAgBoS,EAAM9K,YAAYtH,QAAQG,SAAA,EAEjDT,EAAAA,EAAAA,KAAA,SACEwK,KAAK,OACLjK,MAAOmS,EAAMlP,aACb2F,SAAW1H,GAAMiR,EAAMjP,gBAAgBhC,EAAE4H,OAAO9I,OAChDuK,YAAY,mBACZtL,UAAU,uBAEZqB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEwB,QAASoS,EAAejM,iBACxBnI,UAAU,kBACV8J,SAAUoJ,EAAMlK,YAAckK,EAAMlP,aAAawI,OAAOvL,SAEvDiS,EAAMlK,UAAY,cAAgB,kBAErCxI,EAAAA,EAAAA,KAAA,UACEwB,QAASA,KACPkR,EAAMtO,kBAAiB,GACvBsO,EAAMjP,gBAAgB,IACtBiP,EAAMvO,eAAe,KAAK,EAE5B3E,UAAU,oBAAmBiB,SAC9B,iBAONiS,EAAMS,wBAA0BT,EAAMQ,eACrCrS,EAAAA,EAAAA,MAACgW,EAAK,CACJvV,OAAQoR,EAAMS,uBACd5R,QAASA,IAAMmR,EAAMpO,2BAA0B,GAC/ChE,MAAM,mBAAkBG,SAAA,EAExBI,EAAAA,EAAAA,MAAA,KAAAJ,SAAA,CAAG,+CAAoG,QAAxDgS,EAACC,EAAMxP,WAAWnB,MAAKC,GAAKA,EAAEC,KAAOyQ,EAAMQ,sBAAa,IAAAT,OAAA,EAAvDA,EAAyDnS,MAAM,qGAC/GO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEwB,QAASoS,EAAe7L,oBACxBvI,UAAU,iBACV8J,SAAUoJ,EAAMlK,UAAU/H,SAEzBiS,EAAMlK,UAAY,cAAgB,oBAErCxI,EAAAA,EAAAA,KAAA,UACEwB,QAASA,IAAMkR,EAAMpO,2BAA0B,GAC/C9E,UAAU,oBAAmBiB,SAC9B,kBAQPT,EAAAA,EAAAA,KAAC6W,EAAK,CACJvV,OAAQoR,EAAMU,cACd7R,QAASA,IAAMmR,EAAMW,kBAAiB,GACtC/S,MAAM,eAAcG,UAEpBT,EAAAA,EAAAA,KAAA,OAAKiX,wBAAyB,CAAEC,OAAQxE,EAAMY,wBAGjD,E,2MEpXA,SAAS6D,EAAwBxY,GACtC,OAAOC,EAAAA,EAAAA,IAAqB,cAAeD,EAC7C,EACwBE,EAAAA,EAAAA,GAAuB,cAAe,CAAC,OAAQ,YAAa,QAAS,sBAAuB,UAAW,UAAW,UAAW,oBCArJ,MACA,GAD8BA,EAAAA,EAAAA,GAAuB,oBAAqB,CAAC,OAAQ,eAAgB,QAAS,sBAAuB,WAAY,UAAW,UAAW,aCH9J,SAASuY,EAA8CzY,GAC5D,OAAOC,EAAAA,EAAAA,IAAqB,6BAA8BD,EAC5D,EACuCE,EAAAA,EAAAA,GAAuB,6BAA8B,CAAC,OAAQ,mBAArG,MCgBMwY,GAA8BtY,EAAAA,EAAAA,IAAO,MAAO,CAChDE,KAAM,6BACNN,KAAM,OACN2Y,kBAAmBA,CAAChY,EAAOiY,KACzB,MAAM,WACJ5X,GACEL,EACJ,MAAO,CAACiY,EAAOzX,KAAMH,EAAW6X,gBAAkBD,EAAOC,eAAe,GAPxCzY,CASjC,CACD0Y,SAAU,WACVC,MAAO,GACPC,IAAK,MACLC,UAAW,mBACXC,SAAU,CAAC,CACTvY,MAAO+B,IAAA,IAAC,WACN1B,GACD0B,EAAA,OAAK1B,EAAW6X,cAAc,EAC/BxO,MAAO,CACL0O,MAAO,OAUPI,EAAuC3Y,EAAAA,YAAiB,SAAiCC,EAASC,GACtG,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,gCAEF,UACJO,KACGE,GACDJ,EACEyY,EAAU5Y,EAAAA,WAAiB6Y,EAAAA,GAC3BrY,EAAa,IACdL,EACHkY,eAAgBO,EAAQP,gBAEpB5X,EArDkBD,KACxB,MAAM,eACJ6X,EAAc,QACd5X,GACED,EACEsY,EAAQ,CACZnY,KAAM,CAAC,OAAQ0X,GAAkB,mBAEnC,OAAO3X,EAAAA,EAAAA,GAAeoY,EAAOb,EAA+CxX,EAAQ,EA6CpEG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKqX,EAA6B,CACpD7X,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BG,WAAYA,EACZN,IAAKA,KACFK,GAEP,IAuBAoY,EAAwBI,QAAU,0BAClC,UCtDaC,IAAepZ,EAAAA,EAAAA,IAAO,MAAO,CACxCE,KAAM,cACNN,KAAM,OACN2Y,kBAzB+BA,CAAChY,EAAOiY,KACvC,MAAM,WACJ5X,GACEL,EACJ,MAAO,CAACiY,EAAOzX,KAAMH,EAAWyY,OAASb,EAAOa,MAAiC,eAA1BzY,EAAW0Y,YAA+Bd,EAAOe,oBAAqB3Y,EAAW4Y,SAAWhB,EAAOgB,SAAU5Y,EAAW6X,gBAAkBD,EAAOiB,SAAU7Y,EAAW8Y,gBAAkBlB,EAAOtW,QAAStB,EAAW+Y,oBAAsBnB,EAAOoB,gBAAgB,GAkB7R5Z,EAIzB6Z,EAAAA,EAAAA,IAAUvX,IAAA,IAAC,MACZwX,GACDxX,EAAA,MAAM,CACLyX,QAAS,OACTC,eAAgB,aAChBV,WAAY,SACZZ,SAAU,WACVuB,eAAgB,OAChBC,MAAO,OACPC,UAAW,aACXC,UAAW,OACXtB,SAAU,CAAC,CACTvY,MAAO8Z,IAAA,IAAC,WACNzZ,GACDyZ,EAAA,OAAMzZ,EAAW8Y,cAAc,EAChCzP,MAAO,CACLqQ,WAAY,EACZnY,cAAe,IAEhB,CACD5B,MAAOga,IAAA,IAAC,WACN3Z,GACD2Z,EAAA,OAAM3Z,EAAW8Y,gBAAkB9Y,EAAWyY,KAAK,EACpDpP,MAAO,CACLqQ,WAAY,EACZnY,cAAe,IAEhB,CACD5B,MAAOia,IAAA,IAAC,WACN5Z,GACD4Z,EAAA,OAAM5Z,EAAW8Y,iBAAmB9Y,EAAW6X,cAAc,EAC9DxO,MAAO,CACLC,YAAa,GACbuQ,aAAc,KAEf,CACDla,MAAOma,IAAA,IAAC,WACN9Z,GACD8Z,EAAA,OAAM9Z,EAAW8Y,kBAAoB9Y,EAAWgZ,eAAe,EAChE3P,MAAO,CAGLwQ,aAAc,KAEf,CACDla,MAAOoa,IAAA,IAAC,WACN/Z,GACD+Z,EAAA,QAAO/Z,EAAWgZ,eAAe,EAClC3P,MAAO,CACL,CAAC,QAAQ2Q,EAAsB7Z,QAAS,CACtC0Z,aAAc,MAGjB,CACDla,MAAO,CACL+Y,WAAY,cAEdrP,MAAO,CACLqP,WAAY,eAEb,CACD/Y,MAAOsa,IAAA,IAAC,WACNja,GACDia,EAAA,OAAKja,EAAW4Y,OAAO,EACxBvP,MAAO,CACL6Q,aAAc,cAAchB,EAAMiB,MAAQjB,GAAOkB,QAAQxB,UACzDyB,eAAgB,gBAEjB,CACD1a,MAAO2a,IAAA,IAAC,WACNta,GACDsa,EAAA,OAAKta,EAAWua,MAAM,EACvBlR,MAAO,CACLmR,WAAYtB,EAAMuB,YAAYC,OAAO,mBAAoB,CACvDC,SAAUzB,EAAMuB,YAAYE,SAASC,WAEvC,UAAW,CACTvB,eAAgB,OAChBnL,iBAAkBgL,EAAMiB,MAAQjB,GAAOkB,QAAQrD,OAAO8D,MAEtD,uBAAwB,CACtB3M,gBAAiB,kBAItB,CACDvO,MAAOmb,IAAA,IAAC,WACN9a,GACD8a,EAAA,OAAK9a,EAAW+Y,kBAAkB,EACnC1P,MAAO,CAGLwQ,aAAc,MAGnB,KACKkB,IAAoB3b,EAAAA,EAAAA,IAAO,KAAM,CACrCE,KAAM,cACNN,KAAM,aAFkBI,CAGvB,CACD0Y,SAAU,aAiPZ,GA3O8BtY,EAAAA,YAAiB,SAAkBC,EAASC,GACxE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,iBAEF,WACJoZ,EAAa,SACb5X,SAAUka,EAAY,UACtBnb,EACA2B,UAAWyZ,EAAa,WACxBC,EAAa,CAAC,EAAC,gBACfC,EAAkB,CAAC,EAAC,mBACpBC,EAAqB,KACrBC,gBACExb,UAAWyb,KACRD,GACD,CAAC,EAAC,MACN5C,GAAQ,EAAK,eACbZ,GAAiB,EAAK,eACtBiB,GAAiB,EAAK,QACtBF,GAAU,EAAK,gBACfI,EAAe,UACfuC,EAAY,CAAC,EAAC,MACdjD,EAAQ,CAAC,KACNvY,GACDJ,EACEyY,EAAU5Y,EAAAA,WAAiB6Y,EAAAA,GAC3BmD,EAAehc,EAAAA,SAAc,KAAM,CACvCiZ,MAAOA,GAASL,EAAQK,QAAS,EACjCC,aACAb,oBACE,CAACa,EAAYN,EAAQK,MAAOA,EAAOZ,IACjC4D,EAAcjc,EAAAA,OAAa,MAC3BsB,EAAWtB,EAAAA,SAAekc,QAAQV,GAGlCjC,EAAqBjY,EAASuG,SAAUsU,EAAAA,EAAAA,GAAa7a,EAASA,EAASuG,OAAS,GAAI,CAAC,4BACrFrH,EAAa,IACdL,EACH+Y,aACAD,MAAO+C,EAAa/C,MACpBZ,iBACAiB,iBACAF,UACAG,sBAEI9Y,EA5KkBD,KACxB,MAAM,WACJ0Y,EAAU,QACVzY,EAAO,MACPwY,EAAK,eACLZ,EAAc,eACdiB,EAAc,QACdF,EAAO,mBACPG,GACE/Y,EACEsY,EAAQ,CACZnY,KAAM,CAAC,OAAQsY,GAAS,SAAUZ,GAAkB,WAAYiB,GAAkB,UAAWF,GAAW,UAA0B,eAAfF,GAA+B,sBAAuBK,GAAsB,mBAC/L6C,UAAW,CAAC,cAEd,OAAO1b,EAAAA,EAAAA,GAAeoY,EAAOd,EAAyBvX,EAAQ,EA8J9CG,CAAkBJ,GAC5B6b,GAAYC,EAAAA,EAAAA,GAAWL,EAAa/b,GACpCqc,EAAOzD,EAAMnY,MAAQ+a,EAAWa,MAAQvD,GACxCwD,EAAYT,EAAUpb,MAAQgb,EAAgBhb,MAAQ,CAAC,EACvD8b,EAAiB,CACrBpc,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAM6b,EAAUnc,UAAWA,MAChDE,GAEL,IAAImc,EAAYjB,GAAiB,KAGjC,OAAIlC,GAEFmD,EAAaD,EAAeza,WAAcyZ,EAAwBiB,EAAR,MAG/B,OAAvBd,IACgB,OAAdc,EACFA,EAAY,MAC0B,OAA7BD,EAAeza,YACxBya,EAAeza,UAAY,SAGXnB,EAAAA,EAAAA,KAAKgY,EAAAA,EAAY8D,SAAU,CAC7Cvb,MAAO4a,EACP1a,UAAuBI,EAAAA,EAAAA,MAAM6Z,GAAmB,CAC9CtZ,GAAI2Z,EACJvb,WAAWS,EAAAA,EAAAA,GAAKL,EAAQ2b,UAAWN,GACnC5b,IAAKmc,EACL7b,WAAYA,KACTqb,EACHva,SAAU,EAAcT,EAAAA,EAAAA,KAAK0b,EAAM,IAC9BC,OACEI,EAAAA,EAAAA,GAAgBL,IAAS,CAC5Bta,GAAIya,EACJlc,WAAY,IACPA,KACAgc,EAAUhc,gBAGdic,EACHnb,SAAUA,IACRA,EAASub,aAIChc,EAAAA,EAAAA,KAAKgY,EAAAA,EAAY8D,SAAU,CAC7Cvb,MAAO4a,EACP1a,UAAuBI,EAAAA,EAAAA,MAAM6a,EAAM,IAC9BC,EACHva,GAAIya,EACJxc,IAAKmc,OACAO,EAAAA,EAAAA,GAAgBL,IAAS,CAC5B/b,WAAY,IACPA,KACAgc,EAAUhc,gBAGdic,EACHnb,SAAU,CAACA,EAAUkY,IAAgC3Y,EAAAA,EAAAA,KAAK8X,EAAyB,CACjFrX,SAAUkY,QAIlB,I,qCCxPA,MAeMsD,IAAmBld,EAAAA,EAAAA,IAAO,MAAO,CACrCE,KAAM,kBACNN,KAAM,OACN2Y,kBAAmBA,CAAChY,EAAOiY,KACzB,MAAM,WACJ5X,GACEL,EACJ,MAAO,CAAC,CACN,CAAC,MAAM4c,GAAAA,EAAoBC,WAAY5E,EAAO4E,SAC7C,CACD,CAAC,MAAMD,GAAAA,EAAoBE,aAAc7E,EAAO6E,WAC/C7E,EAAOzX,KAAMH,EAAW0c,OAAS9E,EAAO8E,MAAO1c,EAAWwc,SAAWxc,EAAWyc,WAAa7E,EAAO+E,UAAW3c,EAAWyY,OAASb,EAAOa,MAAM,GAX9HrZ,CAatB,CACDwd,KAAM,WACNC,SAAU,EACVC,UAAW,EACXC,aAAc,EACd,CAAC,IAAIC,GAAAA,EAAkB7c,iBAAiBoc,GAAAA,EAAoBC,YAAa,CACvErD,QAAS,SAEX,CAAC,IAAI6D,GAAAA,EAAkB7c,iBAAiBoc,GAAAA,EAAoBE,cAAe,CACzEtD,QAAS,SAEXjB,SAAU,CAAC,CACTvY,MAAO+B,IAAA,IAAC,WACN1B,GACD0B,EAAA,OAAK1B,EAAWwc,SAAWxc,EAAWyc,SAAS,EAChDpT,MAAO,CACLyT,UAAW,EACXC,aAAc,IAEf,CACDpd,MAAO8Z,IAAA,IAAC,WACNzZ,GACDyZ,EAAA,OAAKzZ,EAAW0c,KAAK,EACtBrT,MAAO,CACLC,YAAa,QAiKnB,GA7JkC9J,EAAAA,YAAiB,SAAsBC,EAASC,GAChF,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,qBAEF,SACJwB,EAAQ,UACRjB,EAAS,kBACTod,GAAoB,EAAK,MACzBP,GAAQ,EACRF,QAASU,EAAW,uBACpBC,EACAV,UAAWW,EAAa,yBACxBC,EAAwB,MACxB/E,EAAQ,CAAC,EAAC,UACViD,EAAY,CAAC,KACVxb,GACDJ,GACE,MACJ8Y,GACEjZ,EAAAA,WAAiB6Y,EAAAA,GACrB,IAAImE,EAAyB,MAAfU,EAAsBA,EAAcpc,EAC9C2b,EAAYW,EAChB,MAAMpd,EAAa,IACdL,EACHsd,oBACAP,QACAF,UAAWA,EACXC,YAAaA,EACbhE,SAEIxY,EAvFkBD,KACxB,MAAM,QACJC,EAAO,MACPyc,EAAK,QACLF,EAAO,UACPC,EAAS,MACThE,GACEzY,EACEsY,EAAQ,CACZnY,KAAM,CAAC,OAAQuc,GAAS,QAASjE,GAAS,QAAS+D,GAAWC,GAAa,aAC3ED,QAAS,CAAC,WACVC,UAAW,CAAC,cAEd,OAAOvc,EAAAA,EAAAA,GAAeoY,EAAO7X,GAAAA,EAA6BR,EAAQ,EA0ElDG,CAAkBJ,GAC5Bsd,EAAyB,CAC7BhF,QACAiD,UAAW,CACTiB,QAASW,EACTV,UAAWY,KACR9B,KAGAgC,EAAUC,IAAiBC,EAAAA,GAAAA,GAAQ,OAAQ,CAChD5d,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9B6d,YAAapB,GACbgB,uBAAwB,IACnBA,KACAvd,GAELC,aACAN,SAEKie,EAAaC,IAAoBH,EAAAA,GAAAA,GAAQ,UAAW,CACzD5d,UAAWI,EAAQuc,QACnBkB,YAAaG,EAAAA,EACbP,yBACAtd,gBAEK8d,EAAeC,IAAsBN,EAAAA,GAAAA,GAAQ,YAAa,CAC/D5d,UAAWI,EAAQwc,UACnBiB,YAAaG,EAAAA,EACbP,yBACAtd,eAkBF,OAhBe,MAAXwc,GAAmBA,EAAQ3R,OAASgT,EAAAA,GAAeZ,IACrDT,GAAuBnc,EAAAA,EAAAA,KAAKsd,EAAa,CACvCK,QAASvF,EAAQ,QAAU,QAC3BjX,UAAWoc,GAAkBI,aAAUxd,EAAY,UAChDod,EACH9c,SAAU0b,KAGG,MAAbC,GAAqBA,EAAU5R,OAASgT,EAAAA,GAAeZ,IACzDR,GAAyBpc,EAAAA,EAAAA,KAAKyd,EAAe,CAC3CE,QAAS,QACTzX,MAAO,mBACJwX,EACHjd,SAAU2b,MAGMvb,EAAAA,EAAAA,MAAMqc,EAAU,IAC/BC,EACH1c,SAAU,CAAC0b,EAASC,IAExB,I,0BC3IA,MAiBMwB,IAAc7e,EAAAA,EAAAA,IAAO,MAAO,CAChCE,KAAM,aACNN,KAAM,OACN2Y,kBAAmBA,CAAChY,EAAOiY,KACzB,MAAM,WACJ5X,GACEL,EACJ,MAAO,CAACiY,EAAOzX,KAAMH,EAAWke,UAAYtG,EAAOsG,SAAUtG,EAAO5X,EAAWge,SAAUhe,EAAWme,OAASvG,EAAOuG,MAAkC,aAA3Bne,EAAWoe,aAA8BxG,EAAOyG,SAAUre,EAAWse,UAAY1G,EAAO0G,SAAUte,EAAWc,UAAY8W,EAAO2G,aAAcve,EAAWc,UAAuC,aAA3Bd,EAAWoe,aAA8BxG,EAAO4G,qBAA+C,UAAzBxe,EAAWwZ,WAAoD,aAA3BxZ,EAAWoe,aAA8BxG,EAAO6G,eAAyC,SAAzBze,EAAWwZ,WAAmD,aAA3BxZ,EAAWoe,aAA8BxG,EAAO8G,cAAc,GAP3hBtf,EASjB6Z,EAAAA,EAAAA,IAAUvX,IAAA,IAAC,MACZwX,GACDxX,EAAA,MAAM,CACLid,OAAQ,EAERC,WAAY,EACZC,YAAa,EACbC,YAAa,QACb3Q,aAAc+K,EAAMiB,MAAQjB,GAAOkB,QAAQxB,QAC3CmG,kBAAmB,OACnB7G,SAAU,CAAC,CACTvY,MAAO,CACLue,UAAU,GAEZ7U,MAAO,CACLyO,SAAU,WACVkH,OAAQ,EACRC,KAAM,EACN3F,MAAO,SAER,CACD3Z,MAAO,CACLwe,OAAO,GAET9U,MAAO,CACL8E,YAAa+K,EAAMiB,KAAO,QAAQjB,EAAMiB,KAAKC,QAAQ8E,0BAA2BC,EAAAA,GAAAA,IAAMjG,EAAMkB,QAAQxB,QAAS,OAE9G,CACDjZ,MAAO,CACLqe,QAAS,SAEX3U,MAAO,CACL+V,WAAY,KAEb,CACDzf,MAAO,CACLqe,QAAS,SACTI,YAAa,cAEf/U,MAAO,CACL+V,WAAYlG,EAAMmG,QAAQ,GAC1BC,YAAapG,EAAMmG,QAAQ,KAE5B,CACD1f,MAAO,CACLqe,QAAS,SACTI,YAAa,YAEf/U,MAAO,CACLyT,UAAW5D,EAAMmG,QAAQ,GACzBtC,aAAc7D,EAAMmG,QAAQ,KAE7B,CACD1f,MAAO,CACLye,YAAa,YAEf/U,MAAO,CACLkW,OAAQ,OACRR,kBAAmB,EACnBS,iBAAkB,SAEnB,CACD7f,MAAO,CACL2e,UAAU,GAEZjV,MAAO,CACLoW,UAAW,UACXF,OAAQ,SAET,CACD5f,MAAO8Z,IAAA,IAAC,WACNzZ,GACDyZ,EAAA,QAAOzZ,EAAWc,QAAQ,EAC3BuI,MAAO,CACL8P,QAAS,OACTK,UAAW,SACXkG,OAAQ,EACRC,eAAgB,QAChBC,gBAAiB,QACjB,sBAAuB,CACrBC,QAAS,KACTJ,UAAW,YAGd,CACD9f,MAAOga,IAAA,IAAC,WACN3Z,GACD2Z,EAAA,OAAK3Z,EAAWc,UAAuC,aAA3Bd,EAAWoe,WAA0B,EAClE/U,MAAO,CACL,sBAAuB,CACrBiQ,MAAO,OACPwG,UAAW,eAAe5G,EAAMiB,MAAQjB,GAAOkB,QAAQxB,UACvD+G,eAAgB,aAGnB,CACDhgB,MAAOia,IAAA,IAAC,WACN5Z,GACD4Z,EAAA,MAAgC,aAA3B5Z,EAAWoe,aAA8Bpe,EAAWc,QAAQ,EAClEuI,MAAO,CACL0W,cAAe,SACf,sBAAuB,CACrBR,OAAQ,OACRS,WAAY,eAAe9G,EAAMiB,MAAQjB,GAAOkB,QAAQxB,UACxDgH,gBAAiB,aAGpB,CACDjgB,MAAOma,IAAA,IAAC,WACN9Z,GACD8Z,EAAA,MAA8B,UAAzB9Z,EAAWwZ,WAAoD,aAA3BxZ,EAAWoe,WAA0B,EAC/E/U,MAAO,CACL,YAAa,CACXiQ,MAAO,OAET,WAAY,CACVA,MAAO,SAGV,CACD3Z,MAAOoa,IAAA,IAAC,WACN/Z,GACD+Z,EAAA,MAA8B,SAAzB/Z,EAAWwZ,WAAmD,aAA3BxZ,EAAWoe,WAA0B,EAC9E/U,MAAO,CACL,YAAa,CACXiQ,MAAO,OAET,WAAY,CACVA,MAAO,UAId,KACK2G,IAAiB7gB,EAAAA,EAAAA,IAAO,OAAQ,CACpCE,KAAM,aACNN,KAAM,UACN2Y,kBAAmBA,CAAChY,EAAOiY,KACzB,MAAM,WACJ5X,GACEL,EACJ,MAAO,CAACiY,EAAOsI,QAAoC,aAA3BlgB,EAAWoe,aAA8BxG,EAAOuI,gBAAgB,GAPrE/gB,EASpB6Z,EAAAA,EAAAA,IAAUgB,IAAA,IAAC,MACZf,GACDe,EAAA,MAAM,CACLd,QAAS,eACT7P,YAAa,QAAQ4P,EAAMmG,QAAQ,YACnCxF,aAAc,QAAQX,EAAMmG,QAAQ,YACpCe,WAAY,SACZlI,SAAU,CAAC,CACTvY,MAAO,CACLye,YAAa,YAEf/U,MAAO,CACLqQ,WAAY,QAAQR,EAAMmG,QAAQ,YAClC9d,cAAe,QAAQ2X,EAAMmG,QAAQ,eAG1C,KACKgB,GAAuB7gB,EAAAA,YAAiB,SAAiBC,EAASC,GACtE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,gBAEF,SACJ4e,GAAW,EAAK,SAChBpd,EAAQ,UACRjB,EAAS,YACTue,EAAc,aAAY,UAC1B5c,GAAYV,GAA4B,aAAhBsd,EAA6B,MAAQ,MAAI,SACjEE,GAAW,EAAK,MAChBH,GAAQ,EAAK,KACb1L,GAAqB,OAAdjR,EAAqB,iBAAchB,GAAS,UACnDgZ,EAAY,SAAQ,QACpBwE,EAAU,eACPje,GACDJ,EACEK,EAAa,IACdL,EACHue,WACA1c,YACA8c,WACAH,QACAC,cACA3L,OACA+G,YACAwE,WAEI/d,EAtNkBD,KACxB,MAAM,SACJke,EAAQ,SACRpd,EAAQ,QACRb,EAAO,SACPqe,EAAQ,MACRH,EAAK,YACLC,EAAW,UACX5E,EAAS,QACTwE,GACEhe,EACEsY,EAAQ,CACZnY,KAAM,CAAC,OAAQ+d,GAAY,WAAYF,EAASG,GAAS,QAAyB,aAAhBC,GAA8B,WAAYE,GAAY,WAAYxd,GAAY,eAAgBA,GAA4B,aAAhBsd,GAA8B,uBAAsC,UAAd5E,GAAyC,aAAhB4E,GAA8B,iBAAgC,SAAd5E,GAAwC,aAAhB4E,GAA8B,iBACjW8B,QAAS,CAAC,UAA2B,aAAhB9B,GAA8B,oBAErD,OAAOle,EAAAA,EAAAA,GAAeoY,EAAOnX,GAAAA,EAAwBlB,EAAQ,EAuM7CG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAK4d,GAAa,CACpCxc,GAAID,EACJ3B,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9B4S,KAAMA,EACN/S,IAAKA,EACLM,WAAYA,EACZ,mBAA6B,cAATyS,GAAuC,OAAdjR,GAAsC,aAAhB4c,OAA4C5d,EAAd4d,KAC9Fre,EACHe,SAAUA,GAAwBT,EAAAA,EAAAA,KAAK4f,GAAgB,CACrDpgB,UAAWI,EAAQigB,QACnBlgB,WAAYA,EACZc,SAAUA,IACP,MAET,IAMIuf,KACFA,GAAQC,sBAAuB,GAiEjC,YCtSaC,GAAwBA,KACnC,MAAOjR,EAASK,IAActC,EAAAA,EAAAA,UAAmB,KAC1CkC,EAAWK,IAAgBvC,EAAAA,EAAAA,UAAqB,KAChDmC,EAASK,IAAcxC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAkB,IACzC7H,EAAOtB,IAAYmJ,EAAAA,EAAAA,UAAwB,OAC3CmT,EAAcC,IAAmBpT,EAAAA,EAAAA,UAAiB,IAClDqT,EAAUC,IAAetT,EAAAA,EAAAA,UAAiB,IAE3C0C,EAAkBjL,UACtB,IACEgL,GAAW,GACX5L,EAAS,MAETqB,QAAQyK,IAAI,mFAGZ,MAAM4Q,QAAsB1Q,EAAAA,EAAcC,qBAK1C,GAHA5K,QAAQyK,IAAI,wDAAoD4Q,EAAcvZ,OAAQ,WACtFoZ,EAAgBG,EAAcvZ,QAED,IAAzBuZ,EAAcvZ,OAMhB,OALA9B,QAAQyK,IAAI,+DACZL,EAAW,IACXC,EAAa,IACbC,EAAW,SACX8Q,EAAY,WAKd,MAAME,EAAgB,IAAIC,IAC1BF,EAAc/d,SAAQuP,IAChBA,EAAO9B,QAAU8B,EAAO9B,OAAOjE,QACjCwU,EAAcE,IAAI3O,EAAO9B,OAAOjE,OAClC,IAGF,MAAMsE,EAAyB1E,MAAM+U,KAAKH,GACvCnQ,OACA3P,KAAI6P,IAAU,CACbtO,GAAIsO,EAAWC,cAAc1K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzE7G,KAAMsR,MAGVrL,QAAQyK,IAAI,yDAAgDW,EAAatJ,QAGzE,MAAM4Z,EAAkB,IAAIC,IAC5BN,EAAc/d,SAAQuP,IAChBA,EAAOpB,UAAYoB,EAAOpB,SAAS3E,QAAU+F,EAAO9B,QAAU8B,EAAO9B,OAAOjE,QAC9E4U,EAAgBE,IAAI/O,EAAOpB,SAAS3E,OAAQ+F,EAAO9B,OAAOjE,OAC5D,IAGF,MAAMiF,EAA6BrF,MAAM+U,KAAKC,EAAgBG,WAC3D1Q,MAAK,CAAAhP,EAAA+X,KAAA,IAAEtI,GAAEzP,GAAG0P,GAAEqI,EAAA,OAAKtI,EAAEE,cAAcD,EAAE,IACrCrQ,KAAI4Y,IAAA,IAAE0H,EAAczQ,GAAW+I,EAAA,MAAM,CACpCrX,GAAI+e,EAAaxQ,cAAc1K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC3E7G,KAAM+hB,EACN9Q,OAAQK,EACT,IAEHrL,QAAQyK,IAAI,2DAAkDsB,EAAejK,QAG7E,MAAMkK,EAAyBqP,EAC5Bzd,QAAOiP,GAAUA,EAAO,gBAAkBA,EAAO,eAAe/F,SAChEtL,KAAIqR,IAAM,CACT9P,GAAI8P,EAAO,eACX9S,KAAM8S,EAAO,eACb7B,OAAQ6B,EAAO9B,QAAU,GACzBS,SAAUqB,EAAOpB,UAAY,GAC7BQ,WAAYY,EAAO,mBAGvB7M,QAAQyK,IAAI,yDAAgDuB,EAAalK,QA8C/E,SACEuZ,EACAtR,EACAC,EACAC,GAQA,GANAjK,QAAQyK,IAAI,wEACZzK,QAAQyK,IAAI,oDAA0C4Q,EAAcvZ,UACpE9B,QAAQyK,IAAI,0DAAgDV,EAAQjI,UACpE9B,QAAQyK,IAAI,4DAAkDT,EAAUlI,UACxE9B,QAAQyK,IAAI,0DAAgDR,EAAQnI,UAEhEmI,EAAQnI,OAAS,EAAG,CAEtB,MAAMia,EAAc9R,EAAQzO,KAAIyR,GAAKA,EAAElT,OAAMoR,OAC7CnL,QAAQyK,IAAI,8DAAoDsR,EAAY,OAC5E/b,QAAQyK,IAAI,6DAAmDsR,EAAYA,EAAYja,OAAS,OAGhG,MAAMka,EAA0C,CAAC,EACjD/R,EAAQ3M,SAAQuP,IACd,MAAMoP,EAAcpP,EAAO9S,KAAKmiB,OAAO,GAAGC,cAC1CH,EAAaC,IAAgBD,EAAaC,IAAgB,GAAK,CAAC,IAGlEjc,QAAQyK,IAAI,4DACZ2R,OAAOC,KAAKL,GAAc7Q,OAAO7N,SAAQgf,IACvCtc,QAAQyK,IAAI,uCAA6B6R,MAAWN,EAAaM,aAAkB,IAIrF,MAAMC,EAAkBtS,EAAQpN,MAAKoQ,GAAKA,EAAElT,KAAKuR,cAAcxF,SAAS,sBAClE0W,EAAqBvS,EAAQpN,MAAKoQ,GAAKA,EAAElT,KAAKuR,cAAcxF,SAAS,yBAsB3E,GApBA9F,QAAQyK,IAAI,sEAA4D8R,KACxEvc,QAAQyK,IAAI,yEAA+D+R,KAEvED,GACFvc,QAAQyK,IAAI,gEAAsD8R,EAAgBxiB,SAEhFyiB,GACFxc,QAAQyK,IAAI,mEAAyD+R,EAAmBziB,SAItFgQ,EAAQjI,OAAS,IACnB9B,QAAQyK,IAAI,sDACZV,EAAQzM,SAAQ0N,IACd,MAAMyR,EAAgBxS,EAAQrM,QAAOqP,GAAKA,EAAEjC,SAAWA,EAAOjR,OAC9DiG,QAAQyK,IAAI,uCAA6BO,EAAOjR,SAAS0iB,EAAc3a,iBAAiB,KAKxFkI,EAAUlI,OAAS,EAAG,CACxB9B,QAAQyK,IAAI,yEACWT,EAAUxO,KAAIgQ,IAAQ,CAC3CzR,KAAMyR,EAASzR,KACf2iB,MAAOzS,EAAQrM,QAAOqP,GAAKA,EAAEzB,WAAaA,EAASzR,OAAM+H,WACvDqJ,MAAK,CAACS,EAAGC,IAAMA,EAAE6Q,MAAQ9Q,EAAE8Q,QAAOC,MAAM,EAAG,IAEhCrf,SAAQkO,IACrBxL,QAAQyK,IAAI,uCAA6Be,EAASzR,SAASyR,EAASkR,gBAAgB,GAExF,CACF,CAEA1c,QAAQyK,IAAI,6DACd,CA/GMmS,CAAoBvB,EAAejQ,EAAcW,EAAgBC,GAGjE5B,EAAWgB,GACXf,EAAa0B,GACbzB,EAAW0B,GACXoP,EAAY,uBAEZpb,QAAQyK,IAAI,yDAEd,CAAE,MAAO1K,GACPC,QAAQC,MAAM,uCAAmCF,GACjDpB,EAAS,iDACTyL,EAAW,IACXC,EAAa,IACbC,EAAW,IACX4Q,EAAgB,GAChBE,EAAY,QACd,CAAC,QACC7Q,GAAW,EACb,GAQF,OAJAtC,EAAAA,EAAAA,YAAU,KACRuC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA1C,UACAtH,QACAiK,QAASM,EACTyQ,eACAE,WACD,EA4EH,MCwFA,GAvRoC0B,KAClC,MAAM,QACJ9S,EAAO,UACPC,EAAS,QACTC,EAAO,QACP1C,EAAO,MACPtH,EAAK,aACLgb,EAAY,SACZE,EAAQ,QACRjR,GACE8Q,KAEJ,GAAIzT,EACF,OACE5L,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAAClJ,QAAQ,OAAO4G,cAAc,SAASrH,WAAW,SAAS4J,EAAG,EAAExhB,SAAA,EAClET,EAAAA,EAAAA,KAACkiB,EAAAA,EAAgB,CAACC,KAAM,MACxBniB,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKyE,GAAI,CAAEC,GAAI,GAAI5hB,SAAC,0DAGxCT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,QAAQzX,MAAM,iBAAiBkc,GAAI,CAAEC,GAAI,GAAI5hB,SAAC,wEAOxE,GAAI0E,EACF,OACEtE,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAACC,EAAG,EAAExhB,SAAA,EACRT,EAAAA,EAAAA,KAACsiB,EAAAA,EAAK,CAACC,SAAS,QAAQH,GAAI,CAAEI,GAAI,GAAI/hB,SACnC0E,KAEHnF,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,QAAOld,SAAC,4FAQlC,MAAMgiB,EAAgD,CAAC,EACvDtT,EAAQ3M,SAAQuP,IACd,MAAMoP,EAAcpP,EAAO9S,KAAKmiB,OAAO,GAAGC,cAC1CoB,EAAmBtB,IAAgBsB,EAAmBtB,IAAgB,GAAK,CAAC,IAG9E,MAAMuB,EAAoBvT,EAAQzO,KAAIyR,GAAKA,EAAElT,OAAMoR,OAC7CoR,EAAkBtS,EAAQpN,MAAKoQ,GAAKA,EAAElT,KAAKuR,cAAcxF,SAAS,sBAClE0W,EAAqBvS,EAAQpN,MAAKoQ,GAAKA,EAAElT,KAAKuR,cAAcxF,SAAS,yBAGrE2X,EAAe1T,EAAQvO,KAAIwP,IAAM,CACrCjR,KAAMiR,EAAOjR,KACb2iB,MAAOzS,EAAQrM,QAAOqP,GAAKA,EAAEjC,SAAWA,EAAOjR,OAAM+H,WACnDqJ,MAAK,CAACS,EAAGC,IAAMA,EAAE6Q,MAAQ9Q,EAAE8Q,QAAOC,MAAM,EAAG,GAGzCe,EAAiB1T,EAAUxO,KAAIgQ,IAAQ,CAC3CzR,KAAMyR,EAASzR,KACf2iB,MAAOzS,EAAQrM,QAAOqP,GAAKA,EAAEzB,WAAaA,EAASzR,OAAM+H,WACvDqJ,MAAK,CAACS,EAAGC,IAAMA,EAAE6Q,MAAQ9Q,EAAE8Q,QAAOC,MAAM,EAAG,IAE/C,OACEhhB,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAACC,EAAG,EAAExhB,SAAA,EACRT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAApiB,SAAC,+CAItCT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,QAAQzX,MAAM,iBAAiB4c,WAAS,EAAAriB,SAAC,uKAM7DI,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAAClJ,QAAQ,OAAOiK,IAAK,EAAGX,GAAI,CAAEI,GAAI,EAAGQ,SAAU,QAASviB,SAAA,EAC1DT,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAO/b,UAC5BT,EAAAA,EAAAA,KAACijB,EAAAA,EAAI,CAAAxiB,UACHI,EAAAA,EAAAA,MAACqiB,EAAAA,EAAW,CAAAziB,SAAA,EACVT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKzX,MAAM,UAASzF,SAAC,mBAGzCT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAIld,SACrB0f,EAAagD,oBAEhBnjB,EAAAA,EAAAA,KAACojB,EAAAA,EAAI,CACHxY,MAAOyV,EACP8B,KAAK,QACLjc,MAAOia,EAAe,IAAO,UAAY,UACzCiC,GAAI,CAAEC,GAAI,aAMlBriB,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAO/b,UAC5BT,EAAAA,EAAAA,KAACijB,EAAAA,EAAI,CAAAxiB,UACHI,EAAAA,EAAAA,MAACqiB,EAAAA,EAAW,CAAAziB,SAAA,EACVT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKzX,MAAM,UAASzF,SAAC,aAGzCT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAIld,SACrBwO,EAAQjI,iBAMjBhH,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAO/b,UAC5BT,EAAAA,EAAAA,KAACijB,EAAAA,EAAI,CAAAxiB,UACHI,EAAAA,EAAAA,MAACqiB,EAAAA,EAAW,CAAAziB,SAAA,EACVT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKzX,MAAM,UAASzF,SAAC,eAGzCT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAIld,SACrByO,EAAUlI,iBAMnBhH,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAO/b,UAC5BT,EAAAA,EAAAA,KAACijB,EAAAA,EAAI,CAAAxiB,UACHI,EAAAA,EAAAA,MAACqiB,EAAAA,EAAW,CAAAziB,SAAA,EACVT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKzX,MAAM,UAASzF,SAAC,aAGzCT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAIld,SACrB0O,EAAQnI,oBAQnBnG,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAAClJ,QAAQ,OAAOiK,IAAK,EAAGX,GAAI,CAAEY,SAAU,QAASviB,SAAA,EACnDT,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAO/b,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACojB,GAAI,CAAEH,EAAG,GAAIxhB,SAAA,EAClBT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAApiB,SAAC,0BAItCI,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAI/hB,SAAA,EACjBT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,YAAWld,SAAC,gCAGhCT,EAAAA,EAAAA,KAACojB,EAAAA,EAAI,CACHxY,MAAOuV,EAAe,IAAO,aAAU,YACvCja,MAAOia,EAAe,IAAO,UAAY,QACzCgC,KAAK,cAITthB,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAI/hB,SAAA,EACjBT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,YAAWld,SAAC,yBAGhCI,EAAAA,EAAAA,MAAC2c,EAAAA,EAAU,CAACG,QAAQ,QAAOld,SAAA,CAAC,WACjBiiB,EAAkB,IAAM,MAAM,QAEzC7hB,EAAAA,EAAAA,MAAC2c,EAAAA,EAAU,CAACG,QAAQ,QAAOld,SAAA,CAAC,UAClBiiB,EAAkBA,EAAkB1b,OAAS,IAAM,MAAM,WAIrEnG,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAI/hB,SAAA,EACjBT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,YAAWld,SAAC,6BAGhCT,EAAAA,EAAAA,KAACojB,EAAAA,EAAI,CACHxY,MAAO6W,EAAkB,aAAU,YACnCvb,MAAOub,EAAkB,UAAY,QACrCU,KAAK,UAENV,IACC5gB,EAAAA,EAAAA,MAAC2c,EAAAA,EAAU,CAACG,QAAQ,QAAQyE,GAAI,CAAEC,GAAI,GAAI5hB,SAAA,CAAC,IACvCghB,EAAgBxiB,KAAK,WAK7B4B,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAI/hB,SAAA,EACjBT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,YAAWld,SAAC,gCAGhCT,EAAAA,EAAAA,KAACojB,EAAAA,EAAI,CACHxY,MAAO8W,EAAqB,aAAU,YACtCxb,MAAOwb,EAAqB,UAAY,QACxCS,KAAK,UAENT,IACC7gB,EAAAA,EAAAA,MAAC2c,EAAAA,EAAU,CAACG,QAAQ,QAAQyE,GAAI,CAAEC,GAAI,GAAI5hB,SAAA,CAAC,IACvCihB,EAAmBziB,KAAK,gBAOpCe,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAO/b,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACojB,GAAI,CAAEH,EAAG,GAAIxhB,SAAA,EAClBT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAApiB,SAAC,yBAGtCT,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACI,GAAI,CAAElU,UAAW,IAAKhP,SAAU,QAASuB,SAC3C6gB,OAAOC,KAAKkB,GAAoBpS,OAAO3P,KAAI8gB,IAC1C3gB,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAAclJ,QAAQ,OAAOC,eAAe,gBAAgBqJ,GAAI,CAAEI,GAAI,GAAI/hB,SAAA,EAC5EI,EAAAA,EAAAA,MAAC2c,EAAAA,EAAU,CAACG,QAAQ,QAAOld,SAAA,CAAE+gB,EAAO,QACpC3gB,EAAAA,EAAAA,MAAC2c,EAAAA,EAAU,CAACG,QAAQ,QAAOld,SAAA,CAAEgiB,EAAmBjB,GAAQ,gBAFhDA,gBAUpB3gB,EAAAA,EAAAA,MAACmhB,EAAAA,EAAG,CAAClJ,QAAQ,OAAOiK,IAAK,EAAGX,GAAI,CAAEC,GAAI,EAAGW,SAAU,QAASviB,SAAA,EAC1DT,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAO/b,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACojB,GAAI,CAAEH,EAAG,GAAIxhB,SAAA,EAClBT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAApiB,SAAC,mCAGtCT,EAAAA,EAAAA,KAACqjB,EAAAA,EAAI,CAACjL,OAAK,EAAA3X,SACRkiB,EAAajiB,KAAI,CAACwP,EAAQtP,KACzBC,EAAAA,EAAAA,MAAC1B,EAAAA,SAAc,CAAAsB,SAAA,EACbT,EAAAA,EAAAA,KAACsjB,GAAQ,CAAA7iB,UACPT,EAAAA,EAAAA,KAACujB,GAAY,CACXpH,QAASjM,EAAOjR,KAChBmd,UAAW,GAAGlM,EAAO0R,oBAGxBhhB,EAAQ+hB,EAAa3b,OAAS,IAAKhH,EAAAA,EAAAA,KAACggB,GAAO,MAPzB9P,EAAOjR,gBAcpCe,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAO/b,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACojB,GAAI,CAAEH,EAAG,GAAIxhB,SAAA,EAClBT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAApiB,SAAC,sCAGtCT,EAAAA,EAAAA,KAACgiB,EAAAA,EAAG,CAACI,GAAI,CAAElU,UAAW,IAAKhP,SAAU,QAASuB,UAC5CT,EAAAA,EAAAA,KAACqjB,EAAAA,EAAI,CAACjL,OAAK,EAAA3X,SACRmiB,EAAeliB,KAAI,CAACgQ,EAAU9P,KAC7BC,EAAAA,EAAAA,MAAC1B,EAAAA,SAAc,CAAAsB,SAAA,EACbT,EAAAA,EAAAA,KAACsjB,GAAQ,CAAA7iB,UACPT,EAAAA,EAAAA,KAACujB,GAAY,CACXpH,QAASzL,EAASzR,KAClBmd,UAAW,GAAG1L,EAASkR,oBAG1BhhB,EAAQgiB,EAAe5b,OAAS,IAAKhH,EAAAA,EAAAA,KAACggB,GAAO,MAP3BtP,EAASzR,oBAiBzCkhB,EAAe,MACdtf,EAAAA,EAAAA,MAACyhB,EAAAA,EAAK,CAACC,SAAS,UAAUH,GAAI,CAAEC,GAAI,GAAI5hB,SAAA,EACtCT,EAAAA,EAAAA,KAACwd,EAAAA,EAAU,CAACG,QAAQ,KAAIld,SAAC,8DAGzBI,EAAAA,EAAAA,MAAC2c,EAAAA,EAAU,CAACG,QAAQ,QAAOld,SAAA,CAAC,kCACM0f,EAAagD,iBAAiB,gKAMhE,EC9JV,GArIiCK,KAC/B,MAAOC,EAAUC,IAAe1W,EAAAA,EAAAA,WAAS,IAClC8F,EAAShP,IAAckJ,EAAAA,EAAAA,UAAS,KAChC7H,EAAOtB,IAAYmJ,EAAAA,EAAAA,UAAS,IAoDnC,OACEnM,EAAAA,EAAAA,MAAA,OAAKmI,MAAO,CACV/H,QAAS,OACTqd,OAAQ,SACRe,OAAQ,oBACRsE,aAAc,MACd9V,gBAAiB,WACjBpN,SAAA,EACAI,EAAAA,EAAAA,MAAA,OAAKmI,MAAO,CAAE8P,QAAS,OAAQT,WAAY,SAAU0K,IAAK,OAAQrG,aAAc,QAASjc,SAAA,CACtFtB,EAAAA,cAAoBykB,EAAAA,IAAqC,CAAEzB,KAAM,GAAIjc,MAAO,aAC7ElG,EAAAA,EAAAA,KAAA,MAAIgJ,MAAO,CAAEsV,OAAQ,EAAGpY,MAAO,WAAYzF,SAAC,yBAG9CT,EAAAA,EAAAA,KAAA,KAAGgJ,MAAO,CAAE0T,aAAc,OAAQxW,MAAO,WAAYzF,SAAC,uMAKrD0E,IACCnF,EAAAA,EAAAA,KAAA,OAAKgJ,MAAO,CACV/H,QAAS,OACTyb,aAAc,OACd7O,gBAAiB,UACjB3H,MAAO,UACPmZ,OAAQ,oBACRsE,aAAc,OACdljB,SACC0E,IAIJ2N,IACC9S,EAAAA,EAAAA,KAAA,OAAKgJ,MAAO,CACV/H,QAAS,OACTyb,aAAc,OACd7O,gBAAiB,UACjB3H,MAAO,UACPmZ,OAAQ,oBACRsE,aAAc,OACdljB,SACCqS,KAILjS,EAAAA,EAAAA,MAAA,UACEW,QA/FiBiD,UACrBif,GAAY,GACZ7f,EAAS,IACTC,EAAW,IAEX,IAEE,MAAM+f,EAAkB,CACtB5hB,GAAI,MACJ3B,MAAO,MACP4B,SAAU,GACVqF,QAAQ,EACRC,OAAQ,KACRL,YAAa,IAAIC,KACjB0c,YAAa,qDACbC,MAAO,EACP/d,KAAM,eAIFkB,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAAS,OAAQgf,SAGhC3c,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc,OAAQ,CACzC5C,GAAI,MACJhD,KAAM,MACN+G,KAAM,UACN9D,SAAU,GACVqF,QAAQ,EACRC,OAAQ,KACRL,YAAa,IAAIC,KACjB0c,YAAa,qDACbC,MAAO,IAGTjgB,EAAW,0GAGX2D,YAAW,KACTuc,OAAOC,SAASC,QAAQ,GACvB,IAEL,CAAE,MAAOjf,GACPC,QAAQC,MAAM,6BAA8BF,GAC5CpB,EAAS,uDACX,CAAC,QACC6f,GAAY,EACd,GAiDIpa,SAAUma,EACVza,MAAO,CACL/H,QAAS,YACT4M,gBAAiB4V,EAAW,UAAY,UACxCvd,MAAO,QACPmZ,OAAQ,OACRsE,aAAc,MACdQ,OAAQV,EAAW,cAAgB,UACnCW,SAAU,OACVC,WAAY,OACZvL,QAAS,OACTT,WAAY,SACZ0K,IAAK,OACLtiB,SAAA,CAEDtB,EAAAA,cAAoBykB,EAAAA,IAAqC,CAAEzB,KAAM,KACjEsB,EAAW,yBAA2B,mCAGzC5iB,EAAAA,EAAAA,MAAA,OAAKmI,MAAO,CAAEyT,UAAW,OAAQ2H,SAAU,OAAQle,MAAO,WAAYzF,SAAA,EACpET,EAAAA,EAAAA,KAAA,UAAAS,SAAQ,wBACRI,EAAAA,EAAAA,MAAA,MAAImI,MAAO,CAAEyT,UAAW,MAAOxT,YAAa,QAASxI,SAAA,EACnDT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,2CACJT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,wCACJT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,8CACJT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,iDAGJ,EC1EV,GAjD4B6jB,KAC1B,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,MAEjBC,EAAUC,KADAC,EAAAA,EAAAA,OACe3X,EAAAA,EAAAA,UAAc,QACvC4X,EAAgBC,IAAqB7X,EAAAA,EAAAA,WAAkB,GAe9D,OAbAG,EAAAA,EAAAA,YAAU,KACc1I,WACpB,GAAI8f,EAAa,CACf,MAAMO,GAAU/f,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,YAAa0f,EAAYQ,KAC3CC,QAAiB1f,EAAAA,EAAAA,IAAOwf,GAC1BE,EAASzf,UACXmf,EAAYM,EAAShgB,OAEzB,GAEFigB,EAAe,GACd,CAACV,KAGF1jB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,sBAAqBiB,SAAA,EAClCT,EAAAA,EAAAA,KAACklB,EAAAA,EAAO,CAACT,SAAUA,KACnB5jB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,CAAC,mBAE1BT,EAAAA,EAAAA,KAAA,UACEwB,QAASA,IAAMqjB,GAAmBD,GAClC5b,MAAO,CACL+V,WAAY,OACZ9d,QAAS,WACT4M,gBAAiB+W,EAAiB,UAAY,UAC9C1e,MAAO,QACPmZ,OAAQ,OACRsE,aAAc,MACdQ,OAAQ,UACRC,SAAU,QACV3jB,SAEDmkB,EAAiB,mBAAqB,iCAG3C5kB,EAAAA,EAAAA,KAACQ,EAAAA,EAAU,KACXR,EAAAA,EAAAA,KAACwjB,GAAc,IACdoB,GAAiB5kB,EAAAA,EAAAA,KAAC+hB,GAAiB,KAAM/hB,EAAAA,EAAAA,KAACuS,EAAW,SAEpD,C", "sources": ["../node_modules/@mui/material/esm/Card/cardClasses.js", "../node_modules/@mui/material/esm/Card/Card.js", "../node_modules/@mui/material/esm/ListItemText/listItemTextClasses.js", "components/shared/StatsCards.tsx", "../node_modules/@mui/material/esm/Divider/dividerClasses.js", "../node_modules/@mui/material/esm/CardContent/cardContentClasses.js", "../node_modules/@mui/material/esm/CardContent/CardContent.js", "components/shared/Modal.tsx", "components/admin/business/utils/cardUtils.ts", "components/admin/business/hooks/useCardManagement.ts", "components/admin/business/hooks/usePageConfiguration.ts", "components/admin/business/components/CardSelector.tsx", "components/admin/business/components/CardManagement.tsx", "components/admin/business/components/FieldConfigItem.tsx", "components/admin/business/components/PageBuilderContent.tsx", "components/admin/business/types/PageBuilderTypes.ts", "components/admin/business/hooks/useOfficeDataSimple.ts", "components/admin/business/components/CheckboxDropdown.tsx", "components/admin/business/components/ReportConfiguration.tsx", "components/admin/business/PageBuilder.tsx", "components/admin/business/hooks/usePageBuilderState.ts", "../node_modules/@mui/material/esm/ListItem/listItemClasses.js", "../node_modules/@mui/material/esm/ListItemButton/listItemButtonClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/listItemSecondaryActionClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/ListItemSecondaryAction.js", "../node_modules/@mui/material/esm/ListItem/ListItem.js", "../node_modules/@mui/material/esm/ListItemText/ListItemText.js", "../node_modules/@mui/material/esm/Divider/Divider.js", "components/admin/business/hooks/useOfficeDataEnhanced.ts", "components/admin/business/OfficeLoadingTest.tsx", "components/admin/AddMMUCategory.tsx", "components/admin/AdminPage.tsx"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root']);\nexport default cardClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getCardUtilityClass } from \"./cardClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardUtilityClass, classes);\n};\nconst CardRoot = styled(Paper, {\n  name: 'MuiCard',\n  slot: 'Root'\n})({\n  overflow: 'hidden'\n});\nconst Card = /*#__PURE__*/React.forwardRef(function Card(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCard'\n  });\n  const {\n    className,\n    raised = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    raised\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardRoot, {\n    className: clsx(classes.root, className),\n    elevation: raised ? 8 : undefined,\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Card.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the card will use raised styling.\n   * @default false\n   */\n  raised: chainPropTypes(PropTypes.bool, props => {\n    if (props.raised && props.variant === 'outlined') {\n      return new Error('MUI: Combining `raised={true}` with `variant=\"outlined\"` has no effect.');\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Card;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemTextUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemText', slot);\n}\nconst listItemTextClasses = generateUtilityClasses('MuiListItemText', ['root', 'multiline', 'dense', 'inset', 'primary', 'secondary']);\nexport default listItemTextClasses;", "import React from 'react';\nimport './StatsCards.css';\n\nconst stats = [\n  { title: 'SB Accounts', value: 123456 },\n  { title: 'BD Revenue', value: '₹24,343' },\n  { title: 'No. Aadhaar Trans', value: 1259 },\n  { title: 'PLI', value: '₹99,99,999' }\n];\n\nconst StatsCards: React.FC = () => {\n  return (\n    <div className=\"stats-grid\">\n      {stats.map((stat, index) => (\n        <div className={`stat-card card-${index}`} key={index}>\n          <h3>{stat.title}</h3>\n          <p className=\"stat-value\">{stat.value}</p>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default StatsCards;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDividerUtilityClass(slot) {\n  return generateUtilityClass('MuiDivider', slot);\n}\nconst dividerClasses = generateUtilityClasses('MuiDivider', ['root', 'absolute', 'fullWidth', 'inset', 'middle', 'flexItem', 'light', 'vertical', 'withChildren', 'withChildrenVertical', 'textAlignRight', 'textAlignLeft', 'wrapper', 'wrapperVertical']);\nexport default dividerClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardContentUtilityClass(slot) {\n  return generateUtilityClass('MuiCardContent', slot);\n}\nconst cardContentClasses = generateUtilityClasses('MuiCardContent', ['root']);\nexport default cardContentClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getCardContentUtilityClass } from \"./cardContentClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardContentUtilityClass, classes);\n};\nconst CardContentRoot = styled('div', {\n  name: 'MuiCardContent',\n  slot: 'Root'\n})({\n  padding: 16,\n  '&:last-child': {\n    paddingBottom: 24\n  }\n});\nconst CardContent = /*#__PURE__*/React.forwardRef(function CardContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardContent'\n  });\n  const {\n    className,\n    component = 'div',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardContentRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardContent;", "import React from 'react';\nimport './Modal.css';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n}\n\nconst Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={e => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2>{title}</h2>\n          <button className=\"close-button\" onClick={onClose}>&times;</button>\n        </div>\n        <div className=\"modal-body\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;", "import { <PERSON>a<PERSON>older, FaFile<PERSON>lt, Fa<PERSON>og, FaFolderOpen } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\n// Helper function to generate card style (icon and color)\nexport const generateCardStyle = (title: string) => {\n  const hash = title\n    .split('')\n    .reduce((acc, char) => acc + char.charCodeAt(0), 0);\n  \n  const icons = [FaFolder, FaFileAlt, FaCog, FaFolderOpen]; // Add more icons if needed\n  const colors = ['#FFC107', '#2196F3', '#4CAF50', '#E91E63', '#9C27B0'];\n\n  const icon = icons[hash % icons.length];\n  const color = colors[hash % colors.length];\n  \n  return { icon, color };\n};\n\n// Helper function to check if a card is a main card\nexport const isMainCard = (cardId: string, allCategories: Category[]): boolean => {\n  const card = allCategories.find(c => c.id === cardId);\n  return card ? !card.parentId : false;\n};\n\n// Helper function to check if a card is a leaf card\nexport const isLeafCard = (cardId: string, allCategories: Category[]): boolean => {\n  return !allCategories.some(c => c.parentId === cardId);\n};\n\n// Helper function to organize cards into a tree structure\nexport const organizeCards = (list: Category[]): Category[] => {\n  const map: { [key: string]: Category } = {};\n  const roots: Category[] = [];\n  list.forEach(item => {\n    map[item.id] = { ...item, children: [] }; \n  });\n  list.forEach(item => {\n    if (item.parentId && map[item.parentId]) {\n      map[item.parentId].children?.push(map[item.id]);\n    } else {\n      roots.push(map[item.id]);\n    }\n  });\n  return roots;\n};\n\n// Helper function to get all descendant IDs\nexport const getAllDescendantIds = (parentId: string, allCategories: Category[]): string[] => {\n  let descendants: string[] = [];\n  const children = allCategories.filter(c => c.parentId === parentId);\n  for (const child of children) {\n    descendants.push(child.id);\n    descendants = descendants.concat(getAllDescendantIds(child.id, allCategories));\n  }\n  return descendants;\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc, collection, getDocs, writeBatch, deleteDoc, updateDoc } from 'firebase/firestore';\nimport { Category } from '../types/PageBuilderTypes';\nimport { generateCardStyle, getAllDescendantIds } from '../utils/cardUtils';\n\ninterface UseCardManagementProps {\n  categories: Category[];\n  setCategories: (categories: Category[]) => void;\n  selectedCard: string;\n  setSelectedCard: (card: string) => void;\n  newCardId: string;\n  setNewCardId: (id: string) => void;\n  newCardTitle: string;\n  setNewCardTitle: (title: string) => void;\n  actionType: string;\n  setActionType: (type: string) => void;\n  setIsLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setShowConfirmModal: (show: boolean) => void;\n  setIsAddingNewCard: (adding: boolean) => void;\n  setPageConfig: (config: any) => void;\n  setFields: (fields: any[]) => void;\n  setEditingCard: (card: Category | null) => void;\n  setShowEditModal: (show: boolean) => void;\n  setCardToDelete: (id: string | null) => void;\n  setShowDeleteConfirmModal: (show: boolean) => void;\n}\n\nexport const useCardManagement = (props: UseCardManagementProps) => {\n  const {\n    categories,\n    setCategories,\n    selectedCard,\n    setSelectedCard,\n    newCardId,\n    setNewCardId,\n    newCardTitle,\n    setNewCardTitle,\n    actionType,\n    setActionType,\n    setIsLoading,\n    setError,\n    setSuccess,\n    setShowConfirmModal,\n    setIsAddingNewCard,\n    setPageConfig,\n    setFields,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n  } = props;\n\n  const fetchCategories = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const querySnapshot = await getDocs(collection(db, 'categories'));\n      const fetchedCategories = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Category));\n      setCategories(fetchedCategories);\n    } catch (err) {\n      setError('Failed to fetch categories.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [setCategories, setIsLoading, setError]);\n\n  const checkDuplicateId = async (id: string): Promise<boolean> => {\n    const docRef = doc(db, 'categories', id);\n    const docSnap = await getDoc(docRef);\n    return docSnap.exists();\n  };\n\n  const handleAddNewCard = async () => {\n    if (!newCardId || !newCardTitle) {\n      setError('Report ID and Title are required.');\n      return;\n    }\n    setIsLoading(true);\n    const isDuplicate = await checkDuplicateId(newCardId);\n    if (isDuplicate) {\n      setError('This Report ID already exists. Please use a unique ID.');\n      setIsLoading(false);\n      return;\n    }\n    setIsLoading(false);\n    setShowConfirmModal(true);\n  };\n\n  const handleConfirmCreate = async () => {\n    if (!newCardId || !newCardTitle) {\n        setError('Report ID and Title cannot be empty.');\n        setShowConfirmModal(false);\n        return;\n    }\n    let parentIdToSet: string | null = null;\n    if (actionType === 'createNestedCard' && selectedCard) {\n      parentIdToSet = selectedCard;\n    } else if (actionType === 'addNewCardGlobal') {\n      parentIdToSet = null; \n    } else if (selectedCard && actionType !== 'addNewCardGlobal') {\n        parentIdToSet = selectedCard;\n    } else if (!selectedCard && actionType !== 'createNestedCard') { \n        parentIdToSet = null;\n    }\n\n    const parentPath = parentIdToSet ? categories.find(c => c.id === parentIdToSet)?.path : '/categories';\n    const newPath = `${parentPath}/${newCardId}`.replace(/\\/+/g, '/');\n\n    try {\n      setIsLoading(true);\n      setShowConfirmModal(false);\n      const cardRef = doc(db, 'categories', newCardId);\n      const { icon: generatedIcon, color: generatedColor } = generateCardStyle(newCardTitle);\n      \n      await setDoc(cardRef, {\n        id: newCardId,\n        title: newCardTitle,\n        path: newPath,\n        parentId: parentIdToSet,\n        lastUpdated: new Date().toISOString(),\n        icon: generatedIcon.name,\n        color: generatedColor,\n        fields: [],\n        isPage: true,\n        pageId: newCardId,\n      });\n  \n      await fetchCategories(); \n      \n      setNewCardId('');\n      setNewCardTitle('');\n      setIsAddingNewCard(false);\n      setActionType(''); \n      setSelectedCard(newCardId);\n      setSuccess(`Report \"${newCardTitle}\" has been created successfully!`);\n      setTimeout(() => setSuccess(null), 3000);\n      \n    } catch (err) {\n      setError('Error creating new report. Check console for details.');\n      console.error('Error creating card:', err);\n    } finally {\n      setIsLoading(false); \n    }\n  };\n\n  const handleEditCard = (card: Category) => {\n    setEditingCard(card);\n    setNewCardTitle(card.title);\n    setShowEditModal(true);\n  };\n\n  const handleUpdateCard = async () => {\n    const editingCard = categories.find(c => c.id === selectedCard);\n    if (!editingCard || !newCardTitle) return;\n    try {\n      setIsLoading(true);\n      const cardRef = doc(db, 'categories', editingCard.id);\n      await updateDoc(cardRef, { title: newCardTitle, lastUpdated: new Date().toISOString() });\n      await fetchCategories();\n      setShowEditModal(false);\n      setEditingCard(null);\n      setNewCardTitle('');\n      setSuccess('Report updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to update report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDeleteClick = (cardId: string) => {\n    setCardToDelete(cardId);\n    setShowDeleteConfirmModal(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (!selectedCard) return;\n    setIsLoading(true);\n    try {\n      const batch = writeBatch(db);\n      const allDescendants = getAllDescendantIds(selectedCard, categories);\n      const idsToDelete = [selectedCard, ...allDescendants];\n\n      for (const id of idsToDelete) {\n        batch.delete(doc(db, 'categories', id));\n        batch.delete(doc(db, 'pages', id));\n      }\n      await batch.commit();\n      await fetchCategories();\n\n      setShowDeleteConfirmModal(false);\n      setCardToDelete(null);\n      setSelectedCard('');\n      setPageConfig(null);\n      setFields([]);\n      setSuccess('Report and all its nested items deleted successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to delete report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return {\n    fetchCategories,\n    handleAddNewCard,\n    handleConfirmCreate,\n    handleEditCard,\n    handleUpdateCard,\n    handleDeleteClick,\n    handleConfirmDelete,\n  };\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc } from 'firebase/firestore';\nimport { FormField, PageConfig, Category } from '../types/PageBuilderTypes';\nimport { <PERSON><PERSON>ield as DynamicFormField, FormConfig as DynamicFormConfig, FormFieldOption } from '../../../shared/DynamicForm';\nimport { supabasePageService } from '../services/supabasePageService';\n\ninterface UsePageConfigurationProps {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  setPageConfig: (config: PageConfig | null) => void;\n  fields: FormField[];\n  setFields: (fields: FormField[]) => void;\n  setAvailableDynamicFields: (fields: DynamicFormField[]) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setPreviewContent: (content: string) => void;\n  setIsPreviewOpen: (open: boolean) => void;\n  // New dropdown values - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  // Setters for dropdown values\n  setSelectedRegions: (regions: string[]) => void;\n  setSelectedDivisions: (divisions: string[]) => void;\n  setSelectedOffices: (offices: string[]) => void;\n  setSelectedFrequency: (frequency: string) => void;\n}\n\nexport const usePageConfiguration = (props: UsePageConfigurationProps) => {\n  const {\n    categories,\n    selectedCard,\n    pageConfig,\n    setPageConfig,\n    fields,\n    setFields,\n    setAvailableDynamicFields,\n    setLoading,\n    setError,\n    setSuccess,\n    setPreviewContent,\n    setIsPreviewOpen,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  } = props;\n\n  const fetchDynamicFormFields = useCallback(async (formId: string) => {\n    if (!formId) return;\n    console.log(`Fetching dynamic form fields for formId: ${formId}`);\n    try {\n      const formConfigRef = doc(db, 'formConfigs', formId);\n      const formConfigSnap = await getDoc(formConfigRef);\n      if (formConfigSnap.exists()) {\n        const formConfigData = formConfigSnap.data() as DynamicFormConfig;\n        setAvailableDynamicFields(formConfigData.fields || []);\n        console.log('Fetched dynamic fields:', formConfigData.fields);\n      } else {\n        console.log(`No dynamic form configuration found for formId: ${formId}`);\n        setAvailableDynamicFields([]);\n      }\n    } catch (err) {\n      console.error('Error fetching dynamic form fields:', err);\n      setError('Failed to fetch dynamic form fields.');\n      setAvailableDynamicFields([]);\n    }\n  }, [setAvailableDynamicFields, setError]);\n\n  const loadPageConfig = useCallback(async (cardId: string) => {\n    if (!cardId) {\n      console.log('loadPageConfig called with no cardId');\n      return;\n    }\n    console.log(`loadPageConfig called for cardId: ${cardId}`);\n    setLoading(true);\n    setError(null);\n    try {\n      // Try loading from Firebase first\n      const docRef = doc(db, 'pages', cardId);\n      const docSnap = await getDoc(docRef);\n\n      let data: PageConfig | null = null;\n\n      if (docSnap.exists()) {\n        // Loading from Firebase\n        data = docSnap.data() as PageConfig;\n      } else {\n        // If not found in Firebase, try Supabase\n        try {\n          data = await supabasePageService.loadPageConfig(cardId);\n        } catch (supabaseError) {\n          // Not found in either database, will create new config\n        }\n      }\n\n      if (data) {\n        setPageConfig(data);\n        setFields(data.fields || []);\n        // Load saved dropdown values - handle both old single values and new arrays\n        setSelectedRegions(data.selectedRegions || (data.selectedRegion ? [data.selectedRegion] : []));\n        setSelectedDivisions(data.selectedDivisions || (data.selectedDivision ? [data.selectedDivision] : []));\n        setSelectedOffices(data.selectedOffices || (data.selectedOffice ? [data.selectedOffice] : []));\n        setSelectedFrequency(data.selectedFrequency || '');\n      } else {\n        // Create new page config\n        const card = categories.find(c => c.id === cardId);\n        setPageConfig({\n          id: cardId,\n          title: card?.title || 'New Page',\n          fields: [],\n          lastUpdated: new Date().toISOString(),\n        });\n        setFields([]);\n        // Reset dropdown values for new page\n        setSelectedRegions([]);\n        setSelectedDivisions([]);\n        setSelectedOffices([]);\n        setSelectedFrequency('');\n      }\n    } catch (err) {\n      setError('Failed to load page configuration.');\n      console.error(err);\n      setPageConfig(null);\n      setFields([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [categories, setLoading, setError, setPageConfig, setFields, setSelectedRegions, setSelectedDivisions, setSelectedOffices, setSelectedFrequency]);\n\n  const addField = () => {\n    const newField: FormField = {\n      id: `field_${Date.now()}`,\n      type: 'text',\n      label: 'New Field',\n      placeholder: '',\n      options: [],\n      required: false,\n      region: '',\n      division: '',\n      office: '',\n    };\n    setFields([...fields, newField]);\n  };\n\n  const addFieldFromDynamic = (dynamicField: DynamicFormField) => {\n    console.log('Attempting to add dynamic field:', dynamicField);\n    const newField: FormField = {\n      id: dynamicField.id,\n      type: dynamicField.type,\n      label: dynamicField.label,\n      placeholder: dynamicField.placeholder,\n      options: dynamicField.options ? dynamicField.options.map((opt: string | FormFieldOption) => {\n        if (typeof opt === 'string') {\n          return { label: opt, value: opt };\n        } else {\n          return { label: opt.label, value: opt.value };\n        }\n      }) : undefined,\n      required: dynamicField.required,\n      defaultValue: dynamicField.defaultValue,\n      min: dynamicField.min,\n      max: dynamicField.max,\n      sectionTitle: undefined,\n      columns: undefined,\n      buttonText: undefined,\n      buttonType: undefined,\n      onClickAction: undefined,\n      value: undefined,\n    };\n\n    if (fields.some(field => field.id === newField.id)) {\n        console.warn(`Duplicate field ID detected: \"${newField.id}\". Field not added.`);\n        setError(`Field with ID \"${newField.id}\" already exists in the page configuration.`);\n        setTimeout(() => setError(null), 3000);\n        return;\n    }\n\n    console.log('Adding new field to state:', newField);\n    setFields([...fields, newField]);\n    setSuccess(`Added field \"${newField.label}\" to page configuration.`);\n    setTimeout(() => setSuccess(null), 3000);\n  };\n\n  const updateField = (index: number, updatedField: FormField) => {\n    const updatedFields = [...fields];\n    updatedFields[index] = updatedField;\n    setFields(updatedFields);\n  };\n\n  const removeField = (index: number) => {\n    setFields(fields.filter((_, i) => i !== index));\n  };\n\n  const handleSave = async () => {\n    if (!selectedCard || !pageConfig) {\n      setError('No report selected or page configuration loaded.');\n      return;\n    }\n\n    // Validate that report frequency is selected\n    if (!selectedFrequency) {\n      setError('Report frequency is required. Please select a frequency before saving.');\n      return;\n    }\n\n    setLoading(true);\n    console.log('Attempting to save page configuration for cardId:', selectedCard);\n    console.log('Fields being saved:', fields);\n    console.log('Report frequency:', selectedFrequency);\n\n    try {\n      const cleanedFields = fields.map(field => {\n        const cleanedField: any = {};\n        for (const key in field) {\n          if (field[key] !== undefined) {\n            cleanedField[key] = field[key];\n          } else {\n            cleanedField[key] = null;\n          }\n        }\n        return cleanedField;\n      });\n\n      const updatedPageConfig: PageConfig = {\n        ...pageConfig,\n        id: selectedCard,\n        title: categories.find(c => c.id === selectedCard)?.title || pageConfig.title,\n        fields: cleanedFields,\n        lastUpdated: new Date().toISOString(),\n        selectedRegions,\n        selectedDivisions,\n        selectedOffices,\n        selectedFrequency,\n      };\n\n      // Save to both Firebase and Supabase\n      const savePromises = [];\n\n      // Save to Firebase\n      savePromises.push(\n        setDoc(doc(db, 'pages', selectedCard), updatedPageConfig)\n          .catch(err => {\n            console.error('Firebase save failed:', err);\n            throw new Error(`Firebase save failed: ${err.message}`);\n          })\n      );\n\n      // Save to Supabase\n      savePromises.push(\n        supabasePageService.savePageConfig(updatedPageConfig)\n          .catch(err => {\n            console.error('Supabase save failed:', err);\n            throw new Error(`Supabase save failed: ${err.message}`);\n          })\n      );\n\n      // Wait for both saves to complete\n      await Promise.all(savePromises);\n\n      setPageConfig(updatedPageConfig);\n      setSuccess('Page configuration saved successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n\n    } catch (err) {\n      console.error('Failed to save page configuration:', err);\n      setError(`Failed to save page configuration: ${err instanceof Error ? err.message : 'Unknown error'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePreview = () => {\n    if (!pageConfig || fields.length === 0) {\n      alert('No page configuration or fields to preview.');\n      return;\n    }\n\n    const generatedPreview = `\n      <h1>${pageConfig.title}</h1>\n      <form>\n        ${fields.map(field => {\n          let fieldHtml = '';\n          switch (field.type) {\n            case 'text':\n            case 'number':\n            case 'date':\n            case 'textarea':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <input type=\"${field.type}\" class=\"form-control\" placeholder=\"${field.placeholder || ''}\" ${field.required ? 'required' : ''} />\n                </div>\n              `;\n              break;\n            case 'dropdown':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <select class=\"form-control\" ${field.required ? 'required' : ''}>\n                    <option value=\"\">Select ${field.label}</option>\n                    ${field.options?.map(option => `<option value=\"${option.value}\">${option.label}</option>`).join('') || ''}\n                  </select>\n                </div>\n              `;\n              break;\n            case 'checkbox':\n              fieldHtml = `\n                <div class=\"form-check mb-3\">\n                  <input type=\"checkbox\" class=\"form-check-input\" id=\"${field.id}\" ${field.required ? 'required' : ''} />\n                  <label class=\"form-check-label\" for=\"${field.id}\">${field.label}${field.required ? ' *' : ''}</label>\n                </div>\n              `;\n              break;\n            case 'radio':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  ${field.options?.map((option, i) => `\n                    <div class=\"form-check\">\n                      <input class=\"form-check-input\" type=\"radio\" name=\"${field.id}\" id=\"${field.id}-${i}\" value=\"${option.value}\" ${field.required ? 'required' : ''}>\n                      <label class=\"form-check-label\" for=\"${field.id}-${i}\">${option.label}</label>\n                    </div>\n                  `).join('') || ''}\n                </div>\n              `;\n              break;\n            case 'section':\n              fieldHtml = `\n                <div class=\"card mt-3 mb-3\">\n                  <div class=\"card-header\">${field.sectionTitle || 'Section'}</div>\n                  <div class=\"card-body\">\n                    <p>Fields for this section would appear here in the actual form.</p>\n                  </div>\n                </div>\n              `;\n              break;\n            case 'button':\n              fieldHtml = `\n                <button type=\"button\" class=\"btn btn-primary mt-3\">${field.buttonText || 'Button'}</button>\n              `;\n              break;\n            default:\n              fieldHtml = `<p>Unsupported field type: ${field.type}</p>`;\n          }\n          return fieldHtml;\n        }).join('')}\n      </form>\n    `;\n\n    setPreviewContent(generatedPreview);\n    setIsPreviewOpen(true);\n  };\n\n  return {\n    fetchDynamicFormFields,\n    loadPageConfig,\n    addField,\n    addFieldFromDynamic,\n    updateField,\n    removeField,\n    handleSave,\n    handlePreview,\n  };\n};\n", "import React from 'react';\nimport { Category } from '../types/PageBuilderTypes';\nimport { organizeCards, isLeafCard, isMainCard } from '../utils/cardUtils';\n\ninterface CardSelectorProps {\n  categories: Category[];\n  selectedCard: string;\n  onCardChange: (cardId: string) => void;\n  actionType: string;\n  onActionChange: (action: string) => void;\n  isLoading: boolean;\n  onCreateAction: () => void;\n  onWebPageAction: () => void;\n}\n\nconst CardSelector: React.FC<CardSelectorProps> = ({\n  categories,\n  selectedCard,\n  onCardChange,\n  actionType,\n  onActionChange,\n  isLoading,\n  onCreateAction,\n  onWebPageAction,\n}) => {\n  const renderCardOptions = (cards: Category[], level = 0): React.ReactElement[] => {\n    return cards.flatMap(card => [\n      <option key={card.id} value={card.id} style={{ paddingLeft: `${level * 20}px` }}>\n        {`${'--'.repeat(level)} ${card.title}`}\n      </option>,\n      ...(card.children && card.children.length > 0 ? renderCardOptions(card.children, level + 1) : []),\n    ]);\n  };\n\n  const handleCardChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newSelectedCard = e.target.value;\n    onCardChange(newSelectedCard);\n  };\n\n  const handleActionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newAction = e.target.value;\n    onActionChange(newAction);\n    \n    if (newAction === 'createNestedCard' || newAction === 'addNewCardGlobal') {\n      onCreateAction();\n    } else if (newAction === 'createWebPage') {\n      onWebPageAction();\n    }\n  };\n\n  return (\n    <div className=\"card-selector\">\n      <select\n        value={selectedCard}\n        onChange={handleCardChange}\n        className=\"form-select\"\n        disabled={isLoading}\n      >\n        <option value=\"\">{isLoading ? 'Loading Reports...' : 'Select or Create New Report'}</option>\n        {renderCardOptions(organizeCards(categories))}\n      </select>\n\n      <div className=\"action-dropdown-container\">\n        <select\n          value={actionType}\n          onChange={handleActionChange}\n          className=\"form-select action-dropdown\"\n        >\n          <option value=\"\">Select Action...</option>\n          <option value=\"addNewCardGlobal\" disabled={!!selectedCard}>\n            Create New Main Report\n          </option>\n          {selectedCard && (\n            <>\n              <option value=\"createNestedCard\">\n                Create Nested Report\n              </option>\n              <option\n                value=\"createWebPage\"\n                disabled={!isLeafCard(selectedCard, categories) || isMainCard(selectedCard, categories)}\n              >\n                Create/Edit Web Page for this Report\n              </option>\n            </>\n          )}\n        </select>\n      </div>\n    </div>\n  );\n};\n\nexport default CardSelector;\n", "import React from 'react';\nimport { FaEdit, FaTrash } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\ninterface CardManagementProps {\n  selectedCard: string;\n  categories: Category[];\n  onEditCard: (card: Category) => void;\n  onDeleteCard: (cardId: string) => void;\n}\n\nconst CardManagement: React.FC<CardManagementProps> = ({\n  selectedCard,\n  categories,\n  onEditCard,\n  onDeleteCard,\n}) => {\n  const selectedCategory = categories.find(c => c.id === selectedCard);\n\n  if (!selectedCategory) {\n    return null;\n  }\n\n  return (\n    <div className=\"card-management\">\n      <h3>Report Details: \"{selectedCategory.title}\"</h3>\n      <div className=\"card-actions\">\n        <button\n          onClick={() => onEditCard(selectedCategory)}\n          className=\"edit-button btn btn-outline-primary btn-sm me-2\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaEdit as React.ComponentType<any>)} Edit Name\n        </button>\n        <button\n          onClick={() => onDeleteCard(selectedCard)}\n          className=\"delete-button btn btn-outline-danger btn-sm\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaTrash as React.ComponentType<any>)} Delete Report\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CardManagement;\n", "import React from 'react';\nimport { FaTrash } from 'react-icons/fa';\nimport { FormField } from '../types/PageBuilderTypes';\n\ninterface FieldConfigItemProps {\n  field: FormField;\n  index: number;\n  onUpdate: (index: number, field: FormField) => void;\n  onRemove: (index: number) => void;\n}\n\nconst FieldConfigItem: React.FC<FieldConfigItemProps> = ({\n  field,\n  index,\n  onUpdate,\n  onRemove,\n}) => {\n  const handleOptionChange = (optIndex: number, value: string, key: 'label' | 'value') => {\n    const newOptions = [...(field.options || [])];\n    newOptions[optIndex] = { ...newOptions[optIndex], [key]: value };\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const addOption = () => {\n    const newOptions = [...(field.options || []), { label: '', value: '' }];\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const removeOption = (optIndex: number) => {\n    const newOptions = field.options?.filter((_, i) => i !== optIndex);\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const handleDefaultValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { value, type } = e.target;\n    let newDefaultValue: any = value;\n    if (type === 'checkbox') {\n      newDefaultValue = (e.target as HTMLInputElement).checked;\n    }\n    onUpdate(index, { ...field, defaultValue: newDefaultValue });\n  };\n\n  return (\n    <div className=\"field-config-item card mb-3\">\n      <div className=\"card-header d-flex justify-content-between align-items-center\">\n        <strong>{field.label || 'Unnamed Field'}</strong> ({field.type})\n        <button onClick={() => onRemove(index)} className=\"btn btn-danger btn-sm\">\n          {React.createElement(FaTrash as React.ComponentType<any>)} Remove\n        </button>\n      </div>\n      <div className=\"card-body\">\n        {/* Field Type Selector */}\n        <div className=\"form-group\">\n          <label htmlFor={`field-type-${index}`} className=\"form-label\">Type: </label>\n          <select\n            id={`field-type-${index}`}\n            className=\"form-control\"\n            value={field.type}\n            onChange={(e) => onUpdate(index, {\n              ...field, \n              type: e.target.value as FormField['type'], \n              options: field.type !== 'dropdown' && field.type !== 'radio' && field.type !== 'checkbox-group' ? undefined : field.options, \n              placeholder: field.type === 'section' || field.type === 'button' ? undefined : field.placeholder \n            })}\n          >\n            <option value=\"text\">Text</option>\n            <option value=\"textarea\">Textarea</option>\n            <option value=\"number\">Number</option>\n            <option value=\"date\">Date</option>\n            <option value=\"dropdown\">Dropdown</option>\n            <option value=\"radio\">Radio Group</option>\n            <option value=\"checkbox\">Checkbox (Single)</option>\n            <option value=\"checkbox-group\">Checkbox Group</option>\n            <option value=\"switch\">Switch</option>\n            <option value=\"file\">File Upload</option>\n            <option value=\"section\">Section Header</option>\n            <option value=\"button\">Button</option>\n          </select>\n        </div>\n\n        <div className=\"form-group\">\n          <label htmlFor={`field-label-${index}`} className=\"form-label\">Label: </label>\n          <input\n            id={`field-label-${index}`}\n            type=\"text\"\n            className=\"form-control\"\n            value={field.label}\n            onChange={(e) => onUpdate(index, {...field, label: e.target.value})}\n            required\n          />\n        </div>\n\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-placeholder-${index}`} className=\"form-label\">Placeholder: </label>\n            <input\n              id={`field-placeholder-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.placeholder || ''}\n              onChange={(e) => onUpdate(index, {...field, placeholder: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'number' && (\n          <>\n            <div className=\"form-group\">\n              <label htmlFor={`field-min-${index}`} className=\"form-label\">Min Value: </label>\n              <input\n                id={`field-min-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.min === undefined ? '' : field.min}\n                onChange={(e) => onUpdate(index, {...field, min: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor={`field-max-${index}`} className=\"form-label\">Max Value: </label>\n              <input\n                id={`field-max-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.max === undefined ? '' : field.max}\n                onChange={(e) => onUpdate(index, {...field, max: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n          </>\n        )}\n\n        {['dropdown', 'radio', 'checkbox-group'].includes(field.type) && (\n          <div className=\"form-group field-options-config\">\n            <label className=\"form-label\">Options: </label>\n            {field.options?.map((opt, optIndex) => (\n              <div key={optIndex} className=\"input-group mb-2\">\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Label\"\n                  value={opt.label}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'label')}\n                />\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Value\"\n                  value={opt.value}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'value')}\n                />\n                <button type=\"button\" onClick={() => removeOption(optIndex)} className=\"btn btn-outline-danger\">\n                  Remove\n                </button>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addOption} className=\"btn btn-secondary btn-sm\">\n              Add Option\n            </button>\n          </div>\n        )}\n\n        {/* Default Value - Type specific handling */}\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n            <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <input\n                    id={`field-default-value-${index}`}\n                    type={field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n            </div>\n        )}\n\n        {(field.type === 'checkbox' || field.type === 'switch') && (\n            <div className=\"form-group form-check\">\n                <input\n                    id={`field-default-value-${index}`}\n                    type=\"checkbox\"\n                    className=\"form-check-input\"\n                    checked={Boolean(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n                <label htmlFor={`field-default-value-${index}`} className=\"form-check-label\">Default Checked: </label>\n            </div>\n        )}\n\n        {['dropdown', 'radio'].includes(field.type) && field.options && field.options.length > 0 && (\n             <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <select\n                    id={`field-default-value-${index}`}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                >\n                    <option value=\"\">-- Select Default --</option>\n                    {field.options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}\n                </select>\n            </div>\n        )}\n\n        {field.type === 'checkbox-group' && (\n            <div className=\"form-group\">\n                <label className=\"form-label\">Default Values (comma-separated): </label>\n                <input\n                    type=\"text\"\n                    className=\"form-control\"\n                    value={Array.isArray(field.defaultValue) ? field.defaultValue.join(',') : ''}\n                    onChange={(e) => onUpdate(index, {...field, defaultValue: e.target.value.split(',').map(s => s.trim()).filter(s => s)})}\n                    placeholder=\"value1,value2\"\n                />\n            </div>\n        )}\n\n        {field.type === 'button' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-button-text-${index}`} className=\"form-label\">Button Text: </label>\n            <input\n              id={`field-button-text-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.buttonText || ''}\n              onChange={(e) => onUpdate(index, {...field, buttonText: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'section' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-section-title-${index}`} className=\"form-label\">Section Title: </label>\n            <input\n              id={`field-section-title-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.sectionTitle || ''}\n              onChange={(e) => onUpdate(index, {...field, sectionTitle: e.target.value})}\n            />\n          </div>\n        )}\n\n        {/* Required Checkbox (excluding button and section) */}\n        {!['button', 'section'].includes(field.type) && (\n          <div className=\"form-group form-check\">\n            <input\n              id={`field-required-${index}`}\n              type=\"checkbox\"\n              className=\"form-check-input\"\n              checked={!!field.required}\n              onChange={(e) => onUpdate(index, {...field, required: e.target.checked})}\n            />\n            <label htmlFor={`field-required-${index}`} className=\"form-check-label\"> Required</label>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FieldConfigItem;\n", "import React from 'react';\nimport { FaPlus, FaSave } from 'react-icons/fa';\nimport { FormField, PageConfig } from '../types/PageBuilderTypes';\nimport FieldConfigItem from './FieldConfigItem';\n\ninterface PageBuilderContentProps {\n  pageConfig: PageConfig;\n  fields: FormField[];\n  onAddField: () => void;\n  onUpdateField: (index: number, field: FormField) => void;\n  onRemoveField: (index: number) => void;\n  onSave: () => void;\n  onPreview: () => void;\n  loading: boolean;\n}\n\nconst PageBuilderContent: React.FC<PageBuilderContentProps> = ({\n  pageConfig,\n  fields,\n  onAddField,\n  onUpdateField,\n  onRemoveField,\n  onSave,\n  onPreview,\n  loading,\n}) => {\n  return (\n    <div className=\"builder-content\">\n      <h4>Page Configuration for: {pageConfig.title}</h4>\n      \n      <h5>Current Page Fields:</h5>\n      {fields.map((field, index) => (\n        <FieldConfigItem\n          key={field.id || index}\n          field={field}\n          index={index}\n          onUpdate={onUpdateField}\n          onRemove={onRemoveField}\n        />\n      ))}\n      \n      <button onClick={onAddField} className=\"btn btn-info mt-3\">\n        {React.createElement(FaPlus as React.ComponentType<any>)} Add Field\n      </button>\n      \n      <button \n        onClick={onSave} \n        className=\"btn btn-success mt-3 ms-2\" \n        disabled={loading || !pageConfig || fields.length === 0}\n      >\n        {React.createElement(FaSave as React.ComponentType<any>)} {loading ? 'Saving...' : 'Save Page Configuration'}\n      </button>\n      \n      <button \n        onClick={onPreview} \n        className=\"btn btn-secondary mt-3 ms-2\" \n        disabled={!pageConfig || fields.length === 0}\n      >\n        Preview Page\n      </button>\n    </div>\n  );\n};\n\nexport default PageBuilderContent;\n", "// Interfaces for PageBuilder component\n\nexport interface FormFieldOption {\n  label: string;\n  value: string;\n}\n\n// NOTE: This FormField interface is for the PageBuilder's internal state\n// and represents the configuration being built for a specific 'page'.\n// It's slightly different from the DynamicFormField used by the DynamicForm component.\nexport interface FormField {\n  id: string;\n  type: 'text' | 'textarea' | 'number' | 'date' | 'dropdown' | 'radio' | 'checkbox' | 'checkbox-group' | 'section' | 'button' | 'file' | 'switch';\n  label: string;\n  placeholder?: string;\n  region?: string;\n  division?: string;\n  office?: string;\n  options?: FormFieldOption[]; // For dropdown, radio, checkbox\n  required?: boolean;\n  value?: any; // Current value of the field (might not be used in builder, but kept for consistency)\n  // For section type\n  sectionTitle?: string;\n  columns?: number; // Number of columns for fields within the section\n  // For button type\n  buttonText?: string;\n  buttonType?: string;\n  onClickAction?: string;\n  defaultValue?: any;\n  min?: number;\n  max?: number;\n  [key: string]: any; // Add this index signature\n}\n\nexport interface PageConfig {\n  id: string;\n  title: string;\n  fields: FormField[];\n  lastUpdated: string;\n  isPage?: boolean; // New field\n  pageId?: string;\n  // Report configuration - updated to support both old single values and new arrays\n  selectedRegion?: string; // Keep for backward compatibility\n  selectedDivision?: string; // Keep for backward compatibility\n  selectedOffice?: string; // Keep for backward compatibility\n  selectedRegions?: string[]; // New array-based selections\n  selectedDivisions?: string[]; // New array-based selections\n  selectedOffices?: string[]; // New array-based selections\n  selectedFrequency?: string;\n}\n\nexport interface Category {\n  id: string;\n  title: string;\n  path: string; // e.g., /categories/parent-id/child-id\n  parentId: string | null;\n  children?: Category[];\n  icon?: string; // Icon name (e.g., 'FaFolder')\n  color?: string; // Color for the icon/card\n  fields?: FormField[]; // If storing form fields directly on category for some reason\n  lastUpdated?: string;\n  isPage: boolean; // New field\n  pageId: string; \n}\n\nexport interface PageBuilderState {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  fields: FormField[];\n  availableDynamicFields: any[];\n  isLoading: boolean;\n  loading: boolean;\n  error: string | null;\n  success: string | null;\n  isAddingNewCard: boolean;\n  newCardId: string;\n  newCardTitle: string;\n  showConfirmModal: boolean;\n  editingCard: Category | null;\n  showEditModal: boolean;\n  cardToDelete: string | null;\n  showDeleteConfirmModal: boolean;\n  actionType: string;\n  isPreviewOpen: boolean;\n  previewContent: string;\n  // New dropdown states - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n}\n\n// Report frequency options\nexport interface ReportFrequency {\n  value: string;\n  label: string;\n}\n\nexport const REPORT_FREQUENCIES: ReportFrequency[] = [\n  { value: 'daily', label: 'Daily' },\n  { value: 'weekly', label: 'Weekly' },\n  { value: 'monthly', label: 'Monthly' }\n];\n\n// Location hierarchy interfaces - matching Supabase table structure\nexport interface Region {\n  id: string;\n  name: string;\n}\n\nexport interface Division {\n  id: string;\n  name: string;\n  region: string; // matches the Region column in Supabase\n}\n\nexport interface Office {\n  id: string; // Now uses office name instead of facility ID\n  name: string;\n  region: string; // matches the Region column in Supabase\n  division: string; // matches the Division column in Supabase\n  facilityId?: string; // Keep facility ID for reference/mapping\n}\n\n// Supabase office record interface (matches actual table structure)\nexport interface SupabaseOfficeRecord {\n  'Facility ID': string;\n  Region: string;\n  Division: string;\n  'Office name': string;\n}\n", "import { useState, useEffect } from 'react';\nimport { supabase } from '../../../../config/supabaseClient';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n}\n\nexport const useOfficeDataSimple = (): UseOfficeDataReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataSimple: Fetching with enhanced pagination...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allData = await OfficeService.fetchAllOfficeData();\n\n      console.log('✅ useOfficeDataSimple: Fetched', allData.length, 'office records');\n\n      // Process regions exactly like SQL: SELECT DISTINCT \"Region\" FROM offices ORDER BY \"Region\"\n      const distinctRegions = allData\n        ?.map(row => row.Region)\n        .filter((region, index, array) => array.indexOf(region) === index)\n        .filter((region): region is string => region != null && region.trim() !== '') // Type guard to ensure string\n        .sort();\n\n      // Process regions successfully\n\n      const regionsArray: Region[] = distinctRegions?.map(regionName => ({\n        id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: regionName,\n      })) || [];\n\n      // Process divisions exactly like SQL: SELECT DISTINCT \"Region\", \"Division\" FROM offices ORDER BY \"Region\", \"Division\"\n      const distinctDivisions = allData\n        ?.map(row => ({ region: row.Region, division: row.Division }))\n        .filter((item, index, array) =>\n          array.findIndex(x => x.region === item.region && x.division === item.division) === index\n        )\n        .filter((item): item is { region: string; division: string } =>\n          item.region != null && item.division != null &&\n          item.region.trim() !== '' && item.division.trim() !== ''\n        )\n        .sort((a, b) => a.region.localeCompare(b.region) || a.division.localeCompare(b.division));\n\n      const divisionsArray: Division[] = distinctDivisions?.map(item => ({\n        id: item.division.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: item.division,\n        region: item.region,\n      })) || [];\n\n      // Process all offices - USE OFFICE NAME AS ID instead of Facility ID\n      const officesArray: Office[] = allData\n        ?.filter(row => row['Office name'] && row.Region && row.Division)\n        .map(row => ({\n          id: row['Office name'], // ✅ FIXED: Use office name as ID for form targeting\n          name: row['Office name'],\n          region: row.Region || '',\n          division: row.Division || '',\n          facilityId: row['Office name'], // Use office name as facility ID for consistency\n        })) || [];\n\n      // Data processing completed successfully\n\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n\n    } catch (err) {\n      console.error('🚨 SIMPLE: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n  };\n};\n", "import React, { useState, useRef, useEffect } from 'react';\n\ninterface Option {\n  id: string;\n  name: string;\n}\n\ninterface CheckboxDropdownProps {\n  id: string;\n  label: string;\n  options: Option[];\n  selectedValues: string[];\n  onChange: (values: string[]) => void;\n  disabled?: boolean;\n  placeholder?: string;\n}\n\nconst CheckboxDropdown: React.FC<CheckboxDropdownProps> = ({\n  id,\n  label,\n  options,\n  selectedValues,\n  onChange,\n  disabled = false,\n  placeholder = \"-- Select Options --\"\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleCheckboxChange = (optionId: string) => {\n    if (selectedValues.includes(optionId)) {\n      // Remove from selection\n      onChange(selectedValues.filter(id => id !== optionId));\n    } else {\n      // Add to selection\n      onChange([...selectedValues, optionId]);\n    }\n  };\n\n  const handleSelectAll = () => {\n    if (selectedValues.length === options.length) {\n      // Deselect all\n      onChange([]);\n    } else {\n      // Select all\n      onChange(options.map(option => option.id));\n    }\n  };\n\n  const getDisplayText = () => {\n    if (selectedValues.length === 0) {\n      return placeholder;\n    } else if (selectedValues.length === 1) {\n      const selectedOption = options.find(option => option.id === selectedValues[0]);\n      return selectedOption?.name || placeholder;\n    } else {\n      return `${selectedValues.length} selected`;\n    }\n  };\n\n  const isAllSelected = selectedValues.length === options.length && options.length > 0;\n  const isIndeterminate = selectedValues.length > 0 && selectedValues.length < options.length;\n\n  return (\n    <div className=\"form-group\">\n      <label htmlFor={id} className=\"form-label\">{label}:</label>\n      <div className=\"dropdown\" ref={dropdownRef}>\n        <button\n          id={id}\n          className={`btn btn-outline-secondary dropdown-toggle w-100 text-start ${disabled ? 'disabled' : ''}`}\n          type=\"button\"\n          onClick={() => !disabled && setIsOpen(!isOpen)}\n          disabled={disabled}\n          style={{ \n            backgroundColor: disabled ? '#e9ecef' : 'white',\n            borderColor: '#ced4da'\n          }}\n        >\n          <span className={selectedValues.length === 0 ? 'text-muted' : ''}>\n            {getDisplayText()}\n          </span>\n        </button>\n        \n        {isOpen && !disabled && (\n          <div className=\"dropdown-menu show w-100\" style={{ maxHeight: '300px', overflowY: 'auto' }}>\n            {/* Select All Option */}\n            {options.length > 1 && (\n              <>\n                <div className=\"dropdown-item\">\n                  <div className=\"form-check\">\n                    <input\n                      className=\"form-check-input\"\n                      type=\"checkbox\"\n                      id={`${id}-select-all`}\n                      checked={isAllSelected}\n                      ref={(input) => {\n                        if (input) input.indeterminate = isIndeterminate;\n                      }}\n                      onChange={handleSelectAll}\n                    />\n                    <label className=\"form-check-label fw-bold\" htmlFor={`${id}-select-all`}>\n                      Select All ({options.length})\n                    </label>\n                  </div>\n                </div>\n                <hr className=\"dropdown-divider\" />\n              </>\n            )}\n            \n            {/* Individual Options */}\n            {options.map(option => (\n              <div key={option.id} className=\"dropdown-item\">\n                <div className=\"form-check\">\n                  <input\n                    className=\"form-check-input\"\n                    type=\"checkbox\"\n                    id={`${id}-${option.id}`}\n                    checked={selectedValues.includes(option.id)}\n                    onChange={() => handleCheckboxChange(option.id)}\n                  />\n                  <label className=\"form-check-label\" htmlFor={`${id}-${option.id}`}>\n                    {option.name}\n                  </label>\n                </div>\n              </div>\n            ))}\n            \n            {options.length === 0 && (\n              <div className=\"dropdown-item text-muted\">\n                <em>No options available</em>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n      \n      {/* Selected count indicator */}\n      {selectedValues.length > 0 && (\n        <small className=\"text-muted mt-1 d-block\">\n          {selectedValues.length} of {options.length} selected\n        </small>\n      )}\n    </div>\n  );\n};\n\nexport default CheckboxDropdown;\n", "import React, { useEffect } from 'react';\nimport { REPORT_FREQUENCIES } from '../types/PageBuilderTypes';\nimport { useOfficeDataSimple as useOfficeData } from '../hooks/useOfficeDataSimple';\nimport CheckboxDropdown from './CheckboxDropdown';\n\ninterface ReportConfigurationProps {\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  onRegionsChange: (regions: string[]) => void;\n  onDivisionsChange: (divisions: string[]) => void;\n  onOfficesChange: (offices: string[]) => void;\n  onFrequencyChange: (frequency: string) => void;\n}\n\nconst ReportConfiguration: React.FC<ReportConfigurationProps> = ({\n  selectedRegions,\n  selectedDivisions,\n  selectedOffices,\n  selectedFrequency,\n  onRegionsChange,\n  onDivisionsChange,\n  onOfficesChange,\n  onFrequencyChange,\n}) => {\n  // Use custom hook to fetch office data from Supabase\n  const { regions, divisions, offices, loading, error, refetch } = useOfficeData();\n\n  // Filter divisions based on selected regions\n  const selectedRegionNames = selectedRegions.map(regionId =>\n    regions.find(r => r.id === regionId)?.name\n  ).filter(Boolean);\n\n  const availableDivisions = selectedRegions.length > 0\n    ? divisions.filter(division => selectedRegionNames.includes(division.region))\n    : divisions; // Show all divisions if no regions selected\n\n  // Filter offices based on selected divisions\n  const selectedDivisionNames = selectedDivisions.map(divisionId =>\n    divisions.find(d => d.id === divisionId)?.name\n  ).filter(Boolean);\n\n  const availableOffices = selectedDivisions.length > 0\n    ? offices.filter(office =>\n        selectedRegionNames.includes(office.region) &&\n        selectedDivisionNames.includes(office.division)\n      )\n    : selectedRegions.length > 0\n      ? offices.filter(office => selectedRegionNames.includes(office.region))\n      : offices; // Show all offices if no filters applied\n\n  // Reset dependent selections when parent selections change\n  useEffect(() => {\n    if (selectedRegions.length > 0) {\n      // Remove divisions that don't belong to selected regions\n      const validDivisions = selectedDivisions.filter(divisionId => {\n        const division = divisions.find(d => d.id === divisionId);\n        return division && selectedRegionNames.includes(division.region);\n      });\n\n      if (validDivisions.length !== selectedDivisions.length) {\n        onDivisionsChange(validDivisions);\n      }\n    }\n  }, [selectedRegions, selectedDivisions, divisions, selectedRegionNames, onDivisionsChange]);\n\n  useEffect(() => {\n    if (selectedDivisions.length > 0) {\n      // Remove offices that don't belong to selected regions/divisions\n      const validOffices = selectedOffices.filter(officeId => {\n        const office = offices.find(o => o.id === officeId);\n        return office &&\n               selectedRegionNames.includes(office.region) &&\n               selectedDivisionNames.includes(office.division);\n      });\n\n      if (validOffices.length !== selectedOffices.length) {\n        onOfficesChange(validOffices);\n      }\n    }\n  }, [selectedDivisions, selectedOffices, offices, selectedRegionNames, selectedDivisionNames, onOfficesChange]);\n\n  return (\n    <div className=\"report-configuration mt-3 mb-3\">\n      <h5>Report Configuration</h5>\n\n      {loading && (\n        <div className=\"alert alert-info\">\n          <div className=\"d-flex align-items-center\">\n            <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n            Loading office data...\n          </div>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"alert alert-danger\">\n          <strong>Error:</strong> {error}\n          <button\n            className=\"btn btn-sm btn-outline-danger ms-2\"\n            onClick={refetch}\n          >\n            Retry\n          </button>\n        </div>\n      )}\n\n      {!loading && !error && (\n        <div className=\"row\">\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"region-select\"\n              label=\"Select Regions\"\n              options={regions}\n              selectedValues={selectedRegions}\n              onChange={onRegionsChange}\n              disabled={loading}\n              placeholder=\"-- Select Regions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"division-select\"\n              label=\"Select Divisions\"\n              options={availableDivisions}\n              selectedValues={selectedDivisions}\n              onChange={onDivisionsChange}\n              disabled={selectedRegions.length === 0 || loading}\n              placeholder=\"-- Select Divisions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"office-select\"\n              label=\"Select Offices\"\n              options={availableOffices}\n              selectedValues={selectedOffices}\n              onChange={onOfficesChange}\n              disabled={selectedDivisions.length === 0 || loading}\n              placeholder=\"-- Select Offices --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <div className=\"form-group\">\n              <label htmlFor=\"frequency-select\" className=\"form-label\">\n                Report Frequency: <span className=\"text-danger\">*</span>\n              </label>\n              <select\n                id=\"frequency-select\"\n                className={`form-select ${!selectedFrequency ? 'is-invalid' : ''}`}\n                value={selectedFrequency}\n                onChange={(e) => onFrequencyChange(e.target.value)}\n                disabled={loading}\n                required\n              >\n                <option value=\"\">-- Select Frequency --</option>\n                {REPORT_FREQUENCIES.map(frequency => (\n                  <option key={frequency.value} value={frequency.value}>\n                    {frequency.label}\n                  </option>\n                ))}\n              </select>\n              {!selectedFrequency && (\n                <div className=\"invalid-feedback\">\n                  Report frequency is required.\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ReportConfiguration;\n", "import React, { useEffect } from 'react';\nimport Modal from '../../shared/Modal';\nimport './PageBuilder.css';\n\n// Import refactored components and hooks\nimport { usePageBuilderState } from './hooks/usePageBuilderState';\nimport { useCardManagement } from './hooks/useCardManagement';\nimport { usePageConfiguration } from './hooks/usePageConfiguration';\nimport CardSelector from './components/CardSelector';\nimport CardManagement from './components/CardManagement';\nimport PageBuilderContent from './components/PageBuilderContent';\nimport ReportConfiguration from './components/ReportConfiguration';\n// Debug components removed - Supabase integration working\nimport { isLeafCard, isMainCard } from './utils/cardUtils';\n\n// All interfaces and utilities are now imported from separate files\n\nconst PageBuilder: React.FC = () => {\n  // Use custom hooks for state management\n  const state = usePageBuilderState();\n\n  // Debug mode removed - using working SQL-based implementation\n\n  // Initialize custom hooks\n  const cardManagement = useCardManagement({\n    categories: state.categories,\n    setCategories: state.setCategories,\n    selectedCard: state.selectedCard,\n    setSelectedCard: state.setSelectedCard,\n    newCardId: state.newCardId,\n    setNewCardId: state.setNewCardId,\n    newCardTitle: state.newCardTitle,\n    setNewCardTitle: state.setNewCardTitle,\n    actionType: state.actionType,\n    setActionType: state.setActionType,\n    setIsLoading: state.setIsLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setShowConfirmModal: state.setShowConfirmModal,\n    setIsAddingNewCard: state.setIsAddingNewCard,\n    setPageConfig: state.setPageConfig,\n    setFields: state.setFields,\n    setEditingCard: state.setEditingCard,\n    setShowEditModal: state.setShowEditModal,\n    setCardToDelete: state.setCardToDelete,\n    setShowDeleteConfirmModal: state.setShowDeleteConfirmModal,\n  });\n\n  const pageConfiguration = usePageConfiguration({\n    categories: state.categories,\n    selectedCard: state.selectedCard,\n    pageConfig: state.pageConfig,\n    setPageConfig: state.setPageConfig,\n    fields: state.fields,\n    setFields: state.setFields,\n    setAvailableDynamicFields: state.setAvailableDynamicFields,\n    setLoading: state.setLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setPreviewContent: state.setPreviewContent,\n    setIsPreviewOpen: state.setIsPreviewOpen,\n    selectedRegions: state.selectedRegions,\n    selectedDivisions: state.selectedDivisions,\n    selectedOffices: state.selectedOffices,\n    selectedFrequency: state.selectedFrequency,\n    setSelectedRegions: state.setSelectedRegions,\n    setSelectedDivisions: state.setSelectedDivisions,\n    setSelectedOffices: state.setSelectedOffices,\n    setSelectedFrequency: state.setSelectedFrequency,\n  });\n\n  // Initialize data on component mount\n  useEffect(() => {\n    cardManagement.fetchCategories();\n  }, []);\n\n  // Handle card and action changes\n  useEffect(() => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.actionType === 'createWebPage') {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n      pageConfiguration.fetchDynamicFormFields(state.selectedCard);\n    } else if (state.selectedCard && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && state.actionType === 'createWebPage') {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n    } else if (!state.selectedCard) {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n      state.setActionType('');\n    }\n\n    if (state.selectedCard && state.actionType !== 'createWebPage') {\n        state.setAvailableDynamicFields([]);\n    }\n  }, [state.selectedCard, state.categories, state.actionType]);\n\n  // Event handlers for UI interactions\n  const handleCardChange = (cardId: string) => {\n    state.setSelectedCard(cardId);\n    state.setActionType('');\n    if (!cardId) {\n        state.setPageConfig(null);\n        state.setFields([]);\n    } else {\n        const cardIsLeaf = isLeafCard(cardId, state.categories);\n        const cardIsMain = isMainCard(cardId, state.categories);\n        if(!cardIsLeaf || cardIsMain) {\n            state.setPageConfig(null);\n            state.setFields([]);\n        }\n    }\n  };\n\n  const handleActionChange = (action: string) => {\n    state.setActionType(action);\n  };\n\n  const handleCreateAction = () => {\n    state.setNewCardId('');\n    state.setNewCardTitle('');\n    state.setIsAddingNewCard(true);\n  };\n\n  const handleWebPageAction = () => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories)) {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n    } else if (state.selectedCard) {\n      state.setError('Web page can only be created/edited for a final nested report (not a main report).');\n      state.setPageConfig(null);\n      state.setFields([]);\n    }\n  };\n\n  // Event handlers for report configuration dropdowns - updated for arrays\n  const handleRegionsChange = (regions: string[]) => {\n    state.setSelectedRegions(regions);\n    // Reset dependent dropdowns when regions change\n    state.setSelectedDivisions([]);\n    state.setSelectedOffices([]);\n  };\n\n  const handleDivisionsChange = (divisions: string[]) => {\n    state.setSelectedDivisions(divisions);\n    // Reset dependent dropdown when divisions change\n    state.setSelectedOffices([]);\n  };\n\n  const handleOfficesChange = (offices: string[]) => {\n    state.setSelectedOffices(offices);\n  };\n\n  const handleFrequencyChange = (frequency: string) => {\n    state.setSelectedFrequency(frequency);\n  };\n\n  // All card management functions are now handled by the useCardManagement hook\n\n  // All page builder functions are now handled by the usePageConfiguration hook\n\n  // All rendering functions are now handled by separate components\n  \n  // All field rendering is now handled by the FieldConfigItem component\n\n  return (\n    <>\n      <div className=\"page-builder\">\n        {state.error && <div className=\"error-message\">{state.error}</div>}\n        {state.success && (\n          <div className=\"success-message\">\n            {state.success}\n          </div>\n        )}\n        <h2>Report & Page Builder</h2>\n\n        <CardSelector\n          categories={state.categories}\n          selectedCard={state.selectedCard}\n          onCardChange={handleCardChange}\n          actionType={state.actionType}\n          onActionChange={handleActionChange}\n          isLoading={state.isLoading}\n          onCreateAction={handleCreateAction}\n          onWebPageAction={handleWebPageAction}\n        />\n\n        {/* Modal for adding/creating new card */}\n        {state.isAddingNewCard && (\n          <Modal\n            isOpen={state.isAddingNewCard}\n            onClose={() => {\n              state.setIsAddingNewCard(false);\n              state.setActionType('');\n              state.setNewCardId('');\n              state.setNewCardTitle('');\n            }}\n            title={\n              state.actionType === 'addNewCardGlobal' ? \"Create New Main Report\" :\n              state.selectedCard && state.actionType === 'createNestedCard' ? `Add Nested Report under \"${state.categories.find(c => c.id === state.selectedCard)?.title}\"` :\n              \"Create New Report\"\n            }\n          >\n            <div className=\"new-card-form\">\n              <input\n                type=\"text\"\n                placeholder=\"Report ID (e.g., 'new-report-id')\"\n                value={state.newCardId}\n                onChange={(e) => state.setNewCardId(e.target.value.toLowerCase().replace(/\\s+/g, '-'))}\n                className=\"form-control mb-2\"\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Report Title\"\n                value={state.newCardTitle}\n                onChange={(e) => state.setNewCardTitle(e.target.value)}\n                className=\"form-control mb-2\"\n              />\n              <div className=\"form-buttons modal-buttons\">\n                <button\n                  onClick={cardManagement.handleConfirmCreate}\n                  disabled={state.isLoading || !state.newCardId || !state.newCardTitle}\n                  className=\"btn btn-primary\"\n                >\n                  {state.isLoading ? 'Creating...' : 'Confirm & Create Report'}\n                </button>\n                <button onClick={() => {\n                  state.setIsAddingNewCard(false);\n                  state.setActionType('');\n                  state.setNewCardId('');\n                  state.setNewCardTitle('');\n                }} className=\"btn btn-secondary\">\n                  Cancel\n                </button>\n              </div>\n            </div>\n          </Modal>\n        )}\n\n        {/* Conditional Rendering for Card Management OR Page Builder OR Warnings */}\n        {state.selectedCard && (\n          <>\n            {/* Card Management Section */}\n            {!(state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig) && (\n              <CardManagement\n                selectedCard={state.selectedCard}\n                categories={state.categories}\n                onEditCard={cardManagement.handleEditCard}\n                onDeleteCard={cardManagement.handleDeleteClick}\n              />\n            )}\n\n            {/* Report Configuration Dropdowns */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && (\n              <ReportConfiguration\n                selectedRegions={state.selectedRegions}\n                selectedDivisions={state.selectedDivisions}\n                selectedOffices={state.selectedOffices}\n                selectedFrequency={state.selectedFrequency}\n                onRegionsChange={handleRegionsChange}\n                onDivisionsChange={handleDivisionsChange}\n                onOfficesChange={handleOfficesChange}\n                onFrequencyChange={handleFrequencyChange}\n              />\n            )}\n\n            {/* Page Builder Content */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig && (\n              <PageBuilderContent\n                pageConfig={state.pageConfig}\n                fields={state.fields}\n                onAddField={pageConfiguration.addField}\n                onUpdateField={pageConfiguration.updateField}\n                onRemoveField={pageConfiguration.removeField}\n                onSave={pageConfiguration.handleSave}\n                onPreview={pageConfiguration.handlePreview}\n                loading={state.loading}\n              />\n            )}\n\n            {/* Warning Messages */}\n            {state.actionType === 'createWebPage' && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && (\n              <div className=\"warning-message mt-3 p-2 bg-warning text-dark rounded\">\n                Page configuration is only available for final nested reports (which are not main reports). Please select an appropriate nested report to configure its page, or create one.\n              </div>\n            )}\n            {state.actionType !== 'createWebPage' && !isLeafCard(state.selectedCard, state.categories) && (\n              <div className=\"info-message mt-3 p-2 bg-info text-dark rounded\">\n                This is a parent report. You can create nested reports under it or select an existing nested report to manage or configure its page.\n              </div>\n            )}\n          </>\n        )}\n\n        {!state.selectedCard && state.actionType === '' && (\n          <div className=\"info-message mt-3 p-3 bg-light border rounded\">\n            <p>Select a report from the dropdown to manage it or configure its web page (if applicable).</p>\n            <p>If no reports exist, or to create a new top-level report, choose \"Create New Main Report\" from the action dropdown after clearing any selection.</p>\n          </div>\n        )}\n\n        {/* Modals for Edit and Delete Confirmation */}\n        {state.showEditModal && state.editingCard && (\n          <Modal\n            isOpen={state.showEditModal}\n            onClose={() => {\n              state.setShowEditModal(false);\n              state.setNewCardTitle('');\n              state.setEditingCard(null);\n            }}\n            title={`Edit Report: ${state.editingCard.title}`}\n          >\n            <input\n              type=\"text\"\n              value={state.newCardTitle}\n              onChange={(e) => state.setNewCardTitle(e.target.value)}\n              placeholder=\"New Report Title\"\n              className=\"form-control mb-2\"\n            />\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleUpdateCard}\n                className=\"btn btn-primary\"\n                disabled={state.isLoading || !state.newCardTitle.trim()}\n              >\n                {state.isLoading ? 'Updating...' : 'Update Title'}\n              </button>\n              <button\n                onClick={() => {\n                  state.setShowEditModal(false);\n                  state.setNewCardTitle('');\n                  state.setEditingCard(null);\n                }}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {state.showDeleteConfirmModal && state.cardToDelete && (\n          <Modal\n            isOpen={state.showDeleteConfirmModal}\n            onClose={() => state.setShowDeleteConfirmModal(false)}\n            title=\"Confirm Deletion\"\n          >\n            <p>Are you sure you want to delete the report \"{state.categories.find(c => c.id === state.cardToDelete)?.title}\" and ALL its nested reports and associated page configurations? This action cannot be undone.</p>\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleConfirmDelete}\n                className=\"btn btn-danger\"\n                disabled={state.isLoading}\n              >\n                {state.isLoading ? 'Deleting...' : 'Confirm Delete'}\n              </button>\n              <button\n                onClick={() => state.setShowDeleteConfirmModal(false)}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {/* Preview Modal */}\n        <Modal\n          isOpen={state.isPreviewOpen}\n          onClose={() => state.setIsPreviewOpen(false)}\n          title=\"Page Preview\"\n        >\n          <div dangerouslySetInnerHTML={{ __html: state.previewContent }} />\n        </Modal>\n      </div>\n    </>\n  );\n};\n\nexport default PageBuilder;\n\n\n", "import { useState } from 'react';\nimport { Category, FormField, PageConfig } from '../types/PageBuilderTypes';\nimport { FormField as DynamicFormField } from '../../../shared/DynamicForm';\n\nexport const usePageBuilderState = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [selectedCard, setSelectedCard] = useState<string>('');\n  const [pageConfig, setPageConfig] = useState<PageConfig | null>(null);\n  const [fields, setFields] = useState<FormField[]>([]);\n  const [availableDynamicFields, setAvailableDynamicFields] = useState<DynamicFormField[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const [isAddingNewCard, setIsAddingNewCard] = useState<boolean>(false);\n  const [newCardId, setNewCardId] = useState<string>('');\n  const [newCardTitle, setNewCardTitle] = useState<string>('');\n  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);\n\n  const [editingCard, setEditingCard] = useState<Category | null>(null);\n  const [showEditModal, setShowEditModal] = useState<boolean>(false);\n\n  const [cardToDelete, setCardToDelete] = useState<string | null>(null);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<boolean>(false);\n\n  const [actionType, setActionType] = useState<string>('');\n\n  // State for Preview Modal\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false);\n  const [previewContent, setPreviewContent] = useState('');\n\n  // New dropdown states - updated to arrays for multiple selections\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\n  const [selectedDivisions, setSelectedDivisions] = useState<string[]>([]);\n  const [selectedOffices, setSelectedOffices] = useState<string[]>([]);\n  const [selectedFrequency, setSelectedFrequency] = useState<string>('');\n\n  return {\n    // State values\n    categories,\n    selectedCard,\n    pageConfig,\n    fields,\n    availableDynamicFields,\n    isLoading,\n    loading,\n    error,\n    success,\n    isAddingNewCard,\n    newCardId,\n    newCardTitle,\n    showConfirmModal,\n    editingCard,\n    showEditModal,\n    cardToDelete,\n    showDeleteConfirmModal,\n    actionType,\n    isPreviewOpen,\n    previewContent,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n\n    // State setters\n    setCategories,\n    setSelectedCard,\n    setPageConfig,\n    setFields,\n    setAvailableDynamicFields,\n    setIsLoading,\n    setLoading,\n    setError,\n    setSuccess,\n    setIsAddingNewCard,\n    setNewCardId,\n    setNewCardTitle,\n    setShowConfirmModal,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n    setActionType,\n    setIsPreviewOpen,\n    setPreviewContent,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  };\n};\n", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemUtilityClass(slot) {\n  return generateUtilityClass('MuiListItem', slot);\n}\nconst listItemClasses = generateUtilityClasses('MuiListItem', ['root', 'container', 'dense', 'alignItemsFlexStart', 'divider', 'gutters', 'padding', 'secondaryAction']);\nexport default listItemClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemButton', slot);\n}\nconst listItemButtonClasses = generateUtilityClasses('MuiListItemButton', ['root', 'focusVisible', 'dense', 'alignItemsFlexStart', 'disabled', 'divider', 'gutters', 'selected']);\nexport default listItemButtonClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemSecondaryActionClassesUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemSecondaryAction', slot);\n}\nconst listItemSecondaryActionClasses = generateUtilityClasses('MuiListItemSecondaryAction', ['root', 'disableGutters']);\nexport default listItemSecondaryActionClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemSecondaryActionClassesUtilityClass } from \"./listItemSecondaryActionClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disableGutters,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getListItemSecondaryActionClassesUtilityClass, classes);\n};\nconst ListItemSecondaryActionRoot = styled('div', {\n  name: 'MuiListItemSecondaryAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.disableGutters && styles.disableGutters];\n  }\n})({\n  position: 'absolute',\n  right: 16,\n  top: '50%',\n  transform: 'translateY(-50%)',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.disableGutters,\n    style: {\n      right: 0\n    }\n  }]\n});\n\n/**\n * Must be used as the last child of ListItem to function properly.\n *\n * @deprecated Use the `secondaryAction` prop in the `ListItem` component instead. This component will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n */\nconst ListItemSecondaryAction = /*#__PURE__*/React.forwardRef(function ListItemSecondaryAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemSecondaryAction'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const ownerState = {\n    ...props,\n    disableGutters: context.disableGutters\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemSecondaryActionRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemSecondaryAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or selection control.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nListItemSecondaryAction.muiName = 'ListItemSecondaryAction';\nexport default ListItemSecondaryAction;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport isMuiElement from \"../utils/isMuiElement.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemUtilityClass } from \"./listItemClasses.js\";\nimport { listItemButtonClasses } from \"../ListItemButton/index.js\";\nimport ListItemSecondaryAction from \"../ListItemSecondaryAction/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.dense && styles.dense, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart, ownerState.divider && styles.divider, !ownerState.disableGutters && styles.gutters, !ownerState.disablePadding && styles.padding, ownerState.hasSecondaryAction && styles.secondaryAction];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes,\n    dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  } = ownerState;\n  const slots = {\n    root: ['root', dense && 'dense', !disableGutters && 'gutters', !disablePadding && 'padding', divider && 'divider', alignItems === 'flex-start' && 'alignItemsFlexStart', hasSecondaryAction && 'secondaryAction'],\n    container: ['container']\n  };\n  return composeClasses(slots, getListItemUtilityClass, classes);\n};\nexport const ListItemRoot = styled('div', {\n  name: 'MuiListItem',\n  slot: 'Root',\n  overridesResolver\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'flex-start',\n  alignItems: 'center',\n  position: 'relative',\n  textDecoration: 'none',\n  width: '100%',\n  boxSizing: 'border-box',\n  textAlign: 'left',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding,\n    style: {\n      paddingTop: 8,\n      paddingBottom: 8\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && ownerState.dense,\n    style: {\n      paddingTop: 4,\n      paddingBottom: 4\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !ownerState.disableGutters,\n    style: {\n      paddingLeft: 16,\n      paddingRight: 16\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !!ownerState.secondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.secondaryAction,\n    style: {\n      [`& > .${listItemButtonClasses.root}`]: {\n        paddingRight: 48\n      }\n    }\n  }, {\n    props: {\n      alignItems: 'flex-start'\n    },\n    style: {\n      alignItems: 'flex-start'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.divider,\n    style: {\n      borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,\n      backgroundClip: 'padding-box'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.button,\n    style: {\n      transition: theme.transitions.create('background-color', {\n        duration: theme.transitions.duration.shortest\n      }),\n      '&:hover': {\n        textDecoration: 'none',\n        backgroundColor: (theme.vars || theme).palette.action.hover,\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: 'transparent'\n        }\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hasSecondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }]\n})));\nconst ListItemContainer = styled('li', {\n  name: 'MuiListItem',\n  slot: 'Container'\n})({\n  position: 'relative'\n});\n\n/**\n * Uses an additional container component if `ListItemSecondaryAction` is the last child.\n */\nconst ListItem = /*#__PURE__*/React.forwardRef(function ListItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItem'\n  });\n  const {\n    alignItems = 'center',\n    children: childrenProp,\n    className,\n    component: componentProp,\n    components = {},\n    componentsProps = {},\n    ContainerComponent = 'li',\n    ContainerProps: {\n      className: ContainerClassName,\n      ...ContainerProps\n    } = {},\n    dense = false,\n    disableGutters = false,\n    disablePadding = false,\n    divider = false,\n    secondaryAction,\n    slotProps = {},\n    slots = {},\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const childContext = React.useMemo(() => ({\n    dense: dense || context.dense || false,\n    alignItems,\n    disableGutters\n  }), [alignItems, context.dense, dense, disableGutters]);\n  const listItemRef = React.useRef(null);\n  const children = React.Children.toArray(childrenProp);\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  const hasSecondaryAction = children.length && isMuiElement(children[children.length - 1], ['ListItemSecondaryAction']);\n  const ownerState = {\n    ...props,\n    alignItems,\n    dense: childContext.dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleRef = useForkRef(listItemRef, ref);\n  const Root = slots.root || components.Root || ListItemRoot;\n  const rootProps = slotProps.root || componentsProps.root || {};\n  const componentProps = {\n    className: clsx(classes.root, rootProps.className, className),\n    ...other\n  };\n  let Component = componentProp || 'li';\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  if (hasSecondaryAction) {\n    // Use div by default.\n    Component = !componentProps.component && !componentProp ? 'div' : Component;\n\n    // Avoid nesting of li > li.\n    if (ContainerComponent === 'li') {\n      if (Component === 'li') {\n        Component = 'div';\n      } else if (componentProps.component === 'li') {\n        componentProps.component = 'div';\n      }\n    }\n    return /*#__PURE__*/_jsx(ListContext.Provider, {\n      value: childContext,\n      children: /*#__PURE__*/_jsxs(ListItemContainer, {\n        as: ContainerComponent,\n        className: clsx(classes.container, ContainerClassName),\n        ref: handleRef,\n        ownerState: ownerState,\n        ...ContainerProps,\n        children: [/*#__PURE__*/_jsx(Root, {\n          ...rootProps,\n          ...(!isHostComponent(Root) && {\n            as: Component,\n            ownerState: {\n              ...ownerState,\n              ...rootProps.ownerState\n            }\n          }),\n          ...componentProps,\n          children: children\n        }), children.pop()]\n      })\n    });\n  }\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsxs(Root, {\n      ...rootProps,\n      as: Component,\n      ref: handleRef,\n      ...(!isHostComponent(Root) && {\n        ownerState: {\n          ...ownerState,\n          ...rootProps.ownerState\n        }\n      }),\n      ...componentProps,\n      children: [children, secondaryAction && /*#__PURE__*/_jsx(ListItemSecondaryAction, {\n        children: secondaryAction\n      })]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Defines the `align-items` style property.\n   * @default 'center'\n   */\n  alignItems: PropTypes.oneOf(['center', 'flex-start']),\n  /**\n   * The content of the component if a `ListItemSecondaryAction` is used it must\n   * be the last child.\n   */\n  children: chainPropTypes(PropTypes.node, props => {\n    const children = React.Children.toArray(props.children);\n\n    // React.Children.toArray(props.children).findLastIndex(isListItemSecondaryAction)\n    let secondaryActionIndex = -1;\n    for (let i = children.length - 1; i >= 0; i -= 1) {\n      const child = children[i];\n      if (isMuiElement(child, ['ListItemSecondaryAction'])) {\n        secondaryActionIndex = i;\n        break;\n      }\n    }\n\n    //  is ListItemSecondaryAction the last child of ListItem\n    if (secondaryActionIndex !== -1 && secondaryActionIndex !== children.length - 1) {\n      return new Error('MUI: You used an element after ListItemSecondaryAction. ' + 'For ListItem to detect that it has a secondary action ' + 'you must pass it as the last child to ListItem.');\n    }\n    return null;\n  }),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The container component used when a `ListItemSecondaryAction` is the last child.\n   * @default 'li'\n   * @deprecated Use the `component` or `slots.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerComponent: elementTypeAcceptingRef,\n  /**\n   * Props applied to the container component if used.\n   * @default {}\n   * @deprecated Use the `slotProps.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerProps: PropTypes.object,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used.\n   * The prop defaults to the value inherited from the parent List component.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * If `true`, all padding is removed.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * If `true`, a 1px light border is added to the bottom of the list item.\n   * @default false\n   */\n  divider: PropTypes.bool,\n  /**\n   * The element to display at the end of ListItem.\n   */\n  secondaryAction: PropTypes.node,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItem;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography, { typographyClasses } from \"../Typography/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport listItemTextClasses, { getListItemTextUtilityClass } from \"./listItemTextClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    inset,\n    primary,\n    secondary,\n    dense\n  } = ownerState;\n  const slots = {\n    root: ['root', inset && 'inset', dense && 'dense', primary && secondary && 'multiline'],\n    primary: ['primary'],\n    secondary: ['secondary']\n  };\n  return composeClasses(slots, getListItemTextUtilityClass, classes);\n};\nconst ListItemTextRoot = styled('div', {\n  name: 'MuiListItemText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${listItemTextClasses.primary}`]: styles.primary\n    }, {\n      [`& .${listItemTextClasses.secondary}`]: styles.secondary\n    }, styles.root, ownerState.inset && styles.inset, ownerState.primary && ownerState.secondary && styles.multiline, ownerState.dense && styles.dense];\n  }\n})({\n  flex: '1 1 auto',\n  minWidth: 0,\n  marginTop: 4,\n  marginBottom: 4,\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.primary})`]: {\n    display: 'block'\n  },\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.secondary})`]: {\n    display: 'block'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.primary && ownerState.secondary,\n    style: {\n      marginTop: 6,\n      marginBottom: 6\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.inset,\n    style: {\n      paddingLeft: 56\n    }\n  }]\n});\nconst ListItemText = /*#__PURE__*/React.forwardRef(function ListItemText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemText'\n  });\n  const {\n    children,\n    className,\n    disableTypography = false,\n    inset = false,\n    primary: primaryProp,\n    primaryTypographyProps,\n    secondary: secondaryProp,\n    secondaryTypographyProps,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const {\n    dense\n  } = React.useContext(ListContext);\n  let primary = primaryProp != null ? primaryProp : children;\n  let secondary = secondaryProp;\n  const ownerState = {\n    ...props,\n    disableTypography,\n    inset,\n    primary: !!primary,\n    secondary: !!secondary,\n    dense\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      primary: primaryTypographyProps,\n      secondary: secondaryTypographyProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: ListItemTextRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    ref\n  });\n  const [PrimarySlot, primarySlotProps] = useSlot('primary', {\n    className: classes.primary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SecondarySlot, secondarySlotProps] = useSlot('secondary', {\n    className: classes.secondary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  if (primary != null && primary.type !== Typography && !disableTypography) {\n    primary = /*#__PURE__*/_jsx(PrimarySlot, {\n      variant: dense ? 'body2' : 'body1',\n      component: primarySlotProps?.variant ? undefined : 'span',\n      ...primarySlotProps,\n      children: primary\n    });\n  }\n  if (secondary != null && secondary.type !== Typography && !disableTypography) {\n    secondary = /*#__PURE__*/_jsx(SecondarySlot, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      ...secondarySlotProps,\n      children: secondary\n    });\n  }\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [primary, secondary]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Alias for the `primary` prop.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the children won't be wrapped by a Typography component.\n   * This can be useful to render an alternative Typography variant by wrapping\n   * the `children` (or `primary`) text, and optional `secondary` text\n   * with the Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * If `true`, the children are indented.\n   * This should be used if there is no left avatar or left icon.\n   * @default false\n   */\n  inset: PropTypes.bool,\n  /**\n   * The main content element.\n   */\n  primary: PropTypes.node,\n  /**\n   * These props will be forwarded to the primary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.primary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  primaryTypographyProps: PropTypes.object,\n  /**\n   * The secondary content element.\n   */\n  secondary: PropTypes.node,\n  /**\n   * These props will be forwarded to the secondary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.secondary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  secondaryTypographyProps: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    primary: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    secondary: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    primary: PropTypes.elementType,\n    root: PropTypes.elementType,\n    secondary: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemText;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDividerUtilityClass } from \"./dividerClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    absolute,\n    children,\n    classes,\n    flexItem,\n    light,\n    orientation,\n    textAlign,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', absolute && 'absolute', variant, light && 'light', orientation === 'vertical' && 'vertical', flexItem && 'flexItem', children && 'withChildren', children && orientation === 'vertical' && 'withChildrenVertical', textAlign === 'right' && orientation !== 'vertical' && 'textAlignRight', textAlign === 'left' && orientation !== 'vertical' && 'textAlignLeft'],\n    wrapper: ['wrapper', orientation === 'vertical' && 'wrapperVertical']\n  };\n  return composeClasses(slots, getDividerUtilityClass, classes);\n};\nconst DividerRoot = styled('div', {\n  name: 'MuiDivider',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.absolute && styles.absolute, styles[ownerState.variant], ownerState.light && styles.light, ownerState.orientation === 'vertical' && styles.vertical, ownerState.flexItem && styles.flexItem, ownerState.children && styles.withChildren, ownerState.children && ownerState.orientation === 'vertical' && styles.withChildrenVertical, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && styles.textAlignRight, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && styles.textAlignLeft];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 0,\n  // Reset browser default style.\n  flexShrink: 0,\n  borderWidth: 0,\n  borderStyle: 'solid',\n  borderColor: (theme.vars || theme).palette.divider,\n  borderBottomWidth: 'thin',\n  variants: [{\n    props: {\n      absolute: true\n    },\n    style: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      width: '100%'\n    }\n  }, {\n    props: {\n      light: true\n    },\n    style: {\n      borderColor: theme.vars ? `rgba(${theme.vars.palette.dividerChannel} / 0.08)` : alpha(theme.palette.divider, 0.08)\n    }\n  }, {\n    props: {\n      variant: 'inset'\n    },\n    style: {\n      marginLeft: 72\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'horizontal'\n    },\n    style: {\n      marginLeft: theme.spacing(2),\n      marginRight: theme.spacing(2)\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'vertical'\n    },\n    style: {\n      marginTop: theme.spacing(1),\n      marginBottom: theme.spacing(1)\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      height: '100%',\n      borderBottomWidth: 0,\n      borderRightWidth: 'thin'\n    }\n  }, {\n    props: {\n      flexItem: true\n    },\n    style: {\n      alignSelf: 'stretch',\n      height: 'auto'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.children,\n    style: {\n      display: 'flex',\n      textAlign: 'center',\n      border: 0,\n      borderTopStyle: 'solid',\n      borderLeftStyle: 'solid',\n      '&::before, &::after': {\n        content: '\"\"',\n        alignSelf: 'center'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.children && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before, &::after': {\n        width: '100%',\n        borderTop: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderTopStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.orientation === 'vertical' && ownerState.children,\n    style: {\n      flexDirection: 'column',\n      '&::before, &::after': {\n        height: '100%',\n        borderLeft: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderLeftStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '90%'\n      },\n      '&::after': {\n        width: '10%'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '10%'\n      },\n      '&::after': {\n        width: '90%'\n      }\n    }\n  }]\n})));\nconst DividerWrapper = styled('span', {\n  name: 'MuiDivider',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.wrapper, ownerState.orientation === 'vertical' && styles.wrapperVertical];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-block',\n  paddingLeft: `calc(${theme.spacing(1)} * 1.2)`,\n  paddingRight: `calc(${theme.spacing(1)} * 1.2)`,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      paddingTop: `calc(${theme.spacing(1)} * 1.2)`,\n      paddingBottom: `calc(${theme.spacing(1)} * 1.2)`\n    }\n  }]\n})));\nconst Divider = /*#__PURE__*/React.forwardRef(function Divider(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDivider'\n  });\n  const {\n    absolute = false,\n    children,\n    className,\n    orientation = 'horizontal',\n    component = children || orientation === 'vertical' ? 'div' : 'hr',\n    flexItem = false,\n    light = false,\n    role = component !== 'hr' ? 'separator' : undefined,\n    textAlign = 'center',\n    variant = 'fullWidth',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    absolute,\n    component,\n    flexItem,\n    light,\n    orientation,\n    role,\n    textAlign,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DividerRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    role: role,\n    ref: ref,\n    ownerState: ownerState,\n    \"aria-orientation\": role === 'separator' && (component !== 'hr' || orientation === 'vertical') ? orientation : undefined,\n    ...other,\n    children: children ? /*#__PURE__*/_jsx(DividerWrapper, {\n      className: classes.wrapper,\n      ownerState: ownerState,\n      children: children\n    }) : null\n  });\n});\n\n/**\n * The following flag is used to ensure that this component isn't tabbable i.e.\n * does not get highlight/focus inside of MUI List.\n */\nif (Divider) {\n  Divider.muiSkipListHighlight = true;\n}\nprocess.env.NODE_ENV !== \"production\" ? Divider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Absolutely position the element.\n   * @default false\n   */\n  absolute: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, a vertical divider will have the correct height when used in flex container.\n   * (By default, a vertical divider will have a calculated height of `0px` if it is the child of a flex container.)\n   * @default false\n   */\n  flexItem: PropTypes.bool,\n  /**\n   * If `true`, the divider will have a lighter color.\n   * @default false\n   * @deprecated Use <Divider sx={{ opacity: 0.6 }} /> (or any opacity or color) instead. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  light: PropTypes.bool,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The text alignment.\n   * @default 'center'\n   */\n  textAlign: PropTypes.oneOf(['center', 'left', 'right']),\n  /**\n   * The variant to use.\n   * @default 'fullWidth'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['fullWidth', 'inset', 'middle']), PropTypes.string])\n} : void 0;\nexport default Divider;", "import { useState, useEffect } from 'react';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataEnhancedReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n  totalRecords: number;\n  approach: string;\n}\n\n/**\n * Enhanced office data hook with comprehensive pagination\n * Mirrors the successful Flutter implementation to overcome 1000-record limit\n */\nexport const useOfficeDataEnhanced = (): UseOfficeDataEnhancedReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [totalRecords, setTotalRecords] = useState<number>(0);\n  const [approach, setApproach] = useState<string>('');\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataEnhanced: Starting comprehensive office data fetch...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allOfficeData = await OfficeService.fetchAllOfficeData();\n      \n      console.log('✅ useOfficeDataEnhanced: Fetched office records:', allOfficeData.length, 'records');\n      setTotalRecords(allOfficeData.length);\n\n      if (allOfficeData.length === 0) {\n        console.log('⚠️ useOfficeDataEnhanced: No office records found');\n        setRegions([]);\n        setDivisions([]);\n        setOffices([]);\n        setApproach('no-data');\n        return;\n      }\n\n      // Process regions - get unique regions\n      const uniqueRegions = new Set<string>();\n      allOfficeData.forEach(office => {\n        if (office.Region && office.Region.trim()) {\n          uniqueRegions.add(office.Region.trim());\n        }\n      });\n\n      const regionsArray: Region[] = Array.from(uniqueRegions)\n        .sort()\n        .map(regionName => ({\n          id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed regions:', regionsArray.length);\n\n      // Process divisions - get unique divisions with their regions\n      const uniqueDivisions = new Map<string, string>();\n      allOfficeData.forEach(office => {\n        if (office.Division && office.Division.trim() && office.Region && office.Region.trim()) {\n          uniqueDivisions.set(office.Division.trim(), office.Region.trim());\n        }\n      });\n\n      const divisionsArray: Division[] = Array.from(uniqueDivisions.entries())\n        .sort(([a], [b]) => a.localeCompare(b))\n        .map(([divisionName, regionName]) => ({\n          id: divisionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: divisionName,\n          region: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed divisions:', divisionsArray.length);\n\n      // Process offices - use office name as ID for consistency\n      const officesArray: Office[] = allOfficeData\n        .filter(office => office['Office name'] && office['Office name'].trim())\n        .map(office => ({\n          id: office['Office name'], // Use office name as ID for form targeting\n          name: office['Office name'],\n          region: office.Region || '',\n          division: office.Division || '',\n          facilityId: office['Office name'], // Keep for reference\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed offices:', officesArray.length);\n\n      // Log comprehensive statistics\n      logOfficeStatistics(allOfficeData, regionsArray, divisionsArray, officesArray);\n\n      // Set the processed data\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n      setApproach('enhanced-pagination');\n\n      console.log('✅ useOfficeDataEnhanced: Data processing complete');\n\n    } catch (err) {\n      console.error('❌ useOfficeDataEnhanced: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n      setTotalRecords(0);\n      setApproach('error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n    totalRecords,\n    approach,\n  };\n};\n\n/**\n * Log comprehensive statistics about the processed office data\n */\nfunction logOfficeStatistics(\n  allOfficeData: any[],\n  regions: Region[],\n  divisions: Division[],\n  offices: Office[]\n): void {\n  console.log('📊 useOfficeDataEnhanced: === COMPREHENSIVE STATISTICS ===');\n  console.log(`📊 useOfficeDataEnhanced: Raw records: ${allOfficeData.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed regions: ${regions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed divisions: ${divisions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed offices: ${offices.length}`);\n\n  if (offices.length > 0) {\n    // Alphabetical range\n    const sortedNames = offices.map(o => o.name).sort();\n    console.log(`📊 useOfficeDataEnhanced: Office range - First: \"${sortedNames[0]}\"`);\n    console.log(`📊 useOfficeDataEnhanced: Office range - Last: \"${sortedNames[sortedNames.length - 1]}\"`);\n\n    // Letter distribution\n    const letterCounts: { [key: string]: number } = {};\n    offices.forEach(office => {\n      const firstLetter = office.name.charAt(0).toUpperCase();\n      letterCounts[firstLetter] = (letterCounts[firstLetter] || 0) + 1;\n    });\n\n    console.log('📊 useOfficeDataEnhanced: Letter distribution:');\n    Object.keys(letterCounts).sort().forEach(letter => {\n      console.log(`📊 useOfficeDataEnhanced: ${letter}: ${letterCounts[letter]} offices`);\n    });\n\n    // Check for specific offices\n    const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n    const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n    \n    console.log(`📊 useOfficeDataEnhanced: Contains \"Tirupur division\": ${!!tirupurDivision}`);\n    console.log(`📊 useOfficeDataEnhanced: Contains \"Coimbatore division\": ${!!coimbatoreDivision}`);\n\n    if (tirupurDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Tirupur division: \"${tirupurDivision.name}\"`);\n    }\n    if (coimbatoreDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Coimbatore division: \"${coimbatoreDivision.name}\"`);\n    }\n\n    // Region breakdown\n    if (regions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Regions found:');\n      regions.forEach(region => {\n        const regionOffices = offices.filter(o => o.region === region.name);\n        console.log(`📊 useOfficeDataEnhanced: ${region.name}: ${regionOffices.length} offices`);\n      });\n    }\n\n    // Division breakdown\n    if (divisions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Top 10 divisions by office count:');\n      const divisionCounts = divisions.map(division => ({\n        name: division.name,\n        count: offices.filter(o => o.division === division.name).length\n      })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n      divisionCounts.forEach(division => {\n        console.log(`📊 useOfficeDataEnhanced: ${division.name}: ${division.count} offices`);\n      });\n    }\n  }\n\n  console.log('📊 useOfficeDataEnhanced: === END STATISTICS ===');\n}\n\nexport default useOfficeDataEnhanced;\n", "import React from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n  Chip,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport { useOfficeDataEnhanced } from './hooks/useOfficeDataEnhanced';\n\n/**\n * Test component to verify enhanced office loading functionality\n * This component displays comprehensive statistics about the loaded office data\n */\nconst OfficeLoadingTest: React.FC = () => {\n  const {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    totalRecords,\n    approach,\n    refetch\n  } = useOfficeDataEnhanced();\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" flexDirection=\"column\" alignItems=\"center\" p={4}>\n        <CircularProgress size={60} />\n        <Typography variant=\"h6\" sx={{ mt: 2 }}>\n          Loading office data with comprehensive pagination...\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n          This may take a moment as we fetch ALL records from the database\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={4}>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n        <Typography variant=\"body2\">\n          Failed to load office data. Please check the console for detailed error information.\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Calculate statistics\n  const letterDistribution: { [key: string]: number } = {};\n  offices.forEach(office => {\n    const firstLetter = office.name.charAt(0).toUpperCase();\n    letterDistribution[firstLetter] = (letterDistribution[firstLetter] || 0) + 1;\n  });\n\n  const sortedOfficeNames = offices.map(o => o.name).sort();\n  const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n  const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n\n  // Top regions by office count\n  const regionCounts = regions.map(region => ({\n    name: region.name,\n    count: offices.filter(o => o.region === region.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 5);\n\n  // Top divisions by office count\n  const divisionCounts = divisions.map(division => ({\n    name: division.name,\n    count: offices.filter(o => o.division === division.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n  return (\n    <Box p={4}>\n      <Typography variant=\"h4\" gutterBottom>\n        Office Loading Test - Enhanced Pagination\n      </Typography>\n      \n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\n        This test verifies that the enhanced office loading system can fetch ALL records from the Supabase database,\n        overcoming the default 1000-record pagination limit.\n      </Typography>\n\n      {/* Summary Cards */}\n      <Box display=\"flex\" gap={3} sx={{ mb: 4, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Total Records\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalRecords.toLocaleString()}\n              </Typography>\n              <Chip\n                label={approach}\n                size=\"small\"\n                color={totalRecords > 1000 ? \"success\" : \"warning\"}\n                sx={{ mt: 1 }}\n              />\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Regions\n              </Typography>\n              <Typography variant=\"h4\">\n                {regions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Divisions\n              </Typography>\n              <Typography variant=\"h4\">\n                {divisions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Offices\n              </Typography>\n              <Typography variant=\"h4\">\n                {offices.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n      </Box>\n\n      {/* Verification Results */}\n      <Box display=\"flex\" gap={3} sx={{ flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Verification Results\n            </Typography>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Records exceed 1000 limit:\n              </Typography>\n              <Chip\n                label={totalRecords > 1000 ? \"✅ YES\" : \"❌ NO\"}\n                color={totalRecords > 1000 ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Alphabetical Range:\n              </Typography>\n              <Typography variant=\"body2\">\n                First: \"{sortedOfficeNames[0] || 'N/A'}\"\n              </Typography>\n              <Typography variant=\"body2\">\n                Last: \"{sortedOfficeNames[sortedOfficeNames.length - 1] || 'N/A'}\"\n              </Typography>\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Tirupur Division Found:\n              </Typography>\n              <Chip\n                label={tirupurDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={tirupurDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {tirupurDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{tirupurDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Coimbatore Division Found:\n              </Typography>\n              <Chip\n                label={coimbatoreDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={coimbatoreDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {coimbatoreDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{coimbatoreDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Letter Distribution\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {Object.keys(letterDistribution).sort().map(letter => (\n                <Box key={letter} display=\"flex\" justifyContent=\"space-between\" sx={{ mb: 1 }}>\n                  <Typography variant=\"body2\">{letter}:</Typography>\n                  <Typography variant=\"body2\">{letterDistribution[letter]} offices</Typography>\n                </Box>\n              ))}\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      <Box display=\"flex\" gap={3} sx={{ mt: 3, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 5 Regions by Office Count\n            </Typography>\n            <List dense>\n              {regionCounts.map((region, index) => (\n                <React.Fragment key={region.name}>\n                  <ListItem>\n                    <ListItemText\n                      primary={region.name}\n                      secondary={`${region.count} offices`}\n                    />\n                  </ListItem>\n                  {index < regionCounts.length - 1 && <Divider />}\n                </React.Fragment>\n              ))}\n            </List>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 10 Divisions by Office Count\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              <List dense>\n                {divisionCounts.map((division, index) => (\n                  <React.Fragment key={division.name}>\n                    <ListItem>\n                      <ListItemText\n                        primary={division.name}\n                        secondary={`${division.count} offices`}\n                      />\n                    </ListItem>\n                    {index < divisionCounts.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      {/* Success Message */}\n      {totalRecords > 1000 && (\n        <Alert severity=\"success\" sx={{ mt: 3 }}>\n          <Typography variant=\"h6\">\n            🎉 Success! Enhanced Office Loading is Working\n          </Typography>\n          <Typography variant=\"body2\">\n            The system successfully loaded {totalRecords.toLocaleString()} office records, \n            which exceeds the default 1000-record Supabase limit. This confirms that the \n            comprehensive pagination solution is working correctly.\n          </Typography>\n        </Alert>\n      )}\n    </Box>\n  );\n};\n\nexport default OfficeLoadingTest;\n", "import React, { useState } from 'react';\nimport { collection, doc, setDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { FaTruck } from 'react-icons/fa';\n\nconst AddMMUCategory: React.FC = () => {\n  const [isAdding, setIsAdding] = useState(false);\n  const [success, setSuccess] = useState('');\n  const [error, setError] = useState('');\n\n  const addMMUCategory = async () => {\n    setIsAdding(true);\n    setError('');\n    setSuccess('');\n\n    try {\n      // Add MMU as a main category to Firebase\n      const mmuCategoryData = {\n        id: 'mmu',\n        title: 'MMU',\n        parentId: '', // Empty string for top-level category\n        isPage: false, // This is a category, not a form page\n        pageId: null,\n        lastUpdated: new Date(),\n        description: 'Mail Motor Unit - Vehicle management and logistics',\n        order: 9, // Position before \"Others\"\n        icon: 'truck', // Icon identifier\n      };\n\n      // Add to Firebase pages collection (used by PageBuilder)\n      await setDoc(doc(db, 'pages', 'mmu'), mmuCategoryData);\n\n      // Also add to categories collection for mobile app\n      await setDoc(doc(db, 'categories', 'mmu'), {\n        id: 'mmu',\n        name: 'MMU',\n        icon: 'fatruck', // This will map to FontAwesome truck icon in mobile\n        parentId: '', // Empty string for top-level category\n        isPage: false,\n        pageId: null,\n        lastUpdated: new Date(),\n        description: 'Mail Motor Unit - Vehicle management and logistics',\n        order: 9,\n      });\n\n      setSuccess('✅ MMU category successfully added! You can now see it in both web and mobile data entry sections.');\n      \n      // Refresh the page after 2 seconds to show the new category\n      setTimeout(() => {\n        window.location.reload();\n      }, 2000);\n\n    } catch (err) {\n      console.error('Error adding MMU category:', err);\n      setError('❌ Failed to add MMU category. Please try again.');\n    } finally {\n      setIsAdding(false);\n    }\n  };\n\n  return (\n    <div style={{\n      padding: '20px',\n      margin: '20px 0',\n      border: '2px solid #007bff',\n      borderRadius: '8px',\n      backgroundColor: '#f8f9fa'\n    }}>\n      <div style={{ display: 'flex', alignItems: 'center', gap: '12px', marginBottom: '15px' }}>\n        {React.createElement(FaTruck as React.ComponentType<any>, { size: 24, color: '#007bff' })}\n        <h3 style={{ margin: 0, color: '#007bff' }}>Add MMU Category</h3>\n      </div>\n      \n      <p style={{ marginBottom: '15px', color: '#495057' }}>\n        Click the button below to add the MMU (Mail Motor Unit) category to both web and mobile data entry sections.\n        This will create a new top-level category for vehicle management and logistics forms.\n      </p>\n\n      {error && (\n        <div style={{\n          padding: '10px',\n          marginBottom: '15px',\n          backgroundColor: '#f8d7da',\n          color: '#721c24',\n          border: '1px solid #f5c6cb',\n          borderRadius: '4px'\n        }}>\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div style={{\n          padding: '10px',\n          marginBottom: '15px',\n          backgroundColor: '#d4edda',\n          color: '#155724',\n          border: '1px solid #c3e6cb',\n          borderRadius: '4px'\n        }}>\n          {success}\n        </div>\n      )}\n\n      <button\n        onClick={addMMUCategory}\n        disabled={isAdding}\n        style={{\n          padding: '12px 24px',\n          backgroundColor: isAdding ? '#6c757d' : '#007bff',\n          color: 'white',\n          border: 'none',\n          borderRadius: '4px',\n          cursor: isAdding ? 'not-allowed' : 'pointer',\n          fontSize: '16px',\n          fontWeight: 'bold',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        }}\n      >\n        {React.createElement(FaTruck as React.ComponentType<any>, { size: 16 })}\n        {isAdding ? 'Adding MMU Category...' : 'Add MMU Category to Database'}\n      </button>\n\n      <div style={{ marginTop: '15px', fontSize: '14px', color: '#6c757d' }}>\n        <strong>What this will do:</strong>\n        <ul style={{ marginTop: '8px', paddingLeft: '20px' }}>\n          <li>Add MMU category to Firebase database</li>\n          <li>Make MMU appear in web admin panel</li>\n          <li>Make MMU appear in mobile app data entry</li>\n          <li>Enable creation of MMU-related forms</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default AddMMUCategory;\n", "import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Sidebar from '../shared/Sidebar';\nimport StatsCards from '../shared/StatsCards';\nimport PageBuilder from './business/PageBuilder';\nimport OfficeLoadingTest from './business/OfficeLoadingTest';\nimport AddMMUCategory from './AddMMUCategory';\n\nconst AdminPage: React.FC = () => {\n  const { currentUser } = useAuth();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState<any>(null);\n  const [showOfficeTest, setShowOfficeTest] = useState<boolean>(false);\n\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        const userRef = doc(db, 'employees', currentUser.uid);\n        const userSnap = await getDoc(userRef);\n        if (userSnap.exists()) {\n          setUserData(userSnap.data());\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n      <div className=\"main-content\">\n        <div className=\"page-title\">\n          Admin Dashboard\n          <button\n            onClick={() => setShowOfficeTest(!showOfficeTest)}\n            style={{\n              marginLeft: '20px',\n              padding: '8px 16px',\n              backgroundColor: showOfficeTest ? '#dc3545' : '#007bff',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {showOfficeTest ? 'Hide Office Test' : 'Show Office Loading Test'}\n          </button>\n        </div>\n        <StatsCards />\n        <AddMMUCategory />\n        {showOfficeTest ? <OfficeLoadingTest /> : <PageBuilder />}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPage;"], "names": ["getCardUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "CardRoot", "styled", "Paper", "name", "overflow", "React", "inProps", "ref", "props", "useDefaultProps", "className", "raised", "other", "ownerState", "classes", "composeClasses", "root", "useUtilityClasses", "_jsx", "clsx", "elevation", "undefined", "getListItemTextUtilityClass", "stats", "title", "value", "StatsCards", "children", "map", "stat", "index", "_jsxs", "getDividerUtilityClass", "getCardContentUtilityClass", "CardContentRoot", "padding", "paddingBottom", "component", "as", "_ref", "isOpen", "onClose", "onClick", "e", "stopPropagation", "isMainCard", "cardId", "allCategories", "card", "find", "c", "id", "parentId", "isLeafCard", "some", "organizeCards", "list", "roots", "for<PERSON>ach", "item", "_map$item$parentId$ch", "push", "getAllDescendantIds", "descendants", "filter", "child", "concat", "useCardManagement", "categories", "setCategories", "selected<PERSON><PERSON>", "setSelectedCard", "newCardId", "setNewCardId", "newCardTitle", "setNewCardTitle", "actionType", "setActionType", "setIsLoading", "setError", "setSuccess", "setShowConfirmModal", "setIsAddingNewCard", "setPageConfig", "setFields", "setEditingCard", "setShowEditModal", "setCardToDelete", "setShowDeleteConfirmModal", "fetchCategories", "useCallback", "async", "fetchedCategories", "getDocs", "collection", "db", "docs", "doc", "data", "err", "console", "error", "handleAddNewCard", "doc<PERSON>ef", "getDoc", "exists", "checkDuplicateId", "handleConfirmCreate", "_categories$find", "parentIdToSet", "newPath", "path", "replace", "cardRef", "icon", "generatedIcon", "color", "generatedColor", "hash", "split", "reduce", "acc", "char", "charCodeAt", "icons", "FaFolder", "FaFileAlt", "FaCog", "FaFolderOpen", "colors", "length", "generateCardStyle", "setDoc", "lastUpdated", "Date", "toISOString", "fields", "isPage", "pageId", "setTimeout", "handleEditCard", "handleUpdateCard", "editingCard", "updateDoc", "handleDeleteClick", "handleConfirmDelete", "batch", "writeBatch", "allDescendants", "idsToDelete", "delete", "commit", "onCardChange", "onActionChange", "isLoading", "onCreateAction", "onWebPageAction", "renderCardOptions", "cards", "level", "arguments", "flatMap", "style", "paddingLeft", "repeat", "onChange", "newSelectedCard", "target", "disabled", "newAction", "_Fragment", "onEditCard", "onDeleteCard", "selectedCate<PERSON><PERSON>", "FaEdit", "FaTrash", "_field$options2", "field", "onUpdate", "onRemove", "handleOptionChange", "optIndex", "key", "newOptions", "options", "handleDefaultValueChange", "type", "newDefaultValue", "checked", "defaultValue", "label", "htmlFor", "placeholder", "required", "includes", "min", "parseFloat", "max", "opt", "_field$options", "_", "i", "removeOption", "addOption", "String", "Boolean", "Array", "isArray", "join", "s", "trim", "buttonText", "sectionTitle", "pageConfig", "onAddField", "onUpdateField", "onRemoveField", "onSave", "onPreview", "loading", "FieldConfigItem", "FaPlus", "FaSave", "REPORT_FREQUENCIES", "<PERSON><PERSON><PERSON><PERSON>", "setIsOpen", "useState", "dropdownRef", "useRef", "useEffect", "handleClickOutside", "event", "current", "contains", "document", "addEventListener", "removeEventListener", "isAllSelected", "isIndeterminate", "backgroundColor", "borderColor", "getDisplayText", "selectedOption", "option", "maxHeight", "overflowY", "input", "indeterminate", "handleSelectAll", "handleCheckboxChange", "optionId", "selectedRegions", "selectedDivisions", "selectedOffices", "selectedFrequency", "onRegionsChange", "onDivisionsChange", "onOfficesChange", "onFrequencyChange", "regions", "divisions", "offices", "refetch", "useOfficeDataSimple", "setRegions", "setDivisions", "setOffices", "setLoading", "fetchOfficeData", "log", "allData", "OfficeService", "fetchAllOfficeData", "distinctRegions", "row", "Region", "region", "array", "indexOf", "sort", "regionsArray", "regionName", "toLowerCase", "distinctDivisions", "division", "Division", "findIndex", "x", "a", "b", "localeCompare", "divisionsArray", "officesArray", "facilityId", "useOfficeData", "selectedRegionNames", "regionId", "_regions$find", "r", "availableDivisions", "selectedDivisionNames", "divisionId", "_divisions$find", "d", "availableOffices", "office", "validDivisions", "validOffices", "officeId", "o", "role", "CheckboxDropdown", "frequency", "PageBuilder", "_state$categories$fin", "_state$categories$fin2", "state", "usePageBuilderState", "availableDynamicFields", "setAvailableDynamicFields", "success", "isAddingNewCard", "showConfirmModal", "showEditModal", "cardToDelete", "showDeleteConfirmModal", "isPreviewOpen", "setIsPreviewOpen", "previewContent", "setPreviewContent", "setSelectedRegions", "setSelectedDivisions", "setSelectedOffices", "setSelectedFrequency", "cardManagement", "pageConfiguration", "fetchDynamicFormFields", "formId", "formConfigRef", "formConfigSnap", "formConfigData", "loadPageConfig", "docSnap", "supabasePageService", "supabaseError", "selectedRegion", "selectedDivision", "selectedOffice", "addField", "newField", "now", "addFieldFromDynamic", "dynamicField", "columns", "buttonType", "onClickAction", "warn", "updateField", "updatedField", "<PERSON><PERSON><PERSON>s", "removeField", "handleSave", "cleanedFields", "cleanedField", "updatedPageConfig", "savePromises", "catch", "Error", "message", "savePageConfig", "Promise", "all", "handlePreview", "alert", "generatedPreview", "fieldHtml", "usePageConfiguration", "CardSelector", "cardIsLeaf", "cardIsMain", "action", "handleCreateAction", "handleWebPageAction", "Modal", "CardManagement", "ReportConfiguration", "PageBuilderContent", "dangerouslySetInnerHTML", "__html", "getListItemUtilityClass", "getListItemSecondaryActionClassesUtilityClass", "ListItemSecondaryActionRoot", "overridesResolver", "styles", "disableGutters", "position", "right", "top", "transform", "variants", "ListItemSecondaryAction", "context", "ListContext", "slots", "mui<PERSON><PERSON>", "ListItemRoot", "dense", "alignItems", "alignItemsFlexStart", "divider", "gutters", "disablePadding", "hasSecondaryAction", "secondaryAction", "memoTheme", "theme", "display", "justifyContent", "textDecoration", "width", "boxSizing", "textAlign", "_ref2", "paddingTop", "_ref3", "_ref4", "paddingRight", "_ref5", "_ref6", "listItemButtonClasses", "_ref7", "borderBottom", "vars", "palette", "backgroundClip", "_ref8", "button", "transition", "transitions", "create", "duration", "shortest", "hover", "_ref9", "ListItemContainer", "childrenProp", "componentProp", "components", "componentsProps", "ContainerComponent", "ContainerProps", "ContainerClassName", "slotProps", "childContext", "listItemRef", "toArray", "isMuiElement", "container", "handleRef", "useForkRef", "Root", "rootProps", "componentProps", "Component", "Provider", "isHostComponent", "pop", "ListItemTextRoot", "listItemTextClasses", "primary", "secondary", "inset", "multiline", "flex", "min<PERSON><PERSON><PERSON>", "marginTop", "marginBottom", "typographyClasses", "disableTypography", "primaryProp", "primaryTypographyProps", "secondaryProp", "secondaryTypographyProps", "externalForwardedProps", "RootSlot", "rootSlotProps", "useSlot", "elementType", "PrimarySlot", "primarySlotProps", "Typography", "SecondarySlot", "secondarySlotProps", "variant", "DividerRoot", "absolute", "light", "orientation", "vertical", "flexItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withChildrenVertical", "textAlignRight", "textAlignLeft", "margin", "flexShrink", "borderWidth", "borderStyle", "borderBottomWidth", "bottom", "left", "dividerChannel", "alpha", "marginLeft", "spacing", "marginRight", "height", "borderRightWidth", "alignSelf", "border", "borderTopStyle", "borderLeftStyle", "content", "borderTop", "flexDirection", "borderLeft", "DividerWrapper", "wrapper", "wrapperVertical", "whiteSpace", "Divider", "muiSkipListHighlight", "useOfficeDataEnhanced", "totalRecords", "setTotalRecords", "approach", "setApproach", "allOfficeData", "uniqueRegions", "Set", "add", "from", "uniqueDivisions", "Map", "set", "entries", "divisionName", "sortedNames", "letterCounts", "firstLetter", "char<PERSON>t", "toUpperCase", "Object", "keys", "letter", "tirupurDivision", "coimbatoreDivision", "regionOffices", "count", "slice", "logOfficeStatistics", "OfficeLoadingTest", "Box", "p", "CircularProgress", "size", "sx", "mt", "<PERSON><PERSON>", "severity", "mb", "letterDistribution", "sortedOfficeNames", "regionCounts", "divisionCounts", "gutterBottom", "paragraph", "gap", "flexWrap", "Card", "<PERSON><PERSON><PERSON><PERSON>", "toLocaleString", "Chip", "List", "ListItem", "ListItemText", "AddMMUCategory", "isAdding", "setIsAdding", "borderRadius", "FaTruck", "mmuCategoryData", "description", "order", "window", "location", "reload", "cursor", "fontSize", "fontWeight", "AdminPage", "currentUser", "useAuth", "userData", "setUserData", "useNavigate", "showOfficeTest", "setShowOfficeTest", "userRef", "uid", "userSnap", "fetchUserData", "Sidebar"], "sourceRoot": ""}