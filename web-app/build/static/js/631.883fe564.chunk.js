"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[631],{907:()=>{},2631:(e,r,o)=>{o.r(r),o.d(r,{default:()=>x});var t=o(5043),i=o(5472),s=o(2073),n=o(9066),l=o(1103),a=o(215);class c{static async getFormSubmissions(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{console.log("\ud83d\udd0d ReportsService: Starting getFormSubmissions..."),console.log("\ud83d\udccb ReportsService: Filters:",JSON.stringify(e,null,2)),console.log("\ud83d\udd17 ReportsService: Testing multiple data sources...");let t=null,i=null;const s=["reports_data_view","dynamic_form_submissions","reports_test_data"];for(const e of s){console.log(`\ud83e\uddea ReportsService: Trying table: ${e}`);try{const o=await a.N.from(e).select("count",{count:"exact",head:!0});if(!o.error&&null!==o.count){t=e,i=o.count,console.log(`\u2705 ReportsService: ${e} works with ${o.count} records`);break}var r;console.log(`\u274c ReportsService: ${e} failed:`,null===(r=o.error)||void 0===r?void 0:r.message)}catch(o){console.log(`\u274c ReportsService: ${e} error:`,o)}}if(!t)throw console.error("\u274c ReportsService: No working data source found"),new Error("No accessible data source found. Please run the DIRECT_QUERY_APPROACH.sql script.");console.log("\u2705 ReportsService: Using data source:",t),console.log("\ud83d\udcca ReportsService: Found",i,"records"),console.log("\ud83d\udce5 ReportsService: Fetching submissions data with user profile join...");let n=a.N.from(t).select("*").order("submitted_at",{ascending:!1});e.formIdentifier&&(console.log("\ud83d\udd0d ReportsService: Applying form identifier filter:",e.formIdentifier),n=n.eq("form_identifier",e.formIdentifier)),e.userId&&(console.log("\ud83d\udd0d ReportsService: Applying user ID filter:",e.userId),n=n.eq("user_id",e.userId)),e.startDate&&(console.log("\ud83d\udd0d ReportsService: Applying start date filter:",e.startDate),n=n.gte("submitted_at",e.startDate)),e.endDate&&(console.log("\ud83d\udd0d ReportsService: Applying end date filter:",e.endDate),n=n.lte("submitted_at",e.endDate)),e.limit&&(console.log("\ud83d\udd0d ReportsService: Applying limit:",e.limit),n=n.limit(e.limit)),e.offset&&(console.log("\ud83d\udd0d ReportsService: Applying offset:",e.offset),n=n.range(e.offset,e.offset+(e.limit||50)-1));const{data:l,error:c}=await n;if(console.log("\ud83d\udce6 ReportsService: Query response:",{dataLength:(null===l||void 0===l?void 0:l.length)||0,hasError:!!c,errorMessage:null===c||void 0===c?void 0:c.message}),c)throw console.error("\u274c ReportsService: Query error:",c),console.error("\ud83d\udd0d ReportsService: Error details:",{message:c.message,details:c.details,hint:c.hint,code:c.code}),new Error(`Failed to fetch form submissions: ${c.message}`);if(!l||0===l.length){console.warn("\u26a0\ufe0f ReportsService: No data returned from query"),console.log("\ud83d\udd0d ReportsService: Possible causes:"),console.log("  1. Table exists but has no data matching filters"),console.log("  2. All data filtered out by applied filters"),console.log("  3. RLS (Row Level Security) blocking access"),console.log("  4. Data exists but query conditions exclude it");const{count:e,error:r}=await a.N.from("dynamic_form_submissions").select("*",{count:"exact",head:!0});return console.log("\ud83d\udcca ReportsService: Total records in table:",e),r&&console.error("\u274c ReportsService: Count query error:",r),[]}console.log("\u2705 ReportsService: Successfully fetched",l.length,"submissions"),console.log("\ud83d\udcc4 ReportsService: First submission sample:",JSON.stringify(l[0],null,2)),console.log("\ud83d\udccb ReportsService: All form identifiers:",l.map((e=>e.form_identifier))),console.log("\ud83d\udccb ReportsService: Using employee_id values directly from database");const d=(l||[]).map((e=>{var r;return{...e,user_name:e.employee_id||"Unknown",user_email:"<EMAIL>",user_office:(null===(r=e.submission_data)||void 0===r?void 0:r.officeName)||"Unknown Office"}}));if(e.officeName){console.log("\ud83d\udd0d ReportsService: Applying office name filter:",e.officeName),console.log("\ud83d\udd0d ReportsService: Looking for office name in submission_data...");const r=d.filter((r=>{var o;const t=null===(o=r.submission_data)||void 0===o?void 0:o.officeName,i=r.user_office;let s=null;if(r.submission_data)for(const[e,l]of Object.entries(r.submission_data))if("string"===typeof l&&(l.includes(" RO")||l.includes(" BO")||l.includes(" SO")||l.includes(" HO")||l.includes(" DO")||l.includes("Office"))){s=l;break}const n=s||t||i||"";return console.log(`\ud83d\udccb Submission ${r.id}: office="${n}", filter="${e.officeName}"`),n.toLowerCase().includes(e.officeName.toLowerCase())}));return console.log("\ud83d\udcca ReportsService: Office filter result:",r.length,"submissions"),r}return console.log("\ud83c\udf89 ReportsService: Returning",d.length,"enhanced submissions"),d}catch(t){throw console.error("\ud83d\udca5 ReportsService: Fatal error in getFormSubmissions:",t),console.error("\ud83d\udd0d ReportsService: Error stack:",t instanceof Error?t.stack:"No stack trace"),t}}static async getReportsSummary(){try{console.log("ReportsService: Fetching reports summary...");const e="reports_summary",r=this.cache.get(e);if(r&&this.isCacheValid(r.timestamp))return console.log("ReportsService: Returning cached summary"),r.data;const o=new Date,t=new Date(o.getFullYear(),o.getMonth(),o.getDate()).toISOString(),i=new Date(o.getTime()-6048e5).toISOString(),s=new Date(o.getFullYear(),o.getMonth(),1).toISOString(),n=await this.findWorkingDataSource(),{count:l,error:c}=await a.N.from(n).select("*",{count:"exact",head:!0});if(c)throw console.error("ReportsService: Error getting total count:",c),c;console.log("ReportsService: Total submissions count:",l);const{data:d,error:f}=await a.N.from(n).select("form_identifier");if(f)throw console.error("ReportsService: Error getting forms:",f),f;const m=new Set(null===d||void 0===d?void 0:d.map((e=>e.form_identifier))).size;console.log("ReportsService: Unique forms count:",m);const{data:u,error:g}=await a.N.from(n).select("user_id");if(g)throw console.error("ReportsService: Error getting users:",g),g;const p=new Set(null===u||void 0===u?void 0:u.map((e=>e.user_id))).size;console.log("ReportsService: Unique users count:",p);const{count:h,error:b}=await a.N.from(n).select("*",{count:"exact",head:!0}).gte("submitted_at",t);if(b)throw console.error("ReportsService: Error getting today count:",b),b;const{count:y,error:x}=await a.N.from(n).select("*",{count:"exact",head:!0}).gte("submitted_at",i);if(x)throw console.error("ReportsService: Error getting week count:",x),x;const{count:v,error:S}=await a.N.from(n).select("*",{count:"exact",head:!0}).gte("submitted_at",s);if(S)throw console.error("ReportsService: Error getting month count:",S),S;const w={totalSubmissions:l||0,uniqueForms:m,uniqueUsers:p,submissionsToday:h||0,submissionsThisWeek:y||0,submissionsThisMonth:v||0};return this.cache.set(e,{data:w,timestamp:new Date}),console.log("ReportsService: Successfully generated summary:",w),w}catch(e){throw console.error("ReportsService: Error in getReportsSummary:",e),e}}static async getFormIdentifiers(){try{console.log("ReportsService: Fetching form identifiers...");const e=await this.findWorkingDataSource(),{data:r,error:o}=await a.N.from(e).select("form_identifier");if(o)throw console.error("ReportsService: Error fetching form identifiers:",o),o;const t=Array.from(new Set((null===r||void 0===r?void 0:r.map((e=>e.form_identifier)))||[])).sort();return console.log("ReportsService: Found",t.length,"unique form identifiers:",t),t}catch(e){throw console.error("ReportsService: Error fetching form identifiers:",e),e}}static async enhanceWithUserData(e){try{console.log("\ud83d\udd0d ReportsService: Enhancing submissions with user profile data...");const r=e.map((e=>e.employee_id)).filter((e=>e&&"string"===typeof e&&e.trim().length>0));console.log("\ud83d\udd0d ReportsService: Found employee IDs to lookup:",r);let o=[];if(r.length>0){const{data:e,error:t}=await a.N.from("user_profile").select("employeeId, full_name, email, office_name, designation, department").in("employeeId",r);t?console.error("\u274c ReportsService: Error fetching user profiles:",t):(o=e||[],console.log("\u2705 ReportsService: Fetched user profiles:",o))}const t=new Map;return o.forEach((e=>{t.set(e.employeeId,e)})),e.map((e=>{var r;const o=e.employee_id?t.get(e.employee_id):null;console.log(`\ud83d\udd0d User profile for submission ${e.id} (employee_id: ${e.employee_id}):`,o);const i={...e,user_name:(null===o||void 0===o?void 0:o.full_name)||e.employee_id||(e.user_id?`User ${e.user_id.substring(0,8)}`:"Unknown User"),user_email:(null===o||void 0===o?void 0:o.email)||"<EMAIL>",user_office:(null===o||void 0===o?void 0:o.office_name)||(null===(r=e.submission_data)||void 0===r?void 0:r.officeName)||"Unknown Office"};return console.log(`\u2705 Enhanced submission ${e.id}:`,{user_name:i.user_name,user_email:i.user_email,user_office:i.user_office,employee_id:e.employee_id}),i}))}catch(r){return console.error("ReportsService: Error enhancing with user data:",r),e.map((e=>({...e,user_name:e.employee_id||"Unknown User",user_email:"<EMAIL>",user_office:"Unknown Office"})))}}static async exportToCSV(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{const r=await this.getFormSubmissions(e);if(0===r.length)throw new Error("No data to export");const o=[["ID","Form Identifier","User ID","User Name","User Office","Submitted At","Submission Data"],...r.map((e=>[e.id,e.form_identifier,e.user_id,e.user_name||"",e.user_office||"",new Date(e.submitted_at).toLocaleString(),JSON.stringify(e.submission_data)]))].map((e=>e.map((e=>`"${e}"`)).join(","))).join("\n");return console.log("ReportsService: Successfully generated CSV with",r.length,"records"),o}catch(r){throw console.error("ReportsService: Error exporting to CSV:",r),r}}static async findWorkingDataSource(){const e=["reports_data_view","dynamic_form_submissions","reports_test_data"];for(const o of e)try{const{count:e,error:r}=await a.N.from(o).select("*",{count:"exact",head:!0});if(!r&&null!==e)return console.log(`\u2705 ReportsService: Using ${o} with ${e} records`),o}catch(r){console.log(`\u274c ReportsService: ${o} not accessible`)}throw new Error("No accessible data source found. Please run the DIRECT_QUERY_APPROACH.sql script.")}static clearCache(){this.cache.clear(),console.log("ReportsService: Cache cleared")}static isCacheValid(e){return((new Date).getTime()-e.getTime())/6e4<this.CACHE_EXPIRY_MINUTES}}c.CACHE_EXPIRY_MINUTES=5,c.cache=new Map;const d=c;class f{static async getFormConfig(e){try{if(this.cache.has(e))return this.cache.get(e);console.log(`\ud83d\udd0d FormConfigService: Fetching config for ${e}`);const o=[`pages/${e}`,`formConfigs/${e}`,`forms/${e}`];for(const t of o)try{const r=(0,i.H9)(s.db,t),o=await(0,i.x7)(r);if(o.exists()){const r=o.data();if(r&&r.fields)return console.log(`\u2705 FormConfigService: Found config at ${t}`,r),this.cache.set(e,r),r}}catch(r){console.log(`\u274c FormConfigService: Failed to fetch from ${t}:`,r)}return console.log(`\u26a0\ufe0f FormConfigService: No config found for ${e}`),null}catch(o){return console.error("FormConfigService: Error fetching form config:",o),null}}static async getFieldMapping(e){try{if(this.fieldMappingCache.has(e))return this.fieldMappingCache.get(e);const r=await this.getFormConfig(e),o=new Map;return r&&r.fields&&r.fields.forEach((e=>{"section"!==e.type&&"button"!==e.type&&o.set(e.id,e.label)})),this.fieldMappingCache.set(e,o),console.log(`\ud83d\udccb FormConfigService: Created field mapping for ${e}:`,o),o}catch(r){return console.error("FormConfigService: Error creating field mapping:",r),new Map}}static async getAllFieldLabels(e){const r=new Set;for(const o of e){(await this.getFieldMapping(o)).forEach((e=>r.add(e)))}return r}static async convertSubmissionData(e,r){try{const o=await this.getFieldMapping(e),t={};return Object.entries(r).forEach((e=>{let[r,i]=e;const s=o.get(r)||r;t[s]=i})),t}catch(o){return console.error("FormConfigService: Error converting submission data:",o),r}}static clearCache(){this.cache.clear(),this.fieldMappingCache.clear(),console.log("FormConfigService: Cache cleared")}}f.cache=new Map,f.fieldMappingCache=new Map;const m=f;var u=o(579);const g=e=>{let{submissions:r,completedOffices:o,formatDate:t}=e;return(0,u.jsxs)("div",{children:[(0,u.jsxs)("div",{style:{marginBottom:"1rem",color:"#666"},children:[o.length," offices have submitted their reports"]}),(0,u.jsx)("div",{style:{maxHeight:"400px",overflowY:"auto"},children:o.map(((e,o)=>{const i=(s=e,r.filter((e=>{if(e.submission_data)for(const[r,o]of Object.entries(e.submission_data))if("string"===typeof o&&o.trim()===s)return!0;return e.user_office===s})));var s;const n=i.sort(((e,r)=>new Date(r.submitted_at).getTime()-new Date(e.submitted_at).getTime()))[0];return(0,u.jsx)("div",{style:{padding:"1rem",border:"1px solid #e9ecef",borderRadius:"8px",marginBottom:"0.5rem",backgroundColor:"#f8f9fa"},children:(0,u.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"start"},children:[(0,u.jsxs)("div",{style:{flex:1},children:[(0,u.jsx)("h6",{style:{margin:"0 0 0.5rem 0",color:"#28a745",fontWeight:"bold"},children:e}),n&&(0,u.jsxs)("div",{style:{fontSize:"0.85rem",color:"#666"},children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("strong",{children:"Latest submission:"})," ",t(n.submitted_at).date," at ",t(n.submitted_at).time]}),(0,u.jsxs)("div",{children:[(0,u.jsx)("strong",{children:"Employee:"})," ",n.user_name||n.employee_id||"Unknown"]}),i.length>1&&(0,u.jsxs)("div",{style:{color:"#007bff"},children:["+",i.length-1," more submission",i.length>2?"s":""]})]})]}),(0,u.jsx)("div",{style:{backgroundColor:"#28a745",color:"white",padding:"0.25rem 0.5rem",borderRadius:"12px",fontSize:"0.75rem",fontWeight:"bold"},children:"\u2713 COMPLETED"})]})},o)}))})]})},p=e=>{let{pendingOffices:r,formIdentifier:o}=e;return 0===r.length?(0,u.jsxs)("div",{style:{textAlign:"center",color:"#28a745",padding:"2rem"},children:[(0,u.jsx)("div",{style:{fontSize:"3rem",marginBottom:"1rem"},children:"\ud83c\udf89"}),(0,u.jsx)("h5",{children:"All offices have submitted!"}),(0,u.jsx)("p",{children:"Every target office has completed their submission for this form."})]}):(0,u.jsxs)("div",{children:[(0,u.jsxs)("div",{style:{marginBottom:"1rem",color:"#666"},children:[r.length," offices haven't submitted yet",o&&(0,u.jsxs)("div",{style:{fontSize:"0.85rem",marginTop:"0.25rem"},children:["Form: ",(0,u.jsx)("strong",{children:o})]})]}),(0,u.jsx)("div",{style:{maxHeight:"400px",overflowY:"auto"},children:r.map(((e,r)=>(0,u.jsx)("div",{style:{padding:"1rem",border:"1px solid #e9ecef",borderRadius:"8px",marginBottom:"0.5rem",backgroundColor:"#fff3cd"},children:(0,u.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("h6",{style:{margin:"0 0 0.25rem 0",color:"#856404",fontWeight:"bold"},children:e}),(0,u.jsx)("div",{style:{fontSize:"0.85rem",color:"#666"},children:"Awaiting submission"})]}),(0,u.jsx)("div",{style:{backgroundColor:"#ffc107",color:"#212529",padding:"0.25rem 0.5rem",borderRadius:"12px",fontSize:"0.75rem",fontWeight:"bold"},children:"\u23f3 PENDING"})]})},r)))})]})},h=e=>{let{submissions:r,loading:o,onRefresh:i,filters:s}=e;const[n,l]=(0,t.useState)(null),[c,d]=(0,t.useState)(!1),[f,m]=(0,t.useState)(null);(0,t.useEffect)((()=>{h()}),[r,s]);const h=async()=>{if(s.formIdentifier){d(!0);try{const e=b(r),o=await y(s.formIdentifier),t=o.filter((r=>!e.some((e=>e.toLowerCase().trim()===r.toLowerCase().trim()))));l({completedOffices:e,pendingOffices:t,totalTargetOffices:o.length,completedCount:e.length,pendingCount:t.length})}catch(e){console.error("Error calculating office summary:",e);const o=b(r);l({completedOffices:o,pendingOffices:[],totalTargetOffices:o.length,completedCount:o.length,pendingCount:0})}finally{d(!1)}}else{const e=b(r);l({completedOffices:e,pendingOffices:[],totalTargetOffices:e.length,completedCount:e.length,pendingCount:0})}},b=e=>{const r=new Set;return e.forEach((e=>{if(e.submission_data)for(const[o,t]of Object.entries(e.submission_data))if("string"===typeof t&&(t.includes(" BO")||t.includes(" SO")||t.includes(" RO")||t.includes(" HO")||t.includes(" DO")||t.includes("Office"))){r.add(t.trim());break}e.user_office&&"Unknown Office"!==e.user_office&&r.add(e.user_office.trim())})),Array.from(r).filter((e=>e.length>0))},y=async e=>{try{console.log("\ud83c\udfaf Fetching target offices for form:",e);const{data:r,error:o}=await a.N.from("page_configurations").select("selected_offices").eq("id",e).single();if(o)return console.error("Error fetching target offices:",o),[];if(null===r||void 0===r||!r.selected_offices)return console.log("No selected_offices found for form:",e),[];const t=Array.isArray(r.selected_offices)?r.selected_offices:[];return console.log("\ud83c\udfaf Target offices for form:",t),t}catch(r){return console.error("Error fetching target offices:",r),[]}},x=e=>{m(f===e?null:e)};return o||c?(0,u.jsx)("div",{style:{padding:"3rem",textAlign:"center",color:"#666"},children:(0,u.jsx)("div",{children:"\ud83d\udd04 Loading submission summary..."})}):n?(0,u.jsxs)("div",{style:{padding:"1.5rem"},children:[(0,u.jsxs)("div",{style:{display:"flex",gap:"1.5rem",marginBottom:"2rem"},children:[(0,u.jsxs)("div",{style:{flex:1,background:"linear-gradient(135deg, #28a745 0%, #20c997 100%)",color:"white",padding:"2rem",borderRadius:"12px",cursor:"pointer",transition:"transform 0.2s, box-shadow 0.2s",boxShadow:"completed"===f?"0 8px 25px rgba(40, 167, 69, 0.3)":"0 4px 15px rgba(40, 167, 69, 0.2)"},onClick:()=>x("completed"),onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-2px)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)"},children:[(0,u.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:"1rem"},children:[(0,u.jsx)("div",{style:{fontSize:"2.5rem",marginRight:"1rem"},children:"\u2705"}),(0,u.jsxs)("div",{children:[(0,u.jsx)("h3",{style:{margin:0,fontSize:"1.5rem"},children:"Completed"}),(0,u.jsx)("p",{style:{margin:0,opacity:.9},children:"Offices that have submitted"})]})]}),(0,u.jsx)("div",{style:{fontSize:"3rem",fontWeight:"bold",textAlign:"center"},children:n.completedCount}),(0,u.jsx)("div",{style:{textAlign:"center",opacity:.9,fontSize:"0.9rem"},children:1===n.completedCount?"office submitted":"offices submitted"}),"completed"===f&&(0,u.jsx)("div",{style:{marginTop:"1rem",fontSize:"0.8rem",opacity:.8,textAlign:"center"},children:"Click to view details"})]}),(0,u.jsxs)("div",{style:{flex:1,background:"linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)",color:"white",padding:"2rem",borderRadius:"12px",cursor:"pointer",transition:"transform 0.2s, box-shadow 0.2s",boxShadow:"pending"===f?"0 8px 25px rgba(255, 193, 7, 0.3)":"0 4px 15px rgba(255, 193, 7, 0.2)"},onClick:()=>x("pending"),onMouseEnter:e=>{e.currentTarget.style.transform="translateY(-2px)"},onMouseLeave:e=>{e.currentTarget.style.transform="translateY(0)"},children:[(0,u.jsxs)("div",{style:{display:"flex",alignItems:"center",marginBottom:"1rem"},children:[(0,u.jsx)("div",{style:{fontSize:"2.5rem",marginRight:"1rem"},children:"\u23f3"}),(0,u.jsxs)("div",{children:[(0,u.jsx)("h3",{style:{margin:0,fontSize:"1.5rem"},children:"Not Completed"}),(0,u.jsx)("p",{style:{margin:0,opacity:.9},children:"Offices pending submission"})]})]}),(0,u.jsx)("div",{style:{fontSize:"3rem",fontWeight:"bold",textAlign:"center"},children:n.pendingCount}),(0,u.jsx)("div",{style:{textAlign:"center",opacity:.9,fontSize:"0.9rem"},children:1===n.pendingCount?"office pending":"offices pending"}),"pending"===f&&(0,u.jsx)("div",{style:{marginTop:"1rem",fontSize:"0.8rem",opacity:.8,textAlign:"center"},children:"Click to view details"})]})]}),f&&(0,u.jsxs)("div",{style:{background:"white",borderRadius:"8px",boxShadow:"0 4px 15px rgba(0,0,0,0.1)",overflow:"hidden"},children:[(0,u.jsx)("div",{style:{padding:"1.5rem",backgroundColor:"completed"===f?"#28a745":"#ffc107",color:"white"},children:(0,u.jsxs)("h4",{style:{margin:0,display:"flex",alignItems:"center"},children:["completed"===f?"\u2705 Completed Submissions":"\u23f3 Pending Submissions",(0,u.jsx)("button",{style:{marginLeft:"auto",background:"rgba(255,255,255,0.2)",border:"none",color:"white",padding:"0.5rem",borderRadius:"4px",cursor:"pointer"},onClick:()=>m(null),children:"\u2715"})]})}),(0,u.jsx)("div",{style:{padding:"1.5rem"},children:"completed"===f?(0,u.jsx)(g,{submissions:r,completedOffices:n.completedOffices,formatDate:e=>{const r=new Date(e);return{date:r.toLocaleDateString(),time:r.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}}}):(0,u.jsx)(p,{pendingOffices:n.pendingOffices,formIdentifier:s.formIdentifier})})]})]}):(0,u.jsxs)("div",{style:{padding:"3rem",textAlign:"center",color:"#666"},children:[(0,u.jsx)("div",{children:"\ud83d\udcca Unable to calculate summary"}),(0,u.jsx)("button",{style:{marginTop:"1rem",padding:"0.5rem 1rem",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px"},onClick:i,children:"\ud83d\udd04 Refresh"})]})},b=e=>{let{submissions:r,loading:o,onRefresh:i}=e;const[s,n]=(0,t.useState)([]),[l,a]=(0,t.useState)([]),[c,d]=(0,t.useState)(!0);(0,t.useEffect)((()=>{f()}),[r]);const f=async()=>{if(0===r.length)return n([]),a([]),void d(!1);d(!0),console.log("\ud83c\udfd7\ufe0f Building dynamic columns for submissions:",r.length);try{const e=Array.from(new Set(r.map((e=>e.form_identifier))));console.log("\ud83d\udccb Unique form identifiers:",e);const o=new Map;for(const r of e){const e=await m.getFieldMapping(r);o.set(r,e)}const t=new Set;o.forEach((e=>{e.forEach((e=>t.add(e)))})),console.log("\ud83c\udff7\ufe0f All field labels found:",Array.from(t));const i=[{key:"form_type",label:"Form Type",type:"form_type"},{key:"submitted_at",label:"Submitted",type:"date"}];Array.from(t).forEach((e=>{i.push({key:e,label:e,type:"field"})})),n(i);const s=await Promise.all(r.map((async e=>{o.get(e.form_identifier)||new Map;const r=await m.convertSubmissionData(e.form_identifier,e.submission_data);console.log(`\ud83d\udd0d Submission ${e.id}:`,{employee_id:e.employee_id,user_id:e.user_id,form_identifier:e.form_identifier,submission_data_keys:Object.keys(e.submission_data)});const t={id:e.id,form_type:g(e.form_identifier),submitted_at:p(e.submitted_at)};return Object.entries(r).forEach((e=>{let[r,o]=e;"officeName"===r||b(r)||(t[r]=h(o))})),t})));a(s),console.log("\u2705 Dynamic table built successfully")}catch(e){console.error("\u274c Error building dynamic columns:",e)}finally{d(!1)}},g=e=>({"employee-registration":"Employee Registration","leave-request":"Leave Request","expense-report":"Expense Report","performance-review":"Performance Review","it-support-request":"IT Support Request","training-registration":"Training Registration","feedback-form":"Feedback Form","inventory-request":"Inventory Request",test:"Test Form"}[e]||e.replace(/-/g," ").replace(/\b\w/g,(e=>e.toUpperCase()))),p=e=>{const r=new Date(e);return{date:r.toLocaleDateString(),time:r.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}},h=e=>{if(null===e||void 0===e)return"";if("string"===typeof e&&e.includes("T")&&e.includes(":"))try{return new Date(e).toLocaleDateString()}catch(r){return e}return String(e)},b=e=>["Employee Name","employeeName","Full Name","fullName","Name","name","First Name","firstName","Last Name","lastName","User Name","userName","Participant Name","participantName","Requested By","requestedBy","Submitted By","submittedBy","Applicant Name","applicantName"].includes(e);return c?(0,u.jsx)("div",{style:{padding:"3rem",textAlign:"center",color:"#666"},children:(0,u.jsx)("div",{children:"\ud83d\udd04 Building dynamic table structure..."})}):o?(0,u.jsx)("div",{style:{padding:"3rem",textAlign:"center",color:"#666"},children:(0,u.jsx)("div",{children:"\ud83d\udd04 Loading submissions..."})}):0===r.length?(0,u.jsxs)("div",{style:{padding:"3rem",textAlign:"center",color:"#666"},children:[(0,u.jsx)("div",{style:{fontSize:"3rem",marginBottom:"1rem"},children:"\ud83d\udced"}),(0,u.jsx)("h5",{children:"No Submissions Found"}),(0,u.jsx)("p",{children:"No form submissions match your current filters."}),(0,u.jsx)("button",{style:{padding:"0.5rem 1rem",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px"},onClick:i,children:"\ud83d\udd04 Refresh"})]}):(0,u.jsxs)("div",{style:{overflowX:"auto"},children:[(0,u.jsxs)("table",{style:{width:"100%",borderCollapse:"collapse",minWidth:"800px"},children:[(0,u.jsx)("thead",{children:(0,u.jsx)("tr",{style:{backgroundColor:"#f8f9fa"},children:s.map((e=>(0,u.jsx)("th",{style:{padding:"1rem 0.75rem",textAlign:"left",fontWeight:"600",borderBottom:"2px solid #dee2e6",whiteSpace:"nowrap",minWidth:"field"===e.type?"120px":"auto"},children:e.label},e.key)))})}),(0,u.jsx)("tbody",{children:l.map(((e,r)=>(0,u.jsx)("tr",{style:{borderBottom:"1px solid #eee"},children:s.map((r=>{var o,t;return(0,u.jsxs)("td",{style:{padding:"0.75rem",verticalAlign:"top",maxWidth:"200px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:["form_type"===r.type&&(0,u.jsx)("span",{style:{backgroundColor:"#e3f2fd",color:"#1976d2",padding:"0.25rem 0.5rem",borderRadius:"4px",fontSize:"0.75rem",fontWeight:"500"},children:e[r.key]}),"date"===r.type&&(0,u.jsxs)("div",{style:{fontSize:"0.8rem",color:"#666"},children:[(0,u.jsx)("div",{children:null===(o=e[r.key])||void 0===o?void 0:o.date}),(0,u.jsx)("small",{children:null===(t=e[r.key])||void 0===t?void 0:t.time})]}),"field"===r.type&&(0,u.jsx)("span",{title:e[r.key]||"",children:e[r.key]||"-"})]},r.key)}))},`${e.id}-${r}`)))})]}),(0,u.jsxs)("div",{style:{padding:"1rem 1.5rem",backgroundColor:"#f8f9fa",borderTop:"1px solid #dee2e6",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,u.jsxs)("div",{style:{fontSize:"0.875rem",color:"#666"},children:["Showing ",r.length," submissions across ",s.length-2," field types"]}),(0,u.jsx)("button",{style:{padding:"0.375rem 0.75rem",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px"},onClick:i,children:"\ud83d\udd04 Refresh"})]})]})};var y=o(4540);o(907);if("undefined"!==typeof document){const e=document.createElement("style");e.textContent="\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n",document.head.appendChild(e)}const x=()=>{const{currentUser:e}=(0,n.A)(),[r,o]=(0,t.useState)(null),[a,c]=(0,t.useState)([]),[f,g]=(0,t.useState)(!0),[p,x]=(0,t.useState)(null),[v,S]=(0,t.useState)((()=>{const e=sessionStorage.getItem("reports-view-mode");return"card"===e||"table"===e?e:"table"})),[w,j]=(0,t.useState)({limit:50,offset:0}),[_,R]=(0,t.useState)(null),[C,k]=(0,t.useState)([]),[E,O]=(0,t.useState)([]),[N,T]=(0,t.useState)(!1),[F,D]=(0,t.useState)("");(0,t.useEffect)((()=>{(async()=>{if(e){const r=(0,i.H9)(s.db,"employees",e.uid),t=await(0,i.x7)(r);t.exists()&&o(t.data())}})()}),[e]),(0,t.useEffect)((()=>{A(),I()}),[]),(0,t.useEffect)((()=>{U()}),[w]),(0,t.useEffect)((()=>{const e="Reports - Employee Management System",r="table"===v?"Table View":"Card View";return document.title=`${e} - ${r}`,()=>{document.title=e}}),[v]);const A=async()=>{try{const[e,r]=await Promise.all([d.getReportsSummary(),d.getFormIdentifiers()]);R(e),k(r)}catch(e){console.error("Error fetching initial data:",e)}},I=(0,t.useCallback)((async()=>{if(!(E.length>0)||F){T(!0),D("");try{console.log("\ud83c\udfe2 Reports: Fetching office names for filter dropdown...");const e=await y.A.fetchUserSpecificOfficeNames(),r=y.A.officeNamesToOptions(e);O(r),console.log("\u2705 Reports: Successfully loaded",r.length,"office options")}catch(p){const r=p instanceof Error?p.message:"Failed to load office names";D(r),console.error("\u274c Reports: Error fetching office names:",p)}finally{T(!1)}}}),[E.length,F]),U=async()=>{try{g(!0),x(null);const e=await d.getFormSubmissions(w);c(e)}catch(e){console.error("Error fetching submissions:",e),x(e instanceof Error?e.message:"Failed to fetch submissions")}finally{g(!1)}},$=e=>{console.log("\ud83d\udd0d Reports: Applying new filters:",e),j({...e,offset:0})},M=e=>{S(e),sessionStorage.setItem("reports-view-mode",e),console.log("\ud83d\udcca Reports: View mode changed to:",e)};return(0,u.jsxs)("div",{className:"dashboard-container",children:[(0,u.jsx)(l.A,{userData:r}),(0,u.jsxs)("div",{className:"main-content",children:[(0,u.jsxs)("div",{className:"page-title",children:["Reports",(0,u.jsx)("button",{className:"btn btn-primary ms-3",onClick:async()=>{try{const e=await d.exportToCSV(w),r=new Blob([e],{type:"text/csv;charset=utf-8;"}),o=document.createElement("a"),t=URL.createObjectURL(r);o.setAttribute("href",t),o.setAttribute("download",`form_submissions_${(new Date).toISOString().split("T")[0]}.csv`),o.style.visibility="hidden",document.body.appendChild(o),o.click(),document.body.removeChild(o)}catch(e){console.error("Error exporting data:",e),alert("Failed to export data. Please try again.")}},disabled:f||0===a.length,style:{marginLeft:"1rem",padding:"0.5rem 1rem",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px"},children:"\ud83d\udce5 Export CSV"}),(0,u.jsx)("button",{onClick:async()=>{if(a.length>0){const e=a[0];console.log("\ud83d\udd0d Testing with first submission:",e);try{const r=await m.convertSubmissionData(e.form_identifier,e.submission_data);alert(`Form: ${e.form_identifier}\n\nRaw Data Keys: ${Object.keys(e.submission_data).join(", ")}\n\nConverted Data Keys: ${Object.keys(r).join(", ")}\n\nConverted Data: ${JSON.stringify(r,null,2)}`)}catch(p){alert(`Error: ${p}`)}}else alert("No submissions available to test")},style:{marginLeft:"0.5rem",padding:"0.5rem 1rem",backgroundColor:"#28a745",color:"white",border:"none",borderRadius:"4px"},children:"\ud83d\udd0d Debug Data"}),(0,u.jsx)("button",{onClick:()=>{if(a.length>0){const e=a.map((e=>{var r;return{id:e.id,user_office:e.user_office,submission_data_office:null===(r=e.submission_data)||void 0===r?void 0:r.officeName,all_submission_fields:Object.keys(e.submission_data||{}),office_fields:Object.entries(e.submission_data||{}).filter((e=>{let[r,o]=e;return"string"===typeof o&&(o.includes(" RO")||o.includes(" BO")||o.includes(" SO")||o.includes(" HO")||o.includes(" DO")||o.includes("Office"))}))}}));console.log("\ud83c\udfe2 Office Debug Data:",e),alert(`Office Debug:\n\n${JSON.stringify(e.slice(0,3),null,2)}`)}else alert("No submissions to debug")},style:{marginLeft:"0.5rem",padding:"0.5rem 1rem",backgroundColor:"#17a2b8",color:"white",border:"none",borderRadius:"4px"},children:"\ud83c\udfe2 Debug Offices"})]}),_&&(0,u.jsx)("div",{style:{marginBottom:"2rem"},children:(0,u.jsx)("div",{style:{display:"flex",gap:"1rem",flexWrap:"wrap"},children:[{icon:"\ud83d\udcc4",value:_.totalSubmissions,label:"Total Submissions",color:"#007bff"},{icon:"\ud83d\udccb",value:_.uniqueForms,label:"Unique Forms",color:"#28a745"},{icon:"\ud83d\udc65",value:_.uniqueUsers,label:"Active Users",color:"#17a2b8"},{icon:"\ud83d\udcc5",value:_.submissionsToday,label:"Today",color:"#ffc107"},{icon:"\ud83d\udcc6",value:_.submissionsThisWeek,label:"This Week",color:"#fd7e14"},{icon:"\ud83d\uddd3\ufe0f",value:_.submissionsThisMonth,label:"This Month",color:"#6f42c1"}].map(((e,r)=>(0,u.jsxs)("div",{style:{background:"white",padding:"1.5rem",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",textAlign:"center",minWidth:"150px",flex:"1"},children:[(0,u.jsx)("div",{style:{fontSize:"2rem",marginBottom:"0.5rem"},children:e.icon}),(0,u.jsx)("div",{style:{fontSize:"2rem",fontWeight:"bold",color:e.color,marginBottom:"0.25rem"},children:e.value.toLocaleString()}),(0,u.jsx)("div",{style:{fontSize:"0.875rem",color:"#666",textTransform:"uppercase",letterSpacing:"0.5px"},children:e.label})]},r)))})}),(0,u.jsxs)("div",{style:{background:"white",padding:"1.5rem",borderRadius:"8px",marginBottom:"2rem",boxShadow:"0 2px 4px rgba(0,0,0,0.1)"},children:[(0,u.jsx)("h5",{style:{marginBottom:"1rem"},children:"\ud83d\udd0d Filters"}),(0,u.jsxs)("div",{style:{display:"flex",gap:"1rem",flexWrap:"wrap"},children:[(0,u.jsxs)("div",{style:{flex:"1",minWidth:"200px"},children:[(0,u.jsx)("label",{style:{display:"block",marginBottom:"0.5rem",fontWeight:"600"},children:"Form Type"}),(0,u.jsxs)("select",{style:{width:"100%",padding:"0.5rem",border:"1px solid #ddd",borderRadius:"4px"},value:w.formIdentifier||"",onChange:e=>$({...w,formIdentifier:e.target.value||void 0}),children:[(0,u.jsx)("option",{value:"",children:"All Forms"}),C.map((e=>(0,u.jsx)("option",{value:e,children:e},e)))]})]}),(0,u.jsxs)("div",{style:{flex:"1",minWidth:"200px"},children:[(0,u.jsx)("label",{style:{display:"block",marginBottom:"0.5rem",fontWeight:"600"},children:"Office Name"}),(0,u.jsxs)("div",{style:{position:"relative"},children:[(0,u.jsxs)("select",{style:{width:"100%",padding:"0.5rem",border:"1px solid #ddd",borderRadius:"4px",backgroundColor:N?"#f8f9fa":"white"},value:w.officeName||"",onChange:e=>$({...w,officeName:e.target.value||void 0}),disabled:N,children:[(0,u.jsx)("option",{value:"",children:N?"Loading offices...":"All Offices"}),E.map((e=>(0,u.jsx)("option",{value:e.value,children:e.label},e.value)))]}),N&&(0,u.jsx)("div",{style:{position:"absolute",right:"8px",top:"50%",transform:"translateY(-50%)",width:"16px",height:"16px"},children:(0,u.jsx)("div",{style:{width:"16px",height:"16px",border:"2px solid #f3f3f3",borderTop:"2px solid #007bff",borderRadius:"50%",animation:"spin 1s linear infinite"}})})]}),F&&(0,u.jsx)("div",{style:{marginTop:"0.5rem",fontSize:"0.875rem",color:"#dc3545"},children:(0,u.jsxs)("div",{style:{display:"flex",alignItems:"center",gap:"0.5rem"},children:[(0,u.jsxs)("span",{children:["\u26a0\ufe0f ",F]}),(0,u.jsx)("button",{type:"button",style:{padding:"0.25rem 0.5rem",fontSize:"0.75rem",backgroundColor:"#dc3545",color:"white",border:"none",borderRadius:"4px",cursor:"pointer"},onClick:I,disabled:N,children:"Retry"})]})}),!N&&!F&&E.length>0&&(0,u.jsxs)("div",{style:{marginTop:"0.5rem",fontSize:"0.875rem",color:"#28a745"},children:["\u2705 ",E.length," offices loaded"]})]}),(0,u.jsxs)("div",{style:{display:"flex",alignItems:"end",gap:"0.5rem"},children:[(0,u.jsx)("button",{style:{padding:"0.5rem 1rem",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px"},onClick:()=>U(),children:"\ud83d\udd0d Apply"}),(0,u.jsx)("button",{style:{padding:"0.5rem 1rem",backgroundColor:"#6c757d",color:"white",border:"none",borderRadius:"4px"},onClick:()=>{j({limit:50,offset:0}),$({limit:50,offset:0})},children:"\u2716\ufe0f Clear"})]})]})]}),(0,u.jsxs)("div",{style:{background:"white",padding:"1rem 1.5rem",borderRadius:"8px",marginBottom:"1rem",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("h5",{style:{margin:0,color:"#333"},children:"\ud83d\udcca Submission Data"}),(0,u.jsx)("p",{style:{margin:"0.25rem 0 0 0",fontSize:"0.875rem",color:"#666"},children:"table"===v?"Detailed table view with all submission fields and data":"Summary view showing completion status by office"})]}),(0,u.jsxs)("div",{style:{display:"flex",backgroundColor:"#f8f9fa",borderRadius:"6px",padding:"4px",border:"1px solid #dee2e6"},children:[(0,u.jsx)("button",{style:{padding:"0.5rem 1rem",border:"none",borderRadius:"4px",backgroundColor:"table"===v?"#007bff":"transparent",color:"table"===v?"white":"#6c757d",fontWeight:"table"===v?"600":"400",cursor:"pointer",transition:"all 0.2s ease",display:"flex",alignItems:"center",gap:"0.5rem",fontSize:"0.875rem"},onClick:()=>M("table"),onMouseEnter:e=>{"table"!==v&&(e.currentTarget.style.backgroundColor="#e9ecef")},onMouseLeave:e=>{"table"!==v&&(e.currentTarget.style.backgroundColor="transparent")},title:"View submissions in a detailed table format with sortable columns",children:"\ud83d\udccb Table View"}),(0,u.jsx)("button",{style:{padding:"0.5rem 1rem",border:"none",borderRadius:"4px",backgroundColor:"card"===v?"#007bff":"transparent",color:"card"===v?"white":"#6c757d",fontWeight:"card"===v?"600":"400",cursor:"pointer",transition:"all 0.2s ease",display:"flex",alignItems:"center",gap:"0.5rem",fontSize:"0.875rem"},onClick:()=>M("card"),onMouseEnter:e=>{"card"!==v&&(e.currentTarget.style.backgroundColor="#e9ecef")},onMouseLeave:e=>{"card"!==v&&(e.currentTarget.style.backgroundColor="transparent")},title:"View submissions as summary cards showing completion status",children:"\ud83d\udcca Card View"})]})]}),(0,u.jsxs)("div",{style:{background:"white",borderRadius:"8px",boxShadow:"0 2px 4px rgba(0,0,0,0.1)",overflow:"hidden"},children:[p&&(0,u.jsxs)("div",{style:{padding:"1rem",backgroundColor:"#f8d7da",color:"#721c24",borderBottom:"1px solid #f5c6cb"},children:["\u26a0\ufe0f ",p,(0,u.jsx)("button",{style:{marginLeft:"1rem",padding:"0.25rem 0.5rem",backgroundColor:"#dc3545",color:"white",border:"none",borderRadius:"4px"},onClick:U,children:"Retry"})]}),"table"===v?(0,u.jsx)(b,{submissions:a,loading:f,onRefresh:U}):(0,u.jsx)(h,{submissions:a,loading:f,onRefresh:U,filters:w})]})]})]})}}}]);
//# sourceMappingURL=631.883fe564.chunk.js.map