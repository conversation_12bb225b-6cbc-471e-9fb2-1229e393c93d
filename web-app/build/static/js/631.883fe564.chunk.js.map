{"version": 3, "file": "static/js/631.883fe564.chunk.js", "mappings": "+NAqCA,MAAMA,EAOJ,+BAAaC,GAAwF,IAArEC,EAAsBC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACxD,IACEG,QAAQC,IAAI,+DACZD,QAAQC,IAAI,wCAA+BC,KAAKC,UAAUP,EAAS,KAAM,IAGzEI,QAAQC,IAAI,iEAEZ,IAAIG,EAAe,KACfC,EAAW,KAIf,MAAMC,EAAc,CAClB,oBACA,2BACA,qBAGF,IAAK,MAAMC,KAAaD,EAAa,CACnCN,QAAQC,IAAI,8CAAoCM,KAChD,IACE,MAAMC,QAAeC,EAAAA,EAClBC,KAAKH,GACLI,OAAO,QAAS,CAAEC,MAAO,QAASC,MAAM,IAE3C,IAAKL,EAAOM,OAA0B,OAAjBN,EAAOI,MAAgB,CAC1CR,EAAeG,EACfF,EAAWG,EAAOI,MAClBZ,QAAQC,IAAI,0BAAqBM,gBAAwBC,EAAOI,iBAChE,KACF,CAAQ,IAADG,EACLf,QAAQC,IAAI,0BAAqBM,YAAiC,QAAdQ,EAAEP,EAAOM,aAAK,IAAAC,OAAA,EAAZA,EAAcC,QAExE,CAAE,MAAOC,GACPjB,QAAQC,IAAI,0BAAqBM,WAAoBU,EACvD,CACF,CAEA,IAAKb,EAEH,MADAJ,QAAQc,MAAM,uDACR,IAAII,MAAM,qFAGlBlB,QAAQC,IAAI,4CAAwCG,GACpDJ,QAAQC,IAAI,qCAA4BI,EAAU,WAGlDL,QAAQC,IAAI,oFAEZ,IAAIkB,EAAQV,EAAAA,EACTC,KAAKN,GACLO,OAAO,KACPS,MAAM,eAAgB,CAAEC,WAAW,IAGlCzB,EAAQ0B,iBACVtB,QAAQC,IAAI,gEAAuDL,EAAQ0B,gBAC3EH,EAAQA,EAAMI,GAAG,kBAAmB3B,EAAQ0B,iBAG1C1B,EAAQ4B,SACVxB,QAAQC,IAAI,wDAA+CL,EAAQ4B,QACnEL,EAAQA,EAAMI,GAAG,UAAW3B,EAAQ4B,SAGlC5B,EAAQ6B,YACVzB,QAAQC,IAAI,2DAAkDL,EAAQ6B,WACtEN,EAAQA,EAAMO,IAAI,eAAgB9B,EAAQ6B,YAGxC7B,EAAQ+B,UACV3B,QAAQC,IAAI,yDAAgDL,EAAQ+B,SACpER,EAAQA,EAAMS,IAAI,eAAgBhC,EAAQ+B,UAGxC/B,EAAQiC,QACV7B,QAAQC,IAAI,+CAAsCL,EAAQiC,OAC1DV,EAAQA,EAAMU,MAAMjC,EAAQiC,QAG1BjC,EAAQkC,SACV9B,QAAQC,IAAI,gDAAuCL,EAAQkC,QAC3DX,EAAQA,EAAMY,MAAMnC,EAAQkC,OAAQlC,EAAQkC,QAAUlC,EAAQiC,OAAS,IAAM,IAG/E,MAAM,KAAEG,EAAI,MAAElB,SAAgBK,EAQ9B,GANAnB,QAAQC,IAAI,+CAAsC,CAChDgC,YAAgB,OAAJD,QAAI,IAAJA,OAAI,EAAJA,EAAMlC,SAAU,EAC5BoC,WAAYpB,EACZqB,aAAmB,OAALrB,QAAK,IAALA,OAAK,EAALA,EAAOE,UAGnBF,EAQF,MAPAd,QAAQc,MAAM,sCAAkCA,GAChDd,QAAQc,MAAM,8CAAqC,CACjDE,QAASF,EAAME,QACfoB,QAAStB,EAAMsB,QACfC,KAAMvB,EAAMuB,KACZC,KAAMxB,EAAMwB,OAER,IAAIpB,MAAM,qCAAqCJ,EAAME,WAG7D,IAAKgB,GAAwB,IAAhBA,EAAKlC,OAAc,CAC9BE,QAAQuC,KAAK,4DACbvC,QAAQC,IAAI,iDACZD,QAAQC,IAAI,sDACZD,QAAQC,IAAI,iDACZD,QAAQC,IAAI,iDACZD,QAAQC,IAAI,oDAGZ,MAAM,MAAEW,EAAOE,MAAO0B,SAAqB/B,EAAAA,EACxCC,KAAK,4BACLC,OAAO,IAAK,CAAEC,MAAO,QAASC,MAAM,IAOvC,OALAb,QAAQC,IAAI,uDAA8CW,GACtD4B,GACFxC,QAAQc,MAAM,4CAAwC0B,GAGjD,EACT,CAEAxC,QAAQC,IAAI,8CAA0C+B,EAAKlC,OAAQ,eACnEE,QAAQC,IAAI,wDAA+CC,KAAKC,UAAU6B,EAAK,GAAI,KAAM,IACzFhC,QAAQC,IAAI,qDAA4C+B,EAAKS,KAAIC,GAAKA,EAAEC,mBAGxE3C,QAAQC,IAAI,gFACZ,MAAM2C,GAAgBZ,GAAQ,IAAIS,KAAKI,IAAe,IAAAC,EAAA,MAAM,IACvDD,EACHE,UAAWF,EAAWG,aAAe,UACrCC,WAAY,mBACZC,aAAuC,QAA1BJ,EAAAD,EAAWM,uBAAe,IAAAL,OAAA,EAA1BA,EAA4BM,aAAc,iBACxD,IAGD,GAAIxD,EAAQwD,WAAY,CACtBpD,QAAQC,IAAI,4DAAmDL,EAAQwD,YACvEpD,QAAQC,IAAI,8EAEZ,MAAMoD,EAAeT,EAAaU,QAAOT,IAAe,IAADU,EAErD,MAAMC,EAAiD,QAA7BD,EAAGV,EAAWM,uBAAe,IAAAI,OAAA,EAA1BA,EAA4BH,WACnDK,EAAaZ,EAAWK,YAG9B,IAAIQ,EAAc,KAClB,GAAIb,EAAWM,gBACb,IAAK,MAAOQ,EAAKC,KAAUC,OAAOC,QAAQjB,EAAWM,iBACnD,GAAqB,kBAAVS,IACTA,EAAMG,SAAS,QAAUH,EAAMG,SAAS,QAAUH,EAAMG,SAAS,QACjEH,EAAMG,SAAS,QAAUH,EAAMG,SAAS,QAAUH,EAAMG,SAAS,WAChE,CACDL,EAAcE,EACd,KACF,CAIJ,MAAMI,EAAgBN,GAAeF,GAAwBC,GAAc,GAG3E,OAFAzD,QAAQC,IAAI,2BAAiB4C,EAAWoB,eAAeD,eAA2BpE,EAAQwD,eAEnFY,EAAcE,cAAcH,SAASnE,EAAQwD,WAAYc,cAAc,IAIhF,OADAlE,QAAQC,IAAI,qDAA4CoD,EAAavD,OAAQ,eACtEuD,CACT,CAGA,OADArD,QAAQC,IAAI,yCAAgC2C,EAAa9C,OAAQ,wBAC1D8C,CAET,CAAE,MAAO9B,GAGP,MAFAd,QAAQc,MAAM,kEAAyDA,GACvEd,QAAQc,MAAM,4CAAmCA,aAAiBI,MAAQJ,EAAMqD,MAAQ,kBAClFrD,CACR,CACF,CAKA,8BAAasD,GACX,IACEpE,QAAQC,IAAI,+CAGZ,MAAMoE,EAAW,kBACXC,EAASC,KAAKC,MAAMC,IAAIJ,GAC9B,GAAIC,GAAUC,KAAKG,aAAaJ,EAAOK,WAErC,OADA3E,QAAQC,IAAI,4CACLqE,EAAOtC,KAGhB,MAAM4C,EAAM,IAAIC,KACVC,EAAQ,IAAID,KAAKD,EAAIG,cAAeH,EAAII,WAAYJ,EAAIK,WAAWC,cACnEC,EAAU,IAAIN,KAAKD,EAAIQ,UAAY,QAAyBF,cAC5DG,EAAW,IAAIR,KAAKD,EAAIG,cAAeH,EAAII,WAAY,GAAGE,cAG1D9E,QAAqBmE,KAAKe,yBAGxB1E,MAAO2E,EAAkBzE,MAAO0E,SAAqB/E,EAAAA,EAC1DC,KAAKN,GACLO,OAAO,IAAK,CAAEC,MAAO,QAASC,MAAM,IAEvC,GAAI2E,EAEF,MADAxF,QAAQc,MAAM,6CAA8C0E,GACtDA,EAGRxF,QAAQC,IAAI,2CAA4CsF,GAGxD,MAAQvD,KAAMyD,EAAW3E,MAAO4E,SAAqBjF,EAAAA,EAClDC,KAAKN,GACLO,OAAO,mBAEV,GAAI+E,EAEF,MADA1F,QAAQc,MAAM,uCAAwC4E,GAChDA,EAGR,MAAMC,EAAc,IAAIC,IAAa,OAATH,QAAS,IAATA,OAAS,EAATA,EAAWhD,KAAKoD,GAAcA,EAAKlD,mBAAkBmD,KACjF9F,QAAQC,IAAI,sCAAuC0F,GAGnD,MAAQ3D,KAAM+D,EAAWjF,MAAOkF,SAAqBvF,EAAAA,EAClDC,KAAKN,GACLO,OAAO,WAEV,GAAIqF,EAEF,MADAhG,QAAQc,MAAM,uCAAwCkF,GAChDA,EAGR,MAAMC,EAAc,IAAIL,IAAa,OAATG,QAAS,IAATA,OAAS,EAATA,EAAWtD,KAAKoD,GAAcA,EAAKK,WAAUJ,KACzE9F,QAAQC,IAAI,sCAAuCgG,GAGnD,MAAQrF,MAAOuF,EAAkBrF,MAAOsF,SAAqB3F,EAAAA,EAC1DC,KAAKN,GACLO,OAAO,IAAK,CAAEC,MAAO,QAASC,MAAM,IACpCa,IAAI,eAAgBoD,GAEvB,GAAIsB,EAEF,MADApG,QAAQc,MAAM,6CAA8CsF,GACtDA,EAIR,MAAQxF,MAAOyF,EAAqBvF,MAAOwF,SAAoB7F,EAAAA,EAC5DC,KAAKN,GACLO,OAAO,IAAK,CAAEC,MAAO,QAASC,MAAM,IACpCa,IAAI,eAAgByD,GAEvB,GAAImB,EAEF,MADAtG,QAAQc,MAAM,4CAA6CwF,GACrDA,EAIR,MAAQ1F,MAAO2F,EAAsBzF,MAAO0F,SAAqB/F,EAAAA,EAC9DC,KAAKN,GACLO,OAAO,IAAK,CAAEC,MAAO,QAASC,MAAM,IACpCa,IAAI,eAAgB2D,GAEvB,GAAImB,EAEF,MADAxG,QAAQc,MAAM,6CAA8C0F,GACtDA,EAGR,MAAMC,EAA0B,CAC9BlB,iBAAkBA,GAAoB,EACtCI,cACAM,cACAE,iBAAkBA,GAAoB,EACtCE,oBAAqBA,GAAuB,EAC5CE,qBAAsBA,GAAwB,GAOhD,OAHAhC,KAAKC,MAAMkC,IAAIrC,EAAU,CAAErC,KAAMyE,EAAS9B,UAAW,IAAIE,OAEzD7E,QAAQC,IAAI,kDAAmDwG,GACxDA,CAET,CAAE,MAAO3F,GAEP,MADAd,QAAQc,MAAM,8CAA+CA,GACvDA,CACR,CACF,CAKA,+BAAa6F,GACX,IACE3G,QAAQC,IAAI,gDAEZ,MAAMG,QAAqBmE,KAAKe,yBAE1B,KAAEtD,EAAI,MAAElB,SAAgBL,EAAAA,EAC3BC,KAAKN,GACLO,OAAO,mBAEV,GAAIG,EAEF,MADAd,QAAQc,MAAM,mDAAoDA,GAC5DA,EAGR,MAAM8F,EAAoBC,MAAMnG,KAC9B,IAAIkF,KAAQ,OAAJ5D,QAAI,IAAJA,OAAI,EAAJA,EAAMS,KAAKoD,GAAcA,EAAKlD,oBAA8B,KACpEmE,OAGF,OADA9G,QAAQC,IAAI,wBAAyB2G,EAAkB9G,OAAQ,2BAA4B8G,GACpFA,CAET,CAAE,MAAO9F,GAEP,MADAd,QAAQc,MAAM,mDAAoDA,GAC5DA,CACR,CACF,CAKA,gCAAqBiG,CAAoBC,GACvC,IACEhH,QAAQC,IAAI,gFAGZ,MAAMgH,EAAcD,EACjBvE,KAAIyE,GAAKA,EAAElE,cACXM,QAAOW,GAAMA,GAAoB,kBAAPA,GAAmBA,EAAGkD,OAAOrH,OAAS,IAEnEE,QAAQC,IAAI,6DAAoDgH,GAGhE,IAAIG,EAAsB,GAC1B,GAAIH,EAAYnH,OAAS,EAAG,CAC1B,MAAQkC,KAAMqF,EAAQ,MAAEvG,SAAgBL,EAAAA,EACrCC,KAAK,gBACLC,OAAO,sEACP2G,GAAG,aAAcL,GAEhBnG,EACFd,QAAQc,MAAM,uDAAmDA,IAEjEsG,EAAeC,GAAY,GAC3BrH,QAAQC,IAAI,gDAA4CmH,GAE5D,CAGA,MAAMG,EAAa,IAAIC,IAKvB,OAJAJ,EAAaK,SAAQC,IACnBH,EAAWb,IAAIgB,EAAQC,WAAYD,EAAQ,IAGtCV,EAAYvE,KAAII,IAAe,IAAD+E,EAEnC,MAAMC,EAAchF,EAAWG,YAAcuE,EAAW9C,IAAI5B,EAAWG,aAAe,KAEtFhD,QAAQC,IAAI,4CAAkC4C,EAAWoB,oBAAoBpB,EAAWG,gBAAiB6E,GAGzG,MAAMC,EAAiD,IAClDjF,EACHE,WAAsB,OAAX8E,QAAW,IAAXA,OAAW,EAAXA,EAAaE,YACdlF,EAAWG,cACVH,EAAWqD,QAAU,QAAQrD,EAAWqD,QAAQ8B,UAAU,EAAG,KAAO,gBAC/E/E,YAAuB,OAAX4E,QAAW,IAAXA,OAAW,EAAXA,EAAaI,QAAS,mBAClC/E,aAAwB,OAAX2E,QAAW,IAAXA,OAAW,EAAXA,EAAaK,eACY,QADDN,EACzB/E,EAAWM,uBAAe,IAAAyE,OAAA,EAA1BA,EAA4BxE,aAC5B,kBAUd,OAPApD,QAAQC,IAAI,8BAAyB4C,EAAWoB,MAAO,CACrDlB,UAAW+E,EAAmB/E,UAC9BE,WAAY6E,EAAmB7E,WAC/BC,YAAa4E,EAAmB5E,YAChCF,YAAaH,EAAWG,cAGnB8E,CAAkB,GAG7B,CAAE,MAAOhH,GAEP,OADAd,QAAQc,MAAM,kDAAmDA,GAC1DkG,EAAYvE,KAAII,IAAU,IAC5BA,EACHE,UAAWF,EAAWG,aAAe,eACrCC,WAAY,mBACZC,YAAa,oBAEjB,CACF,CAKA,wBAAaiF,GAA2D,IAA/CvI,EAAsBC,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAG,CAAC,EACjD,IACE,MAAMmH,QAAoBzC,KAAK5E,mBAAmBC,GAElD,GAA2B,IAAvBoH,EAAYlH,OACd,MAAM,IAAIoB,MAAM,qBAIlB,MAsBMkH,EAAa,CAtBH,CACd,KACA,kBACA,UACA,YACA,cACA,eACA,sBAIWpB,EAAYvE,KAAII,GAAc,CACzCA,EAAWoB,GACXpB,EAAWF,gBACXE,EAAWqD,QACXrD,EAAWE,WAAa,GACxBF,EAAWK,aAAe,GAC1B,IAAI2B,KAAKhC,EAAWwF,cAAcC,iBAClCpI,KAAKC,UAAU0C,EAAWM,qBAKzBV,KAAI8F,GAAOA,EAAI9F,KAAI+F,GAAS,IAAIA,OAAUC,KAAK,OAC/CA,KAAK,MAGR,OADAzI,QAAQC,IAAI,kDAAmD+G,EAAYlH,OAAQ,WAC5EsI,CAET,CAAE,MAAOtH,GAEP,MADAd,QAAQc,MAAM,0CAA2CA,GACnDA,CACR,CACF,CAKA,kCAAqBwE,GACnB,MAAMhF,EAAc,CAClB,oBACA,2BACA,qBAGF,IAAK,MAAMC,KAAaD,EACtB,IACE,MAAM,MAAEM,EAAK,MAAEE,SAAgBL,EAAAA,EAC5BC,KAAKH,GACLI,OAAO,IAAK,CAAEC,MAAO,QAASC,MAAM,IAEvC,IAAKC,GAAmB,OAAVF,EAEZ,OADAZ,QAAQC,IAAI,gCAA2BM,UAAkBK,aAClDL,CAEX,CAAE,MAAOU,GACPjB,QAAQC,IAAI,0BAAqBM,mBACnC,CAGF,MAAM,IAAIW,MAAM,oFAClB,CAKA,iBAAOwH,GACLnE,KAAKC,MAAMmE,QACX3I,QAAQC,IAAI,gCACd,CAKA,mBAAeyE,CAAaC,GAG1B,QAFY,IAAIE,MACSO,UAAYT,EAAUS,WAAS,IACnCb,KAAKqE,oBAC5B,EArfIlJ,EACoBkJ,qBAAuB,EAD3ClJ,EAEW8E,MAAQ,IAAIgD,IAsf7B,UC1gBO,MAAMqB,EAOX,0BAAaC,CAAcxH,GACzB,IAEE,GAAIiD,KAAKC,MAAMuE,IAAIzH,GACjB,OAAOiD,KAAKC,MAAMC,IAAInD,GAGxBtB,QAAQC,IAAI,uDAA6CqB,KAGzD,MAAM0H,EAAgB,CACpB,SAAS1H,IACT,eAAeA,IACf,SAASA,KAGX,IAAK,MAAM2H,KAAQD,EACjB,IACE,MAAME,GAASC,EAAAA,EAAAA,IAAIC,EAAAA,GAAIH,GACjBI,QAAgBC,EAAAA,EAAAA,IAAOJ,GAE7B,GAAIG,EAAQE,SAAU,CACpB,MAAMvH,EAAOqH,EAAQrH,OACrB,GAAIA,GAAQA,EAAKwH,OAGf,OAFAxJ,QAAQC,IAAI,6CAAwCgJ,IAAQjH,GAC5DuC,KAAKC,MAAMkC,IAAIpF,EAAgBU,GACxBA,CAEX,CACF,CAAE,MAAOf,GACPjB,QAAQC,IAAI,kDAA6CgJ,KAAShI,EACpE,CAIF,OADAjB,QAAQC,IAAI,uDAA6CqB,KAClD,IAET,CAAE,MAAOR,GAEP,OADAd,QAAQc,MAAM,iDAAkDA,GACzD,IACT,CACF,CAKA,4BAAa2I,CAAgBnI,GAC3B,IAEE,GAAIiD,KAAKmF,kBAAkBX,IAAIzH,GAC7B,OAAOiD,KAAKmF,kBAAkBjF,IAAInD,GAGpC,MAAMqI,QAAmBpF,KAAKuE,cAAcxH,GACtCsI,EAAU,IAAIpC,IAYpB,OAVImC,GAAcA,EAAWH,QAC3BG,EAAWH,OAAO/B,SAAQe,IACL,YAAfA,EAAMqB,MAAqC,WAAfrB,EAAMqB,MACpCD,EAAQlD,IAAI8B,EAAMvE,GAAIuE,EAAMsB,MAC9B,IAIJvF,KAAKmF,kBAAkBhD,IAAIpF,EAAgBsI,GAC3C5J,QAAQC,IAAI,6DAAmDqB,KAAmBsI,GAC3EA,CAET,CAAE,MAAO9I,GAEP,OADAd,QAAQc,MAAM,mDAAoDA,GAC3D,IAAI0G,GACb,CACF,CAKA,8BAAauC,CAAkBC,GAC7B,MAAMC,EAAY,IAAIrE,IAEtB,IAAK,MAAMsE,KAAUF,EAAiB,QACdzF,KAAKkF,gBAAgBS,IACnCzC,SAAQqC,GAASG,EAAUE,IAAIL,IACzC,CAEA,OAAOG,CACT,CAKA,kCAAaG,CACX9I,EACA+I,GAEA,IACE,MAAMT,QAAgBrF,KAAKkF,gBAAgBnI,GACrCgJ,EAAqC,CAAC,EAO5C,OALAzG,OAAOC,QAAQuG,GAAgB5C,SAAQ8C,IAAuB,IAArBC,EAAS5G,GAAM2G,EACtD,MAAMT,EAAQF,EAAQnF,IAAI+F,IAAYA,EACtCF,EAAcR,GAASlG,CAAK,IAGvB0G,CAET,CAAE,MAAOxJ,GAEP,OADAd,QAAQc,MAAM,uDAAwDA,GAC/DuJ,CACT,CACF,CAKA,iBAAO3B,GACLnE,KAAKC,MAAMmE,QACXpE,KAAKmF,kBAAkBf,QACvB3I,QAAQC,IAAI,mCACd,EA9HW4I,EACIrE,MAAQ,IAAIgD,IADhBqB,EAEIa,kBAAoB,IAAIlC,IA+HzC,U,aCjIA,MAgUMiD,EAIDC,IAAoD,IAAnD,YAAE1D,EAAW,iBAAE2D,EAAgB,WAAEC,GAAYF,EAejD,OACEG,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACED,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEC,aAAc,OAAQC,MAAO,QAASH,SAAA,CACjDH,EAAiB7K,OAAO,4CAG3BoL,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEI,UAAW,QAASC,UAAW,QAASN,SACnDH,EAAiBlI,KAAI,CAAC4I,EAAQC,KAC7B,MAAMC,GArBmBnI,EAqByBiI,EApBjDrE,EAAY1D,QAAOT,IACxB,GAAIA,EAAWM,gBACb,IAAK,MAAOQ,EAAKC,KAAUC,OAAOC,QAAQjB,EAAWM,iBACnD,GAAqB,kBAAVS,GAAsBA,EAAMuD,SAAW/D,EAChD,OAAO,EAIb,OAAOP,EAAWK,cAAgBE,CAAU,KATfA,MAsBzB,MAAMoI,EAAmBD,EAAkBzE,MAAK,CAAC2E,EAAGC,IAClD,IAAI7G,KAAK6G,EAAErD,cAAcjD,UAAY,IAAIP,KAAK4G,EAAEpD,cAAcjD,YAC9D,GAEF,OACE8F,EAAAA,EAAAA,KAAA,OAAiBH,MAAO,CACtBY,QAAS,OACTC,OAAQ,oBACRC,aAAc,MACdb,aAAc,SACdc,gBAAiB,WACjBhB,UACAD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEgB,QAAS,OAAQC,eAAgB,gBAAiBC,WAAY,SAAUnB,SAAA,EACpFD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEmB,KAAM,GAAIpB,SAAA,EACtBI,EAAAA,EAAAA,KAAA,MAAIH,MAAO,CAAEoB,OAAQ,eAAgBlB,MAAO,UAAWmB,WAAY,QAAStB,SACzEO,IAEFG,IACCX,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEsB,SAAU,UAAWpB,MAAO,QAASH,SAAA,EACjDD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEI,EAAAA,EAAAA,KAAA,UAAAJ,SAAQ,uBAA2B,IAAEF,EAAWY,EAAiBnD,cAAciE,KAAK,OAAK1B,EAAWY,EAAiBnD,cAAckE,SAErI1B,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEI,EAAAA,EAAAA,KAAA,UAAAJ,SAAQ,cAAkB,IAAEU,EAAiBzI,WAAayI,EAAiBxI,aAAe,aAE3FuI,EAAkBzL,OAAS,IAC1B+K,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEE,MAAO,WAAYH,SAAA,CAAC,IAC9BS,EAAkBzL,OAAS,EAAE,mBAAiByL,EAAkBzL,OAAS,EAAI,IAAM,aAM/FoL,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CACVe,gBAAiB,UACjBb,MAAO,QACPU,QAAS,iBACTE,aAAc,OACdQ,SAAU,UACVD,WAAY,QACZtB,SAAC,yBAnCGQ,EAuCJ,QAIR,EAKJkB,EAGDC,IAAyC,IAAxC,eAAEC,EAAc,eAAEpL,GAAgBmL,EAEtC,OAA8B,IAA1BC,EAAe5M,QAEf+K,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAE4B,UAAW,SAAU1B,MAAO,UAAWU,QAAS,QAASb,SAAA,EACrEI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,OAAQrB,aAAc,QAASF,SAAC,kBACxDI,EAAAA,EAAAA,KAAA,MAAAJ,SAAI,iCACJI,EAAAA,EAAAA,KAAA,KAAAJ,SAAG,0EAMPD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACED,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEC,aAAc,OAAQC,MAAO,QAASH,SAAA,CACjD4B,EAAe5M,OAAO,iCACtBwB,IACCuJ,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEsB,SAAU,UAAWO,UAAW,WAAY9B,SAAA,CAAC,UACnDI,EAAAA,EAAAA,KAAA,UAAAJ,SAASxJ,WAKrB4J,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEI,UAAW,QAASC,UAAW,QAASN,SACnD4B,EAAejK,KAAI,CAAC4I,EAAQC,KAC3BJ,EAAAA,EAAAA,KAAA,OAAiBH,MAAO,CACtBY,QAAS,OACTC,OAAQ,oBACRC,aAAc,MACdb,aAAc,SACdc,gBAAiB,WACjBhB,UACAD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEgB,QAAS,OAAQC,eAAgB,gBAAiBC,WAAY,UAAWnB,SAAA,EACrFD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEI,EAAAA,EAAAA,KAAA,MAAIH,MAAO,CAAEoB,OAAQ,gBAAiBlB,MAAO,UAAWmB,WAAY,QAAStB,SAC1EO,KAEHH,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,UAAWpB,MAAO,QAASH,SAAC,4BAItDI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CACVe,gBAAiB,UACjBb,MAAO,UACPU,QAAS,iBACTE,aAAc,OACdQ,SAAU,UACVD,WAAY,QACZtB,SAAC,uBAvBGQ,SA8BV,EAIV,EA/cwEf,IAKjE,IALkE,YACvEvD,EAAW,QACX6F,EAAO,UACPC,EAAS,QACTlN,GACD2K,EACC,MAAO9D,EAASsG,IAAcC,EAAAA,EAAAA,UAAyC,OAChEC,EAAgBC,IAAqBF,EAAAA,EAAAA,WAAS,IAC9CG,EAAcC,IAAmBJ,EAAAA,EAAAA,UAAyC,OAEjFK,EAAAA,EAAAA,YAAU,KACRC,GAAwB,GACvB,CAACtG,EAAapH,IAEjB,MAAM0N,EAAyBC,UAC7B,GAAK3N,EAAQ0B,eAAb,CAaA4L,GAAkB,GAClB,IAEE,MAAMvC,EAAmB6C,EAAgCxG,GAGnDyG,QAAsBC,EAAwB9N,EAAQ0B,gBAGtDoL,EAAiBe,EAAcnK,QAAO+H,IACzCV,EAAiBgD,MAAKC,GACrBA,EAAU1J,cAAciD,SAAWkE,EAAOnH,cAAciD,WAI5D4F,EAAW,CACTpC,mBACA+B,iBACAmB,mBAAoBJ,EAAc3N,OAClCgO,eAAgBnD,EAAiB7K,OACjCiO,aAAcrB,EAAe5M,QAGjC,CAAE,MAAOgB,GACPd,QAAQc,MAAM,oCAAqCA,GAEnD,MAAMkN,EAAgBR,EAAgCxG,GACtD+F,EAAW,CACTpC,iBAAkBqD,EAClBtB,eAAgB,GAChBmB,mBAAoBG,EAAclO,OAClCgO,eAAgBE,EAAclO,OAC9BiO,aAAc,GAElB,CAAC,QACCb,GAAkB,EACpB,CAtCA,KAXA,CAEE,MAAMc,EAAgBR,EAAgCxG,GACtD+F,EAAW,CACTpC,iBAAkBqD,EAClBtB,eAAgB,GAChBmB,mBAAoBG,EAAclO,OAClCgO,eAAgBE,EAAclO,OAC9BiO,aAAc,GAGlB,CAsCA,EAGIP,EAAmCxG,IACvC,MAAMiH,EAAY,IAAIrI,IAsBtB,OApBAoB,EAAYS,SAAQ5E,IAElB,GAAIA,EAAWM,gBACb,IAAK,MAAOQ,EAAKC,KAAUC,OAAOC,QAAQjB,EAAWM,iBACnD,GAAqB,kBAAVS,IACTA,EAAMG,SAAS,QAAUH,EAAMG,SAAS,QAAUH,EAAMG,SAAS,QACjEH,EAAMG,SAAS,QAAUH,EAAMG,SAAS,QAAUH,EAAMG,SAAS,WAChE,CACDkK,EAAU9D,IAAIvG,EAAMuD,QACpB,KACF,CAKAtE,EAAWK,aAA0C,mBAA3BL,EAAWK,aACvC+K,EAAU9D,IAAItH,EAAWK,YAAYiE,OACvC,IAGKN,MAAMnG,KAAKuN,GAAW3K,QAAO+H,GAAUA,EAAOvL,OAAS,GAAE,EAG5D4N,EAA0BH,UAC9B,IACEvN,QAAQC,IAAI,iDAAwCqB,GAEpD,MAAM,KAAEU,EAAI,MAAElB,SAAgBL,EAAAA,EAC3BC,KAAK,uBACLC,OAAO,oBACPY,GAAG,KAAMD,GACT4M,SAEH,GAAIpN,EAEF,OADAd,QAAQc,MAAM,iCAAkCA,GACzC,GAGT,GAAS,OAAJkB,QAAI,IAAJA,IAAAA,EAAMmM,iBAET,OADAnO,QAAQC,IAAI,sCAAuCqB,GAC5C,GAIT,MAAMmM,EAAgB5G,MAAMuH,QAAQpM,EAAKmM,kBACrCnM,EAAKmM,iBACL,GAGJ,OADAnO,QAAQC,IAAI,wCAA+BwN,GACpCA,CAET,CAAE,MAAO3M,GAEP,OADAd,QAAQc,MAAM,iCAAkCA,GACzC,EACT,GAGIuN,EAAmBC,IACvBlB,EAAgBD,IAAiBmB,EAAW,KAAOA,EAAS,EAW9D,OAAIzB,GAAWI,GAEX/B,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEY,QAAS,OAAQgB,UAAW,SAAU1B,MAAO,QAASH,UAClEI,EAAAA,EAAAA,KAAA,OAAAJ,SAAK,iDAKNrE,GAsBHoE,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEY,QAAS,UAAWb,SAAA,EAEhCD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEgB,QAAS,OAAQwC,IAAK,SAAUvD,aAAc,QAASF,SAAA,EAEnED,EAAAA,EAAAA,MAAA,OACEE,MAAO,CACLmB,KAAM,EACNsC,WAAY,oDACZvD,MAAO,QACPU,QAAS,OACTE,aAAc,OACd4C,OAAQ,UACRC,WAAY,kCACZC,UAA4B,cAAjBxB,EACP,oCACA,qCAENyB,QAASA,IAAMP,EAAgB,aAC/BQ,aAAeC,IACbA,EAAEC,cAAchE,MAAMiE,UAAY,kBAAkB,EAEtDC,aAAeH,IACbA,EAAEC,cAAchE,MAAMiE,UAAY,eAAe,EACjDlE,SAAA,EAEFD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEgB,QAAS,OAAQE,WAAY,SAAUjB,aAAc,QAASF,SAAA,EAC1EI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,SAAU6C,YAAa,QAASpE,SAAC,YACzDD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEI,EAAAA,EAAAA,KAAA,MAAIH,MAAO,CAAEoB,OAAQ,EAAGE,SAAU,UAAWvB,SAAC,eAC9CI,EAAAA,EAAAA,KAAA,KAAGH,MAAO,CAAEoB,OAAQ,EAAGgD,QAAS,IAAMrE,SAAC,uCAG3CI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,OAAQD,WAAY,OAAQO,UAAW,UAAW7B,SACvErE,EAAQqH,kBAEX5C,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAE4B,UAAW,SAAUwC,QAAS,GAAK9C,SAAU,UAAWvB,SACxC,IAA3BrE,EAAQqH,eAAuB,mBAAqB,sBAErC,cAAjBX,IACCjC,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CACV6B,UAAW,OACXP,SAAU,SACV8C,QAAS,GACTxC,UAAW,UACX7B,SAAC,8BAOPD,EAAAA,EAAAA,MAAA,OACEE,MAAO,CACLmB,KAAM,EACNsC,WAAY,oDACZvD,MAAO,QACPU,QAAS,OACTE,aAAc,OACd4C,OAAQ,UACRC,WAAY,kCACZC,UAA4B,YAAjBxB,EACP,oCACA,qCAENyB,QAASA,IAAMP,EAAgB,WAC/BQ,aAAeC,IACbA,EAAEC,cAAchE,MAAMiE,UAAY,kBAAkB,EAEtDC,aAAeH,IACbA,EAAEC,cAAchE,MAAMiE,UAAY,eAAe,EACjDlE,SAAA,EAEFD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEgB,QAAS,OAAQE,WAAY,SAAUjB,aAAc,QAASF,SAAA,EAC1EI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,SAAU6C,YAAa,QAASpE,SAAC,YACzDD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEI,EAAAA,EAAAA,KAAA,MAAIH,MAAO,CAAEoB,OAAQ,EAAGE,SAAU,UAAWvB,SAAC,mBAC9CI,EAAAA,EAAAA,KAAA,KAAGH,MAAO,CAAEoB,OAAQ,EAAGgD,QAAS,IAAMrE,SAAC,sCAG3CI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,OAAQD,WAAY,OAAQO,UAAW,UAAW7B,SACvErE,EAAQsH,gBAEX7C,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAE4B,UAAW,SAAUwC,QAAS,GAAK9C,SAAU,UAAWvB,SAC1C,IAAzBrE,EAAQsH,aAAqB,iBAAmB,oBAEjC,YAAjBZ,IACCjC,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CACV6B,UAAW,OACXP,SAAU,SACV8C,QAAS,GACTxC,UAAW,UACX7B,SAAC,gCAQRqC,IACCtC,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CACVyD,WAAY,QACZ3C,aAAc,MACd8C,UAAW,6BACXS,SAAU,UACVtE,SAAA,EACAI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CACVY,QAAS,SACTG,gBAAkC,cAAjBqB,EAA+B,UAAY,UAC5DlC,MAAO,SACPH,UACAD,EAAAA,EAAAA,MAAA,MAAIE,MAAO,CAAEoB,OAAQ,EAAGJ,QAAS,OAAQE,WAAY,UAAWnB,SAAA,CAC5C,cAAjBqC,EAA+B,+BAA4B,8BAC5DjC,EAAAA,EAAAA,KAAA,UACEH,MAAO,CACLsE,WAAY,OACZb,WAAY,wBACZ5C,OAAQ,OACRX,MAAO,QACPU,QAAS,SACTE,aAAc,MACd4C,OAAQ,WAEVG,QAASA,IAAMxB,EAAgB,MAAMtC,SACtC,iBAMLI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEY,QAAS,UAAWb,SACd,cAAjBqC,GACCjC,EAAAA,EAAAA,KAACT,EAA2B,CAC1BzD,YAAaA,EACb2D,iBAAkBlE,EAAQkE,iBAC1BC,WA7KM0E,IAClB,MAAMhD,EAAO,IAAIzH,KAAKyK,GACtB,MAAO,CACLhD,KAAMA,EAAKiD,qBACXhD,KAAMD,EAAKkD,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,YAC9D,KA2KSxE,EAAAA,EAAAA,KAACsB,EAAyB,CACxBE,eAAgBjG,EAAQiG,eACxBpL,eAAgB1B,EAAQ0B,0BAhKlCuJ,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEY,QAAS,OAAQgB,UAAW,SAAU1B,MAAO,QAASH,SAAA,EAClEI,EAAAA,EAAAA,KAAA,OAAAJ,SAAK,8CACLI,EAAAA,EAAAA,KAAA,UACEH,MAAO,CACL6B,UAAW,OACXjB,QAAS,cACTG,gBAAiB,UACjBb,MAAO,QACPW,OAAQ,OACRC,aAAc,OAEhB+C,QAAS9B,EAAUhC,SACpB,2BA0JC,ECiJV,EA/cgEP,IAIzD,IAJ0D,YAC/DvD,EAAW,QACX6F,EAAO,UACPC,GACDvC,EACC,MAAOoF,EAASC,IAAc5C,EAAAA,EAAAA,UAAwB,KAC/C6C,EAAeC,IAAoB9C,EAAAA,EAAAA,UAAqC,KACxE+C,EAAgBC,IAAqBhD,EAAAA,EAAAA,WAAS,IAErDK,EAAAA,EAAAA,YAAU,KACR4C,GAAqB,GACpB,CAACjJ,IAEJ,MAAMiJ,EAAsB1C,UAC1B,GAA2B,IAAvBvG,EAAYlH,OAId,OAHA8P,EAAW,IACXE,EAAiB,SACjBE,GAAkB,GAIpBA,GAAkB,GAClBhQ,QAAQC,IAAI,+DAAiD+G,EAAYlH,QAEzE,IAEE,MAAMkK,EAAkBnD,MAAMnG,KAAK,IAAIkF,IAAIoB,EAAYvE,KAAIyE,GAAKA,EAAEvE,oBAClE3C,QAAQC,IAAI,wCAA+B+J,GAG3C,MAAMkG,EAAmB,IAAI1I,IAC7B,IAAK,MAAM0C,KAAUF,EAAiB,CACpC,MAAMJ,QAAgBf,EAAkBY,gBAAgBS,GACxDgG,EAAiBxJ,IAAIwD,EAAQN,EAC/B,CAGA,MAAMuG,EAAiB,IAAIvK,IAC3BsK,EAAiBzI,SAAQmC,IACvBA,EAAQnC,SAAQqC,GAASqG,EAAehG,IAAIL,IAAO,IAGrD9J,QAAQC,IAAI,6CAA+B4G,MAAMnG,KAAKyP,IAGtD,MAAMC,EAA4B,CAChC,CAAEzM,IAAK,YAAamG,MAAO,YAAaD,KAAM,aAC9C,CAAElG,IAAK,eAAgBmG,MAAO,YAAaD,KAAM,SAInDhD,MAAMnG,KAAKyP,GAAgB1I,SAAQqC,IACjCsG,EAAWC,KAAK,CACd1M,IAAKmG,EACLA,MAAOA,EACPD,KAAM,SACN,IAGJ+F,EAAWQ,GAGX,MAAME,QAA6BC,QAAQC,IACzCxJ,EAAYvE,KAAI8K,UACM2C,EAAiBzL,IAAI5B,EAAWF,kBAAoB,IAAI6E,IAA5E,MACM8C,QAAsBzB,EAAkBuB,sBAC5CvH,EAAWF,gBACXE,EAAWM,iBAIbnD,QAAQC,IAAI,2BAAiB4C,EAAWoB,MAAO,CAC7CjB,YAAaH,EAAWG,YACxBkD,QAASrD,EAAWqD,QACpBvD,gBAAiBE,EAAWF,gBAC5B8N,qBAAsB5M,OAAO6M,KAAK7N,EAAWM,mBAG/C,MAAMoF,EAA2B,CAC/BtE,GAAIpB,EAAWoB,GACf0M,UAAWC,EAAmB/N,EAAWF,iBACzC0F,aAAcuC,EAAW/H,EAAWwF,eAWtC,OAPAxE,OAAOC,QAAQwG,GAAe7C,SAAQiD,IAAqB,IAAnBZ,EAAOlG,GAAM8G,EAErC,eAAVZ,GAA2B+G,EAAgB/G,KAC7CvB,EAAIuB,GAASgH,EAAiBlN,GAChC,IAGK2E,CAAG,KAIduH,EAAiBQ,GACjBtQ,QAAQC,IAAI,0CAEd,CAAE,MAAOa,GACPd,QAAQc,MAAM,yCAAqCA,EACrD,CAAC,QACCkP,GAAkB,EACpB,GAGIY,EAAsBtP,IACgB,CACxC,wBAAyB,wBACzB,gBAAiB,gBACjB,iBAAkB,iBAClB,qBAAsB,qBACtB,qBAAsB,qBACtB,wBAAyB,wBACzB,gBAAiB,gBACjB,oBAAqB,oBACrB,KAAQ,aAEOA,IAAmBA,EAAeyP,QAAQ,KAAM,KAAKA,QAAQ,SAASC,GAAKA,EAAEC,iBAwB1FrG,EAAc0E,IAClB,MAAMhD,EAAO,IAAIzH,KAAKyK,GACtB,MAAO,CACLhD,KAAMA,EAAKiD,qBACXhD,KAAMD,EAAKkD,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,YAC9D,EAGGoB,EAAoBlN,IACxB,GAAc,OAAVA,QAA4B7D,IAAV6D,EAAqB,MAAO,GAElD,GAAqB,kBAAVA,GAAsBA,EAAMG,SAAS,MAAQH,EAAMG,SAAS,KACrE,IAEE,OADa,IAAIc,KAAKjB,GACV2L,oBACd,CAAE,MAAOT,GACP,OAAOlL,CACT,CAGF,OAAOsN,OAAOtN,EAAM,EAmChBiN,EAAmBM,GACA,CACrB,gBAAiB,eAAgB,YAAa,WAAY,OAAQ,OAClE,aAAc,YAAa,YAAa,WAAY,YAAa,WACjE,mBAAoB,kBAAmB,eAAgB,cACvD,eAAgB,cAAe,iBAAkB,iBAE7BpN,SAASoN,GA6HjC,OAAIpB,GAEA7E,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEY,QAAS,OAAQgB,UAAW,SAAU1B,MAAO,QAASH,UAClEI,EAAAA,EAAAA,KAAA,OAAAJ,SAAK,uDAKP+B,GAEA3B,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEY,QAAS,OAAQgB,UAAW,SAAU1B,MAAO,QAASH,UAClEI,EAAAA,EAAAA,KAAA,OAAAJ,SAAK,0CAKgB,IAAvB9D,EAAYlH,QAEZ+K,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEY,QAAS,OAAQgB,UAAW,SAAU1B,MAAO,QAASH,SAAA,EAClEI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,OAAQrB,aAAc,QAASF,SAAC,kBACxDI,EAAAA,EAAAA,KAAA,MAAAJ,SAAI,0BACJI,EAAAA,EAAAA,KAAA,KAAAJ,SAAG,qDACHI,EAAAA,EAAAA,KAAA,UACEH,MAAO,CACLY,QAAS,cACTG,gBAAiB,UACjBb,MAAO,QACPW,OAAQ,OACRC,aAAc,OAEhB+C,QAAS9B,EAAUhC,SACpB,6BAQLD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEqG,UAAW,QAAStG,SAAA,EAChCD,EAAAA,EAAAA,MAAA,SAAOE,MAAO,CAAEsG,MAAO,OAAQC,eAAgB,WAAYC,SAAU,SAAUzG,SAAA,EAC7EI,EAAAA,EAAAA,KAAA,SAAAJ,UACEI,EAAAA,EAAAA,KAAA,MAAIH,MAAO,CAAEe,gBAAiB,WAAYhB,SACvC6E,EAAQlN,KAAK+O,IACZtG,EAAAA,EAAAA,KAAA,MAEEH,MAAO,CACLY,QAAS,eACTgB,UAAW,OACXP,WAAY,MACZqF,aAAc,oBACdC,WAAY,SACZH,SAA0B,UAAhBC,EAAO3H,KAAmB,QAAU,QAC9CiB,SAED0G,EAAO1H,OAVH0H,EAAO7N,YAepBuH,EAAAA,EAAAA,KAAA,SAAAJ,SACG+E,EAAcpN,KAAI,CAAC8F,EAAK+C,KACvBJ,EAAAA,EAAAA,KAAA,MAA+BH,MAAO,CAAE0G,aAAc,kBAAmB3G,SACtE6E,EAAQlN,KAAK+O,IAAM,IAAAG,EAAAC,EAAA,OAClB/G,EAAAA,EAAAA,MAAA,MAEEE,MAAO,CACLY,QAAS,UACTkG,cAAe,MACfC,SAAU,QACV1C,SAAU,SACV2C,aAAc,WACdL,WAAY,UACZ5G,SAAA,CAEe,cAAhB0G,EAAO3H,OACNqB,EAAAA,EAAAA,KAAA,QAAMH,MAAO,CACXe,gBAAiB,UACjBb,MAAO,UACPU,QAAS,iBACTE,aAAc,MACdQ,SAAU,UACVD,WAAY,OACZtB,SACCvC,EAAIiJ,EAAO7N,OAGC,SAAhB6N,EAAO3H,OACNgB,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEsB,SAAU,SAAUpB,MAAO,QAASH,SAAA,EAChDI,EAAAA,EAAAA,KAAA,OAAAJ,SAAqB,QAArB6G,EAAMpJ,EAAIiJ,EAAO7N,YAAI,IAAAgO,OAAA,EAAfA,EAAiBrF,QACvBpB,EAAAA,EAAAA,KAAA,SAAAJ,SAAuB,QAAvB8G,EAAQrJ,EAAIiJ,EAAO7N,YAAI,IAAAiO,OAAA,EAAfA,EAAiBrF,UAGZ,UAAhBiF,EAAO3H,OACNqB,EAAAA,EAAAA,KAAA,QAAM8G,MAAOzJ,EAAIiJ,EAAO7N,MAAQ,GAAGmH,SAChCvC,EAAIiJ,EAAO7N,MAAQ,QA9BnB6N,EAAO7N,IAiCT,KApCA,GAAG4E,EAAItE,MAAMqH,aA4C5BT,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CACVY,QAAS,cACTG,gBAAiB,UACjBmG,UAAW,oBACXlG,QAAS,OACTC,eAAgB,gBAChBC,WAAY,UACZnB,SAAA,EACAD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEsB,SAAU,WAAYpB,MAAO,QAASH,SAAA,CAAC,WAC1C9D,EAAYlH,OAAO,uBAAqB6P,EAAQ7P,OAAS,EAAE,mBAEtEoL,EAAAA,EAAAA,KAAA,UACEH,MAAO,CACLY,QAAS,mBACTG,gBAAiB,UACjBb,MAAO,QACPW,OAAQ,OACRC,aAAc,OAEhB+C,QAAS9B,EAAUhC,SACpB,8BAIC,E,qBCtcV,GAAwB,qBAAboH,SAA0B,CACnC,MAAMnH,EAAQmH,SAASC,cAAc,SACrCpH,EAAMqH,YAVa,6GAWnBF,SAASrR,KAAKwR,YAAYtH,EAC5B,CAEA,MAioBA,EAjoB0BuH,KACxB,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,MACjBC,EAAUC,IAAe1F,EAAAA,EAAAA,UAAc,OACvChG,EAAa2L,IAAkB3F,EAAAA,EAAAA,UAAuC,KACtEH,EAAS+F,IAAc5F,EAAAA,EAAAA,WAAS,IAChClM,EAAO+R,IAAY7F,EAAAA,EAAAA,UAAwB,OAC3C8F,EAAUC,IAAe/F,EAAAA,EAAAA,WAA2B,KAEzD,MAAMgG,EAAgBC,eAAeC,QAAQ,qBAC7C,MAA0B,SAAlBF,GAA8C,UAAlBA,EAA6BA,EAAgB,OAAO,KAEnFpT,EAASuT,IAAcnG,EAAAA,EAAAA,UAAwB,CACpDnL,MAAO,GACPC,OAAQ,KAEH2E,EAASsG,IAAcC,EAAAA,EAAAA,UAAc,OACrChD,EAAiBoJ,IAAsBpG,EAAAA,EAAAA,UAAmB,KAG1DqG,EAAeC,IAAoBtG,EAAAA,EAAAA,UAAgD,KACnFuG,EAAeC,IAAoBxG,EAAAA,EAAAA,WAAS,IAC5CyG,EAAaC,IAAkB1G,EAAAA,EAAAA,UAAiB,KAEvDK,EAAAA,EAAAA,YAAU,KACcE,WACpB,GAAIgF,EAAa,CACf,MAAMoB,GAAUxK,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAamJ,EAAYqB,KAC3CC,QAAiBvK,EAAAA,EAAAA,IAAOqK,GAC1BE,EAAStK,UACXmJ,EAAYmB,EAAS7R,OAEzB,GAEF8R,EAAe,GACd,CAACvB,KAEJlF,EAAAA,EAAAA,YAAU,KACR0G,IACAC,GAAkB,GACjB,KAEH3G,EAAAA,EAAAA,YAAU,KACR4G,GAAkB,GACjB,CAACrU,KAGJyN,EAAAA,EAAAA,YAAU,KACR,MAAM6G,EAAY,uCACZC,EAAyB,UAAbrB,EAAuB,aAAe,YAIxD,OAHAZ,SAASF,MAAQ,GAAGkC,OAAeC,IAG5B,KACLjC,SAASF,MAAQkC,CAAS,CAC3B,GACA,CAACpB,IAEJ,MAAMiB,EAAmBxG,UACvB,IACE,MAAO6G,EAAaC,SAAqB9D,QAAQC,IAAI,CACnD9Q,EAAe0E,oBACf1E,EAAeiH,uBAEjBoG,EAAWqH,GACXhB,EAAmBiB,EACrB,CAAE,MAAOpT,GACPjB,QAAQc,MAAM,+BAAgCG,EAChD,GAII+S,GAAmBM,EAAAA,EAAAA,cAAY/G,UACnC,KAAI8F,EAAcvT,OAAS,IAAM2T,EAAjC,CAIAD,GAAiB,GACjBE,EAAe,IAEf,IACE1T,QAAQC,IAAI,sEAGZ,MAAMsU,QAAoBC,EAAAA,EAAcC,+BAClCC,EAAUF,EAAAA,EAAcG,qBAAqBJ,GAEnDjB,EAAiBoB,GACjB1U,QAAQC,IAAI,sCAAkCyU,EAAQ5U,OAAQ,iBAEhE,CAAE,MAAOgB,GACP,MAAMqB,EAAerB,aAAiBI,MAAQJ,EAAME,QAAU,8BAC9D0S,EAAevR,GACfnC,QAAQc,MAAM,+CAA2CA,EAC3D,CAAC,QACC0S,GAAiB,EACnB,CArBA,CAqBA,GACC,CAACH,EAAcvT,OAAQ2T,IAEpBQ,EAAmB1G,UACvB,IACEqF,GAAW,GACXC,EAAS,MAET,MAAM7Q,QAAatC,EAAeC,mBAAmBC,GAErD+S,EAAe3Q,EAEjB,CAAE,MAAOf,GACPjB,QAAQc,MAAM,8BAA+BG,GAC7C4R,EAAS5R,aAAeC,MAAQD,EAAID,QAAU,8BAChD,CAAC,QACC4R,GAAW,EACb,GAGIgC,EAAuBC,IAC3B7U,QAAQC,IAAI,8CAAqC4U,GACjD1B,EAAW,IAAK0B,EAAY/S,OAAQ,GAAI,EAGpCgT,EAAwBC,IAC5BhC,EAAYgC,GAEZ9B,eAAe+B,QAAQ,oBAAqBD,GAC5C/U,QAAQC,IAAI,8CAAqC8U,EAAY,EAqJ/D,OACElK,EAAAA,EAAAA,MAAA,OAAKoK,UAAU,sBAAqBnK,SAAA,EAClCI,EAAAA,EAAAA,KAACgK,EAAAA,EAAO,CAACzC,SAAUA,KAGnB5H,EAAAA,EAAAA,MAAA,OAAKoK,UAAU,eAAcnK,SAAA,EAC3BD,EAAAA,EAAAA,MAAA,OAAKoK,UAAU,aAAYnK,SAAA,CAAC,WAE1BI,EAAAA,EAAAA,KAAA,UACE+J,UAAU,uBACVrG,QA5JWrB,UACnB,IACE,MAAMnF,QAAmB1I,EAAeyI,YAAYvI,GAG9CuV,EAAO,IAAIC,KAAK,CAAChN,GAAa,CAAEyB,KAAM,4BACtCwL,EAAOnD,SAASC,cAAc,KAC9BmD,EAAMC,IAAIC,gBAAgBL,GAChCE,EAAKI,aAAa,OAAQH,GAC1BD,EAAKI,aAAa,WAAY,qBAAoB,IAAI5Q,MAAOK,cAAcwQ,MAAM,KAAK,UACtFL,EAAKtK,MAAM4K,WAAa,SACxBzD,SAAS0D,KAAKvD,YAAYgD,GAC1BA,EAAKQ,QACL3D,SAAS0D,KAAKE,YAAYT,EAE5B,CAAE,MAAOpU,GACPjB,QAAQc,MAAM,wBAAyBG,GACvC8U,MAAM,2CACR,GA2IQC,SAAUnJ,GAAkC,IAAvB7F,EAAYlH,OACjCiL,MAAO,CAAEsE,WAAY,OAAQ1D,QAAS,cAAeG,gBAAiB,UAAWb,MAAO,QAASW,OAAQ,OAAQC,aAAc,OAAQf,SACxI,6BAGDI,EAAAA,EAAAA,KAAA,UACE0D,QAASrB,UACP,GAAIvG,EAAYlH,OAAS,EAAG,CAC1B,MAAMmW,EAAkBjP,EAAY,GACpChH,QAAQC,IAAI,8CAAqCgW,GAEjD,IACE,MAAM3L,QAAsBzB,EAAkBuB,sBAC5C6L,EAAgBtT,gBAChBsT,EAAgB9S,iBAGlB4S,MAAM,SAASE,EAAgBtT,qCAAqCkB,OAAO6M,KAAKuF,EAAgB9S,iBAAiBsF,KAAK,iCAAiC5E,OAAO6M,KAAKpG,GAAe7B,KAAK,4BAA4BvI,KAAKC,UAAUmK,EAAe,KAAM,KACzP,CAAE,MAAOxJ,GACPiV,MAAM,UAAUjV,IAClB,CACF,MACEiV,MAAM,mCACR,EAEFhL,MAAO,CAAEsE,WAAY,SAAU1D,QAAS,cAAeG,gBAAiB,UAAWb,MAAO,QAASW,OAAQ,OAAQC,aAAc,OAAQf,SAC1I,6BAGDI,EAAAA,EAAAA,KAAA,UACE0D,QAASA,KACP,GAAI5H,EAAYlH,OAAS,EAAG,CAC1B,MAAMoW,EAAalP,EAAYvE,KAAI0T,IAAG,IAAAC,EAAA,MAAK,CACzCnS,GAAIkS,EAAIlS,GACRf,YAAaiT,EAAIjT,YACjBmT,uBAA2C,QAArBD,EAAED,EAAIhT,uBAAe,IAAAiT,OAAA,EAAnBA,EAAqBhT,WAC7CkT,sBAAuBzS,OAAO6M,KAAKyF,EAAIhT,iBAAmB,CAAC,GAC3DoT,cAAe1S,OAAOC,QAAQqS,EAAIhT,iBAAmB,CAAC,GAAGG,QAAOoH,IAAA,IAAE/G,EAAKC,GAAM8G,EAAA,MAC1D,kBAAV9G,IACLA,EAAMG,SAAS,QAAUH,EAAMG,SAAS,QAAUH,EAAMG,SAAS,QACjEH,EAAMG,SAAS,QAAUH,EAAMG,SAAS,QAAUH,EAAMG,SAAS,UAClE,IAEJ,IACD/D,QAAQC,IAAI,kCAAyBiW,GACrCH,MAAM,oBAAoB7V,KAAKC,UAAU+V,EAAWM,MAAM,EAAG,GAAI,KAAM,KACzE,MACET,MAAM,0BACR,EAEFhL,MAAO,CAAEsE,WAAY,SAAU1D,QAAS,cAAeG,gBAAiB,UAAWb,MAAO,QAASW,OAAQ,OAAQC,aAAc,OAAQf,SAC1I,kCAMFrE,IACCyE,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEC,aAAc,QAASF,UACnCI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEgB,QAAS,OAAQwC,IAAK,OAAQkI,SAAU,QAAS3L,SAC5D,CACC,CAAE4L,KAAM,eAAM9S,MAAO6C,EAAQlB,iBAAkBuE,MAAO,oBAAqBmB,MAAO,WAClF,CAAEyL,KAAM,eAAM9S,MAAO6C,EAAQd,YAAamE,MAAO,eAAgBmB,MAAO,WACxE,CAAEyL,KAAM,eAAM9S,MAAO6C,EAAQR,YAAa6D,MAAO,eAAgBmB,MAAO,WACxE,CAAEyL,KAAM,eAAM9S,MAAO6C,EAAQN,iBAAkB2D,MAAO,QAASmB,MAAO,WACtE,CAAEyL,KAAM,eAAM9S,MAAO6C,EAAQJ,oBAAqByD,MAAO,YAAamB,MAAO,WAC7E,CAAEyL,KAAM,qBAAO9S,MAAO6C,EAAQF,qBAAsBuD,MAAO,aAAcmB,MAAO,YAChFxI,KAAI,CAACkU,EAAMrL,KACXT,EAAAA,EAAAA,MAAA,OAAiBE,MAAO,CACtByD,WAAY,QACZ7C,QAAS,SACTE,aAAc,MACd8C,UAAW,4BACXhC,UAAW,SACX4E,SAAU,QACVrF,KAAM,KACNpB,SAAA,EACAI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,OAAQrB,aAAc,UAAWF,SAAE6L,EAAKD,QAChExL,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,OAAQD,WAAY,OAAQnB,MAAO0L,EAAK1L,MAAOD,aAAc,WAAYF,SAC9F6L,EAAK/S,MAAM0E,oBAEd4C,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAEsB,SAAU,WAAYpB,MAAO,OAAQ2L,cAAe,YAAaC,cAAe,SAAU/L,SACrG6L,EAAK7M,UAdAwB,UAuBlBT,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CACVyD,WAAY,QACZ7C,QAAS,SACTE,aAAc,MACdb,aAAc,OACd2D,UAAW,6BACX7D,SAAA,EACAI,EAAAA,EAAAA,KAAA,MAAIH,MAAO,CAAEC,aAAc,QAASF,SAAC,0BACrCD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEgB,QAAS,OAAQwC,IAAK,OAAQkI,SAAU,QAAS3L,SAAA,EAC7DD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEmB,KAAM,IAAKqF,SAAU,SAAUzG,SAAA,EAC3CI,EAAAA,EAAAA,KAAA,SAAOH,MAAO,CAAEgB,QAAS,QAASf,aAAc,SAAUoB,WAAY,OAAQtB,SAAC,eAC/ED,EAAAA,EAAAA,MAAA,UACEE,MAAO,CAAEsG,MAAO,OAAQ1F,QAAS,SAAUC,OAAQ,iBAAkBC,aAAc,OACnFjI,MAAOhE,EAAQ0B,gBAAkB,GACjCwV,SAAWhI,GAAM8F,EAAoB,IAAKhV,EAAS0B,eAAgBwN,EAAEiI,OAAOnT,YAAS7D,IAAa+K,SAAA,EAElGI,EAAAA,EAAAA,KAAA,UAAQtH,MAAM,GAAEkH,SAAC,cAChBd,EAAgBvH,KAAIuU,IACnB9L,EAAAA,EAAAA,KAAA,UAAyBtH,MAAOoT,EAAWlM,SACxCkM,GADUA,YAMnBnM,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEmB,KAAM,IAAKqF,SAAU,SAAUzG,SAAA,EAC3CI,EAAAA,EAAAA,KAAA,SAAOH,MAAO,CAAEgB,QAAS,QAASf,aAAc,SAAUoB,WAAY,OAAQtB,SAAC,iBAC/ED,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEkM,SAAU,YAAanM,SAAA,EACnCD,EAAAA,EAAAA,MAAA,UACEE,MAAO,CACLsG,MAAO,OACP1F,QAAS,SACTC,OAAQ,iBACRC,aAAc,MACdC,gBAAiByH,EAAgB,UAAY,SAE/C3P,MAAOhE,EAAQwD,YAAc,GAC7B0T,SAAWhI,GAAM8F,EAAoB,IAAKhV,EAASwD,WAAY0L,EAAEiI,OAAOnT,YAAS7D,IACjFiW,SAAUzC,EAAczI,SAAA,EAExBI,EAAAA,EAAAA,KAAA,UAAQtH,MAAM,GAAEkH,SACbyI,EAAgB,qBAAuB,gBAEzCF,EAAc5Q,KAAIyU,IACjBhM,EAAAA,EAAAA,KAAA,UAA2BtH,MAAOsT,EAAOtT,MAAMkH,SAC5CoM,EAAOpN,OADGoN,EAAOtT,YAOvB2P,IACCrI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CACVkM,SAAU,WACVE,MAAO,MACPC,IAAK,MACLpI,UAAW,mBACXqC,MAAO,OACPgG,OAAQ,QACRvM,UACAI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CACVsG,MAAO,OACPgG,OAAQ,OACRzL,OAAQ,oBACRqG,UAAW,oBACXpG,aAAc,MACdyL,UAAW,kCAOlB7D,IACCvI,EAAAA,EAAAA,KAAA,OAAKH,MAAO,CAAE6B,UAAW,SAAUP,SAAU,WAAYpB,MAAO,WAAYH,UAC1ED,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEgB,QAAS,OAAQE,WAAY,SAAUsC,IAAK,UAAWzD,SAAA,EACnED,EAAAA,EAAAA,MAAA,QAAAC,SAAA,CAAM,gBAAI2I,MACVvI,EAAAA,EAAAA,KAAA,UACErB,KAAK,SACLkB,MAAO,CACLY,QAAS,iBACTU,SAAU,UACVP,gBAAiB,UACjBb,MAAO,QACPW,OAAQ,OACRC,aAAc,MACd4C,OAAQ,WAEVG,QAASoF,EACTgC,SAAUzC,EAAczI,SACzB,gBAQLyI,IAAkBE,GAAeJ,EAAcvT,OAAS,IACxD+K,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAE6B,UAAW,SAAUP,SAAU,WAAYpB,MAAO,WAAYH,SAAA,CAAC,UACxEuI,EAAcvT,OAAO,yBAI9B+K,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CAAEgB,QAAS,OAAQE,WAAY,MAAOsC,IAAK,UAAWzD,SAAA,EAChEI,EAAAA,EAAAA,KAAA,UACEH,MAAO,CAAEY,QAAS,cAAeG,gBAAiB,UAAWb,MAAO,QAASW,OAAQ,OAAQC,aAAc,OAC3G+C,QAASA,IAAMqF,IAAmBnJ,SACnC,wBAGDI,EAAAA,EAAAA,KAAA,UACEH,MAAO,CAAEY,QAAS,cAAeG,gBAAiB,UAAWb,MAAO,QAASW,OAAQ,OAAQC,aAAc,OAC3G+C,QAASA,KACPuE,EAAW,CAAEtR,MAAO,GAAIC,OAAQ,IAChC8S,EAAoB,CAAE/S,MAAO,GAAIC,OAAQ,GAAI,EAC7CgJ,SACH,iCAQPD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CACVyD,WAAY,QACZ7C,QAAS,cACTE,aAAc,MACdb,aAAc,OACd2D,UAAW,4BACX5C,QAAS,OACTC,eAAgB,gBAChBC,WAAY,UACZnB,SAAA,EACAD,EAAAA,EAAAA,MAAA,OAAAC,SAAA,EACEI,EAAAA,EAAAA,KAAA,MAAIH,MAAO,CAAEoB,OAAQ,EAAGlB,MAAO,QAASH,SAAC,kCACzCI,EAAAA,EAAAA,KAAA,KAAGH,MAAO,CAAEoB,OAAQ,gBAAiBE,SAAU,WAAYpB,MAAO,QAASH,SAC3D,UAAbgI,EACG,0DACA,yDAKRjI,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CACVgB,QAAS,OACTD,gBAAiB,UACjBD,aAAc,MACdF,QAAS,MACTC,OAAQ,qBACRd,SAAA,EACAI,EAAAA,EAAAA,KAAA,UACEH,MAAO,CACLY,QAAS,cACTC,OAAQ,OACRC,aAAc,MACdC,gBAA8B,UAAbgH,EAAuB,UAAY,cACpD7H,MAAoB,UAAb6H,EAAuB,QAAU,UACxC1G,WAAyB,UAAb0G,EAAuB,MAAQ,MAC3CrE,OAAQ,UACRC,WAAY,gBACZ3C,QAAS,OACTE,WAAY,SACZsC,IAAK,SACLlC,SAAU,YAEZuC,QAASA,IAAMkG,EAAqB,SACpCjG,aAAeC,IACI,UAAbgE,IACFhE,EAAEC,cAAchE,MAAMe,gBAAkB,UAC1C,EAEFmD,aAAeH,IACI,UAAbgE,IACFhE,EAAEC,cAAchE,MAAMe,gBAAkB,cAC1C,EAEFkG,MAAM,oEAAmElH,SAC1E,6BAGDI,EAAAA,EAAAA,KAAA,UACEH,MAAO,CACLY,QAAS,cACTC,OAAQ,OACRC,aAAc,MACdC,gBAA8B,SAAbgH,EAAsB,UAAY,cACnD7H,MAAoB,SAAb6H,EAAsB,QAAU,UACvC1G,WAAyB,SAAb0G,EAAsB,MAAQ,MAC1CrE,OAAQ,UACRC,WAAY,gBACZ3C,QAAS,OACTE,WAAY,SACZsC,IAAK,SACLlC,SAAU,YAEZuC,QAASA,IAAMkG,EAAqB,QACpCjG,aAAeC,IACI,SAAbgE,IACFhE,EAAEC,cAAchE,MAAMe,gBAAkB,UAC1C,EAEFmD,aAAeH,IACI,SAAbgE,IACFhE,EAAEC,cAAchE,MAAMe,gBAAkB,cAC1C,EAEFkG,MAAM,8DAA6DlH,SACpE,kCAOLD,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CACVyD,WAAY,QACZ3C,aAAc,MACd8C,UAAW,4BACXS,SAAU,UACVtE,SAAA,CACChK,IACC+J,EAAAA,EAAAA,MAAA,OAAKE,MAAO,CACVY,QAAS,OACTG,gBAAiB,UACjBb,MAAO,UACPwG,aAAc,qBACd3G,SAAA,CAAC,gBACGhK,GACJoK,EAAAA,EAAAA,KAAA,UACEH,MAAO,CACLsE,WAAY,OACZ1D,QAAS,iBACTG,gBAAiB,UACjBb,MAAO,QACPW,OAAQ,OACRC,aAAc,OAEhB+C,QAASqF,EAAiBnJ,SAC3B,aAOS,UAAbgI,GACC5H,EAAAA,EAAAA,KAACqM,EAAmB,CAClBvQ,YAAaA,EACb6F,QAASA,EACTC,UAAWmH,KAGb/I,EAAAA,EAAAA,KAACsM,EAAuB,CACtBxQ,YAAaA,EACb6F,QAASA,EACTC,UAAWmH,EACXrU,QAASA,YAKb,C", "sources": ["services/reportsService.ts", "services/formConfigService.ts", "components/Reports/SubmissionsSummaryCards.tsx", "components/Reports/DynamicReportsTable.tsx", "components/Reports/Reports.tsx"], "sourcesContent": ["import { supabase } from '../config/supabaseClient';\n\nexport interface FormSubmission {\n  id: string;\n  form_identifier: string;\n  user_id: string | null;\n  employee_id?: string | null; // New field for employee ID\n  submission_data: Record<string, any>;\n  submitted_at: string;\n  created_at?: string;\n}\n\nexport interface ReportsFilter {\n  formIdentifier?: string;\n  userId?: string;\n  startDate?: string;\n  endDate?: string;\n  officeName?: string;\n  limit?: number;\n  offset?: number;\n}\n\nexport interface ReportsSummary {\n  totalSubmissions: number;\n  uniqueForms: number;\n  uniqueUsers: number;\n  submissionsToday: number;\n  submissionsThisWeek: number;\n  submissionsThisMonth: number;\n}\n\nexport interface FormSubmissionWithUserData extends FormSubmission {\n  user_name?: string;\n  user_email?: string;\n  user_office?: string;\n}\n\nclass ReportsService {\n  private static readonly CACHE_EXPIRY_MINUTES = 5;\n  private static cache = new Map<string, { data: any; timestamp: Date }>();\n\n  /**\n   * Fetches form submissions with optional filtering\n   */\n  static async getFormSubmissions(filters: ReportsFilter = {}): Promise<FormSubmissionWithUserData[]> {\n    try {\n      console.log('🔍 ReportsService: Starting getFormSubmissions...');\n      console.log('📋 ReportsService: Filters:', JSON.stringify(filters, null, 2));\n\n      // Test multiple data sources to find working one\n      console.log('🔗 ReportsService: Testing multiple data sources...');\n\n      let workingTable = null;\n      let testData = null;\n      let testError = null;\n\n      // Try different tables in order of preference\n      const tablesToTry = [\n        'reports_data_view',      // Unified view (preferred)\n        'dynamic_form_submissions', // Original table\n        'reports_test_data'       // Test table fallback\n      ];\n\n      for (const tableName of tablesToTry) {\n        console.log(`🧪 ReportsService: Trying table: ${tableName}`);\n        try {\n          const result = await supabase\n            .from(tableName)\n            .select('count', { count: 'exact', head: true });\n\n          if (!result.error && result.count !== null) {\n            workingTable = tableName;\n            testData = result.count;\n            console.log(`✅ ReportsService: ${tableName} works with ${result.count} records`);\n            break;\n          } else {\n            console.log(`❌ ReportsService: ${tableName} failed:`, result.error?.message);\n          }\n        } catch (err) {\n          console.log(`❌ ReportsService: ${tableName} error:`, err);\n        }\n      }\n\n      if (!workingTable) {\n        console.error('❌ ReportsService: No working data source found');\n        throw new Error('No accessible data source found. Please run the DIRECT_QUERY_APPROACH.sql script.');\n      }\n\n      console.log('✅ ReportsService: Using data source:', workingTable);\n      console.log('📊 ReportsService: Found', testData, 'records');\n\n      // Now fetch the actual data from the working table with user_profile join\n      console.log('📥 ReportsService: Fetching submissions data with user profile join...');\n\n      let query = supabase\n        .from(workingTable)\n        .select('*')\n        .order('submitted_at', { ascending: false });\n\n      // Apply filters\n      if (filters.formIdentifier) {\n        console.log('🔍 ReportsService: Applying form identifier filter:', filters.formIdentifier);\n        query = query.eq('form_identifier', filters.formIdentifier);\n      }\n\n      if (filters.userId) {\n        console.log('🔍 ReportsService: Applying user ID filter:', filters.userId);\n        query = query.eq('user_id', filters.userId);\n      }\n\n      if (filters.startDate) {\n        console.log('🔍 ReportsService: Applying start date filter:', filters.startDate);\n        query = query.gte('submitted_at', filters.startDate);\n      }\n\n      if (filters.endDate) {\n        console.log('🔍 ReportsService: Applying end date filter:', filters.endDate);\n        query = query.lte('submitted_at', filters.endDate);\n      }\n\n      if (filters.limit) {\n        console.log('🔍 ReportsService: Applying limit:', filters.limit);\n        query = query.limit(filters.limit);\n      }\n\n      if (filters.offset) {\n        console.log('🔍 ReportsService: Applying offset:', filters.offset);\n        query = query.range(filters.offset, filters.offset + (filters.limit || 50) - 1);\n      }\n\n      const { data, error } = await query;\n\n      console.log('📦 ReportsService: Query response:', {\n        dataLength: data?.length || 0,\n        hasError: !!error,\n        errorMessage: error?.message\n      });\n\n      if (error) {\n        console.error('❌ ReportsService: Query error:', error);\n        console.error('🔍 ReportsService: Error details:', {\n          message: error.message,\n          details: error.details,\n          hint: error.hint,\n          code: error.code\n        });\n        throw new Error(`Failed to fetch form submissions: ${error.message}`);\n      }\n\n      if (!data || data.length === 0) {\n        console.warn('⚠️ ReportsService: No data returned from query');\n        console.log('🔍 ReportsService: Possible causes:');\n        console.log('  1. Table exists but has no data matching filters');\n        console.log('  2. All data filtered out by applied filters');\n        console.log('  3. RLS (Row Level Security) blocking access');\n        console.log('  4. Data exists but query conditions exclude it');\n\n        // Try a simple count query to see if data exists at all\n        const { count, error: countError } = await supabase\n          .from('dynamic_form_submissions')\n          .select('*', { count: 'exact', head: true });\n\n        console.log('📊 ReportsService: Total records in table:', count);\n        if (countError) {\n          console.error('❌ ReportsService: Count query error:', countError);\n        }\n\n        return [];\n      }\n\n      console.log('✅ ReportsService: Successfully fetched', data.length, 'submissions');\n      console.log('📄 ReportsService: First submission sample:', JSON.stringify(data[0], null, 2));\n      console.log('📋 ReportsService: All form identifiers:', data.map(d => d.form_identifier));\n\n      // Use employee_id directly instead of enhancing with user data\n      console.log('📋 ReportsService: Using employee_id values directly from database');\n      const enhancedData = (data || []).map((submission: any) => ({\n        ...submission,\n        user_name: submission.employee_id || 'Unknown',\n        user_email: '<EMAIL>',\n        user_office: submission.submission_data?.officeName || 'Unknown Office'\n      }));\n\n      // Apply office filter if specified (after user data enhancement)\n      if (filters.officeName) {\n        console.log('🔍 ReportsService: Applying office name filter:', filters.officeName);\n        console.log('🔍 ReportsService: Looking for office name in submission_data...');\n\n        const filteredData = enhancedData.filter(submission => {\n          // Check multiple possible locations for office name\n          const submissionDataOffice = submission.submission_data?.officeName;\n          const userOffice = submission.user_office;\n\n          // Look through all submission_data fields for office names\n          let foundOffice = null;\n          if (submission.submission_data) {\n            for (const [key, value] of Object.entries(submission.submission_data)) {\n              if (typeof value === 'string' && (\n                value.includes(' RO') || value.includes(' BO') || value.includes(' SO') ||\n                value.includes(' HO') || value.includes(' DO') || value.includes('Office')\n              )) {\n                foundOffice = value;\n                break;\n              }\n            }\n          }\n\n          const officeToCheck = foundOffice || submissionDataOffice || userOffice || '';\n          console.log(`📋 Submission ${submission.id}: office=\"${officeToCheck}\", filter=\"${filters.officeName}\"`);\n\n          return officeToCheck.toLowerCase().includes(filters.officeName!.toLowerCase());\n        });\n\n        console.log('📊 ReportsService: Office filter result:', filteredData.length, 'submissions');\n        return filteredData;\n      }\n\n      console.log('🎉 ReportsService: Returning', enhancedData.length, 'enhanced submissions');\n      return enhancedData;\n\n    } catch (error) {\n      console.error('💥 ReportsService: Fatal error in getFormSubmissions:', error);\n      console.error('🔍 ReportsService: Error stack:', error instanceof Error ? error.stack : 'No stack trace');\n      throw error;\n    }\n  }\n\n  /**\n   * Gets summary statistics for reports dashboard\n   */\n  static async getReportsSummary(): Promise<ReportsSummary> {\n    try {\n      console.log('ReportsService: Fetching reports summary...');\n\n      // Check cache first\n      const cacheKey = 'reports_summary';\n      const cached = this.cache.get(cacheKey);\n      if (cached && this.isCacheValid(cached.timestamp)) {\n        console.log('ReportsService: Returning cached summary');\n        return cached.data;\n      }\n\n      const now = new Date();\n      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()).toISOString();\n      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString();\n      const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1).toISOString();\n\n      // Find working data source\n      const workingTable = await this.findWorkingDataSource();\n\n      // Get total submissions\n      const { count: totalSubmissions, error: totalError } = await supabase\n        .from(workingTable)\n        .select('*', { count: 'exact', head: true });\n\n      if (totalError) {\n        console.error('ReportsService: Error getting total count:', totalError);\n        throw totalError;\n      }\n\n      console.log('ReportsService: Total submissions count:', totalSubmissions);\n\n      // Get unique forms\n      const { data: formsData, error: formsError } = await supabase\n        .from(workingTable)\n        .select('form_identifier');\n\n      if (formsError) {\n        console.error('ReportsService: Error getting forms:', formsError);\n        throw formsError;\n      }\n\n      const uniqueForms = new Set(formsData?.map((item: any) => item.form_identifier)).size;\n      console.log('ReportsService: Unique forms count:', uniqueForms);\n\n      // Get unique users\n      const { data: usersData, error: usersError } = await supabase\n        .from(workingTable)\n        .select('user_id');\n\n      if (usersError) {\n        console.error('ReportsService: Error getting users:', usersError);\n        throw usersError;\n      }\n\n      const uniqueUsers = new Set(usersData?.map((item: any) => item.user_id)).size;\n      console.log('ReportsService: Unique users count:', uniqueUsers);\n\n      // Get submissions today\n      const { count: submissionsToday, error: todayError } = await supabase\n        .from(workingTable)\n        .select('*', { count: 'exact', head: true })\n        .gte('submitted_at', today);\n\n      if (todayError) {\n        console.error('ReportsService: Error getting today count:', todayError);\n        throw todayError;\n      }\n\n      // Get submissions this week\n      const { count: submissionsThisWeek, error: weekError } = await supabase\n        .from(workingTable)\n        .select('*', { count: 'exact', head: true })\n        .gte('submitted_at', weekAgo);\n\n      if (weekError) {\n        console.error('ReportsService: Error getting week count:', weekError);\n        throw weekError;\n      }\n\n      // Get submissions this month\n      const { count: submissionsThisMonth, error: monthError } = await supabase\n        .from(workingTable)\n        .select('*', { count: 'exact', head: true })\n        .gte('submitted_at', monthAgo);\n\n      if (monthError) {\n        console.error('ReportsService: Error getting month count:', monthError);\n        throw monthError;\n      }\n\n      const summary: ReportsSummary = {\n        totalSubmissions: totalSubmissions || 0,\n        uniqueForms,\n        uniqueUsers,\n        submissionsToday: submissionsToday || 0,\n        submissionsThisWeek: submissionsThisWeek || 0,\n        submissionsThisMonth: submissionsThisMonth || 0,\n      };\n\n      // Cache the result\n      this.cache.set(cacheKey, { data: summary, timestamp: new Date() });\n\n      console.log('ReportsService: Successfully generated summary:', summary);\n      return summary;\n\n    } catch (error) {\n      console.error('ReportsService: Error in getReportsSummary:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Gets list of unique form identifiers\n   */\n  static async getFormIdentifiers(): Promise<string[]> {\n    try {\n      console.log('ReportsService: Fetching form identifiers...');\n\n      const workingTable = await this.findWorkingDataSource();\n\n      const { data, error } = await supabase\n        .from(workingTable)\n        .select('form_identifier');\n\n      if (error) {\n        console.error('ReportsService: Error fetching form identifiers:', error);\n        throw error;\n      }\n\n      const uniqueIdentifiers = Array.from(\n        new Set(data?.map((item: any) => item.form_identifier as string) || [])\n      ).sort() as string[];\n\n      console.log('ReportsService: Found', uniqueIdentifiers.length, 'unique form identifiers:', uniqueIdentifiers);\n      return uniqueIdentifiers;\n\n    } catch (error) {\n      console.error('ReportsService: Error fetching form identifiers:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Enhances submission data with user information from user_profile table\n   */\n  private static async enhanceWithUserData(submissions: any[]): Promise<FormSubmissionWithUserData[]> {\n    try {\n      console.log('🔍 ReportsService: Enhancing submissions with user profile data...');\n\n      // Get all unique employee IDs from submissions\n      const employeeIds = submissions\n        .map(s => s.employee_id)\n        .filter(id => id && typeof id === 'string' && id.trim().length > 0);\n\n      console.log('🔍 ReportsService: Found employee IDs to lookup:', employeeIds);\n\n      // Fetch user profiles for these employee IDs\n      let userProfiles: any[] = [];\n      if (employeeIds.length > 0) {\n        const { data: profiles, error } = await supabase\n          .from('user_profile')\n          .select('employeeId, full_name, email, office_name, designation, department')\n          .in('employeeId', employeeIds);\n\n        if (error) {\n          console.error('❌ ReportsService: Error fetching user profiles:', error);\n        } else {\n          userProfiles = profiles || [];\n          console.log('✅ ReportsService: Fetched user profiles:', userProfiles);\n        }\n      }\n\n      // Create a map for quick lookup\n      const profileMap = new Map();\n      userProfiles.forEach(profile => {\n        profileMap.set(profile.employeeId, profile);\n      });\n\n      return submissions.map(submission => {\n        // Look up user profile by employee_id\n        const userProfile = submission.employee_id ? profileMap.get(submission.employee_id) : null;\n\n        console.log(`🔍 User profile for submission ${submission.id} (employee_id: ${submission.employee_id}):`, userProfile);\n\n        // Use user_profile data if available, otherwise fall back to employee_id or defaults\n        const enhancedSubmission: FormSubmissionWithUserData = {\n          ...submission,\n          user_name: userProfile?.full_name ||\n                    submission.employee_id ||\n                    (submission.user_id ? `User ${submission.user_id.substring(0, 8)}` : 'Unknown User'),\n          user_email: userProfile?.email || '<EMAIL>',\n          user_office: userProfile?.office_name ||\n                      submission.submission_data?.officeName ||\n                      'Unknown Office'\n        };\n\n        console.log(`✅ Enhanced submission ${submission.id}:`, {\n          user_name: enhancedSubmission.user_name,\n          user_email: enhancedSubmission.user_email,\n          user_office: enhancedSubmission.user_office,\n          employee_id: submission.employee_id\n        });\n\n        return enhancedSubmission;\n      });\n\n    } catch (error) {\n      console.error('ReportsService: Error enhancing with user data:', error);\n      return submissions.map(submission => ({\n        ...submission,\n        user_name: submission.employee_id || 'Unknown User',\n        user_email: '<EMAIL>',\n        user_office: 'Unknown Office'\n      }));\n    }\n  }\n\n  /**\n   * Exports submissions to CSV format\n   */\n  static async exportToCSV(filters: ReportsFilter = {}): Promise<string> {\n    try {\n      const submissions = await this.getFormSubmissions(filters);\n      \n      if (submissions.length === 0) {\n        throw new Error('No data to export');\n      }\n\n      // Create CSV headers\n      const headers = [\n        'ID',\n        'Form Identifier',\n        'User ID',\n        'User Name',\n        'User Office',\n        'Submitted At',\n        'Submission Data'\n      ];\n\n      // Create CSV rows\n      const rows = submissions.map(submission => [\n        submission.id,\n        submission.form_identifier,\n        submission.user_id,\n        submission.user_name || '',\n        submission.user_office || '',\n        new Date(submission.submitted_at).toLocaleString(),\n        JSON.stringify(submission.submission_data)\n      ]);\n\n      // Combine headers and rows\n      const csvContent = [headers, ...rows]\n        .map(row => row.map(field => `\"${field}\"`).join(','))\n        .join('\\n');\n\n      console.log('ReportsService: Successfully generated CSV with', submissions.length, 'records');\n      return csvContent;\n\n    } catch (error) {\n      console.error('ReportsService: Error exporting to CSV:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Finds a working data source from available options\n   */\n  private static async findWorkingDataSource(): Promise<string> {\n    const tablesToTry = [\n      'reports_data_view',      // Unified view (preferred)\n      'dynamic_form_submissions', // Original table\n      'reports_test_data'       // Test table fallback\n    ];\n\n    for (const tableName of tablesToTry) {\n      try {\n        const { count, error } = await supabase\n          .from(tableName)\n          .select('*', { count: 'exact', head: true });\n\n        if (!error && count !== null) {\n          console.log(`✅ ReportsService: Using ${tableName} with ${count} records`);\n          return tableName;\n        }\n      } catch (err) {\n        console.log(`❌ ReportsService: ${tableName} not accessible`);\n      }\n    }\n\n    throw new Error('No accessible data source found. Please run the DIRECT_QUERY_APPROACH.sql script.');\n  }\n\n  /**\n   * Clears the cache\n   */\n  static clearCache(): void {\n    this.cache.clear();\n    console.log('ReportsService: Cache cleared');\n  }\n\n  /**\n   * Checks if cached data is still valid\n   */\n  private static isCacheValid(timestamp: Date): boolean {\n    const now = new Date();\n    const diffMinutes = (now.getTime() - timestamp.getTime()) / (1000 * 60);\n    return diffMinutes < this.CACHE_EXPIRY_MINUTES;\n  }\n}\n\nexport default ReportsService;\n", "import { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../config/firebase';\n\nexport interface FormField {\n  id: string;\n  label: string;\n  type: 'text' | 'textarea' | 'dropdown' | 'radio' | 'button' | 'checkbox' | 'number' | 'date' | 'file' | 'section' | 'switch' | 'checkbox-group';\n  options?: Array<{ label: string; value: string }>;\n  placeholder?: string;\n  required?: boolean;\n  defaultValue?: string | number | boolean | string[];\n}\n\nexport interface FormConfig {\n  id: string;\n  title?: string;\n  fields: FormField[];\n}\n\nexport class FormConfigService {\n  private static cache = new Map<string, FormConfig>();\n  private static fieldMappingCache = new Map<string, Map<string, string>>();\n\n  /**\n   * Fetches form configuration from Firebase\n   */\n  static async getFormConfig(formIdentifier: string): Promise<FormConfig | null> {\n    try {\n      // Check cache first\n      if (this.cache.has(formIdentifier)) {\n        return this.cache.get(formIdentifier)!;\n      }\n\n      console.log(`🔍 FormConfigService: Fetching config for ${formIdentifier}`);\n      \n      // Try different possible document paths\n      const possiblePaths = [\n        `pages/${formIdentifier}`,\n        `formConfigs/${formIdentifier}`,\n        `forms/${formIdentifier}`\n      ];\n\n      for (const path of possiblePaths) {\n        try {\n          const docRef = doc(db, path);\n          const docSnap = await getDoc(docRef);\n\n          if (docSnap.exists()) {\n            const data = docSnap.data() as FormConfig;\n            if (data && data.fields) {\n              console.log(`✅ FormConfigService: Found config at ${path}`, data);\n              this.cache.set(formIdentifier, data);\n              return data;\n            }\n          }\n        } catch (err) {\n          console.log(`❌ FormConfigService: Failed to fetch from ${path}:`, err);\n        }\n      }\n\n      console.log(`⚠️ FormConfigService: No config found for ${formIdentifier}`);\n      return null;\n\n    } catch (error) {\n      console.error('FormConfigService: Error fetching form config:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Gets field ID to label mapping for a form\n   */\n  static async getFieldMapping(formIdentifier: string): Promise<Map<string, string>> {\n    try {\n      // Check cache first\n      if (this.fieldMappingCache.has(formIdentifier)) {\n        return this.fieldMappingCache.get(formIdentifier)!;\n      }\n\n      const formConfig = await this.getFormConfig(formIdentifier);\n      const mapping = new Map<string, string>();\n\n      if (formConfig && formConfig.fields) {\n        formConfig.fields.forEach(field => {\n          if (field.type !== 'section' && field.type !== 'button') {\n            mapping.set(field.id, field.label);\n          }\n        });\n      }\n\n      this.fieldMappingCache.set(formIdentifier, mapping);\n      console.log(`📋 FormConfigService: Created field mapping for ${formIdentifier}:`, mapping);\n      return mapping;\n\n    } catch (error) {\n      console.error('FormConfigService: Error creating field mapping:', error);\n      return new Map();\n    }\n  }\n\n  /**\n   * Gets all unique field labels across multiple form types\n   */\n  static async getAllFieldLabels(formIdentifiers: string[]): Promise<Set<string>> {\n    const allLabels = new Set<string>();\n\n    for (const formId of formIdentifiers) {\n      const mapping = await this.getFieldMapping(formId);\n      mapping.forEach(label => allLabels.add(label));\n    }\n\n    return allLabels;\n  }\n\n  /**\n   * Converts submission data from field IDs to readable labels\n   */\n  static async convertSubmissionData(\n    formIdentifier: string, \n    submissionData: Record<string, any>\n  ): Promise<Record<string, any>> {\n    try {\n      const mapping = await this.getFieldMapping(formIdentifier);\n      const convertedData: Record<string, any> = {};\n\n      Object.entries(submissionData).forEach(([fieldId, value]) => {\n        const label = mapping.get(fieldId) || fieldId;\n        convertedData[label] = value;\n      });\n\n      return convertedData;\n\n    } catch (error) {\n      console.error('FormConfigService: Error converting submission data:', error);\n      return submissionData;\n    }\n  }\n\n  /**\n   * Clears the cache\n   */\n  static clearCache(): void {\n    this.cache.clear();\n    this.fieldMappingCache.clear();\n    console.log('FormConfigService: Cache cleared');\n  }\n}\n\nexport default FormConfigService;\n", "import React, { useState, useEffect } from 'react';\nimport { FormSubmissionWithUserData, ReportsFilter } from '../../services/reportsService';\nimport { supabase } from '../../config/supabaseClient';\n\ninterface SubmissionsSummaryCardsProps {\n  submissions: FormSubmissionWithUserData[];\n  loading: boolean;\n  onRefresh: () => void;\n  filters: ReportsFilter;\n}\n\ninterface OfficeSubmissionSummary {\n  completedOffices: string[];\n  pendingOffices: string[];\n  totalTargetOffices: number;\n  completedCount: number;\n  pendingCount: number;\n}\n\nconst SubmissionsSummaryCards: React.FC<SubmissionsSummaryCardsProps> = ({\n  submissions,\n  loading,\n  onRefresh,\n  filters\n}) => {\n  const [summary, setSummary] = useState<OfficeSubmissionSummary | null>(null);\n  const [summaryLoading, setSummaryLoading] = useState(false);\n  const [expandedCard, setExpandedCard] = useState<'completed' | 'pending' | null>(null);\n\n  useEffect(() => {\n    calculateOfficeSummary();\n  }, [submissions, filters]);\n\n  const calculateOfficeSummary = async () => {\n    if (!filters.formIdentifier) {\n      // If no specific form is selected, show basic submission count\n      const uniqueOffices = getUniqueOfficesFromSubmissions(submissions);\n      setSummary({\n        completedOffices: uniqueOffices,\n        pendingOffices: [],\n        totalTargetOffices: uniqueOffices.length,\n        completedCount: uniqueOffices.length,\n        pendingCount: 0\n      });\n      return;\n    }\n\n    setSummaryLoading(true);\n    try {\n      // Get completed offices from current submissions\n      const completedOffices = getUniqueOfficesFromSubmissions(submissions);\n      \n      // Get target offices from page_configurations\n      const targetOffices = await getTargetOfficesForForm(filters.formIdentifier);\n      \n      // Calculate pending offices\n      const pendingOffices = targetOffices.filter(office => \n        !completedOffices.some(completed => \n          completed.toLowerCase().trim() === office.toLowerCase().trim()\n        )\n      );\n\n      setSummary({\n        completedOffices,\n        pendingOffices,\n        totalTargetOffices: targetOffices.length,\n        completedCount: completedOffices.length,\n        pendingCount: pendingOffices.length\n      });\n\n    } catch (error) {\n      console.error('Error calculating office summary:', error);\n      // Fallback to basic count\n      const uniqueOffices = getUniqueOfficesFromSubmissions(submissions);\n      setSummary({\n        completedOffices: uniqueOffices,\n        pendingOffices: [],\n        totalTargetOffices: uniqueOffices.length,\n        completedCount: uniqueOffices.length,\n        pendingCount: 0\n      });\n    } finally {\n      setSummaryLoading(false);\n    }\n  };\n\n  const getUniqueOfficesFromSubmissions = (submissions: FormSubmissionWithUserData[]): string[] => {\n    const officeSet = new Set<string>();\n    \n    submissions.forEach(submission => {\n      // Look for office name in submission_data\n      if (submission.submission_data) {\n        for (const [key, value] of Object.entries(submission.submission_data)) {\n          if (typeof value === 'string' && (\n            value.includes(' BO') || value.includes(' SO') || value.includes(' RO') ||\n            value.includes(' HO') || value.includes(' DO') || value.includes('Office')\n          )) {\n            officeSet.add(value.trim());\n            break; // Found office name, move to next submission\n          }\n        }\n      }\n      \n      // Fallback to user_office if no office found in submission_data\n      if (submission.user_office && submission.user_office !== 'Unknown Office') {\n        officeSet.add(submission.user_office.trim());\n      }\n    });\n\n    return Array.from(officeSet).filter(office => office.length > 0);\n  };\n\n  const getTargetOfficesForForm = async (formIdentifier: string): Promise<string[]> => {\n    try {\n      console.log('🎯 Fetching target offices for form:', formIdentifier);\n      \n      const { data, error } = await supabase\n        .from('page_configurations')\n        .select('selected_offices')\n        .eq('id', formIdentifier)\n        .single();\n\n      if (error) {\n        console.error('Error fetching target offices:', error);\n        return [];\n      }\n\n      if (!data?.selected_offices) {\n        console.log('No selected_offices found for form:', formIdentifier);\n        return [];\n      }\n\n      // selected_offices should be an array of office names\n      const targetOffices = Array.isArray(data.selected_offices) \n        ? data.selected_offices \n        : [];\n\n      console.log('🎯 Target offices for form:', targetOffices);\n      return targetOffices;\n\n    } catch (error) {\n      console.error('Error fetching target offices:', error);\n      return [];\n    }\n  };\n\n  const handleCardClick = (cardType: 'completed' | 'pending') => {\n    setExpandedCard(expandedCard === cardType ? null : cardType);\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return {\n      date: date.toLocaleDateString(),\n      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n    };\n  };\n\n  if (loading || summaryLoading) {\n    return (\n      <div style={{ padding: '3rem', textAlign: 'center', color: '#666' }}>\n        <div>🔄 Loading submission summary...</div>\n      </div>\n    );\n  }\n\n  if (!summary) {\n    return (\n      <div style={{ padding: '3rem', textAlign: 'center', color: '#666' }}>\n        <div>📊 Unable to calculate summary</div>\n        <button\n          style={{\n            marginTop: '1rem',\n            padding: '0.5rem 1rem',\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px'\n          }}\n          onClick={onRefresh}\n        >\n          🔄 Refresh\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: '1.5rem' }}>\n      {/* Summary Cards */}\n      <div style={{ display: 'flex', gap: '1.5rem', marginBottom: '2rem' }}>\n        {/* Completed Card */}\n        <div\n          style={{\n            flex: 1,\n            background: 'linear-gradient(135deg, #28a745 0%, #20c997 100%)',\n            color: 'white',\n            padding: '2rem',\n            borderRadius: '12px',\n            cursor: 'pointer',\n            transition: 'transform 0.2s, box-shadow 0.2s',\n            boxShadow: expandedCard === 'completed' \n              ? '0 8px 25px rgba(40, 167, 69, 0.3)' \n              : '0 4px 15px rgba(40, 167, 69, 0.2)'\n          }}\n          onClick={() => handleCardClick('completed')}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'translateY(-2px)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'translateY(0)';\n          }}\n        >\n          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n            <div style={{ fontSize: '2.5rem', marginRight: '1rem' }}>✅</div>\n            <div>\n              <h3 style={{ margin: 0, fontSize: '1.5rem' }}>Completed</h3>\n              <p style={{ margin: 0, opacity: 0.9 }}>Offices that have submitted</p>\n            </div>\n          </div>\n          <div style={{ fontSize: '3rem', fontWeight: 'bold', textAlign: 'center' }}>\n            {summary.completedCount}\n          </div>\n          <div style={{ textAlign: 'center', opacity: 0.9, fontSize: '0.9rem' }}>\n            {summary.completedCount === 1 ? 'office submitted' : 'offices submitted'}\n          </div>\n          {expandedCard === 'completed' && (\n            <div style={{ \n              marginTop: '1rem', \n              fontSize: '0.8rem', \n              opacity: 0.8,\n              textAlign: 'center'\n            }}>\n              Click to view details\n            </div>\n          )}\n        </div>\n\n        {/* Pending Card */}\n        <div\n          style={{\n            flex: 1,\n            background: 'linear-gradient(135deg, #ffc107 0%, #fd7e14 100%)',\n            color: 'white',\n            padding: '2rem',\n            borderRadius: '12px',\n            cursor: 'pointer',\n            transition: 'transform 0.2s, box-shadow 0.2s',\n            boxShadow: expandedCard === 'pending' \n              ? '0 8px 25px rgba(255, 193, 7, 0.3)' \n              : '0 4px 15px rgba(255, 193, 7, 0.2)'\n          }}\n          onClick={() => handleCardClick('pending')}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'translateY(-2px)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'translateY(0)';\n          }}\n        >\n          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '1rem' }}>\n            <div style={{ fontSize: '2.5rem', marginRight: '1rem' }}>⏳</div>\n            <div>\n              <h3 style={{ margin: 0, fontSize: '1.5rem' }}>Not Completed</h3>\n              <p style={{ margin: 0, opacity: 0.9 }}>Offices pending submission</p>\n            </div>\n          </div>\n          <div style={{ fontSize: '3rem', fontWeight: 'bold', textAlign: 'center' }}>\n            {summary.pendingCount}\n          </div>\n          <div style={{ textAlign: 'center', opacity: 0.9, fontSize: '0.9rem' }}>\n            {summary.pendingCount === 1 ? 'office pending' : 'offices pending'}\n          </div>\n          {expandedCard === 'pending' && (\n            <div style={{ \n              marginTop: '1rem', \n              fontSize: '0.8rem', \n              opacity: 0.8,\n              textAlign: 'center'\n            }}>\n              Click to view details\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Expanded Details */}\n      {expandedCard && (\n        <div style={{\n          background: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 4px 15px rgba(0,0,0,0.1)',\n          overflow: 'hidden'\n        }}>\n          <div style={{\n            padding: '1.5rem',\n            backgroundColor: expandedCard === 'completed' ? '#28a745' : '#ffc107',\n            color: 'white'\n          }}>\n            <h4 style={{ margin: 0, display: 'flex', alignItems: 'center' }}>\n              {expandedCard === 'completed' ? '✅ Completed Submissions' : '⏳ Pending Submissions'}\n              <button\n                style={{\n                  marginLeft: 'auto',\n                  background: 'rgba(255,255,255,0.2)',\n                  border: 'none',\n                  color: 'white',\n                  padding: '0.5rem',\n                  borderRadius: '4px',\n                  cursor: 'pointer'\n                }}\n                onClick={() => setExpandedCard(null)}\n              >\n                ✕\n              </button>\n            </h4>\n          </div>\n          \n          <div style={{ padding: '1.5rem' }}>\n            {expandedCard === 'completed' ? (\n              <CompletedSubmissionsDetails \n                submissions={submissions}\n                completedOffices={summary.completedOffices}\n                formatDate={formatDate}\n              />\n            ) : (\n              <PendingSubmissionsDetails \n                pendingOffices={summary.pendingOffices}\n                formIdentifier={filters.formIdentifier}\n              />\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Component for showing completed submission details\nconst CompletedSubmissionsDetails: React.FC<{\n  submissions: FormSubmissionWithUserData[];\n  completedOffices: string[];\n  formatDate: (dateString: string) => { date: string; time: string };\n}> = ({ submissions, completedOffices, formatDate }) => {\n\n  const getSubmissionsForOffice = (officeName: string) => {\n    return submissions.filter(submission => {\n      if (submission.submission_data) {\n        for (const [key, value] of Object.entries(submission.submission_data)) {\n          if (typeof value === 'string' && value.trim() === officeName) {\n            return true;\n          }\n        }\n      }\n      return submission.user_office === officeName;\n    });\n  };\n\n  return (\n    <div>\n      <div style={{ marginBottom: '1rem', color: '#666' }}>\n        {completedOffices.length} offices have submitted their reports\n      </div>\n\n      <div style={{ maxHeight: '400px', overflowY: 'auto' }}>\n        {completedOffices.map((office, index) => {\n          const officeSubmissions = getSubmissionsForOffice(office);\n          const latestSubmission = officeSubmissions.sort((a, b) =>\n            new Date(b.submitted_at).getTime() - new Date(a.submitted_at).getTime()\n          )[0];\n\n          return (\n            <div key={index} style={{\n              padding: '1rem',\n              border: '1px solid #e9ecef',\n              borderRadius: '8px',\n              marginBottom: '0.5rem',\n              backgroundColor: '#f8f9fa'\n            }}>\n              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'start' }}>\n                <div style={{ flex: 1 }}>\n                  <h6 style={{ margin: '0 0 0.5rem 0', color: '#28a745', fontWeight: 'bold' }}>\n                    {office}\n                  </h6>\n                  {latestSubmission && (\n                    <div style={{ fontSize: '0.85rem', color: '#666' }}>\n                      <div>\n                        <strong>Latest submission:</strong> {formatDate(latestSubmission.submitted_at).date} at {formatDate(latestSubmission.submitted_at).time}\n                      </div>\n                      <div>\n                        <strong>Employee:</strong> {latestSubmission.user_name || latestSubmission.employee_id || 'Unknown'}\n                      </div>\n                      {officeSubmissions.length > 1 && (\n                        <div style={{ color: '#007bff' }}>\n                          +{officeSubmissions.length - 1} more submission{officeSubmissions.length > 2 ? 's' : ''}\n                        </div>\n                      )}\n                    </div>\n                  )}\n                </div>\n                <div style={{\n                  backgroundColor: '#28a745',\n                  color: 'white',\n                  padding: '0.25rem 0.5rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: 'bold'\n                }}>\n                  ✓ COMPLETED\n                </div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n    </div>\n  );\n};\n\n// Component for showing pending submission details\nconst PendingSubmissionsDetails: React.FC<{\n  pendingOffices: string[];\n  formIdentifier?: string;\n}> = ({ pendingOffices, formIdentifier }) => {\n\n  if (pendingOffices.length === 0) {\n    return (\n      <div style={{ textAlign: 'center', color: '#28a745', padding: '2rem' }}>\n        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>🎉</div>\n        <h5>All offices have submitted!</h5>\n        <p>Every target office has completed their submission for this form.</p>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div style={{ marginBottom: '1rem', color: '#666' }}>\n        {pendingOffices.length} offices haven't submitted yet\n        {formIdentifier && (\n          <div style={{ fontSize: '0.85rem', marginTop: '0.25rem' }}>\n            Form: <strong>{formIdentifier}</strong>\n          </div>\n        )}\n      </div>\n\n      <div style={{ maxHeight: '400px', overflowY: 'auto' }}>\n        {pendingOffices.map((office, index) => (\n          <div key={index} style={{\n            padding: '1rem',\n            border: '1px solid #e9ecef',\n            borderRadius: '8px',\n            marginBottom: '0.5rem',\n            backgroundColor: '#fff3cd'\n          }}>\n            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n              <div>\n                <h6 style={{ margin: '0 0 0.25rem 0', color: '#856404', fontWeight: 'bold' }}>\n                  {office}\n                </h6>\n                <div style={{ fontSize: '0.85rem', color: '#666' }}>\n                  Awaiting submission\n                </div>\n              </div>\n              <div style={{\n                backgroundColor: '#ffc107',\n                color: '#212529',\n                padding: '0.25rem 0.5rem',\n                borderRadius: '12px',\n                fontSize: '0.75rem',\n                fontWeight: 'bold'\n              }}>\n                ⏳ PENDING\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default SubmissionsSummaryCards;\n", "import React, { useState, useEffect } from 'react';\nimport { FormSubmissionWithUserData } from '../../services/reportsService';\nimport FormConfigService from '../../services/formConfigService';\n\ninterface DynamicReportsTableProps {\n  submissions: FormSubmissionWithUserData[];\n  loading: boolean;\n  onRefresh: () => void;\n}\n\ninterface TableColumn {\n  key: string;\n  label: string;\n  type: 'form_type' | 'date' | 'field';\n}\n\nconst DynamicReportsTable: React.FC<DynamicReportsTableProps> = ({\n  submissions,\n  loading,\n  onRefresh\n}) => {\n  const [columns, setColumns] = useState<TableColumn[]>([]);\n  const [processedData, setProcessedData] = useState<Array<Record<string, any>>>([]);\n  const [loadingColumns, setLoadingColumns] = useState(true);\n\n  useEffect(() => {\n    buildDynamicColumns();\n  }, [submissions]);\n\n  const buildDynamicColumns = async () => {\n    if (submissions.length === 0) {\n      setColumns([]);\n      setProcessedData([]);\n      setLoadingColumns(false);\n      return;\n    }\n\n    setLoadingColumns(true);\n    console.log('🏗️ Building dynamic columns for submissions:', submissions.length);\n\n    try {\n      // Get all unique form identifiers\n      const formIdentifiers = Array.from(new Set(submissions.map(s => s.form_identifier)));\n      console.log('📋 Unique form identifiers:', formIdentifiers);\n\n      // Get all field mappings for all forms\n      const allFieldMappings = new Map<string, Map<string, string>>();\n      for (const formId of formIdentifiers) {\n        const mapping = await FormConfigService.getFieldMapping(formId);\n        allFieldMappings.set(formId, mapping);\n      }\n\n      // Collect all unique field labels across all forms\n      const allFieldLabels = new Set<string>();\n      allFieldMappings.forEach(mapping => {\n        mapping.forEach(label => allFieldLabels.add(label));\n      });\n\n      console.log('🏷️ All field labels found:', Array.from(allFieldLabels));\n\n      // Build column structure\n      const newColumns: TableColumn[] = [\n        { key: 'form_type', label: 'Form Type', type: 'form_type' },\n        { key: 'submitted_at', label: 'Submitted', type: 'date' }\n      ];\n\n      // Add dynamic field columns\n      Array.from(allFieldLabels).forEach(label => {\n        newColumns.push({\n          key: label,\n          label: label,\n          type: 'field' as const\n        });\n      });\n\n      setColumns(newColumns);\n\n      // Process submission data\n      const processedSubmissions = await Promise.all(\n        submissions.map(async (submission) => {\n          const formMapping = allFieldMappings.get(submission.form_identifier) || new Map();\n          const convertedData = await FormConfigService.convertSubmissionData(\n            submission.form_identifier,\n            submission.submission_data\n          );\n\n          // Debug logging to see what employee_id we're getting\n          console.log(`🔍 Submission ${submission.id}:`, {\n            employee_id: submission.employee_id,\n            user_id: submission.user_id,\n            form_identifier: submission.form_identifier,\n            submission_data_keys: Object.keys(submission.submission_data)\n          });\n\n          const row: Record<string, any> = {\n            id: submission.id,\n            form_type: getFormTypeDisplay(submission.form_identifier),\n            submitted_at: formatDate(submission.submitted_at)\n          };\n\n          // Add field values using converted labels\n          Object.entries(convertedData).forEach(([label, value]) => {\n            // Skip the fields we're already handling separately\n            if (label !== 'officeName' && !isUserNameField(label)) {\n              row[label] = formatFieldValue(value);\n            }\n          });\n\n          return row;\n        })\n      );\n\n      setProcessedData(processedSubmissions);\n      console.log('✅ Dynamic table built successfully');\n\n    } catch (error) {\n      console.error('❌ Error building dynamic columns:', error);\n    } finally {\n      setLoadingColumns(false);\n    }\n  };\n\n  const getFormTypeDisplay = (formIdentifier: string): string => {\n    const formTypes: Record<string, string> = {\n      'employee-registration': 'Employee Registration',\n      'leave-request': 'Leave Request',\n      'expense-report': 'Expense Report',\n      'performance-review': 'Performance Review',\n      'it-support-request': 'IT Support Request',\n      'training-registration': 'Training Registration',\n      'feedback-form': 'Feedback Form',\n      'inventory-request': 'Inventory Request',\n      'test': 'Test Form'\n    };\n    return formTypes[formIdentifier] || formIdentifier.replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  };\n\n  const getUserDisplayName = (submission: FormSubmissionWithUserData): string => {\n    // Try to extract name from submission data first\n    const data = submission.submission_data;\n    const entries = Object.entries(data);\n    \n    for (const [key, value] of entries) {\n      if (typeof value === 'string' && value.length > 2 && value.length < 50) {\n        if (value.includes('T') && value.includes(':')) continue;\n        if (!isNaN(Number(value))) continue;\n        if (key === 'officeName') continue;\n        if (value.includes(' BO') || value.includes(' SO') || value.includes(' RO') || \n            value.includes(' HO') || value.includes(' DO') || value.includes('Office')) continue;\n        \n        return value;\n      }\n    }\n    \n    const formType = getFormTypeDisplay(submission.form_identifier);\n    return submission.user_id ? `${formType} User ${submission.user_id.substring(0, 8)}` : `${formType} Submitter`;\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return {\n      date: date.toLocaleDateString(),\n      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n    };\n  };\n\n  const formatFieldValue = (value: any): string => {\n    if (value === null || value === undefined) return '';\n\n    if (typeof value === 'string' && value.includes('T') && value.includes(':')) {\n      try {\n        const date = new Date(value);\n        return date.toLocaleDateString();\n      } catch (e) {\n        return value;\n      }\n    }\n\n    return String(value);\n  };\n\n\n\n  const extractActualOfficeName = (convertedData: Record<string, any>): string => {\n    // Common field names that contain office names\n    const officeNameFields = [\n      'Office Name', 'officeName', 'Office', 'office', 'Branch', 'branch',\n      'Location', 'location', 'Workplace', 'workplace', 'Department', 'department',\n      'Division', 'division', 'Unit', 'unit'\n    ];\n\n    // Try to find an office name field\n    for (const field of officeNameFields) {\n      if (convertedData[field] && typeof convertedData[field] === 'string') {\n        const value = convertedData[field].trim();\n        if (value.length > 0) {\n          return value;\n        }\n      }\n    }\n\n    // Look for values that look like office names (contain BO, SO, RO, etc.)\n    for (const [key, value] of Object.entries(convertedData)) {\n      if (typeof value === 'string' &&\n          (value.includes(' BO') || value.includes(' SO') || value.includes(' RO') ||\n           value.includes(' HO') || value.includes(' DO') || value.includes('Office'))) {\n        return value;\n      }\n    }\n\n    return 'Unknown Office';\n  };\n\n  const isUserNameField = (fieldName: string): boolean => {\n    const userNameFields = [\n      'Employee Name', 'employeeName', 'Full Name', 'fullName', 'Name', 'name',\n      'First Name', 'firstName', 'Last Name', 'lastName', 'User Name', 'userName',\n      'Participant Name', 'participantName', 'Requested By', 'requestedBy',\n      'Submitted By', 'submittedBy', 'Applicant Name', 'applicantName'\n    ];\n    return userNameFields.includes(fieldName);\n  };\n\n  const getEmployeeIdFromSubmission = (submission: any): string => {\n    // 1. First try the dedicated employee_id column\n    if (submission.employee_id && typeof submission.employee_id === 'string' && submission.employee_id.trim()) {\n      console.log(`✅ Found employee_id in column: \"${submission.employee_id}\"`);\n      return submission.employee_id.trim();\n    }\n\n    // 2. Try to extract from submission_data VALUES (not field IDs)\n    const data = submission.submission_data || {};\n    console.log(`🔍 Searching in submission_data VALUES:`, data);\n    console.log(`🔍 All field values:`, Object.values(data));\n\n    // Look through all field VALUES to find employee IDs\n    for (const [fieldId, fieldValue] of Object.entries(data)) {\n      if (typeof fieldValue === 'string' && fieldValue.trim().length > 0) {\n        const value = fieldValue.trim();\n\n        // Skip dates\n        if (value.includes('T') && value.includes(':')) continue;\n\n        // Skip office names\n        if (value.includes(' BO') || value.includes(' SO') || value.includes(' RO') ||\n            value.includes(' HO') || value.includes(' DO')) continue;\n\n        // Skip very long values (likely not employee IDs)\n        if (value.length > 50) continue;\n\n        // Check if it looks like an employee ID pattern\n        if (/^(EMP|STAFF|USER|ID)[0-9]{1,6}$/i.test(value)) {\n          console.log(`✅ Found employee ID pattern in field \"${fieldId}\": \"${value}\"`);\n          return value.toUpperCase();\n        }\n\n        // Check if it's a short alphanumeric code (likely an employee ID)\n        if (/^[A-Z0-9]{3,15}$/i.test(value) && !value.includes(' ')) {\n          console.log(`✅ Found potential employee ID in field \"${fieldId}\": \"${value}\"`);\n          return value.toUpperCase();\n        }\n      }\n    }\n\n    // 3. Try to extract a name and create an ID from it\n    for (const [fieldId, fieldValue] of Object.entries(data)) {\n      if (typeof fieldValue === 'string' && fieldValue.trim().length > 0) {\n        const value = fieldValue.trim();\n\n        // Skip dates and office names\n        if (value.includes('T') && value.includes(':')) continue;\n        if (value.includes(' BO') || value.includes(' SO') || value.includes(' RO')) continue;\n\n        // If it looks like a person's name (2-4 words, reasonable length)\n        const words = value.split(/\\s+/);\n        if (words.length >= 2 && words.length <= 4 && value.length >= 5 && value.length <= 50) {\n          // Check if all words are likely name parts (alphabetic)\n          const isLikelyName = words.every(word => /^[A-Za-z]+$/.test(word));\n          if (isLikelyName) {\n            // Create ID from first name + last name initial\n            const firstName = words[0].toUpperCase();\n            const lastInitial = words[words.length - 1].charAt(0).toUpperCase();\n            const nameId = `${firstName}${lastInitial}`;\n            console.log(`✅ Generated employee ID from name \"${value}\": \"${nameId}\"`);\n            return nameId;\n          }\n        }\n      }\n    }\n\n    // 4. Final fallback: Use any reasonable text value as employee ID\n    console.log(`⚠️ No specific employee ID found, trying fallback approach...`);\n    for (const [fieldId, fieldValue] of Object.entries(data)) {\n      if (typeof fieldValue === 'string' && fieldValue.trim().length > 0) {\n        const value = fieldValue.trim();\n\n        // Skip obvious non-employee data\n        if (value.includes('T') && value.includes(':')) continue; // dates\n        if (value.includes('@')) continue; // emails\n        if (value.length > 100) continue; // very long text\n        if (value.includes(' BO') || value.includes(' SO') || value.includes(' RO')) continue; // office names\n\n        // Use the first reasonable text value we find\n        if (value.length >= 2 && value.length <= 50) {\n          // If it's a name, create an ID from it\n          const words = value.split(/\\s+/);\n          if (words.length >= 2) {\n            const nameId = words.map(word => word.charAt(0).toUpperCase()).join('') +\n                          Math.random().toString(36).substring(2, 5).toUpperCase();\n            console.log(`✅ Created fallback ID from \"${value}\": \"${nameId}\"`);\n            return nameId;\n          } else {\n            // Single word, use as-is with some modification\n            const singleId = value.toUpperCase().substring(0, 8) +\n                           Math.random().toString(36).substring(2, 4).toUpperCase();\n            console.log(`✅ Created fallback ID from single word \"${value}\": \"${singleId}\"`);\n            return singleId;\n          }\n        }\n      }\n    }\n\n    // 5. Absolute final fallback\n    const fallbackId = `USER${Date.now().toString().slice(-6)}`;\n    console.log(`❌ No usable data found, using absolute fallback: \"${fallbackId}\"`);\n    return fallbackId;\n  };\n\n  const extractOfficeNameFromRawData = (rawData: Record<string, any>): string => {\n    // Look for office name patterns\n    for (const [key, value] of Object.entries(rawData)) {\n      if (typeof value === 'string') {\n        // Check if it looks like an office name\n        if (value.includes(' BO') || value.includes(' SO') || value.includes(' RO') ||\n            value.includes(' HO') || value.includes(' DO') || value.includes('Office')) {\n          return value;\n        }\n      }\n    }\n\n    return 'Unknown Office';\n  };\n\n\n\n  if (loadingColumns) {\n    return (\n      <div style={{ padding: '3rem', textAlign: 'center', color: '#666' }}>\n        <div>🔄 Building dynamic table structure...</div>\n      </div>\n    );\n  }\n\n  if (loading) {\n    return (\n      <div style={{ padding: '3rem', textAlign: 'center', color: '#666' }}>\n        <div>🔄 Loading submissions...</div>\n      </div>\n    );\n  }\n\n  if (submissions.length === 0) {\n    return (\n      <div style={{ padding: '3rem', textAlign: 'center', color: '#666' }}>\n        <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📭</div>\n        <h5>No Submissions Found</h5>\n        <p>No form submissions match your current filters.</p>\n        <button\n          style={{\n            padding: '0.5rem 1rem',\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px'\n          }}\n          onClick={onRefresh}\n        >\n          🔄 Refresh\n        </button>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ overflowX: 'auto' }}>\n      <table style={{ width: '100%', borderCollapse: 'collapse', minWidth: '800px' }}>\n        <thead>\n          <tr style={{ backgroundColor: '#f8f9fa' }}>\n            {columns.map((column) => (\n              <th\n                key={column.key}\n                style={{\n                  padding: '1rem 0.75rem',\n                  textAlign: 'left',\n                  fontWeight: '600',\n                  borderBottom: '2px solid #dee2e6',\n                  whiteSpace: 'nowrap',\n                  minWidth: column.type === 'field' ? '120px' : 'auto'\n                }}\n              >\n                {column.label}\n              </th>\n            ))}\n          </tr>\n        </thead>\n        <tbody>\n          {processedData.map((row, index) => (\n            <tr key={`${row.id}-${index}`} style={{ borderBottom: '1px solid #eee' }}>\n              {columns.map((column) => (\n                <td\n                  key={column.key}\n                  style={{\n                    padding: '0.75rem',\n                    verticalAlign: 'top',\n                    maxWidth: '200px',\n                    overflow: 'hidden',\n                    textOverflow: 'ellipsis',\n                    whiteSpace: 'nowrap'\n                  }}\n                >\n                  {column.type === 'form_type' && (\n                    <span style={{\n                      backgroundColor: '#e3f2fd',\n                      color: '#1976d2',\n                      padding: '0.25rem 0.5rem',\n                      borderRadius: '4px',\n                      fontSize: '0.75rem',\n                      fontWeight: '500'\n                    }}>\n                      {row[column.key]}\n                    </span>\n                  )}\n                  {column.type === 'date' && (\n                    <div style={{ fontSize: '0.8rem', color: '#666' }}>\n                      <div>{row[column.key]?.date}</div>\n                      <small>{row[column.key]?.time}</small>\n                    </div>\n                  )}\n                  {column.type === 'field' && (\n                    <span title={row[column.key] || ''}>\n                      {row[column.key] || '-'}\n                    </span>\n                  )}\n                </td>\n              ))}\n            </tr>\n          ))}\n        </tbody>\n      </table>\n\n      {/* Table Footer */}\n      <div style={{\n        padding: '1rem 1.5rem',\n        backgroundColor: '#f8f9fa',\n        borderTop: '1px solid #dee2e6',\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center'\n      }}>\n        <div style={{ fontSize: '0.875rem', color: '#666' }}>\n          Showing {submissions.length} submissions across {columns.length - 2} field types\n        </div>\n        <button\n          style={{\n            padding: '0.375rem 0.75rem',\n            backgroundColor: '#007bff',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px'\n          }}\n          onClick={onRefresh}\n        >\n          🔄 Refresh\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default DynamicReportsTable;\n", "import React, { useEffect, useState, useCallback } from 'react';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Sidebar from '../shared/Sidebar';\nimport ReportsService, { ReportsFilter, FormSubmissionWithUserData } from '../../services/reportsService';\nimport FormConfigService from '../../services/formConfigService';\nimport SubmissionsSummaryCards from './SubmissionsSummaryCards';\nimport DynamicReportsTable from './DynamicReportsTable';\nimport OfficeService from '../../services/officeService';\nimport '../dashboard/Dashboard.css';\n\n// Add CSS for loading spinner animation\nconst spinnerStyle = `\n  @keyframes spin {\n    0% { transform: rotate(0deg); }\n    100% { transform: rotate(360deg); }\n  }\n`;\n\n// Inject the CSS\nif (typeof document !== 'undefined') {\n  const style = document.createElement('style');\n  style.textContent = spinnerStyle;\n  document.head.appendChild(style);\n}\n\nconst Reports: React.FC = () => {\n  const { currentUser } = useAuth();\n  const [userData, setUserData] = useState<any>(null);\n  const [submissions, setSubmissions] = useState<FormSubmissionWithUserData[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [viewMode, setViewMode] = useState<'table' | 'card'>(() => {\n    // Get saved preference from session storage, default to table view for backward compatibility\n    const savedViewMode = sessionStorage.getItem('reports-view-mode');\n    return (savedViewMode === 'card' || savedViewMode === 'table') ? savedViewMode : 'table';\n  });\n  const [filters, setFilters] = useState<ReportsFilter>({\n    limit: 50,\n    offset: 0\n  });\n  const [summary, setSummary] = useState<any>(null);\n  const [formIdentifiers, setFormIdentifiers] = useState<string[]>([]);\n\n  // Office dropdown states\n  const [officeOptions, setOfficeOptions] = useState<Array<{label: string, value: string}>>([]);\n  const [officeLoading, setOfficeLoading] = useState(false);\n  const [officeError, setOfficeError] = useState<string>('');\n\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        const userRef = doc(db, 'employees', currentUser.uid);\n        const userSnap = await getDoc(userRef);\n        if (userSnap.exists()) {\n          setUserData(userSnap.data());\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  useEffect(() => {\n    fetchInitialData();\n    fetchOfficeNames();\n  }, []);\n\n  useEffect(() => {\n    fetchSubmissions();\n  }, [filters]);\n\n  // Update document title based on view mode\n  useEffect(() => {\n    const baseTitle = 'Reports - Employee Management System';\n    const viewTitle = viewMode === 'table' ? 'Table View' : 'Card View';\n    document.title = `${baseTitle} - ${viewTitle}`;\n\n    // Cleanup on unmount\n    return () => {\n      document.title = baseTitle;\n    };\n  }, [viewMode]);\n\n  const fetchInitialData = async () => {\n    try {\n      const [summaryData, identifiers] = await Promise.all([\n        ReportsService.getReportsSummary(),\n        ReportsService.getFormIdentifiers()\n      ]);\n      setSummary(summaryData);\n      setFormIdentifiers(identifiers);\n    } catch (err) {\n      console.error('Error fetching initial data:', err);\n    }\n  };\n\n  // Fetch office names for dropdown with hierarchical filtering\n  const fetchOfficeNames = useCallback(async () => {\n    if (officeOptions.length > 0 && !officeError) {\n      return; // Already loaded successfully\n    }\n\n    setOfficeLoading(true);\n    setOfficeError('');\n\n    try {\n      console.log('🏢 Reports: Fetching office names for filter dropdown...');\n\n      // Use user-specific filtering for Office Name dropdowns\n      const officeNames = await OfficeService.fetchUserSpecificOfficeNames();\n      const options = OfficeService.officeNamesToOptions(officeNames);\n\n      setOfficeOptions(options);\n      console.log('✅ Reports: Successfully loaded', options.length, 'office options');\n\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to load office names';\n      setOfficeError(errorMessage);\n      console.error('❌ Reports: Error fetching office names:', error);\n    } finally {\n      setOfficeLoading(false);\n    }\n  }, [officeOptions.length, officeError]);\n\n  const fetchSubmissions = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const data = await ReportsService.getFormSubmissions(filters);\n      // console.log('Raw submissions data:', data);\n      setSubmissions(data);\n\n    } catch (err) {\n      console.error('Error fetching submissions:', err);\n      setError(err instanceof Error ? err.message : 'Failed to fetch submissions');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFiltersChange = (newFilters: ReportsFilter) => {\n    console.log('🔍 Reports: Applying new filters:', newFilters);\n    setFilters({ ...newFilters, offset: 0 }); // Reset pagination when filters change\n  };\n\n  const handleViewModeChange = (newViewMode: 'table' | 'card') => {\n    setViewMode(newViewMode);\n    // Save preference to session storage\n    sessionStorage.setItem('reports-view-mode', newViewMode);\n    console.log('📊 Reports: View mode changed to:', newViewMode);\n  };\n\n  const handleExport = async () => {\n    try {\n      const csvContent = await ReportsService.exportToCSV(filters);\n\n      // Create and download CSV file\n      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\n      const link = document.createElement('a');\n      const url = URL.createObjectURL(blob);\n      link.setAttribute('href', url);\n      link.setAttribute('download', `form_submissions_${new Date().toISOString().split('T')[0]}.csv`);\n      link.style.visibility = 'hidden';\n      document.body.appendChild(link);\n      link.click();\n      document.body.removeChild(link);\n\n    } catch (err) {\n      console.error('Error exporting data:', err);\n      alert('Failed to export data. Please try again.');\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return {\n      date: date.toLocaleDateString(),\n      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })\n    };\n  };\n\n  const formatReadableData = (data: Record<string, any>) => {\n    // console.log('formatReadableData called with:', data);\n\n    // Since we're dealing with dynamic form fields with generated IDs,\n    // let's create a more intelligent display\n    const displayFields: string[] = [];\n    const entries = Object.entries(data);\n\n    // Filter out empty values and format nicely\n    entries.forEach(([key, value]) => {\n      if (value !== null && value !== undefined && value !== '') {\n        // Skip office name as it's shown separately\n        if (key === 'officeName') return;\n\n        // Format the value based on type and content\n        let formattedValue = value;\n        let fieldDescription = 'Data';\n\n        if (typeof value === 'string' && value.includes('T') && value.includes(':')) {\n          // Looks like a date\n          try {\n            const date = new Date(value);\n            formattedValue = date.toLocaleDateString();\n            fieldDescription = 'Date';\n          } catch (e) {\n            // Keep original value if date parsing fails\n          }\n        } else if (typeof value === 'string' && (value.includes(' BO') || value.includes(' SO') || value.includes(' RO'))) {\n          // This is an office name\n          fieldDescription = 'Office';\n          formattedValue = value;\n        } else if (typeof value === 'string' && value.length > 10 && !value.includes(' ')) {\n          // Long string without spaces might be an ID\n          fieldDescription = 'ID';\n          formattedValue = value.length > 15 ? `${value.substring(0, 15)}...` : value;\n        } else if (typeof value === 'number' || !isNaN(Number(value))) {\n          // Numeric value\n          fieldDescription = 'Value';\n          formattedValue = value;\n        } else if (typeof value === 'string' && value.length < 50) {\n          // Short text might be a name or description\n          fieldDescription = 'Text';\n          formattedValue = value;\n        }\n\n        displayFields.push(`${fieldDescription}: ${formattedValue}`);\n      }\n    });\n\n    // Limit to first 3 fields to avoid clutter\n    const limitedFields = displayFields.slice(0, 3);\n    const result = limitedFields.length > 0\n      ? limitedFields.join(', ') + (displayFields.length > 3 ? '...' : '')\n      : 'Form submission data';\n\n    // console.log('Final result:', result);\n    return result;\n  };\n\n  const getFormTypeDisplay = (formIdentifier: string) => {\n    const formTypes: Record<string, string> = {\n      'employee-registration': 'Employee Registration',\n      'leave-request': 'Leave Request',\n      'expense-report': 'Expense Report',\n      'performance-review': 'Performance Review',\n      'it-support-request': 'IT Support Request',\n      'training-registration': 'Training Registration',\n      'feedback-form': 'Feedback Form',\n      'inventory-request': 'Inventory Request'\n    };\n    return formTypes[formIdentifier] || formIdentifier.replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n  };\n\n  const getUserDisplayName = (submission: FormSubmissionWithUserData) => {\n    // console.log('getUserDisplayName called with submission:', submission);\n\n    // Try to get name from submission data\n    const data = submission.submission_data;\n    // console.log('Submission data:', data);\n\n    // Since we're dealing with dynamic fields, look for text values that might be names\n    const entries = Object.entries(data);\n\n    // Look for string values that might be names (not dates, not numbers, not office names)\n    for (const [key, value] of entries) {\n      if (typeof value === 'string' && value.length > 2 && value.length < 50) {\n        // Skip if it looks like a date\n        if (value.includes('T') && value.includes(':')) continue;\n        // Skip if it's just a number\n        if (!isNaN(Number(value))) continue;\n        // Skip office name field\n        if (key === 'officeName') continue;\n        // Skip if it looks like an office name (contains BO, SO, RO, etc.)\n        if (value.includes(' BO') || value.includes(' SO') || value.includes(' RO') ||\n            value.includes(' HO') || value.includes(' DO') || value.includes('Office')) continue;\n\n        // This might be a name - use it\n        // console.log('Found potential name:', value);\n        return value;\n      }\n    }\n\n    // Look for the form identifier to create a more meaningful name\n    const formType = getFormTypeDisplay(submission.form_identifier);\n\n    // Fallback to user_id or form-based name\n    if (submission.user_id) {\n      const fallback = `${formType} User ${submission.user_id.substring(0, 8)}`;\n      // console.log('Using form-based fallback:', fallback);\n      return fallback;\n    }\n\n    const fallback = `${formType} Submitter`;\n    // console.log('Using generic fallback:', fallback);\n    return fallback;\n  };\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n\n      {/* Main Content */}\n      <div className=\"main-content\">\n        <div className=\"page-title\">\n          Reports\n          <button\n            className=\"btn btn-primary ms-3\"\n            onClick={handleExport}\n            disabled={loading || submissions.length === 0}\n            style={{ marginLeft: '1rem', padding: '0.5rem 1rem', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '4px' }}\n          >\n            📥 Export CSV\n          </button>\n          <button\n            onClick={async () => {\n              if (submissions.length > 0) {\n                const firstSubmission = submissions[0];\n                console.log('🔍 Testing with first submission:', firstSubmission);\n\n                try {\n                  const convertedData = await FormConfigService.convertSubmissionData(\n                    firstSubmission.form_identifier,\n                    firstSubmission.submission_data\n                  );\n\n                  alert(`Form: ${firstSubmission.form_identifier}\\n\\nRaw Data Keys: ${Object.keys(firstSubmission.submission_data).join(', ')}\\n\\nConverted Data Keys: ${Object.keys(convertedData).join(', ')}\\n\\nConverted Data: ${JSON.stringify(convertedData, null, 2)}`);\n                } catch (error) {\n                  alert(`Error: ${error}`);\n                }\n              } else {\n                alert('No submissions available to test');\n              }\n            }}\n            style={{ marginLeft: '0.5rem', padding: '0.5rem 1rem', backgroundColor: '#28a745', color: 'white', border: 'none', borderRadius: '4px' }}\n          >\n            🔍 Debug Data\n          </button>\n          <button\n            onClick={() => {\n              if (submissions.length > 0) {\n                const officeData = submissions.map(sub => ({\n                  id: sub.id,\n                  user_office: sub.user_office,\n                  submission_data_office: sub.submission_data?.officeName,\n                  all_submission_fields: Object.keys(sub.submission_data || {}),\n                  office_fields: Object.entries(sub.submission_data || {}).filter(([key, value]) =>\n                    typeof value === 'string' && (\n                      value.includes(' RO') || value.includes(' BO') || value.includes(' SO') ||\n                      value.includes(' HO') || value.includes(' DO') || value.includes('Office')\n                    )\n                  )\n                }));\n                console.log('🏢 Office Debug Data:', officeData);\n                alert(`Office Debug:\\n\\n${JSON.stringify(officeData.slice(0, 3), null, 2)}`);\n              } else {\n                alert('No submissions to debug');\n              }\n            }}\n            style={{ marginLeft: '0.5rem', padding: '0.5rem 1rem', backgroundColor: '#17a2b8', color: 'white', border: 'none', borderRadius: '4px' }}\n          >\n            🏢 Debug Offices\n          </button>\n        </div>\n\n        {/* Reports Summary */}\n        {summary && (\n          <div style={{ marginBottom: '2rem' }}>\n            <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n              {[\n                { icon: '📄', value: summary.totalSubmissions, label: 'Total Submissions', color: '#007bff' },\n                { icon: '📋', value: summary.uniqueForms, label: 'Unique Forms', color: '#28a745' },\n                { icon: '👥', value: summary.uniqueUsers, label: 'Active Users', color: '#17a2b8' },\n                { icon: '📅', value: summary.submissionsToday, label: 'Today', color: '#ffc107' },\n                { icon: '📆', value: summary.submissionsThisWeek, label: 'This Week', color: '#fd7e14' },\n                { icon: '🗓️', value: summary.submissionsThisMonth, label: 'This Month', color: '#6f42c1' }\n              ].map((card, index) => (\n                <div key={index} style={{\n                  background: 'white',\n                  padding: '1.5rem',\n                  borderRadius: '8px',\n                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n                  textAlign: 'center',\n                  minWidth: '150px',\n                  flex: '1'\n                }}>\n                  <div style={{ fontSize: '2rem', marginBottom: '0.5rem' }}>{card.icon}</div>\n                  <div style={{ fontSize: '2rem', fontWeight: 'bold', color: card.color, marginBottom: '0.25rem' }}>\n                    {card.value.toLocaleString()}\n                  </div>\n                  <div style={{ fontSize: '0.875rem', color: '#666', textTransform: 'uppercase', letterSpacing: '0.5px' }}>\n                    {card.label}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Filters */}\n        <div style={{\n          background: 'white',\n          padding: '1.5rem',\n          borderRadius: '8px',\n          marginBottom: '2rem',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n        }}>\n          <h5 style={{ marginBottom: '1rem' }}>🔍 Filters</h5>\n          <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n            <div style={{ flex: '1', minWidth: '200px' }}>\n              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>Form Type</label>\n              <select\n                style={{ width: '100%', padding: '0.5rem', border: '1px solid #ddd', borderRadius: '4px' }}\n                value={filters.formIdentifier || ''}\n                onChange={(e) => handleFiltersChange({ ...filters, formIdentifier: e.target.value || undefined })}\n              >\n                <option value=\"\">All Forms</option>\n                {formIdentifiers.map(identifier => (\n                  <option key={identifier} value={identifier}>\n                    {identifier}\n                  </option>\n                ))}\n              </select>\n            </div>\n            <div style={{ flex: '1', minWidth: '200px' }}>\n              <label style={{ display: 'block', marginBottom: '0.5rem', fontWeight: '600' }}>Office Name</label>\n              <div style={{ position: 'relative' }}>\n                <select\n                  style={{\n                    width: '100%',\n                    padding: '0.5rem',\n                    border: '1px solid #ddd',\n                    borderRadius: '4px',\n                    backgroundColor: officeLoading ? '#f8f9fa' : 'white'\n                  }}\n                  value={filters.officeName || ''}\n                  onChange={(e) => handleFiltersChange({ ...filters, officeName: e.target.value || undefined })}\n                  disabled={officeLoading}\n                >\n                  <option value=\"\">\n                    {officeLoading ? 'Loading offices...' : 'All Offices'}\n                  </option>\n                  {officeOptions.map(option => (\n                    <option key={option.value} value={option.value}>\n                      {option.label}\n                    </option>\n                  ))}\n                </select>\n\n                {/* Loading indicator */}\n                {officeLoading && (\n                  <div style={{\n                    position: 'absolute',\n                    right: '8px',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    width: '16px',\n                    height: '16px'\n                  }}>\n                    <div style={{\n                      width: '16px',\n                      height: '16px',\n                      border: '2px solid #f3f3f3',\n                      borderTop: '2px solid #007bff',\n                      borderRadius: '50%',\n                      animation: 'spin 1s linear infinite'\n                    }}></div>\n                  </div>\n                )}\n              </div>\n\n              {/* Error message with retry button */}\n              {officeError && (\n                <div style={{ marginTop: '0.5rem', fontSize: '0.875rem', color: '#dc3545' }}>\n                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n                    <span>⚠️ {officeError}</span>\n                    <button\n                      type=\"button\"\n                      style={{\n                        padding: '0.25rem 0.5rem',\n                        fontSize: '0.75rem',\n                        backgroundColor: '#dc3545',\n                        color: 'white',\n                        border: 'none',\n                        borderRadius: '4px',\n                        cursor: 'pointer'\n                      }}\n                      onClick={fetchOfficeNames}\n                      disabled={officeLoading}\n                    >\n                      Retry\n                    </button>\n                  </div>\n                </div>\n              )}\n\n              {/* Success indicator */}\n              {!officeLoading && !officeError && officeOptions.length > 0 && (\n                <div style={{ marginTop: '0.5rem', fontSize: '0.875rem', color: '#28a745' }}>\n                  ✅ {officeOptions.length} offices loaded\n                </div>\n              )}\n            </div>\n            <div style={{ display: 'flex', alignItems: 'end', gap: '0.5rem' }}>\n              <button\n                style={{ padding: '0.5rem 1rem', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '4px' }}\n                onClick={() => fetchSubmissions()}\n              >\n                🔍 Apply\n              </button>\n              <button\n                style={{ padding: '0.5rem 1rem', backgroundColor: '#6c757d', color: 'white', border: 'none', borderRadius: '4px' }}\n                onClick={() => {\n                  setFilters({ limit: 50, offset: 0 });\n                  handleFiltersChange({ limit: 50, offset: 0 });\n                }}\n              >\n                ✖️ Clear\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* View Toggle Controls */}\n        <div style={{\n          background: 'white',\n          padding: '1rem 1.5rem',\n          borderRadius: '8px',\n          marginBottom: '1rem',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center'\n        }}>\n          <div>\n            <h5 style={{ margin: 0, color: '#333' }}>📊 Submission Data</h5>\n            <p style={{ margin: '0.25rem 0 0 0', fontSize: '0.875rem', color: '#666' }}>\n              {viewMode === 'table'\n                ? 'Detailed table view with all submission fields and data'\n                : 'Summary view showing completion status by office'}\n            </p>\n          </div>\n\n          {/* View Mode Toggle */}\n          <div style={{\n            display: 'flex',\n            backgroundColor: '#f8f9fa',\n            borderRadius: '6px',\n            padding: '4px',\n            border: '1px solid #dee2e6'\n          }}>\n            <button\n              style={{\n                padding: '0.5rem 1rem',\n                border: 'none',\n                borderRadius: '4px',\n                backgroundColor: viewMode === 'table' ? '#007bff' : 'transparent',\n                color: viewMode === 'table' ? 'white' : '#6c757d',\n                fontWeight: viewMode === 'table' ? '600' : '400',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '0.875rem'\n              }}\n              onClick={() => handleViewModeChange('table')}\n              onMouseEnter={(e) => {\n                if (viewMode !== 'table') {\n                  e.currentTarget.style.backgroundColor = '#e9ecef';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (viewMode !== 'table') {\n                  e.currentTarget.style.backgroundColor = 'transparent';\n                }\n              }}\n              title=\"View submissions in a detailed table format with sortable columns\"\n            >\n              📋 Table View\n            </button>\n            <button\n              style={{\n                padding: '0.5rem 1rem',\n                border: 'none',\n                borderRadius: '4px',\n                backgroundColor: viewMode === 'card' ? '#007bff' : 'transparent',\n                color: viewMode === 'card' ? 'white' : '#6c757d',\n                fontWeight: viewMode === 'card' ? '600' : '400',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                fontSize: '0.875rem'\n              }}\n              onClick={() => handleViewModeChange('card')}\n              onMouseEnter={(e) => {\n                if (viewMode !== 'card') {\n                  e.currentTarget.style.backgroundColor = '#e9ecef';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (viewMode !== 'card') {\n                  e.currentTarget.style.backgroundColor = 'transparent';\n                }\n              }}\n              title=\"View submissions as summary cards showing completion status\"\n            >\n              📊 Card View\n            </button>\n          </div>\n        </div>\n\n        {/* Dynamic Reports Content */}\n        <div style={{\n          background: 'white',\n          borderRadius: '8px',\n          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',\n          overflow: 'hidden'\n        }}>\n          {error && (\n            <div style={{\n              padding: '1rem',\n              backgroundColor: '#f8d7da',\n              color: '#721c24',\n              borderBottom: '1px solid #f5c6cb'\n            }}>\n              ⚠️ {error}\n              <button\n                style={{\n                  marginLeft: '1rem',\n                  padding: '0.25rem 0.5rem',\n                  backgroundColor: '#dc3545',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '4px'\n                }}\n                onClick={fetchSubmissions}\n              >\n                Retry\n              </button>\n            </div>\n          )}\n\n          {/* Conditional rendering based on view mode */}\n          {viewMode === 'table' ? (\n            <DynamicReportsTable\n              submissions={submissions}\n              loading={loading}\n              onRefresh={fetchSubmissions}\n            />\n          ) : (\n            <SubmissionsSummaryCards\n              submissions={submissions}\n              loading={loading}\n              onRefresh={fetchSubmissions}\n              filters={filters}\n            />\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Reports;"], "names": ["ReportsService", "getFormSubmissions", "filters", "arguments", "length", "undefined", "console", "log", "JSON", "stringify", "workingTable", "testData", "tablesToTry", "tableName", "result", "supabase", "from", "select", "count", "head", "error", "_result$error", "message", "err", "Error", "query", "order", "ascending", "formIdentifier", "eq", "userId", "startDate", "gte", "endDate", "lte", "limit", "offset", "range", "data", "dataLength", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "details", "hint", "code", "warn", "countError", "map", "d", "form_identifier", "enhancedData", "submission", "_submission$submissio", "user_name", "employee_id", "user_email", "user_office", "submission_data", "officeName", "filteredData", "filter", "_submission$submissio2", "submissionDataOffice", "userOffice", "foundOffice", "key", "value", "Object", "entries", "includes", "officeToCheck", "id", "toLowerCase", "stack", "getReportsSummary", "cache<PERSON>ey", "cached", "this", "cache", "get", "isCache<PERSON><PERSON>d", "timestamp", "now", "Date", "today", "getFullYear", "getMonth", "getDate", "toISOString", "weekAgo", "getTime", "monthAgo", "findWorkingDataSource", "totalSubmissions", "totalError", "formsData", "formsError", "uniqueForms", "Set", "item", "size", "usersData", "usersError", "uniqueUsers", "user_id", "submissions<PERSON>oday", "todayError", "submissionsThisWeek", "weekError", "submissions<PERSON><PERSON><PERSON><PERSON><PERSON>", "monthError", "summary", "set", "getFormIdentifiers", "uniqueIdentifiers", "Array", "sort", "enhanceWithUserData", "submissions", "employeeIds", "s", "trim", "userProfiles", "profiles", "in", "profileMap", "Map", "for<PERSON>ach", "profile", "employeeId", "_submission$submissio3", "userProfile", "enhancedSubmission", "full_name", "substring", "email", "office_name", "exportToCSV", "csv<PERSON><PERSON>nt", "submitted_at", "toLocaleString", "row", "field", "join", "clearCache", "clear", "CACHE_EXPIRY_MINUTES", "FormConfigService", "getFormConfig", "has", "possiblePaths", "path", "doc<PERSON>ef", "doc", "db", "docSnap", "getDoc", "exists", "fields", "getFieldMapping", "fieldMappingCache", "formConfig", "mapping", "type", "label", "getAllFieldLabels", "formIdentifiers", "allLabels", "formId", "add", "convertSubmissionData", "submissionData", "convertedData", "_ref", "fieldId", "CompletedSubmissionsDetails", "_ref2", "completedOffices", "formatDate", "_jsxs", "children", "style", "marginBottom", "color", "_jsx", "maxHeight", "overflowY", "office", "index", "officeSubmissions", "latestSubmission", "a", "b", "padding", "border", "borderRadius", "backgroundColor", "display", "justifyContent", "alignItems", "flex", "margin", "fontWeight", "fontSize", "date", "time", "PendingSubmissionsDetails", "_ref3", "pendingOffices", "textAlign", "marginTop", "loading", "onRefresh", "set<PERSON>ummary", "useState", "summaryLoading", "setSummaryLoading", "expandedCard", "setExpandedCard", "useEffect", "calculateOfficeSummary", "async", "getUniqueOfficesFromSubmissions", "targetOffices", "getTargetOfficesForForm", "some", "completed", "totalTargetOffices", "completedCount", "pendingCount", "uniqueOffices", "officeSet", "single", "selected_offices", "isArray", "handleCardClick", "cardType", "gap", "background", "cursor", "transition", "boxShadow", "onClick", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "marginRight", "opacity", "overflow", "marginLeft", "dateString", "toLocaleDateString", "toLocaleTimeString", "hour", "minute", "columns", "setColumns", "processedData", "setProcessedData", "loadingColumns", "setLoadingColumns", "buildDynamicColumns", "allFieldMappings", "allFieldLabels", "newColumns", "push", "processedSubmissions", "Promise", "all", "submission_data_keys", "keys", "form_type", "getFormTypeDisplay", "isUserNameField", "formatFieldValue", "replace", "l", "toUpperCase", "String", "fieldName", "overflowX", "width", "borderCollapse", "min<PERSON><PERSON><PERSON>", "column", "borderBottom", "whiteSpace", "_row$column$key", "_row$column$key2", "verticalAlign", "max<PERSON><PERSON><PERSON>", "textOverflow", "title", "borderTop", "document", "createElement", "textContent", "append<PERSON><PERSON><PERSON>", "Reports", "currentUser", "useAuth", "userData", "setUserData", "setSubmissions", "setLoading", "setError", "viewMode", "setViewMode", "savedViewMode", "sessionStorage", "getItem", "setFilters", "setFormIdentifiers", "officeOptions", "setOfficeOptions", "officeLoading", "setOfficeLoading", "officeError", "setOfficeError", "userRef", "uid", "userSnap", "fetchUserData", "fetchInitialData", "fetchOfficeNames", "fetchSubmissions", "baseTitle", "viewTitle", "summaryData", "identifiers", "useCallback", "officeNames", "OfficeService", "fetchUserSpecificOfficeNames", "options", "officeNamesToOptions", "handleFiltersChange", "newFilters", "handleViewModeChange", "newViewMode", "setItem", "className", "Sidebar", "blob", "Blob", "link", "url", "URL", "createObjectURL", "setAttribute", "split", "visibility", "body", "click", "<PERSON><PERSON><PERSON><PERSON>", "alert", "disabled", "firstSubmission", "officeData", "sub", "_sub$submission_data", "submission_data_office", "all_submission_fields", "office_fields", "slice", "flexWrap", "icon", "card", "textTransform", "letterSpacing", "onChange", "target", "identifier", "position", "option", "right", "top", "height", "animation", "DynamicReportsTable", "SubmissionsSummaryCards"], "sourceRoot": ""}