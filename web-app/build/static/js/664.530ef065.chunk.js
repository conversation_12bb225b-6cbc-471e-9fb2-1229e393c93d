"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[664],{5664:(e,s,i)=>{i.r(s),i.d(s,{default:()=>n});var t=i(5043),r=i(215),a=i(579);const n=()=>{const[e,s]=(0,t.useState)([]),[i,n]=(0,t.useState)(!1),o=e=>{s((s=>[...s,`${(new Date).toLocaleTimeString()}: ${e}`]))};return(0,a.jsxs)("div",{style:{padding:"2rem",maxWidth:"800px",margin:"0 auto"},children:[(0,a.jsx)("h2",{children:"\ud83d\udd0d Reports Diagnostics"}),(0,a.jsx)("p",{children:"Use this page to diagnose and fix reports issues."}),(0,a.jsxs)("div",{style:{marginBottom:"2rem"},children:[(0,a.jsx)("button",{onClick:async()=>{n(!0),s([]);try{o("\ud83d\udd0d Starting Supabase diagnostics..."),o("\ud83d\udce1 Testing basic Supabase connection...");try{const{data:e,error:s}=await r.N.from("dynamic_form_submissions").select("count",{count:"exact",head:!0});s?(o(`\u274c Connection test failed: ${s.message}`),s.message.includes("relation")&&s.message.includes("does not exist")&&o('\ud83d\udca1 Table "dynamic_form_submissions" does not exist. Please run the SQL setup script.'),s.message.includes("permission denied")&&o("\ud83d\udca1 Permission denied. Please check Row Level Security settings.")):o(`\u2705 Connection successful! Table has ${e} records`)}catch(e){o(`\ud83d\udca5 Connection error: ${e}`)}o("\ud83d\udce5 Testing data fetch...");try{const{data:e,error:s}=await r.N.from("dynamic_form_submissions").select("*").limit(5);s?o(`\u274c Data fetch failed: ${s.message}`):(o(`\u2705 Data fetch successful! Got ${(null===e||void 0===e?void 0:e.length)||0} records`),e&&e.length>0&&o(`\ud83d\udcc4 Sample record: ${JSON.stringify(e[0],null,2)}`))}catch(e){o(`\ud83d\udca5 Data fetch error: ${e}`)}o("\ud83c\udfd7\ufe0f Checking table structure...");try{const{data:e,error:s}=await r.N.rpc("get_table_info",{table_name:"dynamic_form_submissions"});o(s?`\u26a0\ufe0f Could not check table structure: ${s.message}`:"\ud83d\udccb Table structure check completed")}catch(e){o("\u26a0\ufe0f Table structure check not available")}o("\ud83d\udd0d Testing specific queries...");try{const{data:e,error:s}=await r.N.from("dynamic_form_submissions").select("form_identifier");if(s)o(`\u274c Form identifiers query failed: ${s.message}`);else{const s=new Set(null===e||void 0===e?void 0:e.map((e=>e.form_identifier)));o(`\ud83d\udccb Found ${s.size} unique form types: ${Array.from(s).join(", ")}`)}const{data:i,error:t}=await r.N.from("dynamic_form_submissions").select("user_id");if(t)o(`\u274c User IDs query failed: ${t.message}`);else{const e=new Set(null===i||void 0===i?void 0:i.map((e=>e.user_id)));o(`\ud83d\udc65 Found ${e.size} unique users`)}}catch(e){o(`\ud83d\udca5 Specific queries error: ${e}`)}o("\ud83c\udf89 Diagnostics completed!")}catch(i){o(`\ud83d\udca5 Fatal error during diagnostics: ${i}`)}finally{n(!1)}},disabled:i,style:{padding:"0.75rem 1.5rem",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"4px",marginRight:"1rem"},children:i?"\ud83d\udd04 Running...":"\ud83d\udd0d Run Diagnostics"}),(0,a.jsx)("button",{onClick:async()=>{n(!0),o("\ud83d\udd27 Creating sample data...");try{const e=[{form_identifier:"test-form-1",user_id:"123e4567-e89b-12d3-a456-426614174999",submission_data:{name:"Test User 1",email:"<EMAIL>",officeName:"Test Office 1"},submitted_at:(new Date).toISOString()},{form_identifier:"test-form-2",user_id:"123e4567-e89b-12d3-a456-426614174998",submission_data:{name:"Test User 2",email:"<EMAIL>",officeName:"Test Office 2"},submitted_at:(new Date).toISOString()}],{data:s,error:i}=await r.N.from("dynamic_form_submissions").insert(e);i?o(`\u274c Failed to create sample data: ${i.message}`):(o("\u2705 Sample data created successfully!"),o(`\ud83d\udcca Created ${e.length} test records`))}catch(e){o(`\ud83d\udca5 Error creating sample data: ${e}`)}finally{n(!1)}},disabled:i,style:{padding:"0.75rem 1.5rem",backgroundColor:"#28a745",color:"white",border:"none",borderRadius:"4px"},children:i?"\ud83d\udd04 Creating...":"\ud83d\udd27 Create Sample Data"})]}),(0,a.jsxs)("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"4px",padding:"1rem",maxHeight:"500px",overflowY:"auto"},children:[(0,a.jsx)("h4",{children:"\ud83d\udccb Test Results:"}),0===e.length?(0,a.jsx)("p",{style:{color:"#666",fontStyle:"italic"},children:'Click "Run Diagnostics" to start testing...'}):(0,a.jsx)("div",{style:{fontFamily:"monospace",fontSize:"0.875rem"},children:e.map(((e,s)=>(0,a.jsx)("div",{style:{marginBottom:"0.5rem",wordBreak:"break-word"},children:e},s)))})]}),(0,a.jsxs)("div",{style:{marginTop:"2rem",padding:"1rem",backgroundColor:"#e3f2fd",borderRadius:"4px"},children:[(0,a.jsx)("h4",{children:"\ud83d\udca1 Quick Fixes:"}),(0,a.jsxs)("ul",{children:[(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Table doesn't exist:"})," Run the SQL script from SUPABASE_DIAGNOSTIC_SCRIPT.sql in Supabase SQL Editor"]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Permission denied:"})," Disable Row Level Security: ",(0,a.jsx)("code",{children:"ALTER TABLE dynamic_form_submissions DISABLE ROW LEVEL SECURITY;"})]}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"No data:"}),' Click "Create Sample Data" button above or run the full SQL setup script']}),(0,a.jsxs)("li",{children:[(0,a.jsx)("strong",{children:"Connection issues:"})," Check your Supabase URL and API key in environment variables"]})]})]})]})}}}]);
//# sourceMappingURL=664.530ef065.chunk.js.map