{"version": 3, "file": "static/js/664.530ef065.chunk.js", "mappings": "qLAGA,MA8NA,EA9N8BA,KAC5B,MAAOC,EAAaC,IAAkBC,EAAAA,EAAAA,UAAmB,KAClDC,EAASC,IAAcF,EAAAA,EAAAA,WAAS,GAEjCG,EAAaC,IACjBL,GAAeM,GAAQ,IAAIA,EAAM,IAAG,IAAIC,MAAOC,yBAAyBH,MAAW,EAkJrF,OACEI,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CAAEC,QAAS,OAAQC,SAAU,QAASC,OAAQ,UAAWC,SAAA,EACnEC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,sCACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAG,uDAEHL,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CAAEM,aAAc,QAASF,SAAA,EACnCC,EAAAA,EAAAA,KAAA,UACEE,QAtJeC,UACrBf,GAAW,GACXH,EAAe,IAEf,IACEI,EAAU,iDAGVA,EAAU,qDACV,IACE,MAAM,KAAEe,EAAI,MAAEC,SAAgBC,EAAAA,EAASC,KAAK,4BAA4BC,OAAO,QAAS,CAAEC,MAAO,QAASC,MAAM,IAC5GL,GACFhB,EAAU,kCAA6BgB,EAAMf,WACzCe,EAAMf,QAAQqB,SAAS,aAAeN,EAAMf,QAAQqB,SAAS,mBAC/DtB,EAAU,kGAERgB,EAAMf,QAAQqB,SAAS,sBACzBtB,EAAU,8EAGZA,EAAU,2CAAsCe,YAEpD,CAAE,MAAOQ,GACPvB,EAAU,kCAAwBuB,IACpC,CAGAvB,EAAU,sCACV,IACE,MAAM,KAAEe,EAAI,MAAEC,SAAgBC,EAAAA,EAC3BC,KAAK,4BACLC,OAAO,KACPK,MAAM,GAELR,EACFhB,EAAU,6BAAwBgB,EAAMf,YAExCD,EAAU,sCAAoC,OAAJe,QAAI,IAAJA,OAAI,EAAJA,EAAMU,SAAU,aACtDV,GAAQA,EAAKU,OAAS,GACxBzB,EAAU,+BAAqB0B,KAAKC,UAAUZ,EAAK,GAAI,KAAM,MAGnE,CAAE,MAAOQ,GACPvB,EAAU,kCAAwBuB,IACpC,CAGAvB,EAAU,kDACV,IACE,MAAM,KAAEe,EAAI,MAAEC,SAAgBC,EAAAA,EAASW,IAAI,iBAAkB,CAAEC,WAAY,6BAEzE7B,EADEgB,EACQ,iDAAuCA,EAAMf,UAE7C,+CAEd,CAAE,MAAOsB,GACPvB,EAAU,mDACZ,CAGAA,EAAU,4CACV,IAEE,MAAQe,KAAMe,EAAUd,MAAOe,SAAoBd,EAAAA,EAChDC,KAAK,4BACLC,OAAO,mBAEV,GAAIY,EACF/B,EAAU,yCAAoC+B,EAAU9B,eACnD,CACL,MAAM+B,EAAc,IAAIC,IAAY,OAARH,QAAQ,IAARA,OAAQ,EAARA,EAAUI,KAAKC,GAAcA,EAAKC,mBAC9DpC,EAAU,sBAAYgC,EAAYK,2BAA2BC,MAAMpB,KAAKc,GAAaO,KAAK,QAC5F,CAGA,MAAQxB,KAAMyB,EAAUxB,MAAOyB,SAAoBxB,EAAAA,EAChDC,KAAK,4BACLC,OAAO,WAEV,GAAIsB,EACFzC,EAAU,iCAA4ByC,EAAUxC,eAC3C,CACL,MAAMyC,EAAc,IAAIT,IAAY,OAARO,QAAQ,IAARA,OAAQ,EAARA,EAAUN,KAAKC,GAAcA,EAAKQ,WAC9D3C,EAAU,sBAAY0C,EAAYL,oBACpC,CACF,CAAE,MAAOd,GACPvB,EAAU,wCAA8BuB,IAC1C,CAEAvB,EAAU,sCAEZ,CAAE,MAAOgB,GACPhB,EAAU,gDAAsCgB,IAClD,CAAC,QACCjB,GAAW,EACb,GAwDM6C,SAAU9C,EACVQ,MAAO,CACLC,QAAS,iBACTsC,gBAAiB,UACjBC,MAAO,QACPC,OAAQ,OACRC,aAAc,MACdC,YAAa,QACbvC,SAEDZ,EAAU,0BAAkB,kCAG/Ba,EAAAA,EAAAA,KAAA,UACEE,QAnEiBC,UACvBf,GAAW,GACXC,EAAU,wCAEV,IACE,MAAMkD,EAAa,CACjB,CACEd,gBAAiB,cACjBO,QAAS,uCACTQ,gBAAiB,CACfC,KAAM,cACNC,MAAO,oBACPC,WAAY,iBAEdC,cAAc,IAAIpD,MAAOqD,eAE3B,CACEpB,gBAAiB,cACjBO,QAAS,uCACTQ,gBAAiB,CACfC,KAAM,cACNC,MAAO,oBACPC,WAAY,iBAEdC,cAAc,IAAIpD,MAAOqD,iBAIvB,KAAEzC,EAAI,MAAEC,SAAgBC,EAAAA,EAC3BC,KAAK,4BACLuC,OAAOP,GAENlC,EACFhB,EAAU,wCAAmCgB,EAAMf,YAEnDD,EAAU,4CACVA,EAAU,wBAAckD,EAAWzB,uBAEvC,CAAE,MAAOF,GACPvB,EAAU,4CAAkCuB,IAC9C,CAAC,QACCxB,GAAW,EACb,GA0BM6C,SAAU9C,EACVQ,MAAO,CACLC,QAAS,iBACTsC,gBAAiB,UACjBC,MAAO,QACPC,OAAQ,OACRC,aAAc,OACdtC,SAEDZ,EAAU,2BAAmB,wCAIlCO,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CACVuC,gBAAiB,UACjBE,OAAQ,oBACRC,aAAc,MACdzC,QAAS,OACTmD,UAAW,QACXC,UAAW,QACXjD,SAAA,EACAC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,+BACoB,IAAvBf,EAAY8B,QACXd,EAAAA,EAAAA,KAAA,KAAGL,MAAO,CAAEwC,MAAO,OAAQc,UAAW,UAAWlD,SAAC,iDAElDC,EAAAA,EAAAA,KAAA,OAAKL,MAAO,CAAEuD,WAAY,YAAaC,SAAU,YAAapD,SAC3Df,EAAYuC,KAAI,CAAC6B,EAAQC,KACxBrD,EAAAA,EAAAA,KAAA,OAAiBL,MAAO,CAAEM,aAAc,SAAUqD,UAAW,cAAevD,SACzEqD,GADOC,WAQlB3D,EAAAA,EAAAA,MAAA,OAAKC,MAAO,CAAE4D,UAAW,OAAQ3D,QAAS,OAAQsC,gBAAiB,UAAWG,aAAc,OAAQtC,SAAA,EAClGC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,+BACJL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EACEL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,yBAA6B,qFACzCL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,uBAA2B,iCAA6BC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,yEAC1EL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aAAiB,gFAC7BL,EAAAA,EAAAA,MAAA,MAAAK,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,uBAA2B,2EAGvC,C", "sources": ["components/Reports/ReportsTest.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { supabase } from '../../config/supabaseClient';\n\nconst ReportsTest: React.FC = () => {\n  const [testResults, setTestResults] = useState<string[]>([]);\n  const [loading, setLoading] = useState(false);\n\n  const addResult = (message: string) => {\n    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);\n  };\n\n  const runDiagnostics = async () => {\n    setLoading(true);\n    setTestResults([]);\n    \n    try {\n      addResult('🔍 Starting Supabase diagnostics...');\n\n      // Test 1: Basic connection\n      addResult('📡 Testing basic Supabase connection...');\n      try {\n        const { data, error } = await supabase.from('dynamic_form_submissions').select('count', { count: 'exact', head: true });\n        if (error) {\n          addResult(`❌ Connection test failed: ${error.message}`);\n          if (error.message.includes('relation') && error.message.includes('does not exist')) {\n            addResult('💡 Table \"dynamic_form_submissions\" does not exist. Please run the SQL setup script.');\n          }\n          if (error.message.includes('permission denied')) {\n            addResult('💡 Permission denied. Please check Row Level Security settings.');\n          }\n        } else {\n          addResult(`✅ Connection successful! Table has ${data} records`);\n        }\n      } catch (err) {\n        addResult(`💥 Connection error: ${err}`);\n      }\n\n      // Test 2: Try to fetch data\n      addResult('📥 Testing data fetch...');\n      try {\n        const { data, error } = await supabase\n          .from('dynamic_form_submissions')\n          .select('*')\n          .limit(5);\n        \n        if (error) {\n          addResult(`❌ Data fetch failed: ${error.message}`);\n        } else {\n          addResult(`✅ Data fetch successful! Got ${data?.length || 0} records`);\n          if (data && data.length > 0) {\n            addResult(`📄 Sample record: ${JSON.stringify(data[0], null, 2)}`);\n          }\n        }\n      } catch (err) {\n        addResult(`💥 Data fetch error: ${err}`);\n      }\n\n      // Test 3: Check table structure\n      addResult('🏗️ Checking table structure...');\n      try {\n        const { data, error } = await supabase.rpc('get_table_info', { table_name: 'dynamic_form_submissions' });\n        if (error) {\n          addResult(`⚠️ Could not check table structure: ${error.message}`);\n        } else {\n          addResult(`📋 Table structure check completed`);\n        }\n      } catch (err) {\n        addResult(`⚠️ Table structure check not available`);\n      }\n\n      // Test 4: Test specific queries\n      addResult('🔍 Testing specific queries...');\n      try {\n        // Test form identifiers\n        const { data: formData, error: formError } = await supabase\n          .from('dynamic_form_submissions')\n          .select('form_identifier');\n        \n        if (formError) {\n          addResult(`❌ Form identifiers query failed: ${formError.message}`);\n        } else {\n          const uniqueForms = new Set(formData?.map((item: any) => item.form_identifier));\n          addResult(`📋 Found ${uniqueForms.size} unique form types: ${Array.from(uniqueForms).join(', ')}`);\n        }\n\n        // Test user IDs\n        const { data: userData, error: userError } = await supabase\n          .from('dynamic_form_submissions')\n          .select('user_id');\n        \n        if (userError) {\n          addResult(`❌ User IDs query failed: ${userError.message}`);\n        } else {\n          const uniqueUsers = new Set(userData?.map((item: any) => item.user_id));\n          addResult(`👥 Found ${uniqueUsers.size} unique users`);\n        }\n      } catch (err) {\n        addResult(`💥 Specific queries error: ${err}`);\n      }\n\n      addResult('🎉 Diagnostics completed!');\n\n    } catch (error) {\n      addResult(`💥 Fatal error during diagnostics: ${error}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const createSampleData = async () => {\n    setLoading(true);\n    addResult('🔧 Creating sample data...');\n\n    try {\n      const sampleData = [\n        {\n          form_identifier: 'test-form-1',\n          user_id: '123e4567-e89b-12d3-a456-426614174999',\n          submission_data: {\n            name: 'Test User 1',\n            email: '<EMAIL>',\n            officeName: 'Test Office 1'\n          },\n          submitted_at: new Date().toISOString()\n        },\n        {\n          form_identifier: 'test-form-2',\n          user_id: '123e4567-e89b-12d3-a456-426614174998',\n          submission_data: {\n            name: 'Test User 2',\n            email: '<EMAIL>',\n            officeName: 'Test Office 2'\n          },\n          submitted_at: new Date().toISOString()\n        }\n      ];\n\n      const { data, error } = await supabase\n        .from('dynamic_form_submissions')\n        .insert(sampleData);\n\n      if (error) {\n        addResult(`❌ Failed to create sample data: ${error.message}`);\n      } else {\n        addResult(`✅ Sample data created successfully!`);\n        addResult(`📊 Created ${sampleData.length} test records`);\n      }\n    } catch (err) {\n      addResult(`💥 Error creating sample data: ${err}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div style={{ padding: '2rem', maxWidth: '800px', margin: '0 auto' }}>\n      <h2>🔍 Reports Diagnostics</h2>\n      <p>Use this page to diagnose and fix reports issues.</p>\n      \n      <div style={{ marginBottom: '2rem' }}>\n        <button \n          onClick={runDiagnostics}\n          disabled={loading}\n          style={{ \n            padding: '0.75rem 1.5rem', \n            backgroundColor: '#007bff', \n            color: 'white', \n            border: 'none', \n            borderRadius: '4px',\n            marginRight: '1rem'\n          }}\n        >\n          {loading ? '🔄 Running...' : '🔍 Run Diagnostics'}\n        </button>\n        \n        <button \n          onClick={createSampleData}\n          disabled={loading}\n          style={{ \n            padding: '0.75rem 1.5rem', \n            backgroundColor: '#28a745', \n            color: 'white', \n            border: 'none', \n            borderRadius: '4px'\n          }}\n        >\n          {loading ? '🔄 Creating...' : '🔧 Create Sample Data'}\n        </button>\n      </div>\n\n      <div style={{ \n        backgroundColor: '#f8f9fa', \n        border: '1px solid #dee2e6', \n        borderRadius: '4px', \n        padding: '1rem',\n        maxHeight: '500px',\n        overflowY: 'auto'\n      }}>\n        <h4>📋 Test Results:</h4>\n        {testResults.length === 0 ? (\n          <p style={{ color: '#666', fontStyle: 'italic' }}>Click \"Run Diagnostics\" to start testing...</p>\n        ) : (\n          <div style={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>\n            {testResults.map((result, index) => (\n              <div key={index} style={{ marginBottom: '0.5rem', wordBreak: 'break-word' }}>\n                {result}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      <div style={{ marginTop: '2rem', padding: '1rem', backgroundColor: '#e3f2fd', borderRadius: '4px' }}>\n        <h4>💡 Quick Fixes:</h4>\n        <ul>\n          <li><strong>Table doesn't exist:</strong> Run the SQL script from SUPABASE_DIAGNOSTIC_SCRIPT.sql in Supabase SQL Editor</li>\n          <li><strong>Permission denied:</strong> Disable Row Level Security: <code>ALTER TABLE dynamic_form_submissions DISABLE ROW LEVEL SECURITY;</code></li>\n          <li><strong>No data:</strong> Click \"Create Sample Data\" button above or run the full SQL setup script</li>\n          <li><strong>Connection issues:</strong> Check your Supabase URL and API key in environment variables</li>\n        </ul>\n      </div>\n    </div>\n  );\n};\n\nexport default ReportsTest;\n"], "names": ["ReportsTest", "testResults", "setTestResults", "useState", "loading", "setLoading", "addResult", "message", "prev", "Date", "toLocaleTimeString", "_jsxs", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "children", "_jsx", "marginBottom", "onClick", "async", "data", "error", "supabase", "from", "select", "count", "head", "includes", "err", "limit", "length", "JSON", "stringify", "rpc", "table_name", "formData", "formError", "uniqueForms", "Set", "map", "item", "form_identifier", "size", "Array", "join", "userData", "userError", "uniqueUsers", "user_id", "disabled", "backgroundColor", "color", "border", "borderRadius", "marginRight", "sampleData", "submission_data", "name", "email", "officeName", "submitted_at", "toISOString", "insert", "maxHeight", "overflowY", "fontStyle", "fontFamily", "fontSize", "result", "index", "wordBreak", "marginTop"], "sourceRoot": ""}