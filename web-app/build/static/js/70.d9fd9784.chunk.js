"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[70],{6070:(e,i,o)=>{o.r(i),o.d(i,{default:()=>h});var s=o(5043),l=o(5472),r=o(2073),a=o(9066),n=o(215),c=o(4540),d=o(1103),t=o(2775),m=o(5906),f=o(172),u=o(579);const h=()=>{const{currentUser:e}=(0,a.A)(),[i,o]=(0,s.useState)(null),[h,g]=(0,s.useState)({name:"",officeName:"",divisionName:"",designation:"",mobileNumber:""}),[N,p]=(0,s.useState)([]),[b,v]=(0,s.useState)(!1),[x,j]=(0,s.useState)(!0),[P,y]=(0,s.useState)(!1),[w,D]=(0,s.useState)(!1),[E,F]=(0,s.useState)(null),[C,S]=(0,s.useState)(null);(0,s.useEffect)((()=>{I()}),[e]),(0,s.useEffect)((()=>{b&&A()}),[b]);const I=async()=>{if(!e)return F("User not logged in"),void j(!1);try{console.log("\ud83d\udd0d Profile: Fetching user data...");const i=await(0,l.x7)((0,l.H9)(r.db,"employees",e.uid));if(i.exists()){const e=i.data();o(e),g({name:e.name||"",officeName:e.officeName||"",divisionName:e.divisionName||"",designation:e.designation||"",mobileNumber:e.mobileNumber||""}),console.log("\u2705 Profile: User data loaded successfully")}else F("User profile not found"),console.log("\u274c Profile: User document not found")}catch(i){console.error("\u274c Profile: Error fetching user data:",i),F("Failed to load user data")}finally{j(!1)}},A=async()=>{D(!0),S(null);try{console.log("\ud83c\udfe2 Profile: Fetching office options using OfficeService...");const e=await c.A.fetchOfficeNames();if(p(e),console.log(`\u2705 Profile: Successfully loaded ${e.length} office options using OfficeService`),e.length>0){const i=[...e].sort();console.log("\ud83d\udcca Profile: Office range - First:",i[0]),console.log("\ud83d\udcca Profile: Office range - Last:",i[i.length-1]);const o=e.find((e=>e.toLowerCase().includes("tirupur division"))),s=e.find((e=>e.toLowerCase().includes("coimbatore division")));console.log('\ud83d\udcca Profile: Contains "Tirupur division":',!!o),console.log('\ud83d\udcca Profile: Contains "Coimbatore division":',!!s),o&&console.log("\ud83d\udcca Profile: Found Tirupur division:",o),s&&console.log("\ud83d\udcca Profile: Found Coimbatore division:",s)}}catch(e){console.error("\u274c Profile: Error fetching office options:",e),S("Failed to load office options"),p([])}finally{D(!1)}},U=(e,i)=>{g((o=>({...o,[e]:i})))};return x?(0,u.jsxs)("div",{className:"dashboard-container",children:[(0,u.jsx)(d.A,{userData:i}),(0,u.jsx)("div",{className:"main-content",children:(0,u.jsxs)("div",{className:"loading-container",children:[(0,u.jsx)("div",{className:"spinner"}),(0,u.jsx)("p",{children:"Loading profile..."})]})})]}):i?(0,u.jsxs)("div",{className:"dashboard-container",children:[(0,u.jsx)(d.A,{userData:i}),(0,u.jsx)("div",{className:"main-content",children:(0,u.jsxs)("div",{className:"profile-container",children:[(0,u.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"1rem"},children:[(0,u.jsx)("h1",{className:"page-title",children:"User Profile"}),(0,u.jsx)("button",{onClick:async()=>{console.log("\ud83e\uddea === PROFILE DEBUG: Testing Office Fetching ===");try{c.A.clearCache(),console.log("\ud83e\uddea Profile Debug: Cache cleared");const e=await c.A.fetchOfficeNames();console.log("\ud83e\uddea Profile Debug: OfficeService returned",e.length,"offices"),console.log("\ud83e\uddea Profile Debug: First 10 offices:",e.slice(0,10)),console.log("\ud83e\uddea Profile Debug: Last 10 offices:",e.slice(-10));const i=e.filter((e=>e.toLowerCase().includes("division")));console.log("\ud83e\uddea Profile Debug: Division offices found:",i.length),console.log("\ud83e\uddea Profile Debug: Division offices:",i)}catch(E){console.error("\ud83e\uddea Profile Debug: Error:",E)}console.log("\ud83e\uddea === END PROFILE DEBUG ===")},style:{padding:"0.5rem 1rem",backgroundColor:"#dc3545",color:"white",border:"none",borderRadius:"4px",cursor:"pointer",fontSize:"0.875rem"},children:"\ud83e\uddea Debug Office Fetching"})]}),(0,u.jsxs)("div",{className:"profile-header",children:[(0,u.jsx)("div",{className:"profile-avatar",children:i.name.charAt(0).toUpperCase()}),(0,u.jsxs)("div",{className:"profile-info",children:[(0,u.jsx)("h2",{children:i.name}),(0,u.jsxs)("p",{className:"employee-id",children:["Employee ID: ",i.employeeId]}),i.role&&(0,u.jsx)("span",{className:"role-badge",children:i.role})]})]}),(0,u.jsxs)("div",{className:"profile-card",children:[(0,u.jsx)("h3",{children:"Personal Information"}),(0,u.jsxs)("div",{className:"info-grid",children:[(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Full Name"}),(0,u.jsx)("span",{children:i.name})]}),(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Employee ID"}),(0,u.jsx)("span",{children:i.employeeId})]}),(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Email Address"}),(0,u.jsx)("span",{children:i.email})]}),(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Division"}),(0,u.jsx)("span",{children:i.divisionName||"Not specified"})]}),(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Designation"}),(0,u.jsx)("span",{children:i.designation||"Not specified"})]}),(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Mobile Number"}),(0,u.jsx)("span",{children:i.mobileNumber||"Not specified"})]})]})]}),(0,u.jsxs)("div",{className:"profile-card",children:[(0,u.jsxs)("div",{className:"card-header",children:[(0,u.jsx)("h3",{children:"Editable Information"}),!b&&(0,u.jsxs)("button",{className:"edit-btn",onClick:()=>v(!0),children:[(0,u.jsx)("i",{className:"fas fa-edit"})," Edit Profile"]})]}),b?(0,u.jsxs)("div",{className:"edit-form",children:[(0,u.jsxs)("div",{className:"form-row",children:[(0,u.jsxs)("div",{className:"form-group",children:[(0,u.jsx)("label",{htmlFor:"name",children:"Full Name"}),(0,u.jsx)(t.A,{fullWidth:!0,value:h.name,onChange:e=>U("name",e.target.value),placeholder:"Enter full name",required:!0})]}),(0,u.jsxs)("div",{className:"form-group",children:[(0,u.jsx)("label",{htmlFor:"designation",children:"Designation"}),(0,u.jsx)(t.A,{fullWidth:!0,value:h.designation,onChange:e=>U("designation",e.target.value),placeholder:"Enter designation"})]})]}),(0,u.jsxs)("div",{className:"form-row",children:[(0,u.jsxs)("div",{className:"form-group",children:[(0,u.jsx)("label",{htmlFor:"divisionName",children:"Division"}),(0,u.jsx)(t.A,{fullWidth:!0,value:h.divisionName,onChange:e=>U("divisionName",e.target.value),placeholder:"Enter division name"})]}),(0,u.jsxs)("div",{className:"form-group",children:[(0,u.jsx)("label",{htmlFor:"mobileNumber",children:"Mobile Number"}),(0,u.jsx)(t.A,{fullWidth:!0,value:h.mobileNumber,onChange:e=>U("mobileNumber",e.target.value),placeholder:"Enter mobile number"})]})]}),(0,u.jsxs)("div",{className:"form-group",children:[(0,u.jsx)("label",{htmlFor:"officeName",children:"Office Name"}),(0,u.jsx)(m.A,{fullWidth:!0,options:N,getOptionLabel:e=>e,value:h.officeName||null,onChange:(e,i)=>{U("officeName",i||"")},disabled:w,renderInput:e=>(0,u.jsx)(t.A,{...e,placeholder:w?"Loading offices...":"Select Office",error:!!C,helperText:C,required:!0,InputProps:{...e.InputProps,endAdornment:(0,u.jsxs)(s.Fragment,{children:[w?(0,u.jsx)(f.A,{color:"inherit",size:20}):null,e.InputProps.endAdornment]})}})})]}),(0,u.jsxs)("div",{className:"form-actions",children:[(0,u.jsx)("button",{className:"btn-secondary",onClick:()=>{g({name:(null===i||void 0===i?void 0:i.name)||"",officeName:(null===i||void 0===i?void 0:i.officeName)||"",divisionName:(null===i||void 0===i?void 0:i.divisionName)||"",designation:(null===i||void 0===i?void 0:i.designation)||"",mobileNumber:(null===i||void 0===i?void 0:i.mobileNumber)||""}),v(!1),F(null)},disabled:P,children:"Cancel"}),(0,u.jsx)("button",{className:"btn-primary",onClick:async()=>{if(e&&i)if(h.name.trim())if(h.officeName.trim()){y(!0),F(null);try{console.log("\ud83d\udcbe Profile: Saving profile data...");const a={name:h.name,officeName:h.officeName,divisionName:h.divisionName,designation:h.designation,mobileNumber:h.mobileNumber,updatedAt:new Date};await(0,l.mZ)((0,l.H9)(r.db,"employees",e.uid),a),console.log("\u2705 Profile: Firebase updated successfully"),console.log("\ud83d\udd0d Profile: Attempting Supabase update..."),console.log("\ud83d\udd0d Profile: Employee ID for update:",i.employeeId),console.log("\ud83d\udd0d Profile: Update data:",{name:h.name,officeName:h.officeName,divisionName:h.divisionName,designation:h.designation,mobileNumber:h.mobileNumber});const{data:c,error:d}=await n.N.from("user_profiles").select("*").eq("employeeId",i.employeeId).single();if(d){console.error("\u274c Profile: Error checking existing record:",d),console.log("\ud83d\udd04 Profile: Trying alternative lookup by Firebase UID...");const{data:o,error:l}=await n.N.from("user_profiles").select("*").eq("uid",e.uid).single();if(l){console.error("\u274c Profile: Alternative lookup also failed:",l),console.log("\ud83d\udd04 Profile: User record not found in Supabase. Creating new record...");try{const o={uid:e.uid,employeeId:i.employeeId,name:i.name,email:i.email,officeName:i.officeName,divisionName:i.divisionName||"",designation:i.designation||"",mobileNumber:i.mobileNumber||"",role:i.role||"user"};console.log("\ud83d\udd04 Profile: Creating user record with data:",o);const{data:s,error:l}=await n.N.from("user_profiles").insert(o).select().single();if(l){if(console.error("\u274c Profile: Failed to create user record:",l),"42501"===l.code||l.message.includes("row-level security"))throw new Error("Database security settings are blocking profile creation. Please contact your administrator to disable Row Level Security for the user_profiles table.");throw new Error(`Failed to create user record: ${l.message}`)}console.log("\u2705 Profile: Successfully created user record:",s)}catch(s){throw console.error("\u274c Profile: Error creating user record:",s),new Error(`Unable to create user profile. Please contact support. Error: ${d.message}`)}}else console.log("\u2705 Profile: Found record using Firebase UID:",o),i.employeeId=o.employeeId}else console.log("\ud83d\udd0d Profile: Existing record found:",c);const{data:t,error:m}=await n.N.from("user_profiles").update({name:h.name,officeName:h.officeName,divisionName:h.divisionName,designation:h.designation,mobileNumber:h.mobileNumber}).eq("employeeId",i.employeeId).select();if(m){if(console.error("\u274c Profile: Supabase update error:",m),"42501"===m.code||m.message.includes("row-level security"))throw new Error("Database security settings are blocking profile updates. Please contact your administrator to disable Row Level Security for the user_profiles table.");throw new Error(`Supabase update failed: ${m.message}`)}console.log("\u2705 Profile: Supabase updated successfully"),console.log("\u2705 Profile: Updated record:",t),o((e=>e?{...e,name:h.name,officeName:h.officeName,divisionName:h.divisionName,designation:h.designation,mobileNumber:h.mobileNumber}:null)),v(!1),alert("Profile updated successfully!")}catch(a){console.error("\u274c Profile: Error saving profile:",a),F("Failed to update profile. Please try again.")}finally{y(!1)}}else F("Office name is required");else F("Name is required");else F("User data not available")},disabled:P||w||!h.officeName.trim(),children:P?"Saving...":"Save Changes"})]})]}):(0,u.jsxs)("div",{className:"info-grid",children:[(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Full Name"}),(0,u.jsx)("span",{children:i.name})]}),(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Designation"}),(0,u.jsx)("span",{children:i.designation||"Not specified"})]}),(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Division"}),(0,u.jsx)("span",{children:i.divisionName||"Not specified"})]}),(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Mobile Number"}),(0,u.jsx)("span",{children:i.mobileNumber||"Not specified"})]}),(0,u.jsxs)("div",{className:"info-item",children:[(0,u.jsx)("label",{children:"Office Name"}),(0,u.jsx)("span",{children:i.officeName||"Not specified"})]})]})]}),E&&(0,u.jsxs)("div",{className:"error-message",children:[(0,u.jsx)("i",{className:"fas fa-exclamation-triangle"}),E]})]})})]}):(0,u.jsxs)("div",{className:"dashboard-container",children:[(0,u.jsx)(d.A,{userData:i}),(0,u.jsx)("div",{className:"main-content",children:(0,u.jsxs)("div",{className:"error-container",children:[(0,u.jsx)("h2",{children:"Profile Not Found"}),(0,u.jsx)("p",{children:E||"Unable to load user profile"})]})})]})}}}]);
//# sourceMappingURL=70.d9fd9784.chunk.js.map