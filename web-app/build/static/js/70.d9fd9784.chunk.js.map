{"version": 3, "file": "static/js/70.d9fd9784.chunk.js", "mappings": "mQA6BA,MAijBA,EAjjB0BA,KACxB,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,MACjBC,EAAUC,IAAeC,EAAAA,EAAAA,UAA0B,OACnDC,EAAUC,IAAeF,EAAAA,EAAAA,UAAmB,CACjDG,KAAM,GACNC,WAAY,GACZC,aAAc,GACdC,YAAa,GACbC,aAAc,MAETC,EAAeC,IAAoBT,EAAAA,EAAAA,UAAmB,KACtDU,EAAWC,IAAgBX,EAAAA,EAAAA,WAAS,IACpCY,EAASC,IAAcb,EAAAA,EAAAA,WAAS,IAChCc,EAAQC,IAAaf,EAAAA,EAAAA,WAAS,IAC9BgB,EAAeC,IAAoBjB,EAAAA,EAAAA,WAAS,IAC5CkB,EAAOC,IAAYnB,EAAAA,EAAAA,UAAwB,OAC3CoB,EAAaC,IAAkBrB,EAAAA,EAAAA,UAAwB,OAG9DsB,EAAAA,EAAAA,YAAU,KACRC,GAAe,GACd,CAAC3B,KAGJ0B,EAAAA,EAAAA,YAAU,KACJZ,GACFc,GACF,GACC,CAACd,IAGJ,MAyBMa,EAAgBE,UACpB,IAAK7B,EAGH,OAFAuB,EAAS,2BACTN,GAAW,GAIb,IACEa,QAAQC,IAAI,+CACZ,MAAMC,QAAgBC,EAAAA,EAAAA,KAAOC,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAanC,EAAYoC,MAE9D,GAAIJ,EAAQK,SAAU,CACpB,MAAMC,EAAON,EAAQM,OACrBnC,EAAYmC,GACZhC,EAAY,CACVC,KAAM+B,EAAK/B,MAAQ,GACnBC,WAAY8B,EAAK9B,YAAc,GAC/BC,aAAc6B,EAAK7B,cAAgB,GACnCC,YAAa4B,EAAK5B,aAAe,GACjCC,aAAc2B,EAAK3B,cAAgB,KAErCmB,QAAQC,IAAI,gDACd,MACER,EAAS,0BACTO,QAAQC,IAAI,0CAEhB,CAAE,MAAOQ,GACPT,QAAQR,MAAM,4CAAwCiB,GACtDhB,EAAS,2BACX,CAAC,QACCN,GAAW,EACb,GAIIW,EAAqBC,UACzBR,GAAiB,GACjBI,EAAe,MAEf,IACEK,QAAQC,IAAI,wEAGZ,MAAMS,QAAoBC,EAAAA,EAAcC,mBAMxC,GAJA7B,EAAiB2B,GACjBV,QAAQC,IAAI,uCAAkCS,EAAYG,6CAGtDH,EAAYG,OAAS,EAAG,CAC1B,MAAMC,EAAc,IAAIJ,GAAaK,OACrCf,QAAQC,IAAI,8CAAqCa,EAAY,IAC7Dd,QAAQC,IAAI,6CAAoCa,EAAYA,EAAYD,OAAS,IAGjF,MAAMG,EAAkBN,EAAYO,MAAKC,GAAKA,EAAEC,cAAcC,SAAS,sBACjEC,EAAqBX,EAAYO,MAAKC,GAAKA,EAAEC,cAAcC,SAAS,yBAC1EpB,QAAQC,IAAI,uDAA8Ce,GAC1DhB,QAAQC,IAAI,0DAAiDoB,GACzDL,GACFhB,QAAQC,IAAI,gDAAuCe,GAEjDK,GACFrB,QAAQC,IAAI,mDAA0CoB,EAE1D,CAEF,CAAE,MAAOZ,GACPT,QAAQR,MAAM,iDAA6CiB,GAC3Dd,EAAe,iCACfZ,EAAiB,GACnB,CAAC,QACCQ,GAAiB,EACnB,GAII+B,EAAoBA,CAACC,EAAuBC,KAChDhD,GAAYiD,IAAI,IACXA,EACH,CAACF,GAAQC,KACR,EAuLL,OAAItC,GAEAwC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBC,SAAA,EAClCC,EAAAA,EAAAA,KAACC,EAAAA,EAAO,CAAC1D,SAAUA,KACnByD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,aACfE,EAAAA,EAAAA,KAAA,KAAAD,SAAG,+BAORxD,GAeHsD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBC,SAAA,EAClCC,EAAAA,EAAAA,KAACC,EAAAA,EAAO,CAAC1D,SAAUA,KACnByD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,oBAAmBC,SAAA,EAChCF,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CAAEC,QAAS,OAAQC,eAAgB,gBAAiBC,WAAY,SAAUC,aAAc,QAASP,SAAA,EAC3GC,EAAAA,EAAAA,KAAA,MAAIF,UAAU,aAAYC,SAAC,kBAC3BC,EAAAA,EAAAA,KAAA,UACEO,QArUgBrC,UAC1BC,QAAQC,IAAI,+DACZ,IAEEU,EAAAA,EAAc0B,aACdrC,QAAQC,IAAI,6CAGZ,MAAMS,QAAoBC,EAAAA,EAAcC,mBACxCZ,QAAQC,IAAI,qDAA4CS,EAAYG,OAAQ,WAC5Eb,QAAQC,IAAI,gDAAuCS,EAAY4B,MAAM,EAAG,KACxEtC,QAAQC,IAAI,+CAAsCS,EAAY4B,OAAO,KAGrE,MAAMC,EAAkB7B,EAAY8B,QAAO/D,GAAQA,EAAK0C,cAAcC,SAAS,cAC/EpB,QAAQC,IAAI,sDAA6CsC,EAAgB1B,QACzEb,QAAQC,IAAI,gDAAuCsC,EAErD,CAAE,MAAO/C,GACPQ,QAAQR,MAAM,qCAA4BA,EAC5C,CACAQ,QAAQC,IAAI,yCAA+B,EAiTjC8B,MAAO,CACLU,QAAS,cACTC,gBAAiB,UACjBC,MAAO,QACPC,OAAQ,OACRC,aAAc,MACdC,OAAQ,UACRC,SAAU,YACVnB,SACH,2CAMHF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,iBAAgBC,SAAA,EAC7BC,EAAAA,EAAAA,KAAA,OAAKF,UAAU,iBAAgBC,SAC5BxD,EAASK,KAAKuE,OAAO,GAAGC,iBAE3BvB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,MAAAD,SAAKxD,EAASK,QACdiD,EAAAA,EAAAA,MAAA,KAAGC,UAAU,cAAaC,SAAA,CAAC,gBAAcxD,EAAS8E,cACjD9E,EAAS+E,OAAQtB,EAAAA,EAAAA,KAAA,QAAMF,UAAU,aAAYC,SAAExD,EAAS+E,cAK7DzB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0BACJF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,eACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASK,WAElBiD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,iBACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAAS8E,iBAElBxB,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,mBACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASgF,YAElB1B,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,cACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASO,cAAgB,sBAElC+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,iBACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASQ,aAAe,sBAEjC8C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,mBACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASS,cAAgB,4BAMtC6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,cAAaC,SAAA,EAC1BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0BACF5C,IACA0C,EAAAA,EAAAA,MAAA,UACEC,UAAU,WACVS,QAASA,IAAMnD,GAAa,GAAM2C,SAAA,EAElCC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gBAAkB,sBAKpC3C,GACC0C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOwB,QAAQ,OAAMzB,SAAC,eACtBC,EAAAA,EAAAA,KAACyB,EAAAA,EAAS,CACRC,WAAS,EACT/B,MAAOjD,EAASE,KAChB+E,SAAWC,GAAMnC,EAAkB,OAAQmC,EAAEC,OAAOlC,OACpDmC,YAAY,kBACZC,UAAQ,QAGZlC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOwB,QAAQ,cAAazB,SAAC,iBAC7BC,EAAAA,EAAAA,KAACyB,EAAAA,EAAS,CACRC,WAAS,EACT/B,MAAOjD,EAASK,YAChB4E,SAAWC,GAAMnC,EAAkB,cAAemC,EAAEC,OAAOlC,OAC3DmC,YAAY,6BAKlBjC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,WAAUC,SAAA,EACvBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOwB,QAAQ,eAAczB,SAAC,cAC9BC,EAAAA,EAAAA,KAACyB,EAAAA,EAAS,CACRC,WAAS,EACT/B,MAAOjD,EAASI,aAChB6E,SAAWC,GAAMnC,EAAkB,eAAgBmC,EAAEC,OAAOlC,OAC5DmC,YAAY,4BAGhBjC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOwB,QAAQ,eAAczB,SAAC,mBAC9BC,EAAAA,EAAAA,KAACyB,EAAAA,EAAS,CACRC,WAAS,EACT/B,MAAOjD,EAASM,aAChB2E,SAAWC,GAAMnC,EAAkB,eAAgBmC,EAAEC,OAAOlC,OAC5DmC,YAAY,+BAKlBjC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,aAAYC,SAAA,EACzBC,EAAAA,EAAAA,KAAA,SAAOwB,QAAQ,aAAYzB,SAAC,iBAC5BC,EAAAA,EAAAA,KAACgC,EAAAA,EAAY,CACXN,WAAS,EACTO,QAAShF,EACTiF,eAAiBC,GAAWA,EAC5BxC,MAAOjD,EAASG,YAAc,KAC9B8E,SAAUA,CAACS,EAAOC,KAChB5C,EAAkB,aAAc4C,GAAY,GAAG,EAEjDC,SAAU7E,EACV8E,YAAcC,IACZxC,EAAAA,EAAAA,KAACyB,EAAAA,EAAS,IACJe,EACJV,YAAarE,EAAgB,qBAAuB,gBACpDE,QAASE,EACT4E,WAAY5E,EACZkE,UAAQ,EACRW,WAAY,IACPF,EAAOE,WACVC,cACE9C,EAAAA,EAAAA,MAAC+C,EAAAA,SAAc,CAAA7C,SAAA,CACZtC,GAAgBuC,EAAAA,EAAAA,KAAC6C,EAAAA,EAAgB,CAAC/B,MAAM,UAAUgC,KAAM,KAAS,KACjEN,EAAOE,WAAWC,yBASjC9C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,eAAcC,SAAA,EAC3BC,EAAAA,EAAAA,KAAA,UACEF,UAAU,gBACVS,QAxMGwC,KACnBpG,EAAY,CACVC,MAAc,OAARL,QAAQ,IAARA,OAAQ,EAARA,EAAUK,OAAQ,GACxBC,YAAoB,OAARN,QAAQ,IAARA,OAAQ,EAARA,EAAUM,aAAc,GACpCC,cAAsB,OAARP,QAAQ,IAARA,OAAQ,EAARA,EAAUO,eAAgB,GACxCC,aAAqB,OAARR,QAAQ,IAARA,OAAQ,EAARA,EAAUQ,cAAe,GACtCC,cAAsB,OAART,QAAQ,IAARA,OAAQ,EAARA,EAAUS,eAAgB,KAE1CI,GAAa,GACbQ,EAAS,KAAK,EAgME0E,SAAU/E,EAAOwC,SAClB,YAGDC,EAAAA,EAAAA,KAAA,UACEF,UAAU,cACVS,QAtXCrC,UACjB,GAAK7B,GAAgBE,EAMrB,GAAKG,EAASE,KAAKoG,OAInB,GAAKtG,EAASG,WAAWmG,OAAzB,CAKAxF,GAAU,GACVI,EAAS,MAET,IACEO,QAAQC,IAAI,gDAGZ,MAAM6E,EAAa,CACjBrG,KAAMF,EAASE,KACfC,WAAYH,EAASG,WACrBC,aAAcJ,EAASI,aACvBC,YAAaL,EAASK,YACtBC,aAAcN,EAASM,aACvBkG,UAAW,IAAIC,YAIXC,EAAAA,EAAAA,KAAU7E,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAanC,EAAYoC,KAAMwE,GACvD9E,QAAQC,IAAI,iDAGZD,QAAQC,IAAI,uDACZD,QAAQC,IAAI,gDAAuC7B,EAAS8E,YAC5DlD,QAAQC,IAAI,qCAA4B,CACtCxB,KAAMF,EAASE,KACfC,WAAYH,EAASG,WACrBC,aAAcJ,EAASI,aACvBC,YAAaL,EAASK,YACtBC,aAAcN,EAASM,eAIzB,MAAQ2B,KAAM0E,EAAgB1F,MAAO2F,SAAqBC,EAAAA,EACvDC,KAAK,iBACLC,OAAO,KACPC,GAAG,aAAcnH,EAAS8E,YAC1BsC,SAEH,GAAIL,EAAY,CACdnF,QAAQR,MAAM,kDAA8C2F,GAG5DnF,QAAQC,IAAI,sEACZ,MAAQO,KAAMiF,EAAWjG,MAAOkG,SAAmBN,EAAAA,EAChDC,KAAK,iBACLC,OAAO,KACPC,GAAG,MAAOrH,EAAYoC,KACtBkF,SAEH,GAAIE,EAAU,CACZ1F,QAAQR,MAAM,kDAA8CkG,GAC5D1F,QAAQC,IAAI,mFAGZ,IACE,MAAM0F,EAAgB,CACpBrF,IAAKpC,EAAYoC,IACjB4C,WAAY9E,EAAS8E,WACrBzE,KAAML,EAASK,KACf2E,MAAOhF,EAASgF,MAChB1E,WAAYN,EAASM,WACrBC,aAAcP,EAASO,cAAgB,GACvCC,YAAaR,EAASQ,aAAe,GACrCC,aAAcT,EAASS,cAAgB,GACvCsE,KAAM/E,EAAS+E,MAAQ,QAGzBnD,QAAQC,IAAI,wDAA+C0F,GAE3D,MAAQnF,KAAMoF,EAAepG,MAAOqG,SAAsBT,EAAAA,EACvDC,KAAK,iBACLS,OAAOH,GACPL,SACAE,SAEH,GAAIK,EAAa,CAIf,GAHA7F,QAAQR,MAAM,gDAA4CqG,GAGjC,UAArBA,EAAYE,MAAoBF,EAAYG,QAAQ5E,SAAS,sBAC/D,MAAM,IAAI6E,MAAM,0JAGlB,MAAM,IAAIA,MAAM,iCAAiCJ,EAAYG,UAC/D,CAEAhG,QAAQC,IAAI,oDAAgD2F,EAC9D,CAAE,MAAOC,GAEP,MADA7F,QAAQR,MAAM,8CAA0CqG,GAClD,IAAII,MAAM,iEAAiEd,EAAWa,UAC9F,CACF,MACEhG,QAAQC,IAAI,mDAA+CwF,GAE3DrH,EAAS8E,WAAauC,EAAUvC,UAEpC,MACElD,QAAQC,IAAI,+CAAsCiF,GAIpD,MAAQ1E,KAAM0F,EAAc1G,MAAO2G,SAAwBf,EAAAA,EACxDC,KAAK,iBACLe,OAAO,CACN3H,KAAMF,EAASE,KACfC,WAAYH,EAASG,WACrBC,aAAcJ,EAASI,aACvBC,YAAaL,EAASK,YACtBC,aAAcN,EAASM,eAGxB0G,GAAG,aAAcnH,EAAS8E,YAC1BoC,SAEH,GAAIa,EAAe,CAIjB,GAHAnG,QAAQR,MAAM,yCAAqC2G,GAGxB,UAAvBA,EAAcJ,MAAoBI,EAAcH,QAAQ5E,SAAS,sBACnE,MAAM,IAAI6E,MAAM,yJAGlB,MAAM,IAAIA,MAAM,2BAA2BE,EAAcH,UAC3D,CAEAhG,QAAQC,IAAI,iDACZD,QAAQC,IAAI,kCAA8BiG,GAG1C7H,GAAYoD,GAAQA,EAAO,IACtBA,EACHhD,KAAMF,EAASE,KACfC,WAAYH,EAASG,WACrBC,aAAcJ,EAASI,aACvBC,YAAaL,EAASK,YACtBC,aAAcN,EAASM,cACrB,OACJI,GAAa,GAGboH,MAAM,gCAER,CAAE,MAAO5F,GACPT,QAAQR,MAAM,wCAAoCiB,GAClDhB,EAAS,8CACX,CAAC,QACCJ,GAAU,EACZ,CArJA,MAFEI,EAAS,gCAJTA,EAAS,yBANTA,EAAS,0BAiKX,EAoNgB0E,SAAU/E,GAAUE,IAAkBf,EAASG,WAAWmG,OAAOjD,SAEhExC,EAAS,YAAc,wBAK9BsC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,eACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASK,WAElBiD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,iBACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASQ,aAAe,sBAEjC8C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,cACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASO,cAAgB,sBAElC+C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,mBACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASS,cAAgB,sBAElC6C,EAAAA,EAAAA,MAAA,OAAKC,UAAU,YAAWC,SAAA,EACxBC,EAAAA,EAAAA,KAAA,SAAAD,SAAO,iBACPC,EAAAA,EAAAA,KAAA,QAAAD,SAAOxD,EAASM,YAAc,2BAOrCc,IACCkC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,gBAAeC,SAAA,EAC5BC,EAAAA,EAAAA,KAAA,KAAGF,UAAU,gCACZnC,cAxNTkC,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBC,SAAA,EAClCC,EAAAA,EAAAA,KAACC,EAAAA,EAAO,CAAC1D,SAAUA,KACnByD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,OAAKC,UAAU,kBAAiBC,SAAA,EAC9BC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,uBACJC,EAAAA,EAAAA,KAAA,KAAAD,SAAIpC,GAAS,uCAwNf,C", "sources": ["components/Profile/Profile.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { doc, getDoc, updateDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { supabase } from '../../config/supabaseClient'; // Keep for user profile updates\nimport OfficeService from '../../services/officeService'; // Use OfficeService for office fetching\nimport Sidebar from '../shared/Sidebar';\nimport { Autocomplete, TextField, CircularProgress } from '@mui/material';\nimport './Profile.css';\n\ninterface UserData {\n  name: string;\n  employeeId: string;\n  email: string;\n  officeName: string;\n  divisionName?: string;\n  designation?: string;\n  mobileNumber?: string;\n  role?: string;\n}\n\ninterface FormData {\n  name: string;\n  officeName: string;\n  divisionName: string;\n  designation: string;\n  mobileNumber: string;\n}\n\nconst Profile: React.FC = () => {\n  const { currentUser } = useAuth();\n  const [userData, setUserData] = useState<UserData | null>(null);\n  const [formData, setFormData] = useState<FormData>({\n    name: '',\n    officeName: '',\n    divisionName: '',\n    designation: '',\n    mobileNumber: ''\n  });\n  const [officeOptions, setOfficeOptions] = useState<string[]>([]);\n  const [isEditing, setIsEditing] = useState(false);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [officeLoading, setOfficeLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [officeError, setOfficeError] = useState<string | null>(null);\n\n  // Fetch user data on component mount\n  useEffect(() => {\n    fetchUserData();\n  }, [currentUser]);\n\n  // Fetch office options when editing starts\n  useEffect(() => {\n    if (isEditing) {\n      fetchOfficeOptions();\n    }\n  }, [isEditing]);\n\n  // Debug function to test office fetching\n  const debugOfficeFetching = async () => {\n    console.log('🧪 === PROFILE DEBUG: Testing Office Fetching ===');\n    try {\n      // Clear cache first\n      OfficeService.clearCache();\n      console.log('🧪 Profile Debug: Cache cleared');\n\n      // Test OfficeService\n      const officeNames = await OfficeService.fetchOfficeNames();\n      console.log('🧪 Profile Debug: OfficeService returned', officeNames.length, 'offices');\n      console.log('🧪 Profile Debug: First 10 offices:', officeNames.slice(0, 10));\n      console.log('🧪 Profile Debug: Last 10 offices:', officeNames.slice(-10));\n\n      // Test specific searches\n      const divisionOffices = officeNames.filter(name => name.toLowerCase().includes('division'));\n      console.log('🧪 Profile Debug: Division offices found:', divisionOffices.length);\n      console.log('🧪 Profile Debug: Division offices:', divisionOffices);\n\n    } catch (error) {\n      console.error('🧪 Profile Debug: Error:', error);\n    }\n    console.log('🧪 === END PROFILE DEBUG ===');\n  };\n\n  // Fetch user data from Firebase\n  const fetchUserData = async () => {\n    if (!currentUser) {\n      setError('User not logged in');\n      setLoading(false);\n      return;\n    }\n\n    try {\n      console.log('🔍 Profile: Fetching user data...');\n      const userDoc = await getDoc(doc(db, 'employees', currentUser.uid));\n      \n      if (userDoc.exists()) {\n        const data = userDoc.data() as UserData;\n        setUserData(data);\n        setFormData({\n          name: data.name || '',\n          officeName: data.officeName || '',\n          divisionName: data.divisionName || '',\n          designation: data.designation || '',\n          mobileNumber: data.mobileNumber || ''\n        });\n        console.log('✅ Profile: User data loaded successfully');\n      } else {\n        setError('User profile not found');\n        console.log('❌ Profile: User document not found');\n      }\n    } catch (err) {\n      console.error('❌ Profile: Error fetching user data:', err);\n      setError('Failed to load user data');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch office options - Same logic as Registration screen using OfficeService\n  const fetchOfficeOptions = async () => {\n    setOfficeLoading(true);\n    setOfficeError(null);\n\n    try {\n      console.log('🏢 Profile: Fetching office options using OfficeService...');\n\n      // Use the same OfficeService as Registration screen - fetch ALL offices with comprehensive pagination\n      const officeNames = await OfficeService.fetchOfficeNames();\n\n      setOfficeOptions(officeNames);\n      console.log(`✅ Profile: Successfully loaded ${officeNames.length} office options using OfficeService`);\n\n      // Log some statistics for debugging\n      if (officeNames.length > 0) {\n        const sortedNames = [...officeNames].sort();\n        console.log('📊 Profile: Office range - First:', sortedNames[0]);\n        console.log('📊 Profile: Office range - Last:', sortedNames[sortedNames.length - 1]);\n\n        // Check for specific offices\n        const tirupurDivision = officeNames.find(o => o.toLowerCase().includes('tirupur division'));\n        const coimbatoreDivision = officeNames.find(o => o.toLowerCase().includes('coimbatore division'));\n        console.log('📊 Profile: Contains \"Tirupur division\":', !!tirupurDivision);\n        console.log('📊 Profile: Contains \"Coimbatore division\":', !!coimbatoreDivision);\n        if (tirupurDivision) {\n          console.log('📊 Profile: Found Tirupur division:', tirupurDivision);\n        }\n        if (coimbatoreDivision) {\n          console.log('📊 Profile: Found Coimbatore division:', coimbatoreDivision);\n        }\n      }\n\n    } catch (err) {\n      console.error('❌ Profile: Error fetching office options:', err);\n      setOfficeError('Failed to load office options');\n      setOfficeOptions([]); // Set empty array on error\n    } finally {\n      setOfficeLoading(false);\n    }\n  };\n\n  // Handle form input changes\n  const handleInputChange = (field: keyof FormData, value: string) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  // Save profile changes to both Firebase and Supabase\n  const handleSave = async () => {\n    if (!currentUser || !userData) {\n      setError('User data not available');\n      return;\n    }\n\n    // Validate required fields\n    if (!formData.name.trim()) {\n      setError('Name is required');\n      return;\n    }\n    if (!formData.officeName.trim()) {\n      setError('Office name is required');\n      return;\n    }\n\n    setSaving(true);\n    setError(null);\n\n    try {\n      console.log('💾 Profile: Saving profile data...');\n\n      // Prepare update data\n      const updateData = {\n        name: formData.name,\n        officeName: formData.officeName,\n        divisionName: formData.divisionName,\n        designation: formData.designation,\n        mobileNumber: formData.mobileNumber,\n        updatedAt: new Date()\n      };\n\n      // Update Firebase Firestore (employees collection)\n      await updateDoc(doc(db, 'employees', currentUser.uid), updateData);\n      console.log('✅ Profile: Firebase updated successfully');\n\n      // Update Supabase user_profiles table (same fields as Registration screen)\n      console.log('🔍 Profile: Attempting Supabase update...');\n      console.log('🔍 Profile: Employee ID for update:', userData.employeeId);\n      console.log('🔍 Profile: Update data:', {\n        name: formData.name,\n        officeName: formData.officeName,\n        divisionName: formData.divisionName,\n        designation: formData.designation,\n        mobileNumber: formData.mobileNumber,\n      });\n\n      // First, check if the record exists\n      const { data: existingRecord, error: checkError } = await supabase\n        .from('user_profiles')\n        .select('*')\n        .eq('employeeId', userData.employeeId)\n        .single();\n\n      if (checkError) {\n        console.error('❌ Profile: Error checking existing record:', checkError);\n\n        // Try alternative lookup by Firebase UID\n        console.log('🔄 Profile: Trying alternative lookup by Firebase UID...');\n        const { data: altRecord, error: altError } = await supabase\n          .from('user_profiles')\n          .select('*')\n          .eq('uid', currentUser.uid)\n          .single();\n\n        if (altError) {\n          console.error('❌ Profile: Alternative lookup also failed:', altError);\n          console.log('🔄 Profile: User record not found in Supabase. Creating new record...');\n\n          // Create the missing user record in Supabase\n          try {\n            const newUserRecord = {\n              uid: currentUser.uid,\n              employeeId: userData.employeeId,\n              name: userData.name,\n              email: userData.email,\n              officeName: userData.officeName,\n              divisionName: userData.divisionName || '',\n              designation: userData.designation || '',\n              mobileNumber: userData.mobileNumber || '',\n              role: userData.role || 'user'\n            };\n\n            console.log('🔄 Profile: Creating user record with data:', newUserRecord);\n\n            const { data: createdRecord, error: createError } = await supabase\n              .from('user_profiles')\n              .insert(newUserRecord)\n              .select()\n              .single();\n\n            if (createError) {\n              console.error('❌ Profile: Failed to create user record:', createError);\n\n              // Check for RLS error\n              if (createError.code === '42501' || createError.message.includes('row-level security')) {\n                throw new Error('Database security settings are blocking profile creation. Please contact your administrator to disable Row Level Security for the user_profiles table.');\n              }\n\n              throw new Error(`Failed to create user record: ${createError.message}`);\n            }\n\n            console.log('✅ Profile: Successfully created user record:', createdRecord);\n          } catch (createError) {\n            console.error('❌ Profile: Error creating user record:', createError);\n            throw new Error(`Unable to create user profile. Please contact support. Error: ${checkError.message}`);\n          }\n        } else {\n          console.log('✅ Profile: Found record using Firebase UID:', altRecord);\n          // Update userData with the correct employeeId for the update\n          userData.employeeId = altRecord.employeeId;\n        }\n      } else {\n        console.log('🔍 Profile: Existing record found:', existingRecord);\n      }\n\n      // Perform the update\n      const { data: updateResult, error: supabaseError } = await supabase\n        .from('user_profiles')\n        .update({\n          name: formData.name,\n          officeName: formData.officeName,\n          divisionName: formData.divisionName,\n          designation: formData.designation,\n          mobileNumber: formData.mobileNumber,\n          // Note: No updated_at field - table only has columns from Registration\n        })\n        .eq('employeeId', userData.employeeId)\n        .select(); // Add select to get the updated record\n\n      if (supabaseError) {\n        console.error('❌ Profile: Supabase update error:', supabaseError);\n\n        // Check for RLS error\n        if (supabaseError.code === '42501' || supabaseError.message.includes('row-level security')) {\n          throw new Error('Database security settings are blocking profile updates. Please contact your administrator to disable Row Level Security for the user_profiles table.');\n        }\n\n        throw new Error(`Supabase update failed: ${supabaseError.message}`);\n      }\n\n      console.log('✅ Profile: Supabase updated successfully');\n      console.log('✅ Profile: Updated record:', updateResult);\n\n      // Update local state with all updated fields\n      setUserData(prev => prev ? {\n        ...prev,\n        name: formData.name,\n        officeName: formData.officeName,\n        divisionName: formData.divisionName,\n        designation: formData.designation,\n        mobileNumber: formData.mobileNumber\n      } : null);\n      setIsEditing(false);\n\n      // Show success message\n      alert('Profile updated successfully!');\n\n    } catch (err) {\n      console.error('❌ Profile: Error saving profile:', err);\n      setError('Failed to update profile. Please try again.');\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // Cancel editing\n  const handleCancel = () => {\n    setFormData({\n      name: userData?.name || '',\n      officeName: userData?.officeName || '',\n      divisionName: userData?.divisionName || '',\n      designation: userData?.designation || '',\n      mobileNumber: userData?.mobileNumber || ''\n    });\n    setIsEditing(false);\n    setError(null);\n  };\n\n  if (loading) {\n    return (\n      <div className=\"dashboard-container\">\n        <Sidebar userData={userData} />\n        <div className=\"main-content\">\n          <div className=\"loading-container\">\n            <div className=\"spinner\"></div>\n            <p>Loading profile...</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!userData) {\n    return (\n      <div className=\"dashboard-container\">\n        <Sidebar userData={userData} />\n        <div className=\"main-content\">\n          <div className=\"error-container\">\n            <h2>Profile Not Found</h2>\n            <p>{error || 'Unable to load user profile'}</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n      <div className=\"main-content\">\n        <div className=\"profile-container\">\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>\n            <h1 className=\"page-title\">User Profile</h1>\n            <button\n              onClick={debugOfficeFetching}\n              style={{\n                padding: '0.5rem 1rem',\n                backgroundColor: '#dc3545',\n                color: 'white',\n                border: 'none',\n                borderRadius: '4px',\n                cursor: 'pointer',\n                fontSize: '0.875rem'\n              }}\n            >\n              🧪 Debug Office Fetching\n            </button>\n          </div>\n          \n          {/* Profile Header */}\n          <div className=\"profile-header\">\n            <div className=\"profile-avatar\">\n              {userData.name.charAt(0).toUpperCase()}\n            </div>\n            <div className=\"profile-info\">\n              <h2>{userData.name}</h2>\n              <p className=\"employee-id\">Employee ID: {userData.employeeId}</p>\n              {userData.role && <span className=\"role-badge\">{userData.role}</span>}\n            </div>\n          </div>\n\n          {/* Personal Information Card */}\n          <div className=\"profile-card\">\n            <h3>Personal Information</h3>\n            <div className=\"info-grid\">\n              <div className=\"info-item\">\n                <label>Full Name</label>\n                <span>{userData.name}</span>\n              </div>\n              <div className=\"info-item\">\n                <label>Employee ID</label>\n                <span>{userData.employeeId}</span>\n              </div>\n              <div className=\"info-item\">\n                <label>Email Address</label>\n                <span>{userData.email}</span>\n              </div>\n              <div className=\"info-item\">\n                <label>Division</label>\n                <span>{userData.divisionName || 'Not specified'}</span>\n              </div>\n              <div className=\"info-item\">\n                <label>Designation</label>\n                <span>{userData.designation || 'Not specified'}</span>\n              </div>\n              <div className=\"info-item\">\n                <label>Mobile Number</label>\n                <span>{userData.mobileNumber || 'Not specified'}</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Editable Information Card */}\n          <div className=\"profile-card\">\n            <div className=\"card-header\">\n              <h3>Editable Information</h3>\n              {!isEditing && (\n                <button\n                  className=\"edit-btn\"\n                  onClick={() => setIsEditing(true)}\n                >\n                  <i className=\"fas fa-edit\"></i> Edit Profile\n                </button>\n              )}\n            </div>\n            \n            {isEditing ? (\n              <div className=\"edit-form\">\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label htmlFor=\"name\">Full Name</label>\n                    <TextField\n                      fullWidth\n                      value={formData.name}\n                      onChange={(e) => handleInputChange('name', e.target.value)}\n                      placeholder=\"Enter full name\"\n                      required\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label htmlFor=\"designation\">Designation</label>\n                    <TextField\n                      fullWidth\n                      value={formData.designation}\n                      onChange={(e) => handleInputChange('designation', e.target.value)}\n                      placeholder=\"Enter designation\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"form-row\">\n                  <div className=\"form-group\">\n                    <label htmlFor=\"divisionName\">Division</label>\n                    <TextField\n                      fullWidth\n                      value={formData.divisionName}\n                      onChange={(e) => handleInputChange('divisionName', e.target.value)}\n                      placeholder=\"Enter division name\"\n                    />\n                  </div>\n                  <div className=\"form-group\">\n                    <label htmlFor=\"mobileNumber\">Mobile Number</label>\n                    <TextField\n                      fullWidth\n                      value={formData.mobileNumber}\n                      onChange={(e) => handleInputChange('mobileNumber', e.target.value)}\n                      placeholder=\"Enter mobile number\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"form-group\">\n                  <label htmlFor=\"officeName\">Office Name</label>\n                  <Autocomplete\n                    fullWidth\n                    options={officeOptions}\n                    getOptionLabel={(option) => option}\n                    value={formData.officeName || null}\n                    onChange={(event, newValue) => {\n                      handleInputChange('officeName', newValue || '');\n                    }}\n                    disabled={officeLoading}\n                    renderInput={(params) => (\n                      <TextField\n                        {...params}\n                        placeholder={officeLoading ? 'Loading offices...' : 'Select Office'}\n                        error={!!officeError}\n                        helperText={officeError}\n                        required\n                        InputProps={{\n                          ...params.InputProps,\n                          endAdornment: (\n                            <React.Fragment>\n                              {officeLoading ? <CircularProgress color=\"inherit\" size={20} /> : null}\n                              {params.InputProps.endAdornment}\n                            </React.Fragment>\n                          ),\n                        }}\n                      />\n                    )}\n                  />\n                </div>\n\n                <div className=\"form-actions\">\n                  <button\n                    className=\"btn-secondary\"\n                    onClick={handleCancel}\n                    disabled={saving}\n                  >\n                    Cancel\n                  </button>\n                  <button\n                    className=\"btn-primary\"\n                    onClick={handleSave}\n                    disabled={saving || officeLoading || !formData.officeName.trim()}\n                  >\n                    {saving ? 'Saving...' : 'Save Changes'}\n                  </button>\n                </div>\n              </div>\n            ) : (\n              <div className=\"info-grid\">\n                <div className=\"info-item\">\n                  <label>Full Name</label>\n                  <span>{userData.name}</span>\n                </div>\n                <div className=\"info-item\">\n                  <label>Designation</label>\n                  <span>{userData.designation || 'Not specified'}</span>\n                </div>\n                <div className=\"info-item\">\n                  <label>Division</label>\n                  <span>{userData.divisionName || 'Not specified'}</span>\n                </div>\n                <div className=\"info-item\">\n                  <label>Mobile Number</label>\n                  <span>{userData.mobileNumber || 'Not specified'}</span>\n                </div>\n                <div className=\"info-item\">\n                  <label>Office Name</label>\n                  <span>{userData.officeName || 'Not specified'}</span>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Error Message */}\n          {error && (\n            <div className=\"error-message\">\n              <i className=\"fas fa-exclamation-triangle\"></i>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Profile;\n"], "names": ["Profile", "currentUser", "useAuth", "userData", "setUserData", "useState", "formData", "setFormData", "name", "officeName", "divisionName", "designation", "mobileNumber", "officeOptions", "setOfficeOptions", "isEditing", "setIsEditing", "loading", "setLoading", "saving", "setSaving", "officeLoading", "setOfficeLoading", "error", "setError", "officeError", "setOfficeError", "useEffect", "fetchUserData", "fetchOfficeOptions", "async", "console", "log", "userDoc", "getDoc", "doc", "db", "uid", "exists", "data", "err", "officeNames", "OfficeService", "fetchOfficeNames", "length", "sortedNames", "sort", "tirupurDivision", "find", "o", "toLowerCase", "includes", "coimbatoreDivision", "handleInputChange", "field", "value", "prev", "_jsxs", "className", "children", "_jsx", "Sidebar", "style", "display", "justifyContent", "alignItems", "marginBottom", "onClick", "clearCache", "slice", "divisionOffices", "filter", "padding", "backgroundColor", "color", "border", "borderRadius", "cursor", "fontSize", "char<PERSON>t", "toUpperCase", "employeeId", "role", "email", "htmlFor", "TextField", "fullWidth", "onChange", "e", "target", "placeholder", "required", "Autocomplete", "options", "getOptionLabel", "option", "event", "newValue", "disabled", "renderInput", "params", "helperText", "InputProps", "endAdornment", "React", "CircularProgress", "size", "handleCancel", "trim", "updateData", "updatedAt", "Date", "updateDoc", "existingRecord", "checkError", "supabase", "from", "select", "eq", "single", "altRecord", "altError", "newUserRecord", "createdRecord", "createError", "insert", "code", "message", "Error", "updateResult", "supabaseError", "update", "alert"], "sourceRoot": ""}