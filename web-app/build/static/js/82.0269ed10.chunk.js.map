{"version": 3, "file": "static/js/82.0269ed10.chunk.js", "mappings": "2NAEO,SAASA,EAAoBC,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,EACoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,S,aCOvD,MASMC,GAAWC,EAAAA,EAAAA,IAAOC,EAAAA,EAAO,CAC7BC,KAAM,UACNN,KAAM,QAFSI,CAGd,CACDG,SAAU,WAyDZ,EAvD0BC,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,aAEF,UACJO,EAAS,OACTC,GAAS,KACNC,GACDJ,EACEK,EAAa,IACdL,EACHG,UAEIG,EA7BkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoBpB,EAAqBkB,EAAQ,EAsB1CG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKlB,EAAU,CACjCU,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BU,UAAWT,EAAS,OAAIU,EACxBd,IAAKA,EACLM,WAAYA,KACTD,GAEP,G,kEC/CO,SAASU,EAA4BzB,GAC1C,OAAOC,EAAAA,EAAAA,IAAqB,kBAAmBD,EACjD,CACA,MACA,GAD4BE,EAAAA,EAAAA,GAAuB,kBAAmB,CAAC,OAAQ,YAAa,QAAS,QAAS,UAAW,a,+DCFzH,MAAMwB,EAAQ,CACZ,CAAEC,MAAO,cAAeC,MAAO,QAC/B,CAAED,MAAO,aAAcC,MAAO,gBAC9B,CAAED,MAAO,oBAAqBC,MAAO,MACrC,CAAED,MAAO,MAAOC,MAAO,oBAgBzB,EAb6BC,KAEzBR,EAAAA,EAAAA,KAAA,OAAKR,UAAU,aAAYiB,SACxBJ,EAAMK,KAAI,CAACC,EAAMC,KAChBC,EAAAA,EAAAA,MAAA,OAAKrB,UAAW,kBAAkBoB,IAAQH,SAAA,EACxCT,EAAAA,EAAAA,KAAA,MAAAS,SAAKE,EAAKL,SACVN,EAAAA,EAAAA,KAAA,KAAGR,UAAU,aAAYiB,SAAEE,EAAKJ,UAFcK,M,mICJxD,MAkBA,EAlBoCE,IAA2C,IAA1C,OAAEC,EAAM,QAAEC,EAAO,MAAEV,EAAK,SAAEG,GAAUK,EACvE,OAAKC,GAGHf,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAgByB,QAASD,EAAQP,UAC9CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAgByB,QAASC,GAAKA,EAAEC,kBAAkBV,SAAA,EAC/DI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BT,EAAAA,EAAAA,KAAA,MAAAS,SAAKH,KACLN,EAAAA,EAAAA,KAAA,UAAQR,UAAU,eAAeyB,QAASD,EAAQP,SAAC,aAErDT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,aAAYiB,SACxBA,SAVW,IAaZ,E,cCpBH,MAeMW,EAAaA,CAACC,EAAgBC,KACzC,MAAMC,EAAOD,EAAcE,MAAKC,GAAKA,EAAEC,KAAOL,IAC9C,QAAOE,IAAQA,EAAKI,QAAgB,EAIzBC,EAAaA,CAACP,EAAgBC,KACjCA,EAAcO,MAAKJ,GAAKA,EAAEE,WAAaN,IAIpCS,EAAiBC,IAC5B,MAAMrB,EAAmC,CAAC,EACpCsB,EAAoB,GAW1B,OAVAD,EAAKE,SAAQC,IACXxB,EAAIwB,EAAKR,IAAM,IAAKQ,EAAMzB,SAAU,GAAI,IAE1CsB,EAAKE,SAAQC,IAC+B,IAADC,EAArCD,EAAKP,UAAYjB,EAAIwB,EAAKP,UACD,QAA3BQ,EAAAzB,EAAIwB,EAAKP,UAAUlB,gBAAQ,IAAA0B,GAA3BA,EAA6BC,KAAK1B,EAAIwB,EAAKR,KAE3CM,EAAMI,KAAK1B,EAAIwB,EAAKR,IACtB,IAEKM,CAAK,EAIDK,EAAsBA,CAACV,EAAkBL,KACpD,IAAIgB,EAAwB,GAC5B,MAAM7B,EAAWa,EAAciB,QAAOd,GAAKA,EAAEE,WAAaA,IAC1D,IAAK,MAAMa,KAAS/B,EAClB6B,EAAYF,KAAKI,EAAMd,IACvBY,EAAcA,EAAYG,OAAOJ,EAAoBG,EAAMd,GAAIJ,IAEjE,OAAOgB,CAAW,ECxBPI,EAAqBpD,IAChC,MAAM,WACJqD,EAAU,cACVC,EAAa,aACbC,EAAY,gBACZC,EAAe,UACfC,EAAS,aACTC,EAAY,aACZC,EAAY,gBACZC,EAAe,WACfC,EAAU,cACVC,EAAa,aACbC,EAAY,SACZC,EAAQ,WACRC,EAAU,oBACVC,EAAmB,mBACnBC,EAAkB,cAClBC,EAAa,UACbC,EAAS,eACTC,EAAc,iBACdC,EAAgB,gBAChBC,EAAe,0BACfC,GACEzE,EAEE0E,GAAkBC,EAAAA,EAAAA,cAAYC,UAClCb,GAAa,GACb,IACE,MACMc,SADsBC,EAAAA,EAAAA,KAAQC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,gBACXC,KAAK7D,KAAI8D,IAAG,CAAO9C,GAAI8C,EAAI9C,MAAO8C,EAAIC,WAC9E7B,EAAcuB,EAChB,CAAE,MAAOO,GACPpB,EAAS,+BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,IACC,CAACT,EAAeS,EAAcC,IA+IjC,MAAO,CACLU,kBACAa,iBAzIuBX,UACvB,IAAKnB,IAAcE,EAEjB,YADAK,EAAS,qCAGXD,GAAa,GAEb,QAbuBa,WACvB,MAAMY,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc5C,GAErC,aADsBqD,EAAAA,EAAAA,IAAOD,IACdE,QAAQ,EASGC,CAAiBlC,GAIzC,OAFAO,EAAS,+DACTD,GAAa,GAGfA,GAAa,GACbG,GAAoB,EAAK,EA6HzB0B,oBA1H0BhB,UAAa,IAADiB,EACtC,IAAKpC,IAAcE,EAGf,OAFAK,EAAS,6CACTE,GAAoB,GAGxB,IAAI4B,EAA+B,KAChB,qBAAfjC,GAAqCN,EACvCuC,EAAgBvC,EACQ,qBAAfM,EACTiC,EAAgB,KACPvC,GAA+B,qBAAfM,EACvBiC,EAAgBvC,EACRA,GAA+B,qBAAfM,IACxBiC,EAAgB,MAGpB,MACMC,EAAU,GADGD,EAA4D,QAA/CD,EAAGxC,EAAWnB,MAAKC,GAAKA,EAAEC,KAAO0D,WAAc,IAAAD,OAAA,EAA5CA,EAA8CG,KAAO,iBACvDvC,IAAYwC,QAAQ,OAAQ,KAE7D,IACElC,GAAa,GACbG,GAAoB,GACpB,MAAMgC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcvB,IAC9B0C,KAAMC,EAAeC,MAAOC,GD/GRtF,KAChC,MAAMuF,EAAOvF,EACVwF,MAAM,IACNC,QAAO,CAACC,EAAKC,IAASD,EAAMC,EAAKC,WAAW,IAAI,GAE7CC,EAAQ,CAACC,EAAAA,IAAUC,EAAAA,IAAWC,EAAAA,IAAOC,EAAAA,KACrCC,EAAS,CAAC,UAAW,UAAW,UAAW,UAAW,WAK5D,MAAO,CAAEf,KAHIU,EAAMN,EAAOM,EAAMM,QAGjBd,MAFDa,EAAOX,EAAOW,EAAOC,QAEb,ECoGqCC,CAAkBzD,SAEnE0D,EAAAA,EAAAA,IAAOnB,EAAS,CACpB9D,GAAIqB,EACJzC,MAAO2C,EACPqC,KAAMD,EACN1D,SAAUyD,EACVwB,aAAa,IAAIC,MAAOC,cACxBrB,KAAMC,EAAczG,KACpB0G,MAAOC,EACPmB,OAAQ,GACRC,QAAQ,EACRC,OAAQlE,UAGJiB,IAENhB,EAAa,IACbE,EAAgB,IAChBO,GAAmB,GACnBL,EAAc,IACdN,EAAgBC,GAChBQ,EAAW,WAAWN,qCACtBiE,YAAW,IAAM3D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPpB,EAAS,yDACTqB,QAAQC,MAAM,uBAAwBF,EACxC,CAAC,QACCrB,GAAa,EACf,GAqEA8D,eAlEsB5F,IACtBqC,EAAerC,GACf2B,EAAgB3B,EAAKjB,OACrBuD,GAAiB,EAAK,EAgEtBuD,iBA7DuBlD,UACvB,MAAMmD,EAAc1E,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,IAClD,GAAKwE,GAAgBpE,EACrB,IACEI,GAAa,GACb,MAAMmC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc+C,EAAY3F,UAC5C4F,EAAAA,EAAAA,IAAU9B,EAAS,CAAElF,MAAO2C,EAAc2D,aAAa,IAAIC,MAAOC,sBAClE9C,IACNH,GAAiB,GACjBD,EAAe,MACfV,EAAgB,IAChBK,EAAW,gCACX2D,YAAW,IAAM3D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,GA4CAkE,kBAzCyBlG,IACzByC,EAAgBzC,GAChB0C,GAA0B,EAAK,EAwC/ByD,oBArC0BtD,UAC1B,GAAKrB,EAAL,CACAQ,GAAa,GACb,IACE,MAAMoE,GAAQC,EAAAA,EAAAA,IAAWpD,EAAAA,IACnBqD,EAAiBtF,EAAoBQ,EAAcF,GACnDiF,EAAc,CAAC/E,KAAiB8E,GAEtC,IAAK,MAAMjG,KAAMkG,EACfH,EAAMI,QAAOrD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc5C,IACnC+F,EAAMI,QAAOrD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAAS5C,UAE1B+F,EAAMK,eACN9D,IAEND,GAA0B,GAC1BD,EAAgB,MAChBhB,EAAgB,IAChBY,EAAc,MACdC,EAAU,IACVJ,EAAW,yDACX2D,YAAW,IAAM3D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,CA1ByB,CA0BzB,EAWD,E,cC1LI,MC0EP,EA3FkDvC,IAS3C,IAT4C,WACjD6B,EAAU,aACVE,EAAY,aACZkF,EAAY,WACZ5E,EAAU,eACV6E,EAAc,UACdC,EAAS,eACTC,EAAc,gBACdC,GACDrH,EACC,MAAMsH,EAAoB,SAACC,GAAwD,IAArCC,EAAKC,UAAA9B,OAAA,QAAAtG,IAAAoI,UAAA,GAAAA,UAAA,GAAG,EACpD,OAAOF,EAAMG,SAAQjH,IAEnB,IAAIkH,EAAelH,EAAKjB,MAYxB,OATKmI,GAAwC,KAAxBA,EAAaC,SAG9BD,EADc,QAAZlH,EAAKG,IAAgBH,EAAKG,GAAGiH,cAAcC,SAAS,OACvC,MAEA,oBAIZ,EACL5I,EAAAA,EAAAA,KAAA,UAAsBO,MAAOgB,EAAKG,GAAImH,MAAO,CAAEC,YAAwB,GAARR,EAAH,MAAoB7H,SAC7E,GAAG,KAAKsI,OAAOT,MAAUG,KADflH,EAAKG,OAGdH,EAAKd,UAAYc,EAAKd,SAASgG,OAAS,EAAI2B,EAAkB7G,EAAKd,SAAU6H,EAAQ,GAAK,GAC/F,GAEL,EAkBA,OACEzH,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAeiB,SAAA,EAC5BI,EAAAA,EAAAA,MAAA,UACEN,MAAOsC,EACPmG,SApBoB9H,IACxB,MAAM+H,EAAkB/H,EAAEgI,OAAO3I,MACjCwH,EAAakB,EAAgB,EAmBzBzJ,UAAU,cACV2J,SAAUlB,EAAUxH,SAAA,EAEpBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAEwH,EAAY,qBAAuB,gCACpDG,EAAkBtG,EAAca,QAGnC3C,EAAAA,EAAAA,KAAA,OAAKR,UAAU,4BAA2BiB,UACxCI,EAAAA,EAAAA,MAAA,UACEN,MAAO4C,EACP6F,SA1BoB9H,IAC1B,MAAMkI,EAAYlI,EAAEgI,OAAO3I,MAC3ByH,EAAeoB,GAEG,qBAAdA,GAAkD,qBAAdA,EACtClB,IACuB,kBAAdkB,GACTjB,GACF,EAmBM3I,UAAU,8BAA6BiB,SAAA,EAEvCT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,sBACjBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,mBAAmB4I,WAAYtG,EAAapC,SAAC,2BAG1DoC,IACChC,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,EACET,EAAAA,EAAAA,KAAA,UAAQO,MAAM,mBAAkBE,SAAC,0BAGjCT,EAAAA,EAAAA,KAAA,UACEO,MAAM,gBACN4I,UAAWvH,EAAWiB,EAAcF,IAAevB,EAAWyB,EAAcF,GAAYlC,SACzF,mDAOL,ECxDV,EAnCsDK,IAK/C,IALgD,aACrD+B,EAAY,WACZF,EAAU,WACV2G,EAAU,aACVC,GACDzI,EACC,MAAM0I,EAAmB7G,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,IAEvD,OAAK2G,GAKH3I,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kBAAiBiB,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,oBAAkB+I,EAAiBlJ,MAAM,QAC7CO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMqI,EAAWE,GAC1BhK,UAAU,kDACV2J,UAAWtG,EAAapC,SAAA,CAEvBtB,EAAAA,cAAoBsK,EAAAA,KAAoC,iBAE3D5I,EAAAA,EAAAA,MAAA,UACEI,QAASA,IAAMsI,EAAa1G,GAC5BrD,UAAU,8CACV2J,UAAWtG,EAAapC,SAAA,CAEvBtB,EAAAA,cAAoBuK,EAAAA,KAAqC,0BAnBzD,IAsBD,ECyNV,EAxPwD5I,IAKjD,IAAD6I,EAAA,IALmD,MACvDC,EAAK,MACLhJ,EAAK,SACLiJ,EAAQ,SACRC,GACDhJ,EACC,MAAMiJ,EAAqBA,CAACC,EAAkBzJ,EAAe0J,KAC3D,MAAMC,EAAa,IAAKN,EAAMO,SAAW,IACzCD,EAAWF,GAAY,IAAKE,EAAWF,GAAW,CAACC,GAAM1J,GACzDsJ,EAASjJ,EAAO,IAAKgJ,EAAOO,QAASD,GAAa,EAa9CE,EAA4BlJ,IAChC,MAAM,MAAEX,EAAK,KAAE8J,GAASnJ,EAAEgI,OAC1B,IAAIoB,EAAuB/J,EACd,aAAT8J,IACFC,EAAmBpJ,EAAEgI,OAA4BqB,SAEnDV,EAASjJ,EAAO,IAAKgJ,EAAOY,aAAcF,GAAkB,EAG9D,OACEzJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,8BAA6BiB,SAAA,EAC1CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gEAA+DiB,SAAA,EAC5ET,EAAAA,EAAAA,KAAA,UAAAS,SAASmJ,EAAMa,OAAS,kBAAyB,KAAGb,EAAMS,KAAK,KAC/DxJ,EAAAA,EAAAA,MAAA,UAAQI,QAASA,IAAM6I,EAASlJ,GAAQpB,UAAU,wBAAuBiB,SAAA,CACtEtB,EAAAA,cAAoBuK,EAAAA,KAAqC,iBAG9D7I,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,YAAWiB,SAAA,EAExBI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,cAAc9J,IAASpB,UAAU,aAAYiB,SAAC,YAC9DI,EAAAA,EAAAA,MAAA,UACEa,GAAI,cAAcd,IAClBpB,UAAU,eACVe,MAAOqJ,EAAMS,KACbrB,SAAW9H,GAAM2I,EAASjJ,EAAO,IAC5BgJ,EACHS,KAAMnJ,EAAEgI,OAAO3I,MACf4J,QAAwB,aAAfP,EAAMS,MAAsC,UAAfT,EAAMS,MAAmC,mBAAfT,EAAMS,UAA4BlK,EAAYyJ,EAAMO,QACpHQ,YAA4B,YAAff,EAAMS,MAAqC,WAAfT,EAAMS,UAAoBlK,EAAYyJ,EAAMe,cACpFlK,SAAA,EAEHT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,UACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,cACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,YACvBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,UACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,cACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,QAAOE,SAAC,iBACtBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,uBACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,iBAAgBE,SAAC,oBAC/BT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,YACvBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,iBACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,UAASE,SAAC,oBACxBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,kBAI3BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,eAAe9J,IAASpB,UAAU,aAAYiB,SAAC,aAC/DT,EAAAA,EAAAA,KAAA,SACE0B,GAAI,eAAed,IACnByJ,KAAK,OACL7K,UAAU,eACVe,MAAOqJ,EAAMa,MACbzB,SAAW9H,GAAM2I,EAASjJ,EAAO,IAAIgJ,EAAOa,MAAOvJ,EAAEgI,OAAO3I,QAC5DqK,UAAQ,OAIX,CAAC,OAAQ,WAAY,SAAU,QAAQhC,SAASgB,EAAMS,QACrDxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,qBAAqB9J,IAASpB,UAAU,aAAYiB,SAAC,mBACrET,EAAAA,EAAAA,KAAA,SACE0B,GAAI,qBAAqBd,IACzByJ,KAAK,OACL7K,UAAU,eACVe,MAAOqJ,EAAMe,aAAe,GAC5B3B,SAAW9H,GAAM2I,EAASjJ,EAAO,IAAIgJ,EAAOe,YAAazJ,EAAEgI,OAAO3I,aAKxD,WAAfqJ,EAAMS,OACLxJ,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,EACEI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,aAAa9J,IAASpB,UAAU,aAAYiB,SAAC,iBAC7DT,EAAAA,EAAAA,KAAA,SACE0B,GAAI,aAAad,IACjByJ,KAAK,SACL7K,UAAU,eACVe,WAAqBJ,IAAdyJ,EAAMiB,IAAoB,GAAKjB,EAAMiB,IAC5C7B,SAAW9H,GAAM2I,EAASjJ,EAAO,IAAIgJ,EAAOiB,IAAwB,KAAnB3J,EAAEgI,OAAO3I,WAAeJ,EAAY2K,WAAW5J,EAAEgI,OAAO3I,eAG7GM,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,aAAa9J,IAASpB,UAAU,aAAYiB,SAAC,iBAC7DT,EAAAA,EAAAA,KAAA,SACE0B,GAAI,aAAad,IACjByJ,KAAK,SACL7K,UAAU,eACVe,WAAqBJ,IAAdyJ,EAAMmB,IAAoB,GAAKnB,EAAMmB,IAC5C/B,SAAW9H,GAAM2I,EAASjJ,EAAO,IAAIgJ,EAAOmB,IAAwB,KAAnB7J,EAAEgI,OAAO3I,WAAeJ,EAAY2K,WAAW5J,EAAEgI,OAAO3I,iBAMhH,CAAC,WAAY,QAAS,kBAAkBqI,SAASgB,EAAMS,QACtDxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kCAAiCiB,SAAA,EAC9CT,EAAAA,EAAAA,KAAA,SAAOR,UAAU,aAAYiB,SAAC,cAChB,QADiCkJ,EAC9CC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAejJ,KAAI,CAACsK,EAAKhB,KACxBnJ,EAAAA,EAAAA,MAAA,OAAoBrB,UAAU,mBAAkBiB,SAAA,EAC9CT,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACL7K,UAAU,eACVmL,YAAY,eACZpK,MAAOyK,EAAIP,MACXzB,SAAW9H,GAAM6I,EAAmBC,EAAU9I,EAAEgI,OAAO3I,MAAO,YAEhEP,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACL7K,UAAU,eACVmL,YAAY,eACZpK,MAAOyK,EAAIzK,MACXyI,SAAW9H,GAAM6I,EAAmBC,EAAU9I,EAAEgI,OAAO3I,MAAO,YAEhEP,EAAAA,EAAAA,KAAA,UAAQqK,KAAK,SAASpJ,QAASA,IAzHvB+I,KAAsB,IAADiB,EACzC,MAAMf,EAA0B,QAAhBe,EAAGrB,EAAMO,eAAO,IAAAc,OAAA,EAAbA,EAAe1I,QAAO,CAAC2I,EAAGC,IAAMA,IAAMnB,IACzDH,EAASjJ,EAAO,IAAKgJ,EAAOO,QAASD,GAAa,EAuHDkB,CAAapB,GAAWxK,UAAU,yBAAwBiB,SAAC,aAfxFuJ,MAoBZhK,EAAAA,EAAAA,KAAA,UAAQqK,KAAK,SAASpJ,QAnIdoK,KAChB,MAAMnB,EAAa,IAAKN,EAAMO,SAAW,GAAK,CAAEM,MAAO,GAAIlK,MAAO,KAClEsJ,EAASjJ,EAAO,IAAKgJ,EAAOO,QAASD,GAAa,EAiIA1K,UAAU,2BAA0BiB,SAAC,kBAOlF,CAAC,OAAQ,WAAY,SAAU,QAAQmI,SAASgB,EAAMS,QACnDxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACvBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,uBAAuB9J,IAASpB,UAAU,aAAYiB,SAAC,qBACvET,EAAAA,EAAAA,KAAA,SACI0B,GAAI,uBAAuBd,IAC3ByJ,KAAqB,WAAfT,EAAMS,KAAoB,SAA0B,SAAfT,EAAMS,KAAkB,OAAS,OAC5E7K,UAAU,eACVe,WAA8BJ,IAAvByJ,EAAMY,aAA6B,GAAKc,OAAO1B,EAAMY,cAC5DxB,SAAUoB,QAKL,aAAfR,EAAMS,MAAsC,WAAfT,EAAMS,QACjCxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,wBAAuBiB,SAAA,EAClCT,EAAAA,EAAAA,KAAA,SACI0B,GAAI,uBAAuBd,IAC3ByJ,KAAK,WACL7K,UAAU,mBACV+K,QAASgB,QAAQ3B,EAAMY,cACvBxB,SAAUoB,KAEdpK,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,uBAAuB9J,IAASpB,UAAU,mBAAkBiB,SAAC,yBAIpF,CAAC,WAAY,SAASmI,SAASgB,EAAMS,OAAST,EAAMO,SAAWP,EAAMO,QAAQ1D,OAAS,IAClF5F,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACxBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,uBAAuB9J,IAASpB,UAAU,aAAYiB,SAAC,qBACvEI,EAAAA,EAAAA,MAAA,UACIa,GAAI,uBAAuBd,IAC3BpB,UAAU,eACVe,WAA8BJ,IAAvByJ,EAAMY,aAA6B,GAAKc,OAAO1B,EAAMY,cAC5DxB,SAAUoB,EAAyB3J,SAAA,EAEnCT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,yBAChBmJ,EAAMO,QAAQzJ,KAAIsK,IAAOhL,EAAAA,EAAAA,KAAA,UAAwBO,MAAOyK,EAAIzK,MAAME,SAAEuK,EAAIP,OAAlCO,EAAIzK,eAKvC,mBAAfqJ,EAAMS,OACHxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACvBT,EAAAA,EAAAA,KAAA,SAAOR,UAAU,aAAYiB,SAAC,wCAC9BT,EAAAA,EAAAA,KAAA,SACIqK,KAAK,OACL7K,UAAU,eACVe,MAAOiL,MAAMC,QAAQ7B,EAAMY,cAAgBZ,EAAMY,aAAakB,KAAK,KAAO,GAC1E1C,SAAW9H,GAAM2I,EAASjJ,EAAO,IAAIgJ,EAAOY,aAActJ,EAAEgI,OAAO3I,MAAMuF,MAAM,KAAKpF,KAAIiL,GAAKA,EAAEjD,SAAQnG,QAAOoJ,GAAKA,MACnHhB,YAAY,qBAKR,WAAff,EAAMS,OACLxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,qBAAqB9J,IAASpB,UAAU,aAAYiB,SAAC,mBACrET,EAAAA,EAAAA,KAAA,SACE0B,GAAI,qBAAqBd,IACzByJ,KAAK,OACL7K,UAAU,eACVe,MAAOqJ,EAAMgC,YAAc,GAC3B5C,SAAW9H,GAAM2I,EAASjJ,EAAO,IAAIgJ,EAAOgC,WAAY1K,EAAEgI,OAAO3I,aAKvD,YAAfqJ,EAAMS,OACLxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,uBAAuB9J,IAASpB,UAAU,aAAYiB,SAAC,qBACvET,EAAAA,EAAAA,KAAA,SACE0B,GAAI,uBAAuBd,IAC3ByJ,KAAK,OACL7K,UAAU,eACVe,MAAOqJ,EAAMiC,cAAgB,GAC7B7C,SAAW9H,GAAM2I,EAASjJ,EAAO,IAAIgJ,EAAOiC,aAAc3K,EAAEgI,OAAO3I,cAMvE,CAAC,SAAU,WAAWqI,SAASgB,EAAMS,QACrCxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,wBAAuBiB,SAAA,EACpCT,EAAAA,EAAAA,KAAA,SACE0B,GAAI,kBAAkBd,IACtByJ,KAAK,WACL7K,UAAU,mBACV+K,UAAWX,EAAMgB,SACjB5B,SAAW9H,GAAM2I,EAASjJ,EAAO,IAAIgJ,EAAOgB,SAAU1J,EAAEgI,OAAOqB,aAEjEvK,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,kBAAkB9J,IAASpB,UAAU,mBAAkBiB,SAAC,sBAI1E,EC/LV,EAhD8DK,IASvD,IATwD,WAC7DgL,EAAU,OACV/E,EAAM,WACNgF,EAAU,cACVC,EAAa,cACbC,EAAa,OACbC,EAAM,UACNC,EAAS,QACTC,GACDtL,EACC,OACED,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kBAAiBiB,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,2BAAyBqL,EAAWxL,UAExCN,EAAAA,EAAAA,KAAA,MAAAS,SAAI,yBACHsG,EAAOrG,KAAI,CAACkJ,EAAOhJ,KAClBZ,EAAAA,EAAAA,KAACqM,EAAe,CAEdzC,MAAOA,EACPhJ,MAAOA,EACPiJ,SAAUmC,EACVlC,SAAUmC,GAJLrC,EAAMlI,IAAMd,MAQrBC,EAAAA,EAAAA,MAAA,UAAQI,QAAS8K,EAAYvM,UAAU,oBAAmBiB,SAAA,CACvDtB,EAAAA,cAAoBmN,EAAAA,KAAoC,iBAG3DzL,EAAAA,EAAAA,MAAA,UACEI,QAASiL,EACT1M,UAAU,4BACV2J,SAAUiD,IAAYN,GAAgC,IAAlB/E,EAAON,OAAahG,SAAA,CAEvDtB,EAAAA,cAAoBoN,EAAAA,KAAoC,IAAEH,EAAU,YAAc,8BAGrFpM,EAAAA,EAAAA,KAAA,UACEiB,QAASkL,EACT3M,UAAU,8BACV2J,UAAW2C,GAAgC,IAAlB/E,EAAON,OAAahG,SAC9C,mBAGG,ECuCG+L,EAAwC,CACnD,CAAEjM,MAAO,QAASkK,MAAO,SACzB,CAAElK,MAAO,SAAUkK,MAAO,UAC1B,CAAElK,MAAO,UAAWkK,MAAO,Y,cCxFtB,MCkJP,EA/I0D3J,IAQnD,IARoD,GACzDY,EAAE,MACF+I,EAAK,QACLN,EAAO,eACPsC,EAAc,SACdzD,EAAQ,SACRG,GAAW,EAAK,YAChBwB,EAAc,wBACf7J,EACC,MAAOC,EAAQ2L,IAAaC,EAAAA,EAAAA,WAAS,GAC/BC,GAAcC,EAAAA,EAAAA,QAAuB,OAG3CC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAsBC,IACtBJ,EAAYK,UAAYL,EAAYK,QAAQC,SAASF,EAAM9D,SAC7DwD,GAAU,EACZ,EAIF,OADAS,SAASC,iBAAiB,YAAaL,GAChC,KACLI,SAASE,oBAAoB,YAAaN,EAAmB,CAC9D,GACA,IAEH,MA+BMO,EAAgBb,EAAehG,SAAW0D,EAAQ1D,QAAU0D,EAAQ1D,OAAS,EAC7E8G,EAAkBd,EAAehG,OAAS,GAAKgG,EAAehG,OAAS0D,EAAQ1D,OAErF,OACE5F,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAO6J,QAAShJ,EAAIlC,UAAU,aAAYiB,SAAA,CAAEgK,EAAM,QAClD5J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,WAAWH,IAAKuN,EAAYnM,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACE0B,GAAIA,EACJlC,UAAW,+DAA8D2J,EAAW,WAAa,IACjGkB,KAAK,SACLpJ,QAASA,KAAOkI,GAAYuD,GAAW3L,GACvCoI,SAAUA,EACVN,MAAO,CACL2E,gBAAiBrE,EAAW,UAAY,QACxCsE,YAAa,WACbhN,UAEFT,EAAAA,EAAAA,KAAA,QAAMR,UAAqC,IAA1BiN,EAAehG,OAAe,aAAe,GAAGhG,SA7BlDiN,MACrB,GAA8B,IAA1BjB,EAAehG,OACjB,OAAOkE,EACF,GAA8B,IAA1B8B,EAAehG,OAAc,CACtC,MAAMkH,EAAiBxD,EAAQ3I,MAAKoM,GAAUA,EAAOlM,KAAO+K,EAAe,KAC3E,OAAqB,OAAdkB,QAAc,IAAdA,OAAc,EAAdA,EAAgB1O,OAAQ0L,CACjC,CACE,MAAO,GAAG8B,EAAehG,iBAC3B,EAsBSiH,OAIJ3M,IAAWoI,IACVtI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,2BAA2BqJ,MAAO,CAAEgF,UAAW,QAASC,UAAW,QAASrN,SAAA,CAExF0J,EAAQ1D,OAAS,IAChB5F,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,EACET,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeiB,UAC5BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SACER,UAAU,mBACV6K,KAAK,WACL3I,GAAI,GAAGA,eACP6I,QAAS+C,EACTjO,IAAM0O,IACAA,IAAOA,EAAMC,cAAgBT,EAAe,EAElDvE,SA3DIiF,KAClBxB,EAAehG,SAAW0D,EAAQ1D,OAEpCuC,EAAS,IAGTA,EAASmB,EAAQzJ,KAAIkN,GAAUA,EAAOlM,KACxC,KAsDgBb,EAAAA,EAAAA,MAAA,SAAOrB,UAAU,2BAA2BkL,QAAS,GAAGhJ,eAAgBjB,SAAA,CAAC,eAC1D0J,EAAQ1D,OAAO,aAIlCzG,EAAAA,EAAAA,KAAA,MAAIR,UAAU,wBAKjB2K,EAAQzJ,KAAIkN,IACX5N,EAAAA,EAAAA,KAAA,OAAqBR,UAAU,gBAAeiB,UAC5CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SACER,UAAU,mBACV6K,KAAK,WACL3I,GAAI,GAAGA,KAAMkM,EAAOlM,KACpB6I,QAASkC,EAAe7D,SAASgF,EAAOlM,IACxCsH,SAAUA,KAAMkF,OAzFJC,EAyFyBP,EAAOlM,QAxFxD+K,EAAe7D,SAASuF,GAE1BnF,EAASyD,EAAelK,QAAOb,GAAMA,IAAOyM,KAG5CnF,EAAS,IAAIyD,EAAgB0B,KANHA,KAyFoC,KAElDnO,EAAAA,EAAAA,KAAA,SAAOR,UAAU,mBAAmBkL,QAAS,GAAGhJ,KAAMkM,EAAOlM,KAAKjB,SAC/DmN,EAAO3O,WAVJ2O,EAAOlM,MAgBC,IAAnByI,EAAQ1D,SACPzG,EAAAA,EAAAA,KAAA,OAAKR,UAAU,2BAA0BiB,UACvCT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,iCAQbgM,EAAehG,OAAS,IACvB5F,EAAAA,EAAAA,MAAA,SAAOrB,UAAU,0BAAyBiB,SAAA,CACvCgM,EAAehG,OAAO,OAAK0D,EAAQ1D,OAAO,iBAG3C,ECyBV,EArKgE3F,IASzD,IAT0D,gBAC/DsN,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,GACD7N,EAEC,MAAM,QAAE8N,EAAO,UAAEC,EAAS,QAAEC,EAAO,QAAE1C,EAAO,MAAExH,EAAK,QAAEmK,GFbpBC,MACjC,MAAOJ,EAASK,IAActC,EAAAA,EAAAA,UAAmB,KAC1CkC,EAAWK,IAAgBvC,EAAAA,EAAAA,UAAqB,KAChDmC,EAASK,IAAcxC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAkB,IACzC/H,EAAOtB,IAAYqJ,EAAAA,EAAAA,UAAwB,MAE5C0C,EAAkBnL,UACtB,IACEkL,GAAW,GACX9L,EAAS,MAETqB,QAAQ2K,IAAI,0EAGZ,MAAMC,QAAgBC,EAAAA,EAAcC,qBAEpC9K,QAAQ2K,IAAI,sCAAkCC,EAAQ9I,OAAQ,kBAG9D,MAAMiJ,EAAyB,OAAPH,QAAO,IAAPA,OAAO,EAAPA,EACpB7O,KAAIiP,GAAOA,EAAIC,SAChBrN,QAAO,CAACsN,EAAQjP,EAAOkP,IAAUA,EAAMC,QAAQF,KAAYjP,IAC3D2B,QAAQsN,GAAuC,MAAVA,GAAoC,KAAlBA,EAAOnH,SAC9DsH,OAIGC,GAAwC,OAAfP,QAAe,IAAfA,OAAe,EAAfA,EAAiBhP,KAAIwP,IAAU,CAC5DxO,GAAIwO,EAAWvH,cAAcpD,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzEtG,KAAMiR,QACD,GAGDC,EAA2B,OAAPZ,QAAO,IAAPA,OAAO,EAAPA,EACtB7O,KAAIiP,IAAG,CAAOE,OAAQF,EAAIC,OAAQQ,SAAUT,EAAIU,aACjD9N,QAAO,CAACL,EAAMtB,EAAOkP,IACpBA,EAAMQ,WAAUC,GAAKA,EAAEV,SAAW3N,EAAK2N,QAAUU,EAAEH,WAAalO,EAAKkO,aAAcxP,IAEpF2B,QAAQL,GACQ,MAAfA,EAAK2N,QAAmC,MAAjB3N,EAAKkO,UACL,KAAvBlO,EAAK2N,OAAOnH,QAA0C,KAAzBxG,EAAKkO,SAAS1H,SAE5CsH,MAAK,CAACQ,EAAGC,IAAMD,EAAEX,OAAOa,cAAcD,EAAEZ,SAAWW,EAAEJ,SAASM,cAAcD,EAAEL,YAE3EO,GAA8C,OAAjBR,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmBzP,KAAIwB,IAAI,CAC5DR,GAAIQ,EAAKkO,SAASzH,cAAcpD,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC5EtG,KAAMiD,EAAKkO,SACXP,OAAQ3N,EAAK2N,aACR,GAGDe,GAAgC,OAAPrB,QAAO,IAAPA,OAAO,EAAPA,EAC3BhN,QAAOoN,GAAOA,EAAI,gBAAkBA,EAAIC,QAAUD,EAAIU,WACvD3P,KAAIiP,IAAG,CACNjO,GAAIiO,EAAI,eACR1Q,KAAM0Q,EAAI,eACVE,OAAQF,EAAIC,QAAU,GACtBQ,SAAUT,EAAIU,UAAY,GAC1BQ,WAAYlB,EAAI,qBACX,GAITV,EAAWgB,GACXf,EAAayB,GACbxB,EAAWyB,EAEb,CAAE,MAAOlM,GACPC,QAAQC,MAAM,8BAAqBF,GACnCpB,EAAS,iDACT2L,EAAW,IACXC,EAAa,IACbC,EAAW,GACb,CAAC,QACCC,GAAW,EACb,GAQF,OAJAtC,EAAAA,EAAAA,YAAU,KACRuC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA1C,UACAxH,QACAmK,QAASM,EACV,EE9EgEyB,GAG3DC,EAAsB3C,EAAgB1N,KAAIsQ,IAAQ,IAAAC,EAAA,OAClB,QADkBA,EACtDrC,EAAQpN,MAAK0P,GAAKA,EAAExP,KAAOsP,WAAS,IAAAC,OAAA,EAApCA,EAAsChS,IAAI,IAC1CsD,OAAOgJ,SAEH4F,EAAqB/C,EAAgB3H,OAAS,EAChDoI,EAAUtM,QAAO6N,GAAYW,EAAoBnI,SAASwH,EAASP,UACnEhB,EAGEuC,EAAwB/C,EAAkB3N,KAAI2Q,IAAU,IAAAC,EAAA,OACpB,QADoBA,EAC5DzC,EAAUrN,MAAK+P,GAAKA,EAAE7P,KAAO2P,WAAW,IAAAC,OAAA,EAAxCA,EAA0CrS,IAAI,IAC9CsD,OAAOgJ,SAEHiG,EAAmBnD,EAAkB5H,OAAS,EAChDqI,EAAQvM,QAAOkP,GACbV,EAAoBnI,SAAS6I,EAAO5B,SACpCuB,EAAsBxI,SAAS6I,EAAOrB,YAExChC,EAAgB3H,OAAS,EACvBqI,EAAQvM,QAAOkP,GAAUV,EAAoBnI,SAAS6I,EAAO5B,UAC7Df,EAiCN,OA9BAhC,EAAAA,EAAAA,YAAU,KACR,GAAIsB,EAAgB3H,OAAS,EAAG,CAE9B,MAAMiL,EAAiBrD,EAAkB9L,QAAO8O,IAC9C,MAAMjB,EAAWvB,EAAUrN,MAAK+P,GAAKA,EAAE7P,KAAO2P,IAC9C,OAAOjB,GAAYW,EAAoBnI,SAASwH,EAASP,OAAO,IAG9D6B,EAAejL,SAAW4H,EAAkB5H,QAC9CgI,EAAkBiD,EAEtB,IACC,CAACtD,EAAiBC,EAAmBQ,EAAWkC,EAAqBtC,KAExE3B,EAAAA,EAAAA,YAAU,KACR,GAAIuB,EAAkB5H,OAAS,EAAG,CAEhC,MAAMkL,EAAerD,EAAgB/L,QAAOqP,IAC1C,MAAMH,EAAS3C,EAAQtN,MAAKqQ,GAAKA,EAAEnQ,KAAOkQ,IAC1C,OAAOH,GACAV,EAAoBnI,SAAS6I,EAAO5B,SACpCuB,EAAsBxI,SAAS6I,EAAOrB,SAAS,IAGpDuB,EAAalL,SAAW6H,EAAgB7H,QAC1CiI,EAAgBiD,EAEpB,IACC,CAACtD,EAAmBC,EAAiBQ,EAASiC,EAAqBK,EAAuB1C,KAG3F7N,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,iCAAgCiB,SAAA,EAC7CT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,yBAEH2L,IACCpM,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBiB,UAC/BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,4BAA2BiB,SAAA,EACxCT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,wCAAwCsS,KAAK,SAAQrR,UAClET,EAAAA,EAAAA,KAAA,QAAMR,UAAU,kBAAiBiB,SAAC,iBAC9B,8BAMXmE,IACC/D,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,qBAAoBiB,SAAA,EACjCT,EAAAA,EAAAA,KAAA,UAAAS,SAAQ,WAAe,IAAEmE,GACzB5E,EAAAA,EAAAA,KAAA,UACER,UAAU,qCACVyB,QAAS8N,EAAQtO,SAClB,cAMH2L,IAAYxH,IACZ/D,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,MAAKiB,SAAA,EAClBT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAAC+R,EAAgB,CACfrQ,GAAG,gBACH+I,MAAM,iBACNN,QAASyE,EACTnC,eAAgB2B,EAChBpF,SAAUwF,EACVrF,SAAUiD,EACVzB,YAAY,4BAIhB3K,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAAC+R,EAAgB,CACfrQ,GAAG,kBACH+I,MAAM,mBACNN,QAASgH,EACT1E,eAAgB4B,EAChBrF,SAAUyF,EACVtF,SAAqC,IAA3BiF,EAAgB3H,QAAgB2F,EAC1CzB,YAAY,8BAIhB3K,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAAC+R,EAAgB,CACfrQ,GAAG,gBACH+I,MAAM,iBACNN,QAASqH,EACT/E,eAAgB6B,EAChBtF,SAAU0F,EACVvF,SAAuC,IAA7BkF,EAAkB5H,QAAgB2F,EAC5CzB,YAAY,4BAIhB3K,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAO6J,QAAQ,mBAAmBlL,UAAU,aAAYiB,SAAA,CAAC,sBACrCT,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaiB,SAAC,UAElDI,EAAAA,EAAAA,MAAA,UACEa,GAAG,mBACHlC,UAAW,gBAAgB+O,EAAmC,GAAf,cAC/ChO,MAAOgO,EACPvF,SAAW9H,GAAMyN,EAAkBzN,EAAEgI,OAAO3I,OAC5C4I,SAAUiD,EACVxB,UAAQ,EAAAnK,SAAA,EAERT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,2BAChB+L,EAAmB9L,KAAIsR,IACtBhS,EAAAA,EAAAA,KAAA,UAA8BO,MAAOyR,EAAUzR,MAAME,SAClDuR,EAAUvH,OADAuH,EAAUzR,aAKzBgO,IACAvO,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBiB,SAAC,4CAQxC,ECyMV,EAzW8BwR,KAAO,IAADC,EAAAC,EAElC,MAAMC,ECf2BC,MACjC,MAAO1P,EAAYC,IAAiB+J,EAAAA,EAAAA,UAAqB,KAClD9J,EAAcC,IAAmB6J,EAAAA,EAAAA,UAAiB,KAClDb,EAAYpI,IAAiBiJ,EAAAA,EAAAA,UAA4B,OACzD5F,EAAQpD,IAAagJ,EAAAA,EAAAA,UAAsB,KAC3C2F,EAAwBC,IAA6B5F,EAAAA,EAAAA,UAA6B,KAClF1E,EAAW5E,IAAgBsJ,EAAAA,EAAAA,WAAkB,IAC7CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAS,IAChC/H,EAAOtB,IAAYqJ,EAAAA,EAAAA,UAAwB,OAC3C6F,EAASjP,IAAcoJ,EAAAA,EAAAA,UAAwB,OAE/C8F,EAAiBhP,IAAsBkJ,EAAAA,EAAAA,WAAkB,IACzD5J,EAAWC,IAAgB2J,EAAAA,EAAAA,UAAiB,KAC5C1J,EAAcC,IAAmByJ,EAAAA,EAAAA,UAAiB,KAClD+F,EAAkBlP,IAAuBmJ,EAAAA,EAAAA,WAAkB,IAE3DtF,EAAazD,IAAkB+I,EAAAA,EAAAA,UAA0B,OACzDgG,EAAe9O,IAAoB8I,EAAAA,EAAAA,WAAkB,IAErDiG,EAAc9O,IAAmB6I,EAAAA,EAAAA,UAAwB,OACzDkG,EAAwB9O,IAA6B4I,EAAAA,EAAAA,WAAkB,IAEvExJ,EAAYC,IAAiBuJ,EAAAA,EAAAA,UAAiB,KAG9CmG,EAAeC,IAAoBpG,EAAAA,EAAAA,WAAS,IAC5CqG,EAAgBC,IAAqBtG,EAAAA,EAAAA,UAAS,KAG9CyB,EAAiB8E,IAAsBvG,EAAAA,EAAAA,UAAmB,KAC1D0B,EAAmB8E,IAAwBxG,EAAAA,EAAAA,UAAmB,KAC9D2B,EAAiB8E,IAAsBzG,EAAAA,EAAAA,UAAmB,KAC1D4B,EAAmB8E,IAAwB1G,EAAAA,EAAAA,UAAiB,IAEnE,MAAO,CAELhK,aACAE,eACAiJ,aACA/E,SACAuL,yBACArK,YACAmE,UACAxH,QACA4N,UACAC,kBACA1P,YACAE,eACAyP,mBACArL,cACAsL,gBACAC,eACAC,yBACA1P,aACA2P,gBACAE,iBACA5E,kBACAC,oBACAC,kBACAC,oBAGA3L,gBACAE,kBACAY,gBACAC,YACA4O,4BACAlP,eACA+L,aACA9L,WACAC,aACAE,qBACAT,eACAE,kBACAM,sBACAI,iBACAC,mBACAC,kBACAC,4BACAX,gBACA2P,mBACAE,oBACAC,qBACAC,uBACAC,qBACAC,uBACD,EDvEahB,GAKRiB,EAAiB5Q,EAAkB,CACvCC,WAAYyP,EAAMzP,WAClBC,cAAewP,EAAMxP,cACrBC,aAAcuP,EAAMvP,aACpBC,gBAAiBsP,EAAMtP,gBACvBC,UAAWqP,EAAMrP,UACjBC,aAAcoP,EAAMpP,aACpBC,aAAcmP,EAAMnP,aACpBC,gBAAiBkP,EAAMlP,gBACvBC,WAAYiP,EAAMjP,WAClBC,cAAegP,EAAMhP,cACrBC,aAAc+O,EAAM/O,aACpBC,SAAU8O,EAAM9O,SAChBC,WAAY6O,EAAM7O,WAClBC,oBAAqB4O,EAAM5O,oBAC3BC,mBAAoB2O,EAAM3O,mBAC1BC,cAAe0O,EAAM1O,cACrBC,UAAWyO,EAAMzO,UACjBC,eAAgBwO,EAAMxO,eACtBC,iBAAkBuO,EAAMvO,iBACxBC,gBAAiBsO,EAAMtO,gBACvBC,0BAA2BqO,EAAMrO,4BAG7BwP,EThB6BjU,KACnC,MAAM,WACJqD,EAAU,aACVE,EAAY,WACZiJ,EAAU,cACVpI,EAAa,OACbqD,EAAM,UACNpD,EAAS,0BACT4O,EAAyB,WACzBnD,EAAU,SACV9L,EAAQ,WACRC,EAAU,kBACV0P,EAAiB,iBACjBF,EAAgB,gBAChB3E,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,mBACjB2E,EAAkB,qBAClBC,EAAoB,mBACpBC,EAAkB,qBAClBC,GACE/T,EAoTJ,MAAO,CACLkU,wBAnT6BvP,EAAAA,EAAAA,cAAYC,UACzC,GAAKuP,EAAL,CACA9O,QAAQ2K,IAAI,4CAA4CmE,KACxD,IACE,MAAMC,GAAgBlP,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,cAAemP,GACvCE,QAAuB5O,EAAAA,EAAAA,IAAO2O,GACpC,GAAIC,EAAe3O,SAAU,CAC3B,MAAM4O,EAAiBD,EAAelP,OACtC8N,EAA0BqB,EAAe7M,QAAU,IACnDpC,QAAQ2K,IAAI,0BAA2BsE,EAAe7M,OACxD,MACEpC,QAAQ2K,IAAI,mDAAmDmE,KAC/DlB,EAA0B,GAE9B,CAAE,MAAO7N,GACPC,QAAQC,MAAM,sCAAuCF,GACrDpB,EAAS,wCACTiP,EAA0B,GAC5B,CAjBmB,CAiBnB,GACC,CAACA,EAA2BjP,IAiS7BuQ,gBA/RqB5P,EAAAA,EAAAA,cAAYC,UACjC,GAAK7C,EAAL,CAIAsD,QAAQ2K,IAAI,qCAAqCjO,KACjD+N,GAAW,GACX9L,EAAS,MACT,IAEE,MAAMwB,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASjD,GAC1ByS,QAAgB/O,EAAAA,EAAAA,IAAOD,GAE7B,IAAIL,EAA0B,KAE9B,GAAIqP,EAAQ9O,SAEVP,EAAOqP,EAAQrP,YAGf,IACEA,QAAasP,EAAAA,EAAoBF,eAAexS,EAClD,CAAE,MAAO2S,GACP,CAIJ,GAAIvP,EACFf,EAAce,GACdd,EAAUc,EAAKsC,QAAU,IAEzBmM,EAAmBzO,EAAK2J,kBAAoB3J,EAAKwP,eAAiB,CAACxP,EAAKwP,gBAAkB,KAC1Fd,EAAqB1O,EAAK4J,oBAAsB5J,EAAKyP,iBAAmB,CAACzP,EAAKyP,kBAAoB,KAClGd,EAAmB3O,EAAK6J,kBAAoB7J,EAAK0P,eAAiB,CAAC1P,EAAK0P,gBAAkB,KAC1Fd,EAAqB5O,EAAK8J,mBAAqB,QAC1C,CAEL,MAAMhN,EAAOoB,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOL,IAC3CqC,EAAc,CACZhC,GAAIL,EACJf,OAAW,OAAJiB,QAAI,IAAJA,OAAI,EAAJA,EAAMjB,QAAS,WACtByG,OAAQ,GACRH,aAAa,IAAIC,MAAOC,gBAE1BnD,EAAU,IAEVuP,EAAmB,IACnBC,EAAqB,IACrBC,EAAmB,IACnBC,EAAqB,GACvB,CACF,CAAE,MAAO3O,GACPpB,EAAS,sCACTqB,QAAQC,MAAMF,GACdhB,EAAc,MACdC,EAAU,GACZ,CAAC,QACCyL,GAAW,EACb,CAtDA,MAFEzK,QAAQ2K,IAAI,uCAwDd,GACC,CAAC3M,EAAYyM,EAAY9L,EAAUI,EAAeC,EAAWuP,EAAoBC,EAAsBC,EAAoBC,IAqO5He,SAnOeA,KACf,MAAMC,EAAsB,CAC1B3S,GAAI,SAASmF,KAAKyN,QAClBjK,KAAM,OACNI,MAAO,YACPE,YAAa,GACbR,QAAS,GACTS,UAAU,EACViF,OAAQ,GACRO,SAAU,GACVqB,OAAQ,IAEV9N,EAAU,IAAIoD,EAAQsN,GAAU,EAwNhCE,oBArN2BC,IAC3B7P,QAAQ2K,IAAI,mCAAoCkF,GAChD,MAAMH,EAAsB,CAC1B3S,GAAI8S,EAAa9S,GACjB2I,KAAMmK,EAAanK,KACnBI,MAAO+J,EAAa/J,MACpBE,YAAa6J,EAAa7J,YAC1BR,QAASqK,EAAarK,QAAUqK,EAAarK,QAAQzJ,KAAKsK,GACrC,kBAARA,EACF,CAAEP,MAAOO,EAAKzK,MAAOyK,GAErB,CAAEP,MAAOO,EAAIP,MAAOlK,MAAOyK,EAAIzK,cAErCJ,EACLyK,SAAU4J,EAAa5J,SACvBJ,aAAcgK,EAAahK,aAC3BK,IAAK2J,EAAa3J,IAClBE,IAAKyJ,EAAazJ,IAClBc,kBAAc1L,EACdsU,aAAStU,EACTyL,gBAAYzL,EACZuU,gBAAYvU,EACZwU,mBAAexU,EACfI,WAAOJ,GAGT,GAAI4G,EAAOlF,MAAK+H,GAASA,EAAMlI,KAAO2S,EAAS3S,KAI3C,OAHAiD,QAAQiQ,KAAK,iCAAiCP,EAAS3S,yBACvD4B,EAAS,kBAAkB+Q,EAAS3S,sDACpCwF,YAAW,IAAM5D,EAAS,OAAO,KAIrCqB,QAAQ2K,IAAI,6BAA8B+E,GAC1C1Q,EAAU,IAAIoD,EAAQsN,IACtB9Q,EAAW,gBAAgB8Q,EAAS5J,iCACpCvD,YAAW,IAAM3D,EAAW,OAAO,IAAK,EAkLxCsR,YA/KkBA,CAACjU,EAAekU,KAClC,MAAMC,EAAgB,IAAIhO,GAC1BgO,EAAcnU,GAASkU,EACvBnR,EAAUoR,EAAc,EA6KxBC,YA1KmBpU,IACnB+C,EAAUoD,EAAOxE,QAAO,CAAC2I,EAAGC,IAAMA,IAAMvK,IAAO,EA0K/CqU,WAvKiB/Q,UACjB,GAAKrB,GAAiBiJ,EAMtB,GAAKyC,EAAL,CAKAa,GAAW,GACXzK,QAAQ2K,IAAI,oDAAqDzM,GACjE8B,QAAQ2K,IAAI,sBAAuBvI,GACnCpC,QAAQ2K,IAAI,oBAAqBf,GAEjC,IAAK,IAADpJ,EACF,MAAM+P,EAAgBnO,EAAOrG,KAAIkJ,IAC/B,MAAMuL,EAAoB,CAAC,EAC3B,IAAK,MAAMlL,KAAOL,OACGzJ,IAAfyJ,EAAMK,GACRkL,EAAalL,GAAOL,EAAMK,GAE1BkL,EAAalL,GAAO,KAGxB,OAAOkL,CAAY,IAGfC,EAAgC,IACjCtJ,EACHpK,GAAImB,EACJvC,OAAkD,QAA3C6E,EAAAxC,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,WAAa,IAAAsC,OAAA,EAA3CA,EAA6C7E,QAASwL,EAAWxL,MACxEyG,OAAQmO,EACRtO,aAAa,IAAIC,MAAOC,cACxBsH,kBACAC,oBACAC,kBACAC,qBAII8G,EAAe,GAGrBA,EAAajT,MACXuE,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASzB,GAAeuS,GACpCE,OAAM5Q,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAI6Q,MAAM,yBAAyB7Q,EAAI8Q,UAAU,KAK7DH,EAAajT,KACX2R,EAAAA,EAAoB0B,eAAeL,GAChCE,OAAM5Q,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAI6Q,MAAM,yBAAyB7Q,EAAI8Q,UAAU,WAKvDE,QAAQC,IAAIN,GAElB3R,EAAc0R,GACd7R,EAAW,0CACX2D,YAAW,IAAM3D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPC,QAAQC,MAAM,qCAAsCF,GACpDpB,EAAS,sCAAsCoB,aAAe6Q,MAAQ7Q,EAAI8Q,QAAU,kBACtF,CAAC,QACCpG,GAAW,EACb,CAjEA,MAFE9L,EAAS,+EANTA,EAAS,mDAyEX,EA6FAsS,cA1FoBA,KACpB,IAAK9J,GAAgC,IAAlB/E,EAAON,OAExB,YADAoP,MAAM,+CAIR,MAAMC,EAAmB,eACjBhK,EAAWxL,qCAEbyG,EAAOrG,KAAIkJ,IAAU,IAADqB,EAAAtB,EACpB,IAAIoM,EAAY,GAChB,OAAQnM,EAAMS,MACZ,IAAK,OACL,IAAK,SACL,IAAK,OACL,IAAK,WACH0L,EAAY,gGAEoBnM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8CACnDhB,EAAMS,2CAA2CT,EAAMe,aAAe,OAAOf,EAAMgB,SAAW,WAAa,gDAG9H,MACF,IAAK,WACHmL,EAAY,gGAEoBnM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8DACnChB,EAAMgB,SAAW,WAAa,oDACjChB,EAAMa,wCACjB,QAAbQ,EAAArB,EAAMO,eAAO,IAAAc,OAAA,EAAbA,EAAevK,KAAIkN,GAAU,kBAAkBA,EAAOrN,UAAUqN,EAAOnD,mBAAkBiB,KAAK,MAAO,0EAI7G,MACF,IAAK,WACHqK,EAAY,0HAE8CnM,EAAMlI,OAAOkI,EAAMgB,SAAW,WAAa,iEAC1DhB,EAAMlI,OAAOkI,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,qDAG9F,MACF,IAAK,QACHmL,EAAY,gGAEoBnM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,kCACnD,QAAbjB,EAAAC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAejJ,KAAI,CAACkN,EAAQzC,IAAM,4HAEqBvB,EAAMlI,WAAWkI,EAAMlI,MAAMyJ,aAAayC,EAAOrN,UAAUqJ,EAAMgB,SAAW,WAAa,mEACvGhB,EAAMlI,MAAMyJ,MAAMyC,EAAOnD,kEAEjEiB,KAAK,MAAO,6CAGnB,MACF,IAAK,UACHqK,EAAY,8FAEmBnM,EAAMiC,cAAgB,yNAMrD,MACF,IAAK,SACHkK,EAAY,wEAC2CnM,EAAMgC,YAAc,oCAE3E,MACF,QACEmK,EAAY,8BAA8BnM,EAAMS,WAEpD,OAAO0L,CAAS,IACfrK,KAAK,2BAIZuH,EAAkB6C,GAClB/C,GAAiB,EAAK,EAYvB,ESnUyBiD,CAAqB,CAC7CrT,WAAYyP,EAAMzP,WAClBE,aAAcuP,EAAMvP,aACpBiJ,WAAYsG,EAAMtG,WAClBpI,cAAe0O,EAAM1O,cACrBqD,OAAQqL,EAAMrL,OACdpD,UAAWyO,EAAMzO,UACjB4O,0BAA2BH,EAAMG,0BACjCnD,WAAYgD,EAAMhD,WAClB9L,SAAU8O,EAAM9O,SAChBC,WAAY6O,EAAM7O,WAClB0P,kBAAmBb,EAAMa,kBACzBF,iBAAkBX,EAAMW,iBACxB3E,gBAAiBgE,EAAMhE,gBACvBC,kBAAmB+D,EAAM/D,kBACzBC,gBAAiB8D,EAAM9D,gBACvBC,kBAAmB6D,EAAM7D,kBACzB2E,mBAAoBd,EAAMc,mBAC1BC,qBAAsBf,EAAMe,qBAC5BC,mBAAoBhB,EAAMgB,mBAC1BC,qBAAsBjB,EAAMiB,wBAI9BvG,EAAAA,EAAAA,YAAU,KACRwG,EAAetP,iBAAiB,GAC/B,KAGH8I,EAAAA,EAAAA,YAAU,KACJsF,EAAMvP,cAAgBjB,EAAWwQ,EAAMvP,aAAcuP,EAAMzP,cAAgBvB,EAAWgR,EAAMvP,aAAcuP,EAAMzP,aAAoC,kBAArByP,EAAMjP,YACvIoQ,EAAkBM,eAAezB,EAAMvP,cACvC0Q,EAAkBC,uBAAuBpB,EAAMvP,gBACtCuP,EAAMvP,cAAkBjB,EAAWwQ,EAAMvP,aAAcuP,EAAMzP,cAAevB,EAAWgR,EAAMvP,aAAcuP,EAAMzP,aAAqC,kBAArByP,EAAMjP,WAItIiP,EAAMvP,eAChBuP,EAAM1O,cAAc,MACpB0O,EAAMzO,UAAU,IAChByO,EAAMG,0BAA0B,IAChCH,EAAMhP,cAAc,MAPpBgP,EAAM1O,cAAc,MACpB0O,EAAMzO,UAAU,IAChByO,EAAMG,0BAA0B,KAQ9BH,EAAMvP,cAAqC,kBAArBuP,EAAMjP,YAC5BiP,EAAMG,0BAA0B,GACpC,GACC,CAACH,EAAMvP,aAAcuP,EAAMzP,WAAYyP,EAAMjP,aAqEhD,OACEnD,EAAAA,EAAAA,KAAAqJ,EAAAA,SAAA,CAAA5I,UACEI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,CAC1B2R,EAAMxN,QAAS5E,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeiB,SAAE2R,EAAMxN,QACrDwN,EAAMI,UACLxS,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kBAAiBiB,SAC7B2R,EAAMI,WAGXxS,EAAAA,EAAAA,KAAA,MAAAS,SAAI,2BAEJT,EAAAA,EAAAA,KAACiW,EAAY,CACXtT,WAAYyP,EAAMzP,WAClBE,aAAcuP,EAAMvP,aACpBkF,aAhFkB1G,IAGxB,GAFA+Q,EAAMtP,gBAAgBzB,GACtB+Q,EAAMhP,cAAc,IACf/B,EAGE,CACH,MAAM6U,EAAatU,EAAWP,EAAQ+Q,EAAMzP,YACtCwT,EAAa/U,EAAWC,EAAQ+Q,EAAMzP,YACxCuT,IAAcC,IACd/D,EAAM1O,cAAc,MACpB0O,EAAMzO,UAAU,IAExB,MATIyO,EAAM1O,cAAc,MACpB0O,EAAMzO,UAAU,GAQpB,EAoEMR,WAAYiP,EAAMjP,WAClB6E,eAlEoBoO,IAC1BhE,EAAMhP,cAAcgT,EAAO,EAkErBnO,UAAWmK,EAAMnK,UACjBC,eAhEmBmO,KACzBjE,EAAMpP,aAAa,IACnBoP,EAAMlP,gBAAgB,IACtBkP,EAAM3O,oBAAmB,EAAK,EA8DxB0E,gBA3DoBmO,KACtBlE,EAAMvP,cAAgBjB,EAAWwQ,EAAMvP,aAAcuP,EAAMzP,cAAgBvB,EAAWgR,EAAMvP,aAAcuP,EAAMzP,YAClH4Q,EAAkBM,eAAezB,EAAMvP,cAC9BuP,EAAMvP,eACfuP,EAAM9O,SAAS,sFACf8O,EAAM1O,cAAc,MACpB0O,EAAMzO,UAAU,IAClB,IAwDKyO,EAAMK,kBACLzS,EAAAA,EAAAA,KAACuW,EAAK,CACJxV,OAAQqR,EAAMK,gBACdzR,QAASA,KACPoR,EAAM3O,oBAAmB,GACzB2O,EAAMhP,cAAc,IACpBgP,EAAMpP,aAAa,IACnBoP,EAAMlP,gBAAgB,GAAG,EAE3B5C,MACuB,qBAArB8R,EAAMjP,WAAoC,yBAC1CiP,EAAMvP,cAAqC,qBAArBuP,EAAMjP,WAAoC,4BAAmF,QAAnF+O,EAA4BE,EAAMzP,WAAWnB,MAAKC,GAAKA,EAAEC,KAAO0Q,EAAMvP,sBAAa,IAAAqP,OAAA,EAAvDA,EAAyD5R,SACrJ,oBACDG,UAEDI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAeiB,SAAA,EAC5BT,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACLM,YAAY,oCACZpK,MAAO6R,EAAMrP,UACbiG,SAAW9H,GAAMkR,EAAMpP,aAAa9B,EAAEgI,OAAO3I,MAAMoI,cAAcpD,QAAQ,OAAQ,MACjF/F,UAAU,uBAEZQ,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACLM,YAAY,eACZpK,MAAO6R,EAAMnP,aACb+F,SAAW9H,GAAMkR,EAAMlP,gBAAgBhC,EAAEgI,OAAO3I,OAChDf,UAAU,uBAEZqB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEiB,QAASqS,EAAepO,oBACxBiE,SAAUiJ,EAAMnK,YAAcmK,EAAMrP,YAAcqP,EAAMnP,aACxDzD,UAAU,kBAAiBiB,SAE1B2R,EAAMnK,UAAY,cAAgB,6BAErCjI,EAAAA,EAAAA,KAAA,UAAQiB,QAASA,KACfmR,EAAM3O,oBAAmB,GACzB2O,EAAMhP,cAAc,IACpBgP,EAAMpP,aAAa,IACnBoP,EAAMlP,gBAAgB,GAAG,EACxB1D,UAAU,oBAAmBiB,SAAC,mBASxC2R,EAAMvP,eACLhC,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,GAE0B,kBAArB2R,EAAMjP,YAAkCvB,EAAWwQ,EAAMvP,aAAcuP,EAAMzP,cAAgBvB,EAAWgR,EAAMvP,aAAcuP,EAAMzP,aAAeyP,EAAMtG,cACxJ9L,EAAAA,EAAAA,KAACwW,EAAc,CACb3T,aAAcuP,EAAMvP,aACpBF,WAAYyP,EAAMzP,WAClB2G,WAAYgK,EAAenM,eAC3BoC,aAAc+J,EAAe/L,oBAKX,kBAArB6K,EAAMjP,YAAkCvB,EAAWwQ,EAAMvP,aAAcuP,EAAMzP,cAAgBvB,EAAWgR,EAAMvP,aAAcuP,EAAMzP,cACjI3C,EAAAA,EAAAA,KAACyW,EAAmB,CAClBrI,gBAAiBgE,EAAMhE,gBACvBC,kBAAmB+D,EAAM/D,kBACzBC,gBAAiB8D,EAAM9D,gBACvBC,kBAAmB6D,EAAM7D,kBACzBC,gBA3HeI,IAC3BwD,EAAMc,mBAAmBtE,GAEzBwD,EAAMe,qBAAqB,IAC3Bf,EAAMgB,mBAAmB,GAAG,EAwHhB3E,kBArHiBI,IAC7BuD,EAAMe,qBAAqBtE,GAE3BuD,EAAMgB,mBAAmB,GAAG,EAmHhB1E,gBAhHeI,IAC3BsD,EAAMgB,mBAAmBtE,EAAQ,EAgHrBH,kBA7GiBqD,IAC7BI,EAAMiB,qBAAqBrB,EAAU,IAiHP,kBAArBI,EAAMjP,YAAkCvB,EAAWwQ,EAAMvP,aAAcuP,EAAMzP,cAAgBvB,EAAWgR,EAAMvP,aAAcuP,EAAMzP,aAAeyP,EAAMtG,aACtJ9L,EAAAA,EAAAA,KAAC0W,EAAkB,CACjB5K,WAAYsG,EAAMtG,WAClB/E,OAAQqL,EAAMrL,OACdgF,WAAYwH,EAAkBa,SAC9BpI,cAAeuH,EAAkBsB,YACjC5I,cAAesH,EAAkByB,YACjC9I,OAAQqH,EAAkB0B,WAC1B9I,UAAWoH,EAAkBqC,cAC7BxJ,QAASgG,EAAMhG,UAKG,kBAArBgG,EAAMjP,cAAoCvB,EAAWwQ,EAAMvP,aAAcuP,EAAMzP,aAAevB,EAAWgR,EAAMvP,aAAcuP,EAAMzP,eAClI3C,EAAAA,EAAAA,KAAA,OAAKR,UAAU,wDAAuDiB,SAAC,iLAInD,kBAArB2R,EAAMjP,aAAmCvB,EAAWwQ,EAAMvP,aAAcuP,EAAMzP,cAC7E3C,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kDAAiDiB,SAAC,6IAOrE2R,EAAMvP,cAAqC,KAArBuP,EAAMjP,aAC5BtC,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gDAA+CiB,SAAA,EAC5DT,EAAAA,EAAAA,KAAA,KAAAS,SAAG,+FACHT,EAAAA,EAAAA,KAAA,KAAAS,SAAG,wJAKN2R,EAAMO,eAAiBP,EAAM/K,cAC5BxG,EAAAA,EAAAA,MAAC0V,EAAK,CACJxV,OAAQqR,EAAMO,cACd3R,QAASA,KACPoR,EAAMvO,kBAAiB,GACvBuO,EAAMlP,gBAAgB,IACtBkP,EAAMxO,eAAe,KAAK,EAE5BtD,MAAO,gBAAgB8R,EAAM/K,YAAY/G,QAAQG,SAAA,EAEjDT,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACL9J,MAAO6R,EAAMnP,aACb+F,SAAW9H,GAAMkR,EAAMlP,gBAAgBhC,EAAEgI,OAAO3I,OAChDoK,YAAY,mBACZnL,UAAU,uBAEZqB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEiB,QAASqS,EAAelM,iBACxB5H,UAAU,kBACV2J,SAAUiJ,EAAMnK,YAAcmK,EAAMnP,aAAayF,OAAOjI,SAEvD2R,EAAMnK,UAAY,cAAgB,kBAErCjI,EAAAA,EAAAA,KAAA,UACEiB,QAASA,KACPmR,EAAMvO,kBAAiB,GACvBuO,EAAMlP,gBAAgB,IACtBkP,EAAMxO,eAAe,KAAK,EAE5BpE,UAAU,oBAAmBiB,SAC9B,iBAON2R,EAAMS,wBAA0BT,EAAMQ,eACrC/R,EAAAA,EAAAA,MAAC0V,EAAK,CACJxV,OAAQqR,EAAMS,uBACd7R,QAASA,IAAMoR,EAAMrO,2BAA0B,GAC/CzD,MAAM,mBAAkBG,SAAA,EAExBI,EAAAA,EAAAA,MAAA,KAAAJ,SAAA,CAAG,+CAAoG,QAAxD0R,EAACC,EAAMzP,WAAWnB,MAAKC,GAAKA,EAAEC,KAAO0Q,EAAMQ,sBAAa,IAAAT,OAAA,EAAvDA,EAAyD7R,MAAM,qGAC/GO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEiB,QAASqS,EAAe9L,oBACxBhI,UAAU,iBACV2J,SAAUiJ,EAAMnK,UAAUxH,SAEzB2R,EAAMnK,UAAY,cAAgB,oBAErCjI,EAAAA,EAAAA,KAAA,UACEiB,QAASA,IAAMmR,EAAMrO,2BAA0B,GAC/CvE,UAAU,oBAAmBiB,SAC9B,kBAQPT,EAAAA,EAAAA,KAACuW,EAAK,CACJxV,OAAQqR,EAAMU,cACd9R,QAASA,IAAMoR,EAAMW,kBAAiB,GACtCzS,MAAM,eAAcG,UAEpBT,EAAAA,EAAAA,KAAA,OAAK2W,wBAAyB,CAAEC,OAAQxE,EAAMY,wBAGjD,E,2MEpXA,SAAS6D,EAAwBlY,GACtC,OAAOC,EAAAA,EAAAA,IAAqB,cAAeD,EAC7C,EACwBE,EAAAA,EAAAA,GAAuB,cAAe,CAAC,OAAQ,YAAa,QAAS,sBAAuB,UAAW,UAAW,UAAW,oBCArJ,MACA,GAD8BA,EAAAA,EAAAA,GAAuB,oBAAqB,CAAC,OAAQ,eAAgB,QAAS,sBAAuB,WAAY,UAAW,UAAW,aCH9J,SAASiY,EAA8CnY,GAC5D,OAAOC,EAAAA,EAAAA,IAAqB,6BAA8BD,EAC5D,EACuCE,EAAAA,EAAAA,GAAuB,6BAA8B,CAAC,OAAQ,mBAArG,MCgBMkY,GAA8BhY,EAAAA,EAAAA,IAAO,MAAO,CAChDE,KAAM,6BACNN,KAAM,OACNqY,kBAAmBA,CAAC1X,EAAO2X,KACzB,MAAM,WACJtX,GACEL,EACJ,MAAO,CAAC2X,EAAOnX,KAAMH,EAAWuX,gBAAkBD,EAAOC,eAAe,GAPxCnY,CASjC,CACDoY,SAAU,WACVC,MAAO,GACPC,IAAK,MACLC,UAAW,mBACXC,SAAU,CAAC,CACTjY,MAAOwB,IAAA,IAAC,WACNnB,GACDmB,EAAA,OAAKnB,EAAWuX,cAAc,EAC/BrO,MAAO,CACLuO,MAAO,OAUPI,EAAuCrY,EAAAA,YAAiB,SAAiCC,EAASC,GACtG,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,gCAEF,UACJO,KACGE,GACDJ,EACEmY,EAAUtY,EAAAA,WAAiBuY,EAAAA,GAC3B/X,EAAa,IACdL,EACH4X,eAAgBO,EAAQP,gBAEpBtX,EArDkBD,KACxB,MAAM,eACJuX,EAAc,QACdtX,GACED,EACEgY,EAAQ,CACZ7X,KAAM,CAAC,OAAQoX,GAAkB,mBAEnC,OAAOrX,EAAAA,EAAAA,GAAe8X,EAAOb,EAA+ClX,EAAQ,EA6CpEG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAK+W,EAA6B,CACpDvX,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BG,WAAYA,EACZN,IAAKA,KACFK,GAEP,IAuBA8X,EAAwBI,QAAU,0BAClC,UCtDaC,IAAe9Y,EAAAA,EAAAA,IAAO,MAAO,CACxCE,KAAM,cACNN,KAAM,OACNqY,kBAzB+BA,CAAC1X,EAAO2X,KACvC,MAAM,WACJtX,GACEL,EACJ,MAAO,CAAC2X,EAAOnX,KAAMH,EAAWmY,OAASb,EAAOa,MAAiC,eAA1BnY,EAAWoY,YAA+Bd,EAAOe,oBAAqBrY,EAAWsY,SAAWhB,EAAOgB,SAAUtY,EAAWuX,gBAAkBD,EAAOiB,SAAUvY,EAAWwY,gBAAkBlB,EAAOmB,QAASzY,EAAW0Y,oBAAsBpB,EAAOqB,gBAAgB,GAkB7RvZ,EAIzBwZ,EAAAA,EAAAA,IAAUzX,IAAA,IAAC,MACZ0X,GACD1X,EAAA,MAAM,CACL2X,QAAS,OACTC,eAAgB,aAChBX,WAAY,SACZZ,SAAU,WACVwB,eAAgB,OAChBC,MAAO,OACPC,UAAW,aACXC,UAAW,OACXvB,SAAU,CAAC,CACTjY,MAAOyZ,IAAA,IAAC,WACNpZ,GACDoZ,EAAA,OAAMpZ,EAAWwY,cAAc,EAChCtP,MAAO,CACLmQ,WAAY,EACZC,cAAe,IAEhB,CACD3Z,MAAO4Z,IAAA,IAAC,WACNvZ,GACDuZ,EAAA,OAAMvZ,EAAWwY,gBAAkBxY,EAAWmY,KAAK,EACpDjP,MAAO,CACLmQ,WAAY,EACZC,cAAe,IAEhB,CACD3Z,MAAO6Z,IAAA,IAAC,WACNxZ,GACDwZ,EAAA,OAAMxZ,EAAWwY,iBAAmBxY,EAAWuX,cAAc,EAC9DrO,MAAO,CACLC,YAAa,GACbsQ,aAAc,KAEf,CACD9Z,MAAO+Z,IAAA,IAAC,WACN1Z,GACD0Z,EAAA,OAAM1Z,EAAWwY,kBAAoBxY,EAAW2Y,eAAe,EAChEzP,MAAO,CAGLuQ,aAAc,KAEf,CACD9Z,MAAOga,IAAA,IAAC,WACN3Z,GACD2Z,EAAA,QAAO3Z,EAAW2Y,eAAe,EAClCzP,MAAO,CACL,CAAC,QAAQ0Q,EAAsBzZ,QAAS,CACtCsZ,aAAc,MAGjB,CACD9Z,MAAO,CACLyY,WAAY,cAEdlP,MAAO,CACLkP,WAAY,eAEb,CACDzY,MAAOka,IAAA,IAAC,WACN7Z,GACD6Z,EAAA,OAAK7Z,EAAWsY,OAAO,EACxBpP,MAAO,CACL4Q,aAAc,cAAcjB,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,UACzD2B,eAAgB,gBAEjB,CACDta,MAAOua,IAAA,IAAC,WACNla,GACDka,EAAA,OAAKla,EAAWma,MAAM,EACvBjR,MAAO,CACLkR,WAAYvB,EAAMwB,YAAYC,OAAO,mBAAoB,CACvDC,SAAU1B,EAAMwB,YAAYE,SAASC,WAEvC,UAAW,CACTxB,eAAgB,OAChBnL,iBAAkBgL,EAAMkB,MAAQlB,GAAOmB,QAAQvD,OAAOgE,MAEtD,uBAAwB,CACtB5M,gBAAiB,kBAItB,CACDlO,MAAO+a,IAAA,IAAC,WACN1a,GACD0a,EAAA,OAAK1a,EAAW0Y,kBAAkB,EACnCxP,MAAO,CAGLuQ,aAAc,MAGnB,KACKkB,IAAoBvb,EAAAA,EAAAA,IAAO,KAAM,CACrCE,KAAM,cACNN,KAAM,aAFkBI,CAGvB,CACDoY,SAAU,aAiPZ,GA3O8BhY,EAAAA,YAAiB,SAAkBC,EAASC,GACxE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,iBAEF,WACJ8Y,EAAa,SACbtX,SAAU8Z,EAAY,UACtB/a,EACAgb,UAAWC,EAAa,WACxBC,EAAa,CAAC,EAAC,gBACfC,EAAkB,CAAC,EAAC,mBACpBC,EAAqB,KACrBC,gBACErb,UAAWsb,KACRD,GACD,CAAC,EAAC,MACN/C,GAAQ,EAAK,eACbZ,GAAiB,EAAK,eACtBiB,GAAiB,EAAK,QACtBF,GAAU,EAAK,gBACfK,EAAe,UACfyC,EAAY,CAAC,EAAC,MACdpD,EAAQ,CAAC,KACNjY,GACDJ,EACEmY,EAAUtY,EAAAA,WAAiBuY,EAAAA,GAC3BsD,EAAe7b,EAAAA,SAAc,KAAM,CACvC2Y,MAAOA,GAASL,EAAQK,QAAS,EACjCC,aACAb,oBACE,CAACa,EAAYN,EAAQK,MAAOA,EAAOZ,IACjC+D,EAAc9b,EAAAA,OAAa,MAC3BsB,EAAWtB,EAAAA,SAAe+b,QAAQX,GAGlClC,EAAqB5X,EAASgG,SAAU0U,EAAAA,EAAAA,GAAa1a,EAASA,EAASgG,OAAS,GAAI,CAAC,4BACrF9G,EAAa,IACdL,EACHyY,aACAD,MAAOkD,EAAalD,MACpBZ,iBACAiB,iBACAF,UACAI,sBAEIzY,EA5KkBD,KACxB,MAAM,WACJoY,EAAU,QACVnY,EAAO,MACPkY,EAAK,eACLZ,EAAc,eACdiB,EAAc,QACdF,EAAO,mBACPI,GACE1Y,EACEgY,EAAQ,CACZ7X,KAAM,CAAC,OAAQgY,GAAS,SAAUZ,GAAkB,WAAYiB,GAAkB,UAAWF,GAAW,UAA0B,eAAfF,GAA+B,sBAAuBM,GAAsB,mBAC/L+C,UAAW,CAAC,cAEd,OAAOvb,EAAAA,EAAAA,GAAe8X,EAAOd,EAAyBjX,EAAQ,EA8J9CG,CAAkBJ,GAC5B0b,GAAYC,EAAAA,EAAAA,GAAWL,EAAa5b,GACpCkc,EAAO5D,EAAM7X,MAAQ4a,EAAWa,MAAQ1D,GACxC2D,EAAYT,EAAUjb,MAAQ6a,EAAgB7a,MAAQ,CAAC,EACvD2b,EAAiB,CACrBjc,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAM0b,EAAUhc,UAAWA,MAChDE,GAEL,IAAIgc,EAAYjB,GAAiB,KAGjC,OAAIpC,GAEFqD,EAAaD,EAAejB,WAAcC,EAAwBiB,EAAR,MAG/B,OAAvBd,IACgB,OAAdc,EACFA,EAAY,MAC0B,OAA7BD,EAAejB,YACxBiB,EAAejB,UAAY,SAGXxa,EAAAA,EAAAA,KAAK0X,EAAAA,EAAYiE,SAAU,CAC7Cpb,MAAOya,EACPva,UAAuBI,EAAAA,EAAAA,MAAMyZ,GAAmB,CAC9CsB,GAAIhB,EACJpb,WAAWS,EAAAA,EAAAA,GAAKL,EAAQwb,UAAWN,GACnCzb,IAAKgc,EACL1b,WAAYA,KACTkb,EACHpa,SAAU,EAAcT,EAAAA,EAAAA,KAAKub,EAAM,IAC9BC,OACEK,EAAAA,EAAAA,GAAgBN,IAAS,CAC5BK,GAAIF,EACJ/b,WAAY,IACPA,KACA6b,EAAU7b,gBAGd8b,EACHhb,SAAUA,IACRA,EAASqb,aAIC9b,EAAAA,EAAAA,KAAK0X,EAAAA,EAAYiE,SAAU,CAC7Cpb,MAAOya,EACPva,UAAuBI,EAAAA,EAAAA,MAAM0a,EAAM,IAC9BC,EACHI,GAAIF,EACJrc,IAAKgc,OACAQ,EAAAA,EAAAA,GAAgBN,IAAS,CAC5B5b,WAAY,IACPA,KACA6b,EAAU7b,gBAGd8b,EACHhb,SAAU,CAACA,EAAU6X,IAAgCtY,EAAAA,EAAAA,KAAKwX,EAAyB,CACjF/W,SAAU6X,QAIlB,I,qCCxPA,MAeMyD,IAAmBhd,EAAAA,EAAAA,IAAO,MAAO,CACrCE,KAAM,kBACNN,KAAM,OACNqY,kBAAmBA,CAAC1X,EAAO2X,KACzB,MAAM,WACJtX,GACEL,EACJ,MAAO,CAAC,CACN,CAAC,MAAM0c,GAAAA,EAAoBC,WAAYhF,EAAOgF,SAC7C,CACD,CAAC,MAAMD,GAAAA,EAAoBE,aAAcjF,EAAOiF,WAC/CjF,EAAOnX,KAAMH,EAAWwc,OAASlF,EAAOkF,MAAOxc,EAAWsc,SAAWtc,EAAWuc,WAAajF,EAAOmF,UAAWzc,EAAWmY,OAASb,EAAOa,MAAM,GAX9H/Y,CAatB,CACDsd,KAAM,WACNC,SAAU,EACVC,UAAW,EACXC,aAAc,EACd,CAAC,IAAIC,GAAAA,EAAkB3c,iBAAiBkc,GAAAA,EAAoBC,YAAa,CACvExD,QAAS,SAEX,CAAC,IAAIgE,GAAAA,EAAkB3c,iBAAiBkc,GAAAA,EAAoBE,cAAe,CACzEzD,QAAS,SAEXlB,SAAU,CAAC,CACTjY,MAAOwB,IAAA,IAAC,WACNnB,GACDmB,EAAA,OAAKnB,EAAWsc,SAAWtc,EAAWuc,SAAS,EAChDrT,MAAO,CACL0T,UAAW,EACXC,aAAc,IAEf,CACDld,MAAOyZ,IAAA,IAAC,WACNpZ,GACDoZ,EAAA,OAAKpZ,EAAWwc,KAAK,EACtBtT,MAAO,CACLC,YAAa,QAiKnB,GA7JkC3J,EAAAA,YAAiB,SAAsBC,EAASC,GAChF,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,qBAEF,SACJwB,EAAQ,UACRjB,EAAS,kBACTkd,GAAoB,EAAK,MACzBP,GAAQ,EACRF,QAASU,EAAW,uBACpBC,EACAV,UAAWW,EAAa,yBACxBC,EAAwB,MACxBnF,EAAQ,CAAC,EAAC,UACVoD,EAAY,CAAC,KACVrb,GACDJ,GACE,MACJwY,GACE3Y,EAAAA,WAAiBuY,EAAAA,GACrB,IAAIuE,EAAyB,MAAfU,EAAsBA,EAAclc,EAC9Cyb,EAAYW,EAChB,MAAMld,EAAa,IACdL,EACHod,oBACAP,QACAF,UAAWA,EACXC,YAAaA,EACbpE,SAEIlY,EAvFkBD,KACxB,MAAM,QACJC,EAAO,MACPuc,EAAK,QACLF,EAAO,UACPC,EAAS,MACTpE,GACEnY,EACEgY,EAAQ,CACZ7X,KAAM,CAAC,OAAQqc,GAAS,QAASrE,GAAS,QAASmE,GAAWC,GAAa,aAC3ED,QAAS,CAAC,WACVC,UAAW,CAAC,cAEd,OAAOrc,EAAAA,EAAAA,GAAe8X,EAAOvX,GAAAA,EAA6BR,EAAQ,EA0ElDG,CAAkBJ,GAC5Bod,EAAyB,CAC7BpF,QACAoD,UAAW,CACTkB,QAASW,EACTV,UAAWY,KACR/B,KAGAiC,EAAUC,IAAiBC,EAAAA,GAAAA,GAAQ,OAAQ,CAChD1d,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9B2d,YAAapB,GACbgB,uBAAwB,IACnBA,KACArd,GAELC,aACAN,SAEK+d,EAAaC,IAAoBH,EAAAA,GAAAA,GAAQ,UAAW,CACzD1d,UAAWI,EAAQqc,QACnBkB,YAAaG,EAAAA,EACbP,yBACApd,gBAEK4d,EAAeC,IAAsBN,EAAAA,GAAAA,GAAQ,YAAa,CAC/D1d,UAAWI,EAAQsc,UACnBiB,YAAaG,EAAAA,EACbP,yBACApd,eAkBF,OAhBe,MAAXsc,GAAmBA,EAAQ5R,OAASiT,EAAAA,GAAeZ,IACrDT,GAAuBjc,EAAAA,EAAAA,KAAKod,EAAa,CACvCK,QAAS3F,EAAQ,QAAU,QAC3B0C,UAAW6C,GAAkBI,aAAUtd,EAAY,UAChDkd,EACH5c,SAAUwb,KAGG,MAAbC,GAAqBA,EAAU7R,OAASiT,EAAAA,GAAeZ,IACzDR,GAAyBlc,EAAAA,EAAAA,KAAKud,EAAe,CAC3CE,QAAS,QACT9X,MAAO,mBACJ6X,EACH/c,SAAUyb,MAGMrb,EAAAA,EAAAA,MAAMmc,EAAU,IAC/BC,EACHxc,SAAU,CAACwb,EAASC,IAExB,I,0BC3IA,MAiBMwB,IAAc3e,EAAAA,EAAAA,IAAO,MAAO,CAChCE,KAAM,aACNN,KAAM,OACNqY,kBAAmBA,CAAC1X,EAAO2X,KACzB,MAAM,WACJtX,GACEL,EACJ,MAAO,CAAC2X,EAAOnX,KAAMH,EAAWge,UAAY1G,EAAO0G,SAAU1G,EAAOtX,EAAW8d,SAAU9d,EAAWie,OAAS3G,EAAO2G,MAAkC,aAA3Bje,EAAWke,aAA8B5G,EAAO6G,SAAUne,EAAWoe,UAAY9G,EAAO8G,SAAUpe,EAAWc,UAAYwW,EAAO+G,aAAcre,EAAWc,UAAuC,aAA3Bd,EAAWke,aAA8B5G,EAAOgH,qBAA+C,UAAzBte,EAAWmZ,WAAoD,aAA3BnZ,EAAWke,aAA8B5G,EAAOiH,eAAyC,SAAzBve,EAAWmZ,WAAmD,aAA3BnZ,EAAWke,aAA8B5G,EAAOkH,cAAc,GAP3hBpf,EASjBwZ,EAAAA,EAAAA,IAAUzX,IAAA,IAAC,MACZ0X,GACD1X,EAAA,MAAM,CACLsd,OAAQ,EAERC,WAAY,EACZC,YAAa,EACbC,YAAa,QACb9Q,aAAc+K,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,QAC3CuG,kBAAmB,OACnBjH,SAAU,CAAC,CACTjY,MAAO,CACLqe,UAAU,GAEZ9U,MAAO,CACLsO,SAAU,WACVsH,OAAQ,EACRC,KAAM,EACN9F,MAAO,SAER,CACDtZ,MAAO,CACLse,OAAO,GAET/U,MAAO,CACL4E,YAAa+K,EAAMkB,KAAO,QAAQlB,EAAMkB,KAAKC,QAAQgF,0BAA2BC,EAAAA,GAAAA,IAAMpG,EAAMmB,QAAQ1B,QAAS,OAE9G,CACD3Y,MAAO,CACLme,QAAS,SAEX5U,MAAO,CACLgW,WAAY,KAEb,CACDvf,MAAO,CACLme,QAAS,SACTI,YAAa,cAEfhV,MAAO,CACLgW,WAAYrG,EAAMsG,QAAQ,GAC1BC,YAAavG,EAAMsG,QAAQ,KAE5B,CACDxf,MAAO,CACLme,QAAS,SACTI,YAAa,YAEfhV,MAAO,CACL0T,UAAW/D,EAAMsG,QAAQ,GACzBtC,aAAchE,EAAMsG,QAAQ,KAE7B,CACDxf,MAAO,CACLue,YAAa,YAEfhV,MAAO,CACLmW,OAAQ,OACRR,kBAAmB,EACnBS,iBAAkB,SAEnB,CACD3f,MAAO,CACLye,UAAU,GAEZlV,MAAO,CACLqW,UAAW,UACXF,OAAQ,SAET,CACD1f,MAAOyZ,IAAA,IAAC,WACNpZ,GACDoZ,EAAA,QAAOpZ,EAAWc,QAAQ,EAC3BoI,MAAO,CACL4P,QAAS,OACTK,UAAW,SACXqG,OAAQ,EACRC,eAAgB,QAChBC,gBAAiB,QACjB,sBAAuB,CACrBC,QAAS,KACTJ,UAAW,YAGd,CACD5f,MAAO4Z,IAAA,IAAC,WACNvZ,GACDuZ,EAAA,OAAKvZ,EAAWc,UAAuC,aAA3Bd,EAAWke,WAA0B,EAClEhV,MAAO,CACL,sBAAuB,CACrB+P,MAAO,OACP2G,UAAW,eAAe/G,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,UACvDmH,eAAgB,aAGnB,CACD9f,MAAO6Z,IAAA,IAAC,WACNxZ,GACDwZ,EAAA,MAAgC,aAA3BxZ,EAAWke,aAA8Ble,EAAWc,QAAQ,EAClEoI,MAAO,CACL2W,cAAe,SACf,sBAAuB,CACrBR,OAAQ,OACRS,WAAY,eAAejH,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,UACxDoH,gBAAiB,aAGpB,CACD/f,MAAO+Z,IAAA,IAAC,WACN1Z,GACD0Z,EAAA,MAA8B,UAAzB1Z,EAAWmZ,WAAoD,aAA3BnZ,EAAWke,WAA0B,EAC/EhV,MAAO,CACL,YAAa,CACX+P,MAAO,OAET,WAAY,CACVA,MAAO,SAGV,CACDtZ,MAAOga,IAAA,IAAC,WACN3Z,GACD2Z,EAAA,MAA8B,SAAzB3Z,EAAWmZ,WAAmD,aAA3BnZ,EAAWke,WAA0B,EAC9EhV,MAAO,CACL,YAAa,CACX+P,MAAO,OAET,WAAY,CACVA,MAAO,UAId,KACK8G,IAAiB3gB,EAAAA,EAAAA,IAAO,OAAQ,CACpCE,KAAM,aACNN,KAAM,UACNqY,kBAAmBA,CAAC1X,EAAO2X,KACzB,MAAM,WACJtX,GACEL,EACJ,MAAO,CAAC2X,EAAO0I,QAAoC,aAA3BhgB,EAAWke,aAA8B5G,EAAO2I,gBAAgB,GAPrE7gB,EASpBwZ,EAAAA,EAAAA,IAAUiB,IAAA,IAAC,MACZhB,GACDgB,EAAA,MAAM,CACLf,QAAS,eACT3P,YAAa,QAAQ0P,EAAMsG,QAAQ,YACnC1F,aAAc,QAAQZ,EAAMsG,QAAQ,YACpCe,WAAY,SACZtI,SAAU,CAAC,CACTjY,MAAO,CACLue,YAAa,YAEfhV,MAAO,CACLmQ,WAAY,QAAQR,EAAMsG,QAAQ,YAClC7F,cAAe,QAAQT,EAAMsG,QAAQ,eAG1C,KACKgB,GAAuB3gB,EAAAA,YAAiB,SAAiBC,EAASC,GACtE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,gBAEF,SACJ0e,GAAW,EAAK,SAChBld,EAAQ,UACRjB,EAAS,YACTqe,EAAc,aAAY,UAC1BrD,GAAY/Z,GAA4B,aAAhBod,EAA6B,MAAQ,MAAI,SACjEE,GAAW,EAAK,MAChBH,GAAQ,EAAK,KACb9L,GAAqB,OAAd0I,EAAqB,iBAAcra,GAAS,UACnD2Y,EAAY,SAAQ,QACpB2E,EAAU,eACP/d,GACDJ,EACEK,EAAa,IACdL,EACHqe,WACAnD,YACAuD,WACAH,QACAC,cACA/L,OACAgH,YACA2E,WAEI7d,EAtNkBD,KACxB,MAAM,SACJge,EAAQ,SACRld,EAAQ,QACRb,EAAO,SACPme,EAAQ,MACRH,EAAK,YACLC,EAAW,UACX/E,EAAS,QACT2E,GACE9d,EACEgY,EAAQ,CACZ7X,KAAM,CAAC,OAAQ6d,GAAY,WAAYF,EAASG,GAAS,QAAyB,aAAhBC,GAA8B,WAAYE,GAAY,WAAYtd,GAAY,eAAgBA,GAA4B,aAAhBod,GAA8B,uBAAsC,UAAd/E,GAAyC,aAAhB+E,GAA8B,iBAAgC,SAAd/E,GAAwC,aAAhB+E,GAA8B,iBACjW8B,QAAS,CAAC,UAA2B,aAAhB9B,GAA8B,oBAErD,OAAOhe,EAAAA,EAAAA,GAAe8X,EAAOoI,GAAAA,EAAwBngB,EAAQ,EAuM7CG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAK0d,GAAa,CACpC9B,GAAIpB,EACJhb,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BsS,KAAMA,EACNzS,IAAKA,EACLM,WAAYA,EACZ,mBAA6B,cAATmS,GAAuC,OAAd0I,GAAsC,aAAhBqD,OAA4C1d,EAAd0d,KAC9Fne,EACHe,SAAUA,GAAwBT,EAAAA,EAAAA,KAAK0f,GAAgB,CACrDlgB,UAAWI,EAAQ+f,QACnBhgB,WAAYA,EACZc,SAAUA,IACP,MAET,IAMIqf,KACFA,GAAQE,sBAAuB,GAiEjC,YCtSaC,GAAwBA,KACnC,MAAOrR,EAASK,IAActC,EAAAA,EAAAA,UAAmB,KAC1CkC,EAAWK,IAAgBvC,EAAAA,EAAAA,UAAqB,KAChDmC,EAASK,IAAcxC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAkB,IACzC/H,EAAOtB,IAAYqJ,EAAAA,EAAAA,UAAwB,OAC3CuT,EAAcC,IAAmBxT,EAAAA,EAAAA,UAAiB,IAClDyT,EAAUC,IAAe1T,EAAAA,EAAAA,UAAiB,IAE3C0C,EAAkBnL,UACtB,IACEkL,GAAW,GACX9L,EAAS,MAETqB,QAAQ2K,IAAI,mFAGZ,MAAMgR,QAAsB9Q,EAAAA,EAAcC,qBAK1C,GAHA9K,QAAQ2K,IAAI,wDAAoDgR,EAAc7Z,OAAQ,WACtF0Z,EAAgBG,EAAc7Z,QAED,IAAzB6Z,EAAc7Z,OAMhB,OALA9B,QAAQ2K,IAAI,+DACZL,EAAW,IACXC,EAAa,IACbC,EAAW,SACXkR,EAAY,WAKd,MAAME,EAAgB,IAAIC,IAC1BF,EAAcre,SAAQwP,IAChBA,EAAO7B,QAAU6B,EAAO7B,OAAOlH,QACjC6X,EAAcE,IAAIhP,EAAO7B,OAAOlH,OAClC,IAGF,MAAMuH,EAAyBzE,MAAMkV,KAAKH,GACvCvQ,OACAtP,KAAIwP,IAAU,CACbxO,GAAIwO,EAAWvH,cAAcpD,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzEtG,KAAMiR,MAGVvL,QAAQ2K,IAAI,yDAAgDW,EAAaxJ,QAGzE,MAAMka,EAAkB,IAAIC,IAC5BN,EAAcre,SAAQwP,IAChBA,EAAOpB,UAAYoB,EAAOpB,SAAS3H,QAAU+I,EAAO7B,QAAU6B,EAAO7B,OAAOlH,QAC9EiY,EAAgBE,IAAIpP,EAAOpB,SAAS3H,OAAQ+I,EAAO7B,OAAOlH,OAC5D,IAGF,MAAMiI,EAA6BnF,MAAMkV,KAAKC,EAAgBG,WAC3D9Q,MAAK,CAAAlP,EAAAiY,KAAA,IAAEvI,GAAE1P,GAAG2P,GAAEsI,EAAA,OAAKvI,EAAEE,cAAcD,EAAE,IACrC/P,KAAIwY,IAAA,IAAE6H,EAAc7Q,GAAWgJ,EAAA,MAAM,CACpCxX,GAAIqf,EAAapY,cAAcpD,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC3EtG,KAAM8hB,EACNlR,OAAQK,EACT,IAEHvL,QAAQ2K,IAAI,2DAAkDqB,EAAelK,QAG7E,MAAMmK,EAAyB0P,EAC5B/d,QAAOkP,GAAUA,EAAO,gBAAkBA,EAAO,eAAe/I,SAChEhI,KAAI+Q,IAAM,CACT/P,GAAI+P,EAAO,eACXxS,KAAMwS,EAAO,eACb5B,OAAQ4B,EAAO7B,QAAU,GACzBQ,SAAUqB,EAAOpB,UAAY,GAC7BQ,WAAYY,EAAO,mBAGvB9M,QAAQ2K,IAAI,yDAAgDsB,EAAanK,QA8C/E,SACE6Z,EACA1R,EACAC,EACAC,GAQA,GANAnK,QAAQ2K,IAAI,wEACZ3K,QAAQ2K,IAAI,oDAA0CgR,EAAc7Z,UACpE9B,QAAQ2K,IAAI,0DAAgDV,EAAQnI,UACpE9B,QAAQ2K,IAAI,4DAAkDT,EAAUpI,UACxE9B,QAAQ2K,IAAI,0DAAgDR,EAAQrI,UAEhEqI,EAAQrI,OAAS,EAAG,CAEtB,MAAMua,EAAclS,EAAQpO,KAAImR,GAAKA,EAAE5S,OAAM+Q,OAC7CrL,QAAQ2K,IAAI,8DAAoD0R,EAAY,OAC5Erc,QAAQ2K,IAAI,6DAAmD0R,EAAYA,EAAYva,OAAS,OAGhG,MAAMwa,EAA0C,CAAC,EACjDnS,EAAQ7M,SAAQwP,IACd,MAAMyP,EAAczP,EAAOxS,KAAKkiB,OAAO,GAAGC,cAC1CH,EAAaC,IAAgBD,EAAaC,IAAgB,GAAK,CAAC,IAGlEvc,QAAQ2K,IAAI,4DACZ+R,OAAOC,KAAKL,GAAcjR,OAAO/N,SAAQsf,IACvC5c,QAAQ2K,IAAI,uCAA6BiS,MAAWN,EAAaM,aAAkB,IAIrF,MAAMC,EAAkB1S,EAAQtN,MAAKqQ,GAAKA,EAAE5S,KAAK0J,cAAcC,SAAS,sBAClE6Y,EAAqB3S,EAAQtN,MAAKqQ,GAAKA,EAAE5S,KAAK0J,cAAcC,SAAS,yBAsB3E,GApBAjE,QAAQ2K,IAAI,sEAA4DkS,KACxE7c,QAAQ2K,IAAI,yEAA+DmS,KAEvED,GACF7c,QAAQ2K,IAAI,gEAAsDkS,EAAgBviB,SAEhFwiB,GACF9c,QAAQ2K,IAAI,mEAAyDmS,EAAmBxiB,SAItF2P,EAAQnI,OAAS,IACnB9B,QAAQ2K,IAAI,sDACZV,EAAQ3M,SAAQ4N,IACd,MAAM6R,EAAgB5S,EAAQvM,QAAOsP,GAAKA,EAAEhC,SAAWA,EAAO5Q,OAC9D0F,QAAQ2K,IAAI,uCAA6BO,EAAO5Q,SAASyiB,EAAcjb,iBAAiB,KAKxFoI,EAAUpI,OAAS,EAAG,CACxB9B,QAAQ2K,IAAI,yEACWT,EAAUnO,KAAI0P,IAAQ,CAC3CnR,KAAMmR,EAASnR,KACf0iB,MAAO7S,EAAQvM,QAAOsP,GAAKA,EAAEzB,WAAaA,EAASnR,OAAMwH,WACvDuJ,MAAK,CAACQ,EAAGC,IAAMA,EAAEkR,MAAQnR,EAAEmR,QAAOC,MAAM,EAAG,IAEhC3f,SAAQmO,IACrBzL,QAAQ2K,IAAI,uCAA6Bc,EAASnR,SAASmR,EAASuR,gBAAgB,GAExF,CACF,CAEAhd,QAAQ2K,IAAI,6DACd,CA/GMuS,CAAoBvB,EAAerQ,EAAcU,EAAgBC,GAGjE3B,EAAWgB,GACXf,EAAayB,GACbxB,EAAWyB,GACXyP,EAAY,uBAEZ1b,QAAQ2K,IAAI,yDAEd,CAAE,MAAO5K,GACPC,QAAQC,MAAM,uCAAmCF,GACjDpB,EAAS,iDACT2L,EAAW,IACXC,EAAa,IACbC,EAAW,IACXgR,EAAgB,GAChBE,EAAY,QACd,CAAC,QACCjR,GAAW,EACb,GAQF,OAJAtC,EAAAA,EAAAA,YAAU,KACRuC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA1C,UACAxH,QACAmK,QAASM,EACT6Q,eACAE,WACD,EA4EH,MCwFA,GAvRoC0B,KAClC,MAAM,QACJlT,EAAO,UACPC,EAAS,QACTC,EAAO,QACP1C,EAAO,MACPxH,EAAK,aACLsb,EAAY,SACZE,EAAQ,QACRrR,GACEkR,KAEJ,GAAI7T,EACF,OACEvL,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACtJ,QAAQ,OAAO+G,cAAc,SAASzH,WAAW,SAASiK,EAAG,EAAEvhB,SAAA,EAClET,EAAAA,EAAAA,KAACiiB,EAAAA,EAAgB,CAACC,KAAM,MACxBliB,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAK0E,GAAI,CAAEC,GAAI,GAAI3hB,SAAC,0DAGxCT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,QAAQ9X,MAAM,iBAAiBwc,GAAI,CAAEC,GAAI,GAAI3hB,SAAC,wEAOxE,GAAImE,EACF,OACE/D,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACC,EAAG,EAAEvhB,SAAA,EACRT,EAAAA,EAAAA,KAACqiB,EAAAA,EAAK,CAACC,SAAS,QAAQH,GAAI,CAAEI,GAAI,GAAI9hB,SACnCmE,KAEH5E,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,QAAOhd,SAAC,4FAQlC,MAAM+hB,EAAgD,CAAC,EACvD1T,EAAQ7M,SAAQwP,IACd,MAAMyP,EAAczP,EAAOxS,KAAKkiB,OAAO,GAAGC,cAC1CoB,EAAmBtB,IAAgBsB,EAAmBtB,IAAgB,GAAK,CAAC,IAG9E,MAAMuB,EAAoB3T,EAAQpO,KAAImR,GAAKA,EAAE5S,OAAM+Q,OAC7CwR,EAAkB1S,EAAQtN,MAAKqQ,GAAKA,EAAE5S,KAAK0J,cAAcC,SAAS,sBAClE6Y,EAAqB3S,EAAQtN,MAAKqQ,GAAKA,EAAE5S,KAAK0J,cAAcC,SAAS,yBAGrE8Z,EAAe9T,EAAQlO,KAAImP,IAAM,CACrC5Q,KAAM4Q,EAAO5Q,KACb0iB,MAAO7S,EAAQvM,QAAOsP,GAAKA,EAAEhC,SAAWA,EAAO5Q,OAAMwH,WACnDuJ,MAAK,CAACQ,EAAGC,IAAMA,EAAEkR,MAAQnR,EAAEmR,QAAOC,MAAM,EAAG,GAGzCe,EAAiB9T,EAAUnO,KAAI0P,IAAQ,CAC3CnR,KAAMmR,EAASnR,KACf0iB,MAAO7S,EAAQvM,QAAOsP,GAAKA,EAAEzB,WAAaA,EAASnR,OAAMwH,WACvDuJ,MAAK,CAACQ,EAAGC,IAAMA,EAAEkR,MAAQnR,EAAEmR,QAAOC,MAAM,EAAG,IAE/C,OACE/gB,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACC,EAAG,EAAEvhB,SAAA,EACRT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAAniB,SAAC,+CAItCT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,QAAQ9X,MAAM,iBAAiBkd,WAAS,EAAApiB,SAAC,uKAM7DI,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACtJ,QAAQ,OAAOqK,IAAK,EAAGX,GAAI,CAAEI,GAAI,EAAGQ,SAAU,QAAStiB,SAAA,EAC1DT,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAAC1F,KAAK,IAAIC,SAAS,QAAO7b,UAC5BT,EAAAA,EAAAA,KAACgjB,EAAAA,EAAI,CAAAviB,UACHI,EAAAA,EAAAA,MAACoiB,EAAAA,EAAW,CAAAxiB,SAAA,EACVT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAK9X,MAAM,UAASlF,SAAC,mBAGzCT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAIhd,SACrByf,EAAagD,oBAEhBljB,EAAAA,EAAAA,KAACmjB,EAAAA,EAAI,CACH1Y,MAAO2V,EACP8B,KAAK,QACLvc,MAAOua,EAAe,IAAO,UAAY,UACzCiC,GAAI,CAAEC,GAAI,aAMlBpiB,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAAC1F,KAAK,IAAIC,SAAS,QAAO7b,UAC5BT,EAAAA,EAAAA,KAACgjB,EAAAA,EAAI,CAAAviB,UACHI,EAAAA,EAAAA,MAACoiB,EAAAA,EAAW,CAAAxiB,SAAA,EACVT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAK9X,MAAM,UAASlF,SAAC,aAGzCT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAIhd,SACrBmO,EAAQnI,iBAMjBzG,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAAC1F,KAAK,IAAIC,SAAS,QAAO7b,UAC5BT,EAAAA,EAAAA,KAACgjB,EAAAA,EAAI,CAAAviB,UACHI,EAAAA,EAAAA,MAACoiB,EAAAA,EAAW,CAAAxiB,SAAA,EACVT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAK9X,MAAM,UAASlF,SAAC,eAGzCT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAIhd,SACrBoO,EAAUpI,iBAMnBzG,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAAC1F,KAAK,IAAIC,SAAS,QAAO7b,UAC5BT,EAAAA,EAAAA,KAACgjB,EAAAA,EAAI,CAAAviB,UACHI,EAAAA,EAAAA,MAACoiB,EAAAA,EAAW,CAAAxiB,SAAA,EACVT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAK9X,MAAM,UAASlF,SAAC,aAGzCT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAIhd,SACrBqO,EAAQrI,oBAQnB5F,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACtJ,QAAQ,OAAOqK,IAAK,EAAGX,GAAI,CAAEY,SAAU,QAAStiB,SAAA,EACnDT,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAAC1F,KAAK,IAAIC,SAAS,QAAO7b,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACmjB,GAAI,CAAEH,EAAG,GAAIvhB,SAAA,EAClBT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAAniB,SAAC,0BAItCI,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAI9hB,SAAA,EACjBT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,YAAWhd,SAAC,gCAGhCT,EAAAA,EAAAA,KAACmjB,EAAAA,EAAI,CACH1Y,MAAOyV,EAAe,IAAO,aAAU,YACvCva,MAAOua,EAAe,IAAO,UAAY,QACzCgC,KAAK,cAITrhB,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAI9hB,SAAA,EACjBT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,YAAWhd,SAAC,yBAGhCI,EAAAA,EAAAA,MAACyc,EAAAA,EAAU,CAACG,QAAQ,QAAOhd,SAAA,CAAC,WACjBgiB,EAAkB,IAAM,MAAM,QAEzC5hB,EAAAA,EAAAA,MAACyc,EAAAA,EAAU,CAACG,QAAQ,QAAOhd,SAAA,CAAC,UAClBgiB,EAAkBA,EAAkBhc,OAAS,IAAM,MAAM,WAIrE5F,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAI9hB,SAAA,EACjBT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,YAAWhd,SAAC,6BAGhCT,EAAAA,EAAAA,KAACmjB,EAAAA,EAAI,CACH1Y,MAAO+W,EAAkB,aAAU,YACnC7b,MAAO6b,EAAkB,UAAY,QACrCU,KAAK,UAENV,IACC3gB,EAAAA,EAAAA,MAACyc,EAAAA,EAAU,CAACG,QAAQ,QAAQ0E,GAAI,CAAEC,GAAI,GAAI3hB,SAAA,CAAC,IACvC+gB,EAAgBviB,KAAK,WAK7B4B,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAI9hB,SAAA,EACjBT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,YAAWhd,SAAC,gCAGhCT,EAAAA,EAAAA,KAACmjB,EAAAA,EAAI,CACH1Y,MAAOgX,EAAqB,aAAU,YACtC9b,MAAO8b,EAAqB,UAAY,QACxCS,KAAK,UAENT,IACC5gB,EAAAA,EAAAA,MAACyc,EAAAA,EAAU,CAACG,QAAQ,QAAQ0E,GAAI,CAAEC,GAAI,GAAI3hB,SAAA,CAAC,IACvCghB,EAAmBxiB,KAAK,gBAOpCe,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAAC1F,KAAK,IAAIC,SAAS,QAAO7b,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACmjB,GAAI,CAAEH,EAAG,GAAIvhB,SAAA,EAClBT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAAniB,SAAC,yBAGtCT,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAACI,GAAI,CAAEtU,UAAW,IAAK3O,SAAU,QAASuB,SAC3C4gB,OAAOC,KAAKkB,GAAoBxS,OAAOtP,KAAI6gB,IAC1C1gB,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAActJ,QAAQ,OAAOC,eAAe,gBAAgByJ,GAAI,CAAEI,GAAI,GAAI9hB,SAAA,EAC5EI,EAAAA,EAAAA,MAACyc,EAAAA,EAAU,CAACG,QAAQ,QAAOhd,SAAA,CAAE8gB,EAAO,QACpC1gB,EAAAA,EAAAA,MAACyc,EAAAA,EAAU,CAACG,QAAQ,QAAOhd,SAAA,CAAE+hB,EAAmBjB,GAAQ,gBAFhDA,gBAUpB1gB,EAAAA,EAAAA,MAACkhB,EAAAA,EAAG,CAACtJ,QAAQ,OAAOqK,IAAK,EAAGX,GAAI,CAAEC,GAAI,EAAGW,SAAU,QAAStiB,SAAA,EAC1DT,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAAC1F,KAAK,IAAIC,SAAS,QAAO7b,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACmjB,GAAI,CAAEH,EAAG,GAAIvhB,SAAA,EAClBT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAAniB,SAAC,mCAGtCT,EAAAA,EAAAA,KAACojB,EAAAA,EAAI,CAACtL,OAAK,EAAArX,SACRiiB,EAAahiB,KAAI,CAACmP,EAAQjP,KACzBC,EAAAA,EAAAA,MAAC1B,EAAAA,SAAc,CAAAsB,SAAA,EACbT,EAAAA,EAAAA,KAACqjB,GAAQ,CAAA5iB,UACPT,EAAAA,EAAAA,KAACsjB,GAAY,CACXrH,QAASpM,EAAO5Q,KAChBid,UAAW,GAAGrM,EAAO8R,oBAGxB/gB,EAAQ8hB,EAAajc,OAAS,IAAKzG,EAAAA,EAAAA,KAAC8f,GAAO,MAPzBjQ,EAAO5Q,gBAcpCe,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAAC1F,KAAK,IAAIC,SAAS,QAAO7b,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACmjB,GAAI,CAAEH,EAAG,GAAIvhB,SAAA,EAClBT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAKmF,cAAY,EAAAniB,SAAC,sCAGtCT,EAAAA,EAAAA,KAAC+hB,EAAAA,EAAG,CAACI,GAAI,CAAEtU,UAAW,IAAK3O,SAAU,QAASuB,UAC5CT,EAAAA,EAAAA,KAACojB,EAAAA,EAAI,CAACtL,OAAK,EAAArX,SACRkiB,EAAejiB,KAAI,CAAC0P,EAAUxP,KAC7BC,EAAAA,EAAAA,MAAC1B,EAAAA,SAAc,CAAAsB,SAAA,EACbT,EAAAA,EAAAA,KAACqjB,GAAQ,CAAA5iB,UACPT,EAAAA,EAAAA,KAACsjB,GAAY,CACXrH,QAAS7L,EAASnR,KAClBid,UAAW,GAAG9L,EAASuR,oBAG1B/gB,EAAQ+hB,EAAelc,OAAS,IAAKzG,EAAAA,EAAAA,KAAC8f,GAAO,MAP3B1P,EAASnR,oBAiBzCihB,EAAe,MACdrf,EAAAA,EAAAA,MAACwhB,EAAAA,EAAK,CAACC,SAAS,UAAUH,GAAI,CAAEC,GAAI,GAAI3hB,SAAA,EACtCT,EAAAA,EAAAA,KAACsd,EAAAA,EAAU,CAACG,QAAQ,KAAIhd,SAAC,8DAGzBI,EAAAA,EAAAA,MAACyc,EAAAA,EAAU,CAACG,QAAQ,QAAOhd,SAAA,CAAC,kCACMyf,EAAagD,iBAAiB,gKAMhE,EC9OV,GAhD4BK,KAC1B,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,MAEjBC,EAAUC,KADAC,EAAAA,EAAAA,OACejX,EAAAA,EAAAA,UAAc,QACvCkX,EAAgBC,IAAqBnX,EAAAA,EAAAA,WAAkB,GAe9D,OAbAG,EAAAA,EAAAA,YAAU,KACc5I,WACpB,GAAIsf,EAAa,CACf,MAAMO,GAAUvf,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,YAAakf,EAAYQ,KAC3CC,QAAiBlf,EAAAA,EAAAA,IAAOgf,GAC1BE,EAASjf,UACX2e,EAAYM,EAASxf,OAEzB,GAEFyf,EAAe,GACd,CAACV,KAGF3iB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,sBAAqBiB,SAAA,EAClCT,EAAAA,EAAAA,KAACmkB,EAAAA,EAAO,CAACT,SAAUA,KACnB7iB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,CAAC,mBAE1BT,EAAAA,EAAAA,KAAA,UACEiB,QAASA,IAAM6iB,GAAmBD,GAClChb,MAAO,CACLgW,WAAY,OACZzG,QAAS,WACT5K,gBAAiBqW,EAAiB,UAAY,UAC9Cle,MAAO,QACPwZ,OAAQ,OACRiF,aAAc,MACdC,OAAQ,UACRC,SAAU,QACV7jB,SAEDojB,EAAiB,mBAAqB,iCAG3C7jB,EAAAA,EAAAA,KAACQ,EAAAA,EAAU,IACVqjB,GAAiB7jB,EAAAA,EAAAA,KAAC8hB,GAAiB,KAAM9hB,EAAAA,EAAAA,KAACiS,EAAW,SAEpD,C,kECpDH,SAAS8N,EAAuBphB,GACrC,OAAOC,EAAAA,EAAAA,IAAqB,aAAcD,EAC5C,CACA,MACA,GADuBE,EAAAA,EAAAA,GAAuB,aAAc,CAAC,OAAQ,WAAY,YAAa,QAAS,SAAU,WAAY,QAAS,WAAY,eAAgB,uBAAwB,iBAAkB,gBAAiB,UAAW,mB,4GCHjO,SAAS0lB,EAA2B5lB,GACzC,OAAOC,EAAAA,EAAAA,IAAqB,iBAAkBD,EAChD,EAC2BE,EAAAA,EAAAA,GAAuB,iBAAkB,CAAC,S,aCKrE,MASM2lB,GAAkBzlB,EAAAA,EAAAA,IAAO,MAAO,CACpCE,KAAM,iBACNN,KAAM,QAFgBI,CAGrB,CACDqZ,QAAS,GACT,eAAgB,CACda,cAAe,MAqDnB,EAlDiC9Z,EAAAA,YAAiB,SAAqBC,EAASC,GAC9E,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,oBAEF,UACJO,EAAS,UACTgb,EAAY,SACT9a,GACDJ,EACEK,EAAa,IACdL,EACHkb,aAEI5a,EAhCkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoBykB,EAA4B3kB,EAAQ,EAyBjDG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKwkB,EAAiB,CACxC5I,GAAIpB,EACJhb,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BG,WAAYA,EACZN,IAAKA,KACFK,GAEP,G", "sources": ["../node_modules/@mui/material/esm/Card/cardClasses.js", "../node_modules/@mui/material/esm/Card/Card.js", "../node_modules/@mui/material/esm/ListItemText/listItemTextClasses.js", "components/shared/StatsCards.tsx", "components/shared/Modal.tsx", "components/admin/business/utils/cardUtils.ts", "components/admin/business/hooks/useCardManagement.ts", "components/admin/business/hooks/usePageConfiguration.ts", "components/admin/business/components/CardSelector.tsx", "components/admin/business/components/CardManagement.tsx", "components/admin/business/components/FieldConfigItem.tsx", "components/admin/business/components/PageBuilderContent.tsx", "components/admin/business/types/PageBuilderTypes.ts", "components/admin/business/hooks/useOfficeDataSimple.ts", "components/admin/business/components/CheckboxDropdown.tsx", "components/admin/business/components/ReportConfiguration.tsx", "components/admin/business/PageBuilder.tsx", "components/admin/business/hooks/usePageBuilderState.ts", "../node_modules/@mui/material/esm/ListItem/listItemClasses.js", "../node_modules/@mui/material/esm/ListItemButton/listItemButtonClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/listItemSecondaryActionClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/ListItemSecondaryAction.js", "../node_modules/@mui/material/esm/ListItem/ListItem.js", "../node_modules/@mui/material/esm/ListItemText/ListItemText.js", "../node_modules/@mui/material/esm/Divider/Divider.js", "components/admin/business/hooks/useOfficeDataEnhanced.ts", "components/admin/business/OfficeLoadingTest.tsx", "components/admin/AdminPage.tsx", "../node_modules/@mui/material/esm/Divider/dividerClasses.js", "../node_modules/@mui/material/esm/CardContent/cardContentClasses.js", "../node_modules/@mui/material/esm/CardContent/CardContent.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root']);\nexport default cardClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getCardUtilityClass } from \"./cardClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardUtilityClass, classes);\n};\nconst CardRoot = styled(Paper, {\n  name: 'MuiCard',\n  slot: 'Root'\n})({\n  overflow: 'hidden'\n});\nconst Card = /*#__PURE__*/React.forwardRef(function Card(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCard'\n  });\n  const {\n    className,\n    raised = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    raised\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardRoot, {\n    className: clsx(classes.root, className),\n    elevation: raised ? 8 : undefined,\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Card.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the card will use raised styling.\n   * @default false\n   */\n  raised: chainPropTypes(PropTypes.bool, props => {\n    if (props.raised && props.variant === 'outlined') {\n      return new Error('MUI: Combining `raised={true}` with `variant=\"outlined\"` has no effect.');\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Card;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemTextUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemText', slot);\n}\nconst listItemTextClasses = generateUtilityClasses('MuiListItemText', ['root', 'multiline', 'dense', 'inset', 'primary', 'secondary']);\nexport default listItemTextClasses;", "import React from 'react';\nimport './StatsCards.css';\n\nconst stats = [\n  { title: 'SB Accounts', value: 123456 },\n  { title: 'BD Revenue', value: '₹24,343' },\n  { title: 'No. Aadhaar Trans', value: 1259 },\n  { title: 'PLI', value: '₹99,99,999' }\n];\n\nconst StatsCards: React.FC = () => {\n  return (\n    <div className=\"stats-grid\">\n      {stats.map((stat, index) => (\n        <div className={`stat-card card-${index}`} key={index}>\n          <h3>{stat.title}</h3>\n          <p className=\"stat-value\">{stat.value}</p>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default StatsCards;", "import React from 'react';\nimport './Modal.css';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n}\n\nconst Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={e => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2>{title}</h2>\n          <button className=\"close-button\" onClick={onClose}>&times;</button>\n        </div>\n        <div className=\"modal-body\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;", "import { <PERSON>a<PERSON>older, FaFile<PERSON>lt, Fa<PERSON>og, FaFolderOpen } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\n// Helper function to generate card style (icon and color)\nexport const generateCardStyle = (title: string) => {\n  const hash = title\n    .split('')\n    .reduce((acc, char) => acc + char.charCodeAt(0), 0);\n  \n  const icons = [FaFolder, FaFileAlt, FaCog, FaFolderOpen]; // Add more icons if needed\n  const colors = ['#FFC107', '#2196F3', '#4CAF50', '#E91E63', '#9C27B0'];\n\n  const icon = icons[hash % icons.length];\n  const color = colors[hash % colors.length];\n  \n  return { icon, color };\n};\n\n// Helper function to check if a card is a main card\nexport const isMainCard = (cardId: string, allCategories: Category[]): boolean => {\n  const card = allCategories.find(c => c.id === cardId);\n  return card ? !card.parentId : false;\n};\n\n// Helper function to check if a card is a leaf card\nexport const isLeafCard = (cardId: string, allCategories: Category[]): boolean => {\n  return !allCategories.some(c => c.parentId === cardId);\n};\n\n// Helper function to organize cards into a tree structure\nexport const organizeCards = (list: Category[]): Category[] => {\n  const map: { [key: string]: Category } = {};\n  const roots: Category[] = [];\n  list.forEach(item => {\n    map[item.id] = { ...item, children: [] }; \n  });\n  list.forEach(item => {\n    if (item.parentId && map[item.parentId]) {\n      map[item.parentId].children?.push(map[item.id]);\n    } else {\n      roots.push(map[item.id]);\n    }\n  });\n  return roots;\n};\n\n// Helper function to get all descendant IDs\nexport const getAllDescendantIds = (parentId: string, allCategories: Category[]): string[] => {\n  let descendants: string[] = [];\n  const children = allCategories.filter(c => c.parentId === parentId);\n  for (const child of children) {\n    descendants.push(child.id);\n    descendants = descendants.concat(getAllDescendantIds(child.id, allCategories));\n  }\n  return descendants;\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc, collection, getDocs, writeBatch, deleteDoc, updateDoc } from 'firebase/firestore';\nimport { Category } from '../types/PageBuilderTypes';\nimport { generateCardStyle, getAllDescendantIds } from '../utils/cardUtils';\n\ninterface UseCardManagementProps {\n  categories: Category[];\n  setCategories: (categories: Category[]) => void;\n  selectedCard: string;\n  setSelectedCard: (card: string) => void;\n  newCardId: string;\n  setNewCardId: (id: string) => void;\n  newCardTitle: string;\n  setNewCardTitle: (title: string) => void;\n  actionType: string;\n  setActionType: (type: string) => void;\n  setIsLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setShowConfirmModal: (show: boolean) => void;\n  setIsAddingNewCard: (adding: boolean) => void;\n  setPageConfig: (config: any) => void;\n  setFields: (fields: any[]) => void;\n  setEditingCard: (card: Category | null) => void;\n  setShowEditModal: (show: boolean) => void;\n  setCardToDelete: (id: string | null) => void;\n  setShowDeleteConfirmModal: (show: boolean) => void;\n}\n\nexport const useCardManagement = (props: UseCardManagementProps) => {\n  const {\n    categories,\n    setCategories,\n    selectedCard,\n    setSelectedCard,\n    newCardId,\n    setNewCardId,\n    newCardTitle,\n    setNewCardTitle,\n    actionType,\n    setActionType,\n    setIsLoading,\n    setError,\n    setSuccess,\n    setShowConfirmModal,\n    setIsAddingNewCard,\n    setPageConfig,\n    setFields,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n  } = props;\n\n  const fetchCategories = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const querySnapshot = await getDocs(collection(db, 'categories'));\n      const fetchedCategories = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Category));\n      setCategories(fetchedCategories);\n    } catch (err) {\n      setError('Failed to fetch categories.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [setCategories, setIsLoading, setError]);\n\n  const checkDuplicateId = async (id: string): Promise<boolean> => {\n    const docRef = doc(db, 'categories', id);\n    const docSnap = await getDoc(docRef);\n    return docSnap.exists();\n  };\n\n  const handleAddNewCard = async () => {\n    if (!newCardId || !newCardTitle) {\n      setError('Report ID and Title are required.');\n      return;\n    }\n    setIsLoading(true);\n    const isDuplicate = await checkDuplicateId(newCardId);\n    if (isDuplicate) {\n      setError('This Report ID already exists. Please use a unique ID.');\n      setIsLoading(false);\n      return;\n    }\n    setIsLoading(false);\n    setShowConfirmModal(true);\n  };\n\n  const handleConfirmCreate = async () => {\n    if (!newCardId || !newCardTitle) {\n        setError('Report ID and Title cannot be empty.');\n        setShowConfirmModal(false);\n        return;\n    }\n    let parentIdToSet: string | null = null;\n    if (actionType === 'createNestedCard' && selectedCard) {\n      parentIdToSet = selectedCard;\n    } else if (actionType === 'addNewCardGlobal') {\n      parentIdToSet = null; \n    } else if (selectedCard && actionType !== 'addNewCardGlobal') {\n        parentIdToSet = selectedCard;\n    } else if (!selectedCard && actionType !== 'createNestedCard') { \n        parentIdToSet = null;\n    }\n\n    const parentPath = parentIdToSet ? categories.find(c => c.id === parentIdToSet)?.path : '/categories';\n    const newPath = `${parentPath}/${newCardId}`.replace(/\\/+/g, '/');\n\n    try {\n      setIsLoading(true);\n      setShowConfirmModal(false);\n      const cardRef = doc(db, 'categories', newCardId);\n      const { icon: generatedIcon, color: generatedColor } = generateCardStyle(newCardTitle);\n      \n      await setDoc(cardRef, {\n        id: newCardId,\n        title: newCardTitle,\n        path: newPath,\n        parentId: parentIdToSet,\n        lastUpdated: new Date().toISOString(),\n        icon: generatedIcon.name,\n        color: generatedColor,\n        fields: [],\n        isPage: true,\n        pageId: newCardId,\n      });\n  \n      await fetchCategories(); \n      \n      setNewCardId('');\n      setNewCardTitle('');\n      setIsAddingNewCard(false);\n      setActionType(''); \n      setSelectedCard(newCardId);\n      setSuccess(`Report \"${newCardTitle}\" has been created successfully!`);\n      setTimeout(() => setSuccess(null), 3000);\n      \n    } catch (err) {\n      setError('Error creating new report. Check console for details.');\n      console.error('Error creating card:', err);\n    } finally {\n      setIsLoading(false); \n    }\n  };\n\n  const handleEditCard = (card: Category) => {\n    setEditingCard(card);\n    setNewCardTitle(card.title);\n    setShowEditModal(true);\n  };\n\n  const handleUpdateCard = async () => {\n    const editingCard = categories.find(c => c.id === selectedCard);\n    if (!editingCard || !newCardTitle) return;\n    try {\n      setIsLoading(true);\n      const cardRef = doc(db, 'categories', editingCard.id);\n      await updateDoc(cardRef, { title: newCardTitle, lastUpdated: new Date().toISOString() });\n      await fetchCategories();\n      setShowEditModal(false);\n      setEditingCard(null);\n      setNewCardTitle('');\n      setSuccess('Report updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to update report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDeleteClick = (cardId: string) => {\n    setCardToDelete(cardId);\n    setShowDeleteConfirmModal(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (!selectedCard) return;\n    setIsLoading(true);\n    try {\n      const batch = writeBatch(db);\n      const allDescendants = getAllDescendantIds(selectedCard, categories);\n      const idsToDelete = [selectedCard, ...allDescendants];\n\n      for (const id of idsToDelete) {\n        batch.delete(doc(db, 'categories', id));\n        batch.delete(doc(db, 'pages', id));\n      }\n      await batch.commit();\n      await fetchCategories();\n\n      setShowDeleteConfirmModal(false);\n      setCardToDelete(null);\n      setSelectedCard('');\n      setPageConfig(null);\n      setFields([]);\n      setSuccess('Report and all its nested items deleted successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to delete report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return {\n    fetchCategories,\n    handleAddNewCard,\n    handleConfirmCreate,\n    handleEditCard,\n    handleUpdateCard,\n    handleDeleteClick,\n    handleConfirmDelete,\n  };\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc } from 'firebase/firestore';\nimport { FormField, PageConfig, Category } from '../types/PageBuilderTypes';\nimport { <PERSON><PERSON>ield as DynamicFormField, FormConfig as DynamicFormConfig, FormFieldOption } from '../../../shared/DynamicForm';\nimport { supabasePageService } from '../services/supabasePageService';\n\ninterface UsePageConfigurationProps {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  setPageConfig: (config: PageConfig | null) => void;\n  fields: FormField[];\n  setFields: (fields: FormField[]) => void;\n  setAvailableDynamicFields: (fields: DynamicFormField[]) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setPreviewContent: (content: string) => void;\n  setIsPreviewOpen: (open: boolean) => void;\n  // New dropdown values - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  // Setters for dropdown values\n  setSelectedRegions: (regions: string[]) => void;\n  setSelectedDivisions: (divisions: string[]) => void;\n  setSelectedOffices: (offices: string[]) => void;\n  setSelectedFrequency: (frequency: string) => void;\n}\n\nexport const usePageConfiguration = (props: UsePageConfigurationProps) => {\n  const {\n    categories,\n    selectedCard,\n    pageConfig,\n    setPageConfig,\n    fields,\n    setFields,\n    setAvailableDynamicFields,\n    setLoading,\n    setError,\n    setSuccess,\n    setPreviewContent,\n    setIsPreviewOpen,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  } = props;\n\n  const fetchDynamicFormFields = useCallback(async (formId: string) => {\n    if (!formId) return;\n    console.log(`Fetching dynamic form fields for formId: ${formId}`);\n    try {\n      const formConfigRef = doc(db, 'formConfigs', formId);\n      const formConfigSnap = await getDoc(formConfigRef);\n      if (formConfigSnap.exists()) {\n        const formConfigData = formConfigSnap.data() as DynamicFormConfig;\n        setAvailableDynamicFields(formConfigData.fields || []);\n        console.log('Fetched dynamic fields:', formConfigData.fields);\n      } else {\n        console.log(`No dynamic form configuration found for formId: ${formId}`);\n        setAvailableDynamicFields([]);\n      }\n    } catch (err) {\n      console.error('Error fetching dynamic form fields:', err);\n      setError('Failed to fetch dynamic form fields.');\n      setAvailableDynamicFields([]);\n    }\n  }, [setAvailableDynamicFields, setError]);\n\n  const loadPageConfig = useCallback(async (cardId: string) => {\n    if (!cardId) {\n      console.log('loadPageConfig called with no cardId');\n      return;\n    }\n    console.log(`loadPageConfig called for cardId: ${cardId}`);\n    setLoading(true);\n    setError(null);\n    try {\n      // Try loading from Firebase first\n      const docRef = doc(db, 'pages', cardId);\n      const docSnap = await getDoc(docRef);\n\n      let data: PageConfig | null = null;\n\n      if (docSnap.exists()) {\n        // Loading from Firebase\n        data = docSnap.data() as PageConfig;\n      } else {\n        // If not found in Firebase, try Supabase\n        try {\n          data = await supabasePageService.loadPageConfig(cardId);\n        } catch (supabaseError) {\n          // Not found in either database, will create new config\n        }\n      }\n\n      if (data) {\n        setPageConfig(data);\n        setFields(data.fields || []);\n        // Load saved dropdown values - handle both old single values and new arrays\n        setSelectedRegions(data.selectedRegions || (data.selectedRegion ? [data.selectedRegion] : []));\n        setSelectedDivisions(data.selectedDivisions || (data.selectedDivision ? [data.selectedDivision] : []));\n        setSelectedOffices(data.selectedOffices || (data.selectedOffice ? [data.selectedOffice] : []));\n        setSelectedFrequency(data.selectedFrequency || '');\n      } else {\n        // Create new page config\n        const card = categories.find(c => c.id === cardId);\n        setPageConfig({\n          id: cardId,\n          title: card?.title || 'New Page',\n          fields: [],\n          lastUpdated: new Date().toISOString(),\n        });\n        setFields([]);\n        // Reset dropdown values for new page\n        setSelectedRegions([]);\n        setSelectedDivisions([]);\n        setSelectedOffices([]);\n        setSelectedFrequency('');\n      }\n    } catch (err) {\n      setError('Failed to load page configuration.');\n      console.error(err);\n      setPageConfig(null);\n      setFields([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [categories, setLoading, setError, setPageConfig, setFields, setSelectedRegions, setSelectedDivisions, setSelectedOffices, setSelectedFrequency]);\n\n  const addField = () => {\n    const newField: FormField = {\n      id: `field_${Date.now()}`,\n      type: 'text',\n      label: 'New Field',\n      placeholder: '',\n      options: [],\n      required: false,\n      region: '',\n      division: '',\n      office: '',\n    };\n    setFields([...fields, newField]);\n  };\n\n  const addFieldFromDynamic = (dynamicField: DynamicFormField) => {\n    console.log('Attempting to add dynamic field:', dynamicField);\n    const newField: FormField = {\n      id: dynamicField.id,\n      type: dynamicField.type,\n      label: dynamicField.label,\n      placeholder: dynamicField.placeholder,\n      options: dynamicField.options ? dynamicField.options.map((opt: string | FormFieldOption) => {\n        if (typeof opt === 'string') {\n          return { label: opt, value: opt };\n        } else {\n          return { label: opt.label, value: opt.value };\n        }\n      }) : undefined,\n      required: dynamicField.required,\n      defaultValue: dynamicField.defaultValue,\n      min: dynamicField.min,\n      max: dynamicField.max,\n      sectionTitle: undefined,\n      columns: undefined,\n      buttonText: undefined,\n      buttonType: undefined,\n      onClickAction: undefined,\n      value: undefined,\n    };\n\n    if (fields.some(field => field.id === newField.id)) {\n        console.warn(`Duplicate field ID detected: \"${newField.id}\". Field not added.`);\n        setError(`Field with ID \"${newField.id}\" already exists in the page configuration.`);\n        setTimeout(() => setError(null), 3000);\n        return;\n    }\n\n    console.log('Adding new field to state:', newField);\n    setFields([...fields, newField]);\n    setSuccess(`Added field \"${newField.label}\" to page configuration.`);\n    setTimeout(() => setSuccess(null), 3000);\n  };\n\n  const updateField = (index: number, updatedField: FormField) => {\n    const updatedFields = [...fields];\n    updatedFields[index] = updatedField;\n    setFields(updatedFields);\n  };\n\n  const removeField = (index: number) => {\n    setFields(fields.filter((_, i) => i !== index));\n  };\n\n  const handleSave = async () => {\n    if (!selectedCard || !pageConfig) {\n      setError('No report selected or page configuration loaded.');\n      return;\n    }\n\n    // Validate that report frequency is selected\n    if (!selectedFrequency) {\n      setError('Report frequency is required. Please select a frequency before saving.');\n      return;\n    }\n\n    setLoading(true);\n    console.log('Attempting to save page configuration for cardId:', selectedCard);\n    console.log('Fields being saved:', fields);\n    console.log('Report frequency:', selectedFrequency);\n\n    try {\n      const cleanedFields = fields.map(field => {\n        const cleanedField: any = {};\n        for (const key in field) {\n          if (field[key] !== undefined) {\n            cleanedField[key] = field[key];\n          } else {\n            cleanedField[key] = null;\n          }\n        }\n        return cleanedField;\n      });\n\n      const updatedPageConfig: PageConfig = {\n        ...pageConfig,\n        id: selectedCard,\n        title: categories.find(c => c.id === selectedCard)?.title || pageConfig.title,\n        fields: cleanedFields,\n        lastUpdated: new Date().toISOString(),\n        selectedRegions,\n        selectedDivisions,\n        selectedOffices,\n        selectedFrequency,\n      };\n\n      // Save to both Firebase and Supabase\n      const savePromises = [];\n\n      // Save to Firebase\n      savePromises.push(\n        setDoc(doc(db, 'pages', selectedCard), updatedPageConfig)\n          .catch(err => {\n            console.error('Firebase save failed:', err);\n            throw new Error(`Firebase save failed: ${err.message}`);\n          })\n      );\n\n      // Save to Supabase\n      savePromises.push(\n        supabasePageService.savePageConfig(updatedPageConfig)\n          .catch(err => {\n            console.error('Supabase save failed:', err);\n            throw new Error(`Supabase save failed: ${err.message}`);\n          })\n      );\n\n      // Wait for both saves to complete\n      await Promise.all(savePromises);\n\n      setPageConfig(updatedPageConfig);\n      setSuccess('Page configuration saved successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n\n    } catch (err) {\n      console.error('Failed to save page configuration:', err);\n      setError(`Failed to save page configuration: ${err instanceof Error ? err.message : 'Unknown error'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePreview = () => {\n    if (!pageConfig || fields.length === 0) {\n      alert('No page configuration or fields to preview.');\n      return;\n    }\n\n    const generatedPreview = `\n      <h1>${pageConfig.title}</h1>\n      <form>\n        ${fields.map(field => {\n          let fieldHtml = '';\n          switch (field.type) {\n            case 'text':\n            case 'number':\n            case 'date':\n            case 'textarea':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <input type=\"${field.type}\" class=\"form-control\" placeholder=\"${field.placeholder || ''}\" ${field.required ? 'required' : ''} />\n                </div>\n              `;\n              break;\n            case 'dropdown':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <select class=\"form-control\" ${field.required ? 'required' : ''}>\n                    <option value=\"\">Select ${field.label}</option>\n                    ${field.options?.map(option => `<option value=\"${option.value}\">${option.label}</option>`).join('') || ''}\n                  </select>\n                </div>\n              `;\n              break;\n            case 'checkbox':\n              fieldHtml = `\n                <div class=\"form-check mb-3\">\n                  <input type=\"checkbox\" class=\"form-check-input\" id=\"${field.id}\" ${field.required ? 'required' : ''} />\n                  <label class=\"form-check-label\" for=\"${field.id}\">${field.label}${field.required ? ' *' : ''}</label>\n                </div>\n              `;\n              break;\n            case 'radio':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  ${field.options?.map((option, i) => `\n                    <div class=\"form-check\">\n                      <input class=\"form-check-input\" type=\"radio\" name=\"${field.id}\" id=\"${field.id}-${i}\" value=\"${option.value}\" ${field.required ? 'required' : ''}>\n                      <label class=\"form-check-label\" for=\"${field.id}-${i}\">${option.label}</label>\n                    </div>\n                  `).join('') || ''}\n                </div>\n              `;\n              break;\n            case 'section':\n              fieldHtml = `\n                <div class=\"card mt-3 mb-3\">\n                  <div class=\"card-header\">${field.sectionTitle || 'Section'}</div>\n                  <div class=\"card-body\">\n                    <p>Fields for this section would appear here in the actual form.</p>\n                  </div>\n                </div>\n              `;\n              break;\n            case 'button':\n              fieldHtml = `\n                <button type=\"button\" class=\"btn btn-primary mt-3\">${field.buttonText || 'Button'}</button>\n              `;\n              break;\n            default:\n              fieldHtml = `<p>Unsupported field type: ${field.type}</p>`;\n          }\n          return fieldHtml;\n        }).join('')}\n      </form>\n    `;\n\n    setPreviewContent(generatedPreview);\n    setIsPreviewOpen(true);\n  };\n\n  return {\n    fetchDynamicFormFields,\n    loadPageConfig,\n    addField,\n    addFieldFromDynamic,\n    updateField,\n    removeField,\n    handleSave,\n    handlePreview,\n  };\n};\n", "import React from 'react';\nimport { Category } from '../types/PageBuilderTypes';\nimport { organizeCards, isLeafCard, isMainCard } from '../utils/cardUtils';\n\ninterface CardSelectorProps {\n  categories: Category[];\n  selectedCard: string;\n  onCardChange: (cardId: string) => void;\n  actionType: string;\n  onActionChange: (action: string) => void;\n  isLoading: boolean;\n  onCreateAction: () => void;\n  onWebPageAction: () => void;\n}\n\nconst CardSelector: React.FC<CardSelectorProps> = ({\n  categories,\n  selectedCard,\n  onCardChange,\n  actionType,\n  onActionChange,\n  isLoading,\n  onCreateAction,\n  onWebPageAction,\n}) => {\n  const renderCardOptions = (cards: Category[], level = 0): React.ReactElement[] => {\n    return cards.flatMap(card => {\n      // Handle undefined or empty titles gracefully\n      let displayTitle = card.title;\n\n      // If title is undefined, null, or empty, check if it's an MMU-related entry\n      if (!displayTitle || displayTitle.trim() === '') {\n        // Check if this might be an MMU entry based on ID or other properties\n        if (card.id === 'mmu' || card.id.toLowerCase().includes('mmu')) {\n          displayTitle = 'MMU';\n        } else {\n          displayTitle = '[Unnamed Report]';\n        }\n      }\n\n      return [\n        <option key={card.id} value={card.id} style={{ paddingLeft: `${level * 20}px` }}>\n          {`${'--'.repeat(level)} ${displayTitle}`}\n        </option>,\n        ...(card.children && card.children.length > 0 ? renderCardOptions(card.children, level + 1) : []),\n      ];\n    });\n  };\n\n  const handleCardChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newSelectedCard = e.target.value;\n    onCardChange(newSelectedCard);\n  };\n\n  const handleActionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newAction = e.target.value;\n    onActionChange(newAction);\n    \n    if (newAction === 'createNestedCard' || newAction === 'addNewCardGlobal') {\n      onCreateAction();\n    } else if (newAction === 'createWebPage') {\n      onWebPageAction();\n    }\n  };\n\n  return (\n    <div className=\"card-selector\">\n      <select\n        value={selectedCard}\n        onChange={handleCardChange}\n        className=\"form-select\"\n        disabled={isLoading}\n      >\n        <option value=\"\">{isLoading ? 'Loading Reports...' : 'Select or Create New Report'}</option>\n        {renderCardOptions(organizeCards(categories))}\n      </select>\n\n      <div className=\"action-dropdown-container\">\n        <select\n          value={actionType}\n          onChange={handleActionChange}\n          className=\"form-select action-dropdown\"\n        >\n          <option value=\"\">Select Action...</option>\n          <option value=\"addNewCardGlobal\" disabled={!!selectedCard}>\n            Create New Main Report\n          </option>\n          {selectedCard && (\n            <>\n              <option value=\"createNestedCard\">\n                Create Nested Report\n              </option>\n              <option\n                value=\"createWebPage\"\n                disabled={!isLeafCard(selectedCard, categories) || isMainCard(selectedCard, categories)}\n              >\n                Create/Edit Web Page for this Report\n              </option>\n            </>\n          )}\n        </select>\n      </div>\n    </div>\n  );\n};\n\nexport default CardSelector;\n", "import React from 'react';\nimport { FaEdit, FaTrash } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\ninterface CardManagementProps {\n  selectedCard: string;\n  categories: Category[];\n  onEditCard: (card: Category) => void;\n  onDeleteCard: (cardId: string) => void;\n}\n\nconst CardManagement: React.FC<CardManagementProps> = ({\n  selectedCard,\n  categories,\n  onEditCard,\n  onDeleteCard,\n}) => {\n  const selectedCategory = categories.find(c => c.id === selectedCard);\n\n  if (!selectedCategory) {\n    return null;\n  }\n\n  return (\n    <div className=\"card-management\">\n      <h3>Report Details: \"{selectedCategory.title}\"</h3>\n      <div className=\"card-actions\">\n        <button\n          onClick={() => onEditCard(selectedCategory)}\n          className=\"edit-button btn btn-outline-primary btn-sm me-2\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaEdit as React.ComponentType<any>)} Edit Name\n        </button>\n        <button\n          onClick={() => onDeleteCard(selectedCard)}\n          className=\"delete-button btn btn-outline-danger btn-sm\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaTrash as React.ComponentType<any>)} Delete Report\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CardManagement;\n", "import React from 'react';\nimport { FaTrash } from 'react-icons/fa';\nimport { FormField } from '../types/PageBuilderTypes';\n\ninterface FieldConfigItemProps {\n  field: FormField;\n  index: number;\n  onUpdate: (index: number, field: FormField) => void;\n  onRemove: (index: number) => void;\n}\n\nconst FieldConfigItem: React.FC<FieldConfigItemProps> = ({\n  field,\n  index,\n  onUpdate,\n  onRemove,\n}) => {\n  const handleOptionChange = (optIndex: number, value: string, key: 'label' | 'value') => {\n    const newOptions = [...(field.options || [])];\n    newOptions[optIndex] = { ...newOptions[optIndex], [key]: value };\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const addOption = () => {\n    const newOptions = [...(field.options || []), { label: '', value: '' }];\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const removeOption = (optIndex: number) => {\n    const newOptions = field.options?.filter((_, i) => i !== optIndex);\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const handleDefaultValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { value, type } = e.target;\n    let newDefaultValue: any = value;\n    if (type === 'checkbox') {\n      newDefaultValue = (e.target as HTMLInputElement).checked;\n    }\n    onUpdate(index, { ...field, defaultValue: newDefaultValue });\n  };\n\n  return (\n    <div className=\"field-config-item card mb-3\">\n      <div className=\"card-header d-flex justify-content-between align-items-center\">\n        <strong>{field.label || 'Unnamed Field'}</strong> ({field.type})\n        <button onClick={() => onRemove(index)} className=\"btn btn-danger btn-sm\">\n          {React.createElement(FaTrash as React.ComponentType<any>)} Remove\n        </button>\n      </div>\n      <div className=\"card-body\">\n        {/* Field Type Selector */}\n        <div className=\"form-group\">\n          <label htmlFor={`field-type-${index}`} className=\"form-label\">Type: </label>\n          <select\n            id={`field-type-${index}`}\n            className=\"form-control\"\n            value={field.type}\n            onChange={(e) => onUpdate(index, {\n              ...field, \n              type: e.target.value as FormField['type'], \n              options: field.type !== 'dropdown' && field.type !== 'radio' && field.type !== 'checkbox-group' ? undefined : field.options, \n              placeholder: field.type === 'section' || field.type === 'button' ? undefined : field.placeholder \n            })}\n          >\n            <option value=\"text\">Text</option>\n            <option value=\"textarea\">Textarea</option>\n            <option value=\"number\">Number</option>\n            <option value=\"date\">Date</option>\n            <option value=\"dropdown\">Dropdown</option>\n            <option value=\"radio\">Radio Group</option>\n            <option value=\"checkbox\">Checkbox (Single)</option>\n            <option value=\"checkbox-group\">Checkbox Group</option>\n            <option value=\"switch\">Switch</option>\n            <option value=\"file\">File Upload</option>\n            <option value=\"section\">Section Header</option>\n            <option value=\"button\">Button</option>\n          </select>\n        </div>\n\n        <div className=\"form-group\">\n          <label htmlFor={`field-label-${index}`} className=\"form-label\">Label: </label>\n          <input\n            id={`field-label-${index}`}\n            type=\"text\"\n            className=\"form-control\"\n            value={field.label}\n            onChange={(e) => onUpdate(index, {...field, label: e.target.value})}\n            required\n          />\n        </div>\n\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-placeholder-${index}`} className=\"form-label\">Placeholder: </label>\n            <input\n              id={`field-placeholder-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.placeholder || ''}\n              onChange={(e) => onUpdate(index, {...field, placeholder: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'number' && (\n          <>\n            <div className=\"form-group\">\n              <label htmlFor={`field-min-${index}`} className=\"form-label\">Min Value: </label>\n              <input\n                id={`field-min-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.min === undefined ? '' : field.min}\n                onChange={(e) => onUpdate(index, {...field, min: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor={`field-max-${index}`} className=\"form-label\">Max Value: </label>\n              <input\n                id={`field-max-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.max === undefined ? '' : field.max}\n                onChange={(e) => onUpdate(index, {...field, max: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n          </>\n        )}\n\n        {['dropdown', 'radio', 'checkbox-group'].includes(field.type) && (\n          <div className=\"form-group field-options-config\">\n            <label className=\"form-label\">Options: </label>\n            {field.options?.map((opt, optIndex) => (\n              <div key={optIndex} className=\"input-group mb-2\">\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Label\"\n                  value={opt.label}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'label')}\n                />\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Value\"\n                  value={opt.value}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'value')}\n                />\n                <button type=\"button\" onClick={() => removeOption(optIndex)} className=\"btn btn-outline-danger\">\n                  Remove\n                </button>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addOption} className=\"btn btn-secondary btn-sm\">\n              Add Option\n            </button>\n          </div>\n        )}\n\n        {/* Default Value - Type specific handling */}\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n            <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <input\n                    id={`field-default-value-${index}`}\n                    type={field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n            </div>\n        )}\n\n        {(field.type === 'checkbox' || field.type === 'switch') && (\n            <div className=\"form-group form-check\">\n                <input\n                    id={`field-default-value-${index}`}\n                    type=\"checkbox\"\n                    className=\"form-check-input\"\n                    checked={Boolean(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n                <label htmlFor={`field-default-value-${index}`} className=\"form-check-label\">Default Checked: </label>\n            </div>\n        )}\n\n        {['dropdown', 'radio'].includes(field.type) && field.options && field.options.length > 0 && (\n             <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <select\n                    id={`field-default-value-${index}`}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                >\n                    <option value=\"\">-- Select Default --</option>\n                    {field.options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}\n                </select>\n            </div>\n        )}\n\n        {field.type === 'checkbox-group' && (\n            <div className=\"form-group\">\n                <label className=\"form-label\">Default Values (comma-separated): </label>\n                <input\n                    type=\"text\"\n                    className=\"form-control\"\n                    value={Array.isArray(field.defaultValue) ? field.defaultValue.join(',') : ''}\n                    onChange={(e) => onUpdate(index, {...field, defaultValue: e.target.value.split(',').map(s => s.trim()).filter(s => s)})}\n                    placeholder=\"value1,value2\"\n                />\n            </div>\n        )}\n\n        {field.type === 'button' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-button-text-${index}`} className=\"form-label\">Button Text: </label>\n            <input\n              id={`field-button-text-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.buttonText || ''}\n              onChange={(e) => onUpdate(index, {...field, buttonText: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'section' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-section-title-${index}`} className=\"form-label\">Section Title: </label>\n            <input\n              id={`field-section-title-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.sectionTitle || ''}\n              onChange={(e) => onUpdate(index, {...field, sectionTitle: e.target.value})}\n            />\n          </div>\n        )}\n\n        {/* Required Checkbox (excluding button and section) */}\n        {!['button', 'section'].includes(field.type) && (\n          <div className=\"form-group form-check\">\n            <input\n              id={`field-required-${index}`}\n              type=\"checkbox\"\n              className=\"form-check-input\"\n              checked={!!field.required}\n              onChange={(e) => onUpdate(index, {...field, required: e.target.checked})}\n            />\n            <label htmlFor={`field-required-${index}`} className=\"form-check-label\"> Required</label>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FieldConfigItem;\n", "import React from 'react';\nimport { FaPlus, FaSave } from 'react-icons/fa';\nimport { FormField, PageConfig } from '../types/PageBuilderTypes';\nimport FieldConfigItem from './FieldConfigItem';\n\ninterface PageBuilderContentProps {\n  pageConfig: PageConfig;\n  fields: FormField[];\n  onAddField: () => void;\n  onUpdateField: (index: number, field: FormField) => void;\n  onRemoveField: (index: number) => void;\n  onSave: () => void;\n  onPreview: () => void;\n  loading: boolean;\n}\n\nconst PageBuilderContent: React.FC<PageBuilderContentProps> = ({\n  pageConfig,\n  fields,\n  onAddField,\n  onUpdateField,\n  onRemoveField,\n  onSave,\n  onPreview,\n  loading,\n}) => {\n  return (\n    <div className=\"builder-content\">\n      <h4>Page Configuration for: {pageConfig.title}</h4>\n      \n      <h5>Current Page Fields:</h5>\n      {fields.map((field, index) => (\n        <FieldConfigItem\n          key={field.id || index}\n          field={field}\n          index={index}\n          onUpdate={onUpdateField}\n          onRemove={onRemoveField}\n        />\n      ))}\n      \n      <button onClick={onAddField} className=\"btn btn-info mt-3\">\n        {React.createElement(FaPlus as React.ComponentType<any>)} Add Field\n      </button>\n      \n      <button \n        onClick={onSave} \n        className=\"btn btn-success mt-3 ms-2\" \n        disabled={loading || !pageConfig || fields.length === 0}\n      >\n        {React.createElement(FaSave as React.ComponentType<any>)} {loading ? 'Saving...' : 'Save Page Configuration'}\n      </button>\n      \n      <button \n        onClick={onPreview} \n        className=\"btn btn-secondary mt-3 ms-2\" \n        disabled={!pageConfig || fields.length === 0}\n      >\n        Preview Page\n      </button>\n    </div>\n  );\n};\n\nexport default PageBuilderContent;\n", "// Interfaces for PageBuilder component\n\nexport interface FormFieldOption {\n  label: string;\n  value: string;\n}\n\n// NOTE: This FormField interface is for the PageBuilder's internal state\n// and represents the configuration being built for a specific 'page'.\n// It's slightly different from the DynamicFormField used by the DynamicForm component.\nexport interface FormField {\n  id: string;\n  type: 'text' | 'textarea' | 'number' | 'date' | 'dropdown' | 'radio' | 'checkbox' | 'checkbox-group' | 'section' | 'button' | 'file' | 'switch';\n  label: string;\n  placeholder?: string;\n  region?: string;\n  division?: string;\n  office?: string;\n  options?: FormFieldOption[]; // For dropdown, radio, checkbox\n  required?: boolean;\n  value?: any; // Current value of the field (might not be used in builder, but kept for consistency)\n  // For section type\n  sectionTitle?: string;\n  columns?: number; // Number of columns for fields within the section\n  // For button type\n  buttonText?: string;\n  buttonType?: string;\n  onClickAction?: string;\n  defaultValue?: any;\n  min?: number;\n  max?: number;\n  [key: string]: any; // Add this index signature\n}\n\nexport interface PageConfig {\n  id: string;\n  title: string;\n  fields: FormField[];\n  lastUpdated: string;\n  isPage?: boolean; // New field\n  pageId?: string;\n  // Report configuration - updated to support both old single values and new arrays\n  selectedRegion?: string; // Keep for backward compatibility\n  selectedDivision?: string; // Keep for backward compatibility\n  selectedOffice?: string; // Keep for backward compatibility\n  selectedRegions?: string[]; // New array-based selections\n  selectedDivisions?: string[]; // New array-based selections\n  selectedOffices?: string[]; // New array-based selections\n  selectedFrequency?: string;\n}\n\nexport interface Category {\n  id: string;\n  title: string;\n  path: string; // e.g., /categories/parent-id/child-id\n  parentId: string | null;\n  children?: Category[];\n  icon?: string; // Icon name (e.g., 'FaFolder')\n  color?: string; // Color for the icon/card\n  fields?: FormField[]; // If storing form fields directly on category for some reason\n  lastUpdated?: string;\n  isPage: boolean; // New field\n  pageId: string; \n}\n\nexport interface PageBuilderState {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  fields: FormField[];\n  availableDynamicFields: any[];\n  isLoading: boolean;\n  loading: boolean;\n  error: string | null;\n  success: string | null;\n  isAddingNewCard: boolean;\n  newCardId: string;\n  newCardTitle: string;\n  showConfirmModal: boolean;\n  editingCard: Category | null;\n  showEditModal: boolean;\n  cardToDelete: string | null;\n  showDeleteConfirmModal: boolean;\n  actionType: string;\n  isPreviewOpen: boolean;\n  previewContent: string;\n  // New dropdown states - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n}\n\n// Report frequency options\nexport interface ReportFrequency {\n  value: string;\n  label: string;\n}\n\nexport const REPORT_FREQUENCIES: ReportFrequency[] = [\n  { value: 'daily', label: 'Daily' },\n  { value: 'weekly', label: 'Weekly' },\n  { value: 'monthly', label: 'Monthly' }\n];\n\n// Location hierarchy interfaces - matching Supabase table structure\nexport interface Region {\n  id: string;\n  name: string;\n}\n\nexport interface Division {\n  id: string;\n  name: string;\n  region: string; // matches the Region column in Supabase\n}\n\nexport interface Office {\n  id: string; // Now uses office name instead of facility ID\n  name: string;\n  region: string; // matches the Region column in Supabase\n  division: string; // matches the Division column in Supabase\n  facilityId?: string; // Keep facility ID for reference/mapping\n}\n\n// Supabase office record interface (matches actual table structure)\nexport interface SupabaseOfficeRecord {\n  'Facility ID': string;\n  Region: string;\n  Division: string;\n  'Office name': string;\n}\n", "import { useState, useEffect } from 'react';\nimport { supabase } from '../../../../config/supabaseClient';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n}\n\nexport const useOfficeDataSimple = (): UseOfficeDataReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataSimple: Fetching with enhanced pagination...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allData = await OfficeService.fetchAllOfficeData();\n\n      console.log('✅ useOfficeDataSimple: Fetched', allData.length, 'office records');\n\n      // Process regions exactly like SQL: SELECT DISTINCT \"Region\" FROM offices ORDER BY \"Region\"\n      const distinctRegions = allData\n        ?.map(row => row.Region)\n        .filter((region, index, array) => array.indexOf(region) === index)\n        .filter((region): region is string => region != null && region.trim() !== '') // Type guard to ensure string\n        .sort();\n\n      // Process regions successfully\n\n      const regionsArray: Region[] = distinctRegions?.map(regionName => ({\n        id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: regionName,\n      })) || [];\n\n      // Process divisions exactly like SQL: SELECT DISTINCT \"Region\", \"Division\" FROM offices ORDER BY \"Region\", \"Division\"\n      const distinctDivisions = allData\n        ?.map(row => ({ region: row.Region, division: row.Division }))\n        .filter((item, index, array) =>\n          array.findIndex(x => x.region === item.region && x.division === item.division) === index\n        )\n        .filter((item): item is { region: string; division: string } =>\n          item.region != null && item.division != null &&\n          item.region.trim() !== '' && item.division.trim() !== ''\n        )\n        .sort((a, b) => a.region.localeCompare(b.region) || a.division.localeCompare(b.division));\n\n      const divisionsArray: Division[] = distinctDivisions?.map(item => ({\n        id: item.division.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: item.division,\n        region: item.region,\n      })) || [];\n\n      // Process all offices - USE OFFICE NAME AS ID instead of Facility ID\n      const officesArray: Office[] = allData\n        ?.filter(row => row['Office name'] && row.Region && row.Division)\n        .map(row => ({\n          id: row['Office name'], // ✅ FIXED: Use office name as ID for form targeting\n          name: row['Office name'],\n          region: row.Region || '',\n          division: row.Division || '',\n          facilityId: row['Office name'], // Use office name as facility ID for consistency\n        })) || [];\n\n      // Data processing completed successfully\n\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n\n    } catch (err) {\n      console.error('🚨 SIMPLE: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n  };\n};\n", "import React, { useState, useRef, useEffect } from 'react';\n\ninterface Option {\n  id: string;\n  name: string;\n}\n\ninterface CheckboxDropdownProps {\n  id: string;\n  label: string;\n  options: Option[];\n  selectedValues: string[];\n  onChange: (values: string[]) => void;\n  disabled?: boolean;\n  placeholder?: string;\n}\n\nconst CheckboxDropdown: React.FC<CheckboxDropdownProps> = ({\n  id,\n  label,\n  options,\n  selectedValues,\n  onChange,\n  disabled = false,\n  placeholder = \"-- Select Options --\"\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleCheckboxChange = (optionId: string) => {\n    if (selectedValues.includes(optionId)) {\n      // Remove from selection\n      onChange(selectedValues.filter(id => id !== optionId));\n    } else {\n      // Add to selection\n      onChange([...selectedValues, optionId]);\n    }\n  };\n\n  const handleSelectAll = () => {\n    if (selectedValues.length === options.length) {\n      // Deselect all\n      onChange([]);\n    } else {\n      // Select all\n      onChange(options.map(option => option.id));\n    }\n  };\n\n  const getDisplayText = () => {\n    if (selectedValues.length === 0) {\n      return placeholder;\n    } else if (selectedValues.length === 1) {\n      const selectedOption = options.find(option => option.id === selectedValues[0]);\n      return selectedOption?.name || placeholder;\n    } else {\n      return `${selectedValues.length} selected`;\n    }\n  };\n\n  const isAllSelected = selectedValues.length === options.length && options.length > 0;\n  const isIndeterminate = selectedValues.length > 0 && selectedValues.length < options.length;\n\n  return (\n    <div className=\"form-group\">\n      <label htmlFor={id} className=\"form-label\">{label}:</label>\n      <div className=\"dropdown\" ref={dropdownRef}>\n        <button\n          id={id}\n          className={`btn btn-outline-secondary dropdown-toggle w-100 text-start ${disabled ? 'disabled' : ''}`}\n          type=\"button\"\n          onClick={() => !disabled && setIsOpen(!isOpen)}\n          disabled={disabled}\n          style={{ \n            backgroundColor: disabled ? '#e9ecef' : 'white',\n            borderColor: '#ced4da'\n          }}\n        >\n          <span className={selectedValues.length === 0 ? 'text-muted' : ''}>\n            {getDisplayText()}\n          </span>\n        </button>\n        \n        {isOpen && !disabled && (\n          <div className=\"dropdown-menu show w-100\" style={{ maxHeight: '300px', overflowY: 'auto' }}>\n            {/* Select All Option */}\n            {options.length > 1 && (\n              <>\n                <div className=\"dropdown-item\">\n                  <div className=\"form-check\">\n                    <input\n                      className=\"form-check-input\"\n                      type=\"checkbox\"\n                      id={`${id}-select-all`}\n                      checked={isAllSelected}\n                      ref={(input) => {\n                        if (input) input.indeterminate = isIndeterminate;\n                      }}\n                      onChange={handleSelectAll}\n                    />\n                    <label className=\"form-check-label fw-bold\" htmlFor={`${id}-select-all`}>\n                      Select All ({options.length})\n                    </label>\n                  </div>\n                </div>\n                <hr className=\"dropdown-divider\" />\n              </>\n            )}\n            \n            {/* Individual Options */}\n            {options.map(option => (\n              <div key={option.id} className=\"dropdown-item\">\n                <div className=\"form-check\">\n                  <input\n                    className=\"form-check-input\"\n                    type=\"checkbox\"\n                    id={`${id}-${option.id}`}\n                    checked={selectedValues.includes(option.id)}\n                    onChange={() => handleCheckboxChange(option.id)}\n                  />\n                  <label className=\"form-check-label\" htmlFor={`${id}-${option.id}`}>\n                    {option.name}\n                  </label>\n                </div>\n              </div>\n            ))}\n            \n            {options.length === 0 && (\n              <div className=\"dropdown-item text-muted\">\n                <em>No options available</em>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n      \n      {/* Selected count indicator */}\n      {selectedValues.length > 0 && (\n        <small className=\"text-muted mt-1 d-block\">\n          {selectedValues.length} of {options.length} selected\n        </small>\n      )}\n    </div>\n  );\n};\n\nexport default CheckboxDropdown;\n", "import React, { useEffect } from 'react';\nimport { REPORT_FREQUENCIES } from '../types/PageBuilderTypes';\nimport { useOfficeDataSimple as useOfficeData } from '../hooks/useOfficeDataSimple';\nimport CheckboxDropdown from './CheckboxDropdown';\n\ninterface ReportConfigurationProps {\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  onRegionsChange: (regions: string[]) => void;\n  onDivisionsChange: (divisions: string[]) => void;\n  onOfficesChange: (offices: string[]) => void;\n  onFrequencyChange: (frequency: string) => void;\n}\n\nconst ReportConfiguration: React.FC<ReportConfigurationProps> = ({\n  selectedRegions,\n  selectedDivisions,\n  selectedOffices,\n  selectedFrequency,\n  onRegionsChange,\n  onDivisionsChange,\n  onOfficesChange,\n  onFrequencyChange,\n}) => {\n  // Use custom hook to fetch office data from Supabase\n  const { regions, divisions, offices, loading, error, refetch } = useOfficeData();\n\n  // Filter divisions based on selected regions\n  const selectedRegionNames = selectedRegions.map(regionId =>\n    regions.find(r => r.id === regionId)?.name\n  ).filter(Boolean);\n\n  const availableDivisions = selectedRegions.length > 0\n    ? divisions.filter(division => selectedRegionNames.includes(division.region))\n    : divisions; // Show all divisions if no regions selected\n\n  // Filter offices based on selected divisions\n  const selectedDivisionNames = selectedDivisions.map(divisionId =>\n    divisions.find(d => d.id === divisionId)?.name\n  ).filter(Boolean);\n\n  const availableOffices = selectedDivisions.length > 0\n    ? offices.filter(office =>\n        selectedRegionNames.includes(office.region) &&\n        selectedDivisionNames.includes(office.division)\n      )\n    : selectedRegions.length > 0\n      ? offices.filter(office => selectedRegionNames.includes(office.region))\n      : offices; // Show all offices if no filters applied\n\n  // Reset dependent selections when parent selections change\n  useEffect(() => {\n    if (selectedRegions.length > 0) {\n      // Remove divisions that don't belong to selected regions\n      const validDivisions = selectedDivisions.filter(divisionId => {\n        const division = divisions.find(d => d.id === divisionId);\n        return division && selectedRegionNames.includes(division.region);\n      });\n\n      if (validDivisions.length !== selectedDivisions.length) {\n        onDivisionsChange(validDivisions);\n      }\n    }\n  }, [selectedRegions, selectedDivisions, divisions, selectedRegionNames, onDivisionsChange]);\n\n  useEffect(() => {\n    if (selectedDivisions.length > 0) {\n      // Remove offices that don't belong to selected regions/divisions\n      const validOffices = selectedOffices.filter(officeId => {\n        const office = offices.find(o => o.id === officeId);\n        return office &&\n               selectedRegionNames.includes(office.region) &&\n               selectedDivisionNames.includes(office.division);\n      });\n\n      if (validOffices.length !== selectedOffices.length) {\n        onOfficesChange(validOffices);\n      }\n    }\n  }, [selectedDivisions, selectedOffices, offices, selectedRegionNames, selectedDivisionNames, onOfficesChange]);\n\n  return (\n    <div className=\"report-configuration mt-3 mb-3\">\n      <h5>Report Configuration</h5>\n\n      {loading && (\n        <div className=\"alert alert-info\">\n          <div className=\"d-flex align-items-center\">\n            <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n            Loading office data...\n          </div>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"alert alert-danger\">\n          <strong>Error:</strong> {error}\n          <button\n            className=\"btn btn-sm btn-outline-danger ms-2\"\n            onClick={refetch}\n          >\n            Retry\n          </button>\n        </div>\n      )}\n\n      {!loading && !error && (\n        <div className=\"row\">\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"region-select\"\n              label=\"Select Regions\"\n              options={regions}\n              selectedValues={selectedRegions}\n              onChange={onRegionsChange}\n              disabled={loading}\n              placeholder=\"-- Select Regions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"division-select\"\n              label=\"Select Divisions\"\n              options={availableDivisions}\n              selectedValues={selectedDivisions}\n              onChange={onDivisionsChange}\n              disabled={selectedRegions.length === 0 || loading}\n              placeholder=\"-- Select Divisions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"office-select\"\n              label=\"Select Offices\"\n              options={availableOffices}\n              selectedValues={selectedOffices}\n              onChange={onOfficesChange}\n              disabled={selectedDivisions.length === 0 || loading}\n              placeholder=\"-- Select Offices --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <div className=\"form-group\">\n              <label htmlFor=\"frequency-select\" className=\"form-label\">\n                Report Frequency: <span className=\"text-danger\">*</span>\n              </label>\n              <select\n                id=\"frequency-select\"\n                className={`form-select ${!selectedFrequency ? 'is-invalid' : ''}`}\n                value={selectedFrequency}\n                onChange={(e) => onFrequencyChange(e.target.value)}\n                disabled={loading}\n                required\n              >\n                <option value=\"\">-- Select Frequency --</option>\n                {REPORT_FREQUENCIES.map(frequency => (\n                  <option key={frequency.value} value={frequency.value}>\n                    {frequency.label}\n                  </option>\n                ))}\n              </select>\n              {!selectedFrequency && (\n                <div className=\"invalid-feedback\">\n                  Report frequency is required.\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ReportConfiguration;\n", "import React, { useEffect } from 'react';\nimport Modal from '../../shared/Modal';\nimport './PageBuilder.css';\n\n// Import refactored components and hooks\nimport { usePageBuilderState } from './hooks/usePageBuilderState';\nimport { useCardManagement } from './hooks/useCardManagement';\nimport { usePageConfiguration } from './hooks/usePageConfiguration';\nimport CardSelector from './components/CardSelector';\nimport CardManagement from './components/CardManagement';\nimport PageBuilderContent from './components/PageBuilderContent';\nimport ReportConfiguration from './components/ReportConfiguration';\n// Debug components removed - Supabase integration working\nimport { isLeafCard, isMainCard } from './utils/cardUtils';\n\n// All interfaces and utilities are now imported from separate files\n\nconst PageBuilder: React.FC = () => {\n  // Use custom hooks for state management\n  const state = usePageBuilderState();\n\n  // Debug mode removed - using working SQL-based implementation\n\n  // Initialize custom hooks\n  const cardManagement = useCardManagement({\n    categories: state.categories,\n    setCategories: state.setCategories,\n    selectedCard: state.selectedCard,\n    setSelectedCard: state.setSelectedCard,\n    newCardId: state.newCardId,\n    setNewCardId: state.setNewCardId,\n    newCardTitle: state.newCardTitle,\n    setNewCardTitle: state.setNewCardTitle,\n    actionType: state.actionType,\n    setActionType: state.setActionType,\n    setIsLoading: state.setIsLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setShowConfirmModal: state.setShowConfirmModal,\n    setIsAddingNewCard: state.setIsAddingNewCard,\n    setPageConfig: state.setPageConfig,\n    setFields: state.setFields,\n    setEditingCard: state.setEditingCard,\n    setShowEditModal: state.setShowEditModal,\n    setCardToDelete: state.setCardToDelete,\n    setShowDeleteConfirmModal: state.setShowDeleteConfirmModal,\n  });\n\n  const pageConfiguration = usePageConfiguration({\n    categories: state.categories,\n    selectedCard: state.selectedCard,\n    pageConfig: state.pageConfig,\n    setPageConfig: state.setPageConfig,\n    fields: state.fields,\n    setFields: state.setFields,\n    setAvailableDynamicFields: state.setAvailableDynamicFields,\n    setLoading: state.setLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setPreviewContent: state.setPreviewContent,\n    setIsPreviewOpen: state.setIsPreviewOpen,\n    selectedRegions: state.selectedRegions,\n    selectedDivisions: state.selectedDivisions,\n    selectedOffices: state.selectedOffices,\n    selectedFrequency: state.selectedFrequency,\n    setSelectedRegions: state.setSelectedRegions,\n    setSelectedDivisions: state.setSelectedDivisions,\n    setSelectedOffices: state.setSelectedOffices,\n    setSelectedFrequency: state.setSelectedFrequency,\n  });\n\n  // Initialize data on component mount\n  useEffect(() => {\n    cardManagement.fetchCategories();\n  }, []);\n\n  // Handle card and action changes\n  useEffect(() => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.actionType === 'createWebPage') {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n      pageConfiguration.fetchDynamicFormFields(state.selectedCard);\n    } else if (state.selectedCard && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && state.actionType === 'createWebPage') {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n    } else if (!state.selectedCard) {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n      state.setActionType('');\n    }\n\n    if (state.selectedCard && state.actionType !== 'createWebPage') {\n        state.setAvailableDynamicFields([]);\n    }\n  }, [state.selectedCard, state.categories, state.actionType]);\n\n  // Event handlers for UI interactions\n  const handleCardChange = (cardId: string) => {\n    state.setSelectedCard(cardId);\n    state.setActionType('');\n    if (!cardId) {\n        state.setPageConfig(null);\n        state.setFields([]);\n    } else {\n        const cardIsLeaf = isLeafCard(cardId, state.categories);\n        const cardIsMain = isMainCard(cardId, state.categories);\n        if(!cardIsLeaf || cardIsMain) {\n            state.setPageConfig(null);\n            state.setFields([]);\n        }\n    }\n  };\n\n  const handleActionChange = (action: string) => {\n    state.setActionType(action);\n  };\n\n  const handleCreateAction = () => {\n    state.setNewCardId('');\n    state.setNewCardTitle('');\n    state.setIsAddingNewCard(true);\n  };\n\n  const handleWebPageAction = () => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories)) {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n    } else if (state.selectedCard) {\n      state.setError('Web page can only be created/edited for a final nested report (not a main report).');\n      state.setPageConfig(null);\n      state.setFields([]);\n    }\n  };\n\n  // Event handlers for report configuration dropdowns - updated for arrays\n  const handleRegionsChange = (regions: string[]) => {\n    state.setSelectedRegions(regions);\n    // Reset dependent dropdowns when regions change\n    state.setSelectedDivisions([]);\n    state.setSelectedOffices([]);\n  };\n\n  const handleDivisionsChange = (divisions: string[]) => {\n    state.setSelectedDivisions(divisions);\n    // Reset dependent dropdown when divisions change\n    state.setSelectedOffices([]);\n  };\n\n  const handleOfficesChange = (offices: string[]) => {\n    state.setSelectedOffices(offices);\n  };\n\n  const handleFrequencyChange = (frequency: string) => {\n    state.setSelectedFrequency(frequency);\n  };\n\n  // All card management functions are now handled by the useCardManagement hook\n\n  // All page builder functions are now handled by the usePageConfiguration hook\n\n  // All rendering functions are now handled by separate components\n  \n  // All field rendering is now handled by the FieldConfigItem component\n\n  return (\n    <>\n      <div className=\"page-builder\">\n        {state.error && <div className=\"error-message\">{state.error}</div>}\n        {state.success && (\n          <div className=\"success-message\">\n            {state.success}\n          </div>\n        )}\n        <h2>Report & Page Builder</h2>\n\n        <CardSelector\n          categories={state.categories}\n          selectedCard={state.selectedCard}\n          onCardChange={handleCardChange}\n          actionType={state.actionType}\n          onActionChange={handleActionChange}\n          isLoading={state.isLoading}\n          onCreateAction={handleCreateAction}\n          onWebPageAction={handleWebPageAction}\n        />\n\n        {/* Modal for adding/creating new card */}\n        {state.isAddingNewCard && (\n          <Modal\n            isOpen={state.isAddingNewCard}\n            onClose={() => {\n              state.setIsAddingNewCard(false);\n              state.setActionType('');\n              state.setNewCardId('');\n              state.setNewCardTitle('');\n            }}\n            title={\n              state.actionType === 'addNewCardGlobal' ? \"Create New Main Report\" :\n              state.selectedCard && state.actionType === 'createNestedCard' ? `Add Nested Report under \"${state.categories.find(c => c.id === state.selectedCard)?.title}\"` :\n              \"Create New Report\"\n            }\n          >\n            <div className=\"new-card-form\">\n              <input\n                type=\"text\"\n                placeholder=\"Report ID (e.g., 'new-report-id')\"\n                value={state.newCardId}\n                onChange={(e) => state.setNewCardId(e.target.value.toLowerCase().replace(/\\s+/g, '-'))}\n                className=\"form-control mb-2\"\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Report Title\"\n                value={state.newCardTitle}\n                onChange={(e) => state.setNewCardTitle(e.target.value)}\n                className=\"form-control mb-2\"\n              />\n              <div className=\"form-buttons modal-buttons\">\n                <button\n                  onClick={cardManagement.handleConfirmCreate}\n                  disabled={state.isLoading || !state.newCardId || !state.newCardTitle}\n                  className=\"btn btn-primary\"\n                >\n                  {state.isLoading ? 'Creating...' : 'Confirm & Create Report'}\n                </button>\n                <button onClick={() => {\n                  state.setIsAddingNewCard(false);\n                  state.setActionType('');\n                  state.setNewCardId('');\n                  state.setNewCardTitle('');\n                }} className=\"btn btn-secondary\">\n                  Cancel\n                </button>\n              </div>\n            </div>\n          </Modal>\n        )}\n\n        {/* Conditional Rendering for Card Management OR Page Builder OR Warnings */}\n        {state.selectedCard && (\n          <>\n            {/* Card Management Section */}\n            {!(state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig) && (\n              <CardManagement\n                selectedCard={state.selectedCard}\n                categories={state.categories}\n                onEditCard={cardManagement.handleEditCard}\n                onDeleteCard={cardManagement.handleDeleteClick}\n              />\n            )}\n\n            {/* Report Configuration Dropdowns */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && (\n              <ReportConfiguration\n                selectedRegions={state.selectedRegions}\n                selectedDivisions={state.selectedDivisions}\n                selectedOffices={state.selectedOffices}\n                selectedFrequency={state.selectedFrequency}\n                onRegionsChange={handleRegionsChange}\n                onDivisionsChange={handleDivisionsChange}\n                onOfficesChange={handleOfficesChange}\n                onFrequencyChange={handleFrequencyChange}\n              />\n            )}\n\n            {/* Page Builder Content */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig && (\n              <PageBuilderContent\n                pageConfig={state.pageConfig}\n                fields={state.fields}\n                onAddField={pageConfiguration.addField}\n                onUpdateField={pageConfiguration.updateField}\n                onRemoveField={pageConfiguration.removeField}\n                onSave={pageConfiguration.handleSave}\n                onPreview={pageConfiguration.handlePreview}\n                loading={state.loading}\n              />\n            )}\n\n            {/* Warning Messages */}\n            {state.actionType === 'createWebPage' && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && (\n              <div className=\"warning-message mt-3 p-2 bg-warning text-dark rounded\">\n                Page configuration is only available for final nested reports (which are not main reports). Please select an appropriate nested report to configure its page, or create one.\n              </div>\n            )}\n            {state.actionType !== 'createWebPage' && !isLeafCard(state.selectedCard, state.categories) && (\n              <div className=\"info-message mt-3 p-2 bg-info text-dark rounded\">\n                This is a parent report. You can create nested reports under it or select an existing nested report to manage or configure its page.\n              </div>\n            )}\n          </>\n        )}\n\n        {!state.selectedCard && state.actionType === '' && (\n          <div className=\"info-message mt-3 p-3 bg-light border rounded\">\n            <p>Select a report from the dropdown to manage it or configure its web page (if applicable).</p>\n            <p>If no reports exist, or to create a new top-level report, choose \"Create New Main Report\" from the action dropdown after clearing any selection.</p>\n          </div>\n        )}\n\n        {/* Modals for Edit and Delete Confirmation */}\n        {state.showEditModal && state.editingCard && (\n          <Modal\n            isOpen={state.showEditModal}\n            onClose={() => {\n              state.setShowEditModal(false);\n              state.setNewCardTitle('');\n              state.setEditingCard(null);\n            }}\n            title={`Edit Report: ${state.editingCard.title}`}\n          >\n            <input\n              type=\"text\"\n              value={state.newCardTitle}\n              onChange={(e) => state.setNewCardTitle(e.target.value)}\n              placeholder=\"New Report Title\"\n              className=\"form-control mb-2\"\n            />\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleUpdateCard}\n                className=\"btn btn-primary\"\n                disabled={state.isLoading || !state.newCardTitle.trim()}\n              >\n                {state.isLoading ? 'Updating...' : 'Update Title'}\n              </button>\n              <button\n                onClick={() => {\n                  state.setShowEditModal(false);\n                  state.setNewCardTitle('');\n                  state.setEditingCard(null);\n                }}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {state.showDeleteConfirmModal && state.cardToDelete && (\n          <Modal\n            isOpen={state.showDeleteConfirmModal}\n            onClose={() => state.setShowDeleteConfirmModal(false)}\n            title=\"Confirm Deletion\"\n          >\n            <p>Are you sure you want to delete the report \"{state.categories.find(c => c.id === state.cardToDelete)?.title}\" and ALL its nested reports and associated page configurations? This action cannot be undone.</p>\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleConfirmDelete}\n                className=\"btn btn-danger\"\n                disabled={state.isLoading}\n              >\n                {state.isLoading ? 'Deleting...' : 'Confirm Delete'}\n              </button>\n              <button\n                onClick={() => state.setShowDeleteConfirmModal(false)}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {/* Preview Modal */}\n        <Modal\n          isOpen={state.isPreviewOpen}\n          onClose={() => state.setIsPreviewOpen(false)}\n          title=\"Page Preview\"\n        >\n          <div dangerouslySetInnerHTML={{ __html: state.previewContent }} />\n        </Modal>\n      </div>\n    </>\n  );\n};\n\nexport default PageBuilder;\n\n\n", "import { useState } from 'react';\nimport { Category, FormField, PageConfig } from '../types/PageBuilderTypes';\nimport { FormField as DynamicFormField } from '../../../shared/DynamicForm';\n\nexport const usePageBuilderState = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [selectedCard, setSelectedCard] = useState<string>('');\n  const [pageConfig, setPageConfig] = useState<PageConfig | null>(null);\n  const [fields, setFields] = useState<FormField[]>([]);\n  const [availableDynamicFields, setAvailableDynamicFields] = useState<DynamicFormField[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const [isAddingNewCard, setIsAddingNewCard] = useState<boolean>(false);\n  const [newCardId, setNewCardId] = useState<string>('');\n  const [newCardTitle, setNewCardTitle] = useState<string>('');\n  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);\n\n  const [editingCard, setEditingCard] = useState<Category | null>(null);\n  const [showEditModal, setShowEditModal] = useState<boolean>(false);\n\n  const [cardToDelete, setCardToDelete] = useState<string | null>(null);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<boolean>(false);\n\n  const [actionType, setActionType] = useState<string>('');\n\n  // State for Preview Modal\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false);\n  const [previewContent, setPreviewContent] = useState('');\n\n  // New dropdown states - updated to arrays for multiple selections\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\n  const [selectedDivisions, setSelectedDivisions] = useState<string[]>([]);\n  const [selectedOffices, setSelectedOffices] = useState<string[]>([]);\n  const [selectedFrequency, setSelectedFrequency] = useState<string>('');\n\n  return {\n    // State values\n    categories,\n    selectedCard,\n    pageConfig,\n    fields,\n    availableDynamicFields,\n    isLoading,\n    loading,\n    error,\n    success,\n    isAddingNewCard,\n    newCardId,\n    newCardTitle,\n    showConfirmModal,\n    editingCard,\n    showEditModal,\n    cardToDelete,\n    showDeleteConfirmModal,\n    actionType,\n    isPreviewOpen,\n    previewContent,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n\n    // State setters\n    setCategories,\n    setSelectedCard,\n    setPageConfig,\n    setFields,\n    setAvailableDynamicFields,\n    setIsLoading,\n    setLoading,\n    setError,\n    setSuccess,\n    setIsAddingNewCard,\n    setNewCardId,\n    setNewCardTitle,\n    setShowConfirmModal,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n    setActionType,\n    setIsPreviewOpen,\n    setPreviewContent,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  };\n};\n", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemUtilityClass(slot) {\n  return generateUtilityClass('MuiListItem', slot);\n}\nconst listItemClasses = generateUtilityClasses('MuiListItem', ['root', 'container', 'dense', 'alignItemsFlexStart', 'divider', 'gutters', 'padding', 'secondaryAction']);\nexport default listItemClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemButton', slot);\n}\nconst listItemButtonClasses = generateUtilityClasses('MuiListItemButton', ['root', 'focusVisible', 'dense', 'alignItemsFlexStart', 'disabled', 'divider', 'gutters', 'selected']);\nexport default listItemButtonClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemSecondaryActionClassesUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemSecondaryAction', slot);\n}\nconst listItemSecondaryActionClasses = generateUtilityClasses('MuiListItemSecondaryAction', ['root', 'disableGutters']);\nexport default listItemSecondaryActionClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemSecondaryActionClassesUtilityClass } from \"./listItemSecondaryActionClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disableGutters,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getListItemSecondaryActionClassesUtilityClass, classes);\n};\nconst ListItemSecondaryActionRoot = styled('div', {\n  name: 'MuiListItemSecondaryAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.disableGutters && styles.disableGutters];\n  }\n})({\n  position: 'absolute',\n  right: 16,\n  top: '50%',\n  transform: 'translateY(-50%)',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.disableGutters,\n    style: {\n      right: 0\n    }\n  }]\n});\n\n/**\n * Must be used as the last child of ListItem to function properly.\n *\n * @deprecated Use the `secondaryAction` prop in the `ListItem` component instead. This component will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n */\nconst ListItemSecondaryAction = /*#__PURE__*/React.forwardRef(function ListItemSecondaryAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemSecondaryAction'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const ownerState = {\n    ...props,\n    disableGutters: context.disableGutters\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemSecondaryActionRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemSecondaryAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or selection control.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nListItemSecondaryAction.muiName = 'ListItemSecondaryAction';\nexport default ListItemSecondaryAction;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport isMuiElement from \"../utils/isMuiElement.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemUtilityClass } from \"./listItemClasses.js\";\nimport { listItemButtonClasses } from \"../ListItemButton/index.js\";\nimport ListItemSecondaryAction from \"../ListItemSecondaryAction/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.dense && styles.dense, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart, ownerState.divider && styles.divider, !ownerState.disableGutters && styles.gutters, !ownerState.disablePadding && styles.padding, ownerState.hasSecondaryAction && styles.secondaryAction];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes,\n    dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  } = ownerState;\n  const slots = {\n    root: ['root', dense && 'dense', !disableGutters && 'gutters', !disablePadding && 'padding', divider && 'divider', alignItems === 'flex-start' && 'alignItemsFlexStart', hasSecondaryAction && 'secondaryAction'],\n    container: ['container']\n  };\n  return composeClasses(slots, getListItemUtilityClass, classes);\n};\nexport const ListItemRoot = styled('div', {\n  name: 'MuiListItem',\n  slot: 'Root',\n  overridesResolver\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'flex-start',\n  alignItems: 'center',\n  position: 'relative',\n  textDecoration: 'none',\n  width: '100%',\n  boxSizing: 'border-box',\n  textAlign: 'left',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding,\n    style: {\n      paddingTop: 8,\n      paddingBottom: 8\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && ownerState.dense,\n    style: {\n      paddingTop: 4,\n      paddingBottom: 4\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !ownerState.disableGutters,\n    style: {\n      paddingLeft: 16,\n      paddingRight: 16\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !!ownerState.secondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.secondaryAction,\n    style: {\n      [`& > .${listItemButtonClasses.root}`]: {\n        paddingRight: 48\n      }\n    }\n  }, {\n    props: {\n      alignItems: 'flex-start'\n    },\n    style: {\n      alignItems: 'flex-start'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.divider,\n    style: {\n      borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,\n      backgroundClip: 'padding-box'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.button,\n    style: {\n      transition: theme.transitions.create('background-color', {\n        duration: theme.transitions.duration.shortest\n      }),\n      '&:hover': {\n        textDecoration: 'none',\n        backgroundColor: (theme.vars || theme).palette.action.hover,\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: 'transparent'\n        }\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hasSecondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }]\n})));\nconst ListItemContainer = styled('li', {\n  name: 'MuiListItem',\n  slot: 'Container'\n})({\n  position: 'relative'\n});\n\n/**\n * Uses an additional container component if `ListItemSecondaryAction` is the last child.\n */\nconst ListItem = /*#__PURE__*/React.forwardRef(function ListItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItem'\n  });\n  const {\n    alignItems = 'center',\n    children: childrenProp,\n    className,\n    component: componentProp,\n    components = {},\n    componentsProps = {},\n    ContainerComponent = 'li',\n    ContainerProps: {\n      className: ContainerClassName,\n      ...ContainerProps\n    } = {},\n    dense = false,\n    disableGutters = false,\n    disablePadding = false,\n    divider = false,\n    secondaryAction,\n    slotProps = {},\n    slots = {},\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const childContext = React.useMemo(() => ({\n    dense: dense || context.dense || false,\n    alignItems,\n    disableGutters\n  }), [alignItems, context.dense, dense, disableGutters]);\n  const listItemRef = React.useRef(null);\n  const children = React.Children.toArray(childrenProp);\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  const hasSecondaryAction = children.length && isMuiElement(children[children.length - 1], ['ListItemSecondaryAction']);\n  const ownerState = {\n    ...props,\n    alignItems,\n    dense: childContext.dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleRef = useForkRef(listItemRef, ref);\n  const Root = slots.root || components.Root || ListItemRoot;\n  const rootProps = slotProps.root || componentsProps.root || {};\n  const componentProps = {\n    className: clsx(classes.root, rootProps.className, className),\n    ...other\n  };\n  let Component = componentProp || 'li';\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  if (hasSecondaryAction) {\n    // Use div by default.\n    Component = !componentProps.component && !componentProp ? 'div' : Component;\n\n    // Avoid nesting of li > li.\n    if (ContainerComponent === 'li') {\n      if (Component === 'li') {\n        Component = 'div';\n      } else if (componentProps.component === 'li') {\n        componentProps.component = 'div';\n      }\n    }\n    return /*#__PURE__*/_jsx(ListContext.Provider, {\n      value: childContext,\n      children: /*#__PURE__*/_jsxs(ListItemContainer, {\n        as: ContainerComponent,\n        className: clsx(classes.container, ContainerClassName),\n        ref: handleRef,\n        ownerState: ownerState,\n        ...ContainerProps,\n        children: [/*#__PURE__*/_jsx(Root, {\n          ...rootProps,\n          ...(!isHostComponent(Root) && {\n            as: Component,\n            ownerState: {\n              ...ownerState,\n              ...rootProps.ownerState\n            }\n          }),\n          ...componentProps,\n          children: children\n        }), children.pop()]\n      })\n    });\n  }\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsxs(Root, {\n      ...rootProps,\n      as: Component,\n      ref: handleRef,\n      ...(!isHostComponent(Root) && {\n        ownerState: {\n          ...ownerState,\n          ...rootProps.ownerState\n        }\n      }),\n      ...componentProps,\n      children: [children, secondaryAction && /*#__PURE__*/_jsx(ListItemSecondaryAction, {\n        children: secondaryAction\n      })]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Defines the `align-items` style property.\n   * @default 'center'\n   */\n  alignItems: PropTypes.oneOf(['center', 'flex-start']),\n  /**\n   * The content of the component if a `ListItemSecondaryAction` is used it must\n   * be the last child.\n   */\n  children: chainPropTypes(PropTypes.node, props => {\n    const children = React.Children.toArray(props.children);\n\n    // React.Children.toArray(props.children).findLastIndex(isListItemSecondaryAction)\n    let secondaryActionIndex = -1;\n    for (let i = children.length - 1; i >= 0; i -= 1) {\n      const child = children[i];\n      if (isMuiElement(child, ['ListItemSecondaryAction'])) {\n        secondaryActionIndex = i;\n        break;\n      }\n    }\n\n    //  is ListItemSecondaryAction the last child of ListItem\n    if (secondaryActionIndex !== -1 && secondaryActionIndex !== children.length - 1) {\n      return new Error('MUI: You used an element after ListItemSecondaryAction. ' + 'For ListItem to detect that it has a secondary action ' + 'you must pass it as the last child to ListItem.');\n    }\n    return null;\n  }),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The container component used when a `ListItemSecondaryAction` is the last child.\n   * @default 'li'\n   * @deprecated Use the `component` or `slots.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerComponent: elementTypeAcceptingRef,\n  /**\n   * Props applied to the container component if used.\n   * @default {}\n   * @deprecated Use the `slotProps.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerProps: PropTypes.object,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used.\n   * The prop defaults to the value inherited from the parent List component.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * If `true`, all padding is removed.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * If `true`, a 1px light border is added to the bottom of the list item.\n   * @default false\n   */\n  divider: PropTypes.bool,\n  /**\n   * The element to display at the end of ListItem.\n   */\n  secondaryAction: PropTypes.node,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItem;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography, { typographyClasses } from \"../Typography/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport listItemTextClasses, { getListItemTextUtilityClass } from \"./listItemTextClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    inset,\n    primary,\n    secondary,\n    dense\n  } = ownerState;\n  const slots = {\n    root: ['root', inset && 'inset', dense && 'dense', primary && secondary && 'multiline'],\n    primary: ['primary'],\n    secondary: ['secondary']\n  };\n  return composeClasses(slots, getListItemTextUtilityClass, classes);\n};\nconst ListItemTextRoot = styled('div', {\n  name: 'MuiListItemText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${listItemTextClasses.primary}`]: styles.primary\n    }, {\n      [`& .${listItemTextClasses.secondary}`]: styles.secondary\n    }, styles.root, ownerState.inset && styles.inset, ownerState.primary && ownerState.secondary && styles.multiline, ownerState.dense && styles.dense];\n  }\n})({\n  flex: '1 1 auto',\n  minWidth: 0,\n  marginTop: 4,\n  marginBottom: 4,\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.primary})`]: {\n    display: 'block'\n  },\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.secondary})`]: {\n    display: 'block'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.primary && ownerState.secondary,\n    style: {\n      marginTop: 6,\n      marginBottom: 6\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.inset,\n    style: {\n      paddingLeft: 56\n    }\n  }]\n});\nconst ListItemText = /*#__PURE__*/React.forwardRef(function ListItemText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemText'\n  });\n  const {\n    children,\n    className,\n    disableTypography = false,\n    inset = false,\n    primary: primaryProp,\n    primaryTypographyProps,\n    secondary: secondaryProp,\n    secondaryTypographyProps,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const {\n    dense\n  } = React.useContext(ListContext);\n  let primary = primaryProp != null ? primaryProp : children;\n  let secondary = secondaryProp;\n  const ownerState = {\n    ...props,\n    disableTypography,\n    inset,\n    primary: !!primary,\n    secondary: !!secondary,\n    dense\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      primary: primaryTypographyProps,\n      secondary: secondaryTypographyProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: ListItemTextRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    ref\n  });\n  const [PrimarySlot, primarySlotProps] = useSlot('primary', {\n    className: classes.primary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SecondarySlot, secondarySlotProps] = useSlot('secondary', {\n    className: classes.secondary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  if (primary != null && primary.type !== Typography && !disableTypography) {\n    primary = /*#__PURE__*/_jsx(PrimarySlot, {\n      variant: dense ? 'body2' : 'body1',\n      component: primarySlotProps?.variant ? undefined : 'span',\n      ...primarySlotProps,\n      children: primary\n    });\n  }\n  if (secondary != null && secondary.type !== Typography && !disableTypography) {\n    secondary = /*#__PURE__*/_jsx(SecondarySlot, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      ...secondarySlotProps,\n      children: secondary\n    });\n  }\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [primary, secondary]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Alias for the `primary` prop.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the children won't be wrapped by a Typography component.\n   * This can be useful to render an alternative Typography variant by wrapping\n   * the `children` (or `primary`) text, and optional `secondary` text\n   * with the Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * If `true`, the children are indented.\n   * This should be used if there is no left avatar or left icon.\n   * @default false\n   */\n  inset: PropTypes.bool,\n  /**\n   * The main content element.\n   */\n  primary: PropTypes.node,\n  /**\n   * These props will be forwarded to the primary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.primary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  primaryTypographyProps: PropTypes.object,\n  /**\n   * The secondary content element.\n   */\n  secondary: PropTypes.node,\n  /**\n   * These props will be forwarded to the secondary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.secondary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  secondaryTypographyProps: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    primary: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    secondary: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    primary: PropTypes.elementType,\n    root: PropTypes.elementType,\n    secondary: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemText;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDividerUtilityClass } from \"./dividerClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    absolute,\n    children,\n    classes,\n    flexItem,\n    light,\n    orientation,\n    textAlign,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', absolute && 'absolute', variant, light && 'light', orientation === 'vertical' && 'vertical', flexItem && 'flexItem', children && 'withChildren', children && orientation === 'vertical' && 'withChildrenVertical', textAlign === 'right' && orientation !== 'vertical' && 'textAlignRight', textAlign === 'left' && orientation !== 'vertical' && 'textAlignLeft'],\n    wrapper: ['wrapper', orientation === 'vertical' && 'wrapperVertical']\n  };\n  return composeClasses(slots, getDividerUtilityClass, classes);\n};\nconst DividerRoot = styled('div', {\n  name: 'MuiDivider',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.absolute && styles.absolute, styles[ownerState.variant], ownerState.light && styles.light, ownerState.orientation === 'vertical' && styles.vertical, ownerState.flexItem && styles.flexItem, ownerState.children && styles.withChildren, ownerState.children && ownerState.orientation === 'vertical' && styles.withChildrenVertical, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && styles.textAlignRight, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && styles.textAlignLeft];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 0,\n  // Reset browser default style.\n  flexShrink: 0,\n  borderWidth: 0,\n  borderStyle: 'solid',\n  borderColor: (theme.vars || theme).palette.divider,\n  borderBottomWidth: 'thin',\n  variants: [{\n    props: {\n      absolute: true\n    },\n    style: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      width: '100%'\n    }\n  }, {\n    props: {\n      light: true\n    },\n    style: {\n      borderColor: theme.vars ? `rgba(${theme.vars.palette.dividerChannel} / 0.08)` : alpha(theme.palette.divider, 0.08)\n    }\n  }, {\n    props: {\n      variant: 'inset'\n    },\n    style: {\n      marginLeft: 72\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'horizontal'\n    },\n    style: {\n      marginLeft: theme.spacing(2),\n      marginRight: theme.spacing(2)\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'vertical'\n    },\n    style: {\n      marginTop: theme.spacing(1),\n      marginBottom: theme.spacing(1)\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      height: '100%',\n      borderBottomWidth: 0,\n      borderRightWidth: 'thin'\n    }\n  }, {\n    props: {\n      flexItem: true\n    },\n    style: {\n      alignSelf: 'stretch',\n      height: 'auto'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.children,\n    style: {\n      display: 'flex',\n      textAlign: 'center',\n      border: 0,\n      borderTopStyle: 'solid',\n      borderLeftStyle: 'solid',\n      '&::before, &::after': {\n        content: '\"\"',\n        alignSelf: 'center'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.children && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before, &::after': {\n        width: '100%',\n        borderTop: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderTopStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.orientation === 'vertical' && ownerState.children,\n    style: {\n      flexDirection: 'column',\n      '&::before, &::after': {\n        height: '100%',\n        borderLeft: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderLeftStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '90%'\n      },\n      '&::after': {\n        width: '10%'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '10%'\n      },\n      '&::after': {\n        width: '90%'\n      }\n    }\n  }]\n})));\nconst DividerWrapper = styled('span', {\n  name: 'MuiDivider',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.wrapper, ownerState.orientation === 'vertical' && styles.wrapperVertical];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-block',\n  paddingLeft: `calc(${theme.spacing(1)} * 1.2)`,\n  paddingRight: `calc(${theme.spacing(1)} * 1.2)`,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      paddingTop: `calc(${theme.spacing(1)} * 1.2)`,\n      paddingBottom: `calc(${theme.spacing(1)} * 1.2)`\n    }\n  }]\n})));\nconst Divider = /*#__PURE__*/React.forwardRef(function Divider(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDivider'\n  });\n  const {\n    absolute = false,\n    children,\n    className,\n    orientation = 'horizontal',\n    component = children || orientation === 'vertical' ? 'div' : 'hr',\n    flexItem = false,\n    light = false,\n    role = component !== 'hr' ? 'separator' : undefined,\n    textAlign = 'center',\n    variant = 'fullWidth',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    absolute,\n    component,\n    flexItem,\n    light,\n    orientation,\n    role,\n    textAlign,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DividerRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    role: role,\n    ref: ref,\n    ownerState: ownerState,\n    \"aria-orientation\": role === 'separator' && (component !== 'hr' || orientation === 'vertical') ? orientation : undefined,\n    ...other,\n    children: children ? /*#__PURE__*/_jsx(DividerWrapper, {\n      className: classes.wrapper,\n      ownerState: ownerState,\n      children: children\n    }) : null\n  });\n});\n\n/**\n * The following flag is used to ensure that this component isn't tabbable i.e.\n * does not get highlight/focus inside of MUI List.\n */\nif (Divider) {\n  Divider.muiSkipListHighlight = true;\n}\nprocess.env.NODE_ENV !== \"production\" ? Divider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Absolutely position the element.\n   * @default false\n   */\n  absolute: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, a vertical divider will have the correct height when used in flex container.\n   * (By default, a vertical divider will have a calculated height of `0px` if it is the child of a flex container.)\n   * @default false\n   */\n  flexItem: PropTypes.bool,\n  /**\n   * If `true`, the divider will have a lighter color.\n   * @default false\n   * @deprecated Use <Divider sx={{ opacity: 0.6 }} /> (or any opacity or color) instead. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  light: PropTypes.bool,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The text alignment.\n   * @default 'center'\n   */\n  textAlign: PropTypes.oneOf(['center', 'left', 'right']),\n  /**\n   * The variant to use.\n   * @default 'fullWidth'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['fullWidth', 'inset', 'middle']), PropTypes.string])\n} : void 0;\nexport default Divider;", "import { useState, useEffect } from 'react';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataEnhancedReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n  totalRecords: number;\n  approach: string;\n}\n\n/**\n * Enhanced office data hook with comprehensive pagination\n * Mirrors the successful Flutter implementation to overcome 1000-record limit\n */\nexport const useOfficeDataEnhanced = (): UseOfficeDataEnhancedReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [totalRecords, setTotalRecords] = useState<number>(0);\n  const [approach, setApproach] = useState<string>('');\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataEnhanced: Starting comprehensive office data fetch...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allOfficeData = await OfficeService.fetchAllOfficeData();\n      \n      console.log('✅ useOfficeDataEnhanced: Fetched office records:', allOfficeData.length, 'records');\n      setTotalRecords(allOfficeData.length);\n\n      if (allOfficeData.length === 0) {\n        console.log('⚠️ useOfficeDataEnhanced: No office records found');\n        setRegions([]);\n        setDivisions([]);\n        setOffices([]);\n        setApproach('no-data');\n        return;\n      }\n\n      // Process regions - get unique regions\n      const uniqueRegions = new Set<string>();\n      allOfficeData.forEach(office => {\n        if (office.Region && office.Region.trim()) {\n          uniqueRegions.add(office.Region.trim());\n        }\n      });\n\n      const regionsArray: Region[] = Array.from(uniqueRegions)\n        .sort()\n        .map(regionName => ({\n          id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed regions:', regionsArray.length);\n\n      // Process divisions - get unique divisions with their regions\n      const uniqueDivisions = new Map<string, string>();\n      allOfficeData.forEach(office => {\n        if (office.Division && office.Division.trim() && office.Region && office.Region.trim()) {\n          uniqueDivisions.set(office.Division.trim(), office.Region.trim());\n        }\n      });\n\n      const divisionsArray: Division[] = Array.from(uniqueDivisions.entries())\n        .sort(([a], [b]) => a.localeCompare(b))\n        .map(([divisionName, regionName]) => ({\n          id: divisionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: divisionName,\n          region: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed divisions:', divisionsArray.length);\n\n      // Process offices - use office name as ID for consistency\n      const officesArray: Office[] = allOfficeData\n        .filter(office => office['Office name'] && office['Office name'].trim())\n        .map(office => ({\n          id: office['Office name'], // Use office name as ID for form targeting\n          name: office['Office name'],\n          region: office.Region || '',\n          division: office.Division || '',\n          facilityId: office['Office name'], // Keep for reference\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed offices:', officesArray.length);\n\n      // Log comprehensive statistics\n      logOfficeStatistics(allOfficeData, regionsArray, divisionsArray, officesArray);\n\n      // Set the processed data\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n      setApproach('enhanced-pagination');\n\n      console.log('✅ useOfficeDataEnhanced: Data processing complete');\n\n    } catch (err) {\n      console.error('❌ useOfficeDataEnhanced: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n      setTotalRecords(0);\n      setApproach('error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n    totalRecords,\n    approach,\n  };\n};\n\n/**\n * Log comprehensive statistics about the processed office data\n */\nfunction logOfficeStatistics(\n  allOfficeData: any[],\n  regions: Region[],\n  divisions: Division[],\n  offices: Office[]\n): void {\n  console.log('📊 useOfficeDataEnhanced: === COMPREHENSIVE STATISTICS ===');\n  console.log(`📊 useOfficeDataEnhanced: Raw records: ${allOfficeData.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed regions: ${regions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed divisions: ${divisions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed offices: ${offices.length}`);\n\n  if (offices.length > 0) {\n    // Alphabetical range\n    const sortedNames = offices.map(o => o.name).sort();\n    console.log(`📊 useOfficeDataEnhanced: Office range - First: \"${sortedNames[0]}\"`);\n    console.log(`📊 useOfficeDataEnhanced: Office range - Last: \"${sortedNames[sortedNames.length - 1]}\"`);\n\n    // Letter distribution\n    const letterCounts: { [key: string]: number } = {};\n    offices.forEach(office => {\n      const firstLetter = office.name.charAt(0).toUpperCase();\n      letterCounts[firstLetter] = (letterCounts[firstLetter] || 0) + 1;\n    });\n\n    console.log('📊 useOfficeDataEnhanced: Letter distribution:');\n    Object.keys(letterCounts).sort().forEach(letter => {\n      console.log(`📊 useOfficeDataEnhanced: ${letter}: ${letterCounts[letter]} offices`);\n    });\n\n    // Check for specific offices\n    const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n    const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n    \n    console.log(`📊 useOfficeDataEnhanced: Contains \"Tirupur division\": ${!!tirupurDivision}`);\n    console.log(`📊 useOfficeDataEnhanced: Contains \"Coimbatore division\": ${!!coimbatoreDivision}`);\n\n    if (tirupurDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Tirupur division: \"${tirupurDivision.name}\"`);\n    }\n    if (coimbatoreDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Coimbatore division: \"${coimbatoreDivision.name}\"`);\n    }\n\n    // Region breakdown\n    if (regions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Regions found:');\n      regions.forEach(region => {\n        const regionOffices = offices.filter(o => o.region === region.name);\n        console.log(`📊 useOfficeDataEnhanced: ${region.name}: ${regionOffices.length} offices`);\n      });\n    }\n\n    // Division breakdown\n    if (divisions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Top 10 divisions by office count:');\n      const divisionCounts = divisions.map(division => ({\n        name: division.name,\n        count: offices.filter(o => o.division === division.name).length\n      })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n      divisionCounts.forEach(division => {\n        console.log(`📊 useOfficeDataEnhanced: ${division.name}: ${division.count} offices`);\n      });\n    }\n  }\n\n  console.log('📊 useOfficeDataEnhanced: === END STATISTICS ===');\n}\n\nexport default useOfficeDataEnhanced;\n", "import React from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n  Chip,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport { useOfficeDataEnhanced } from './hooks/useOfficeDataEnhanced';\n\n/**\n * Test component to verify enhanced office loading functionality\n * This component displays comprehensive statistics about the loaded office data\n */\nconst OfficeLoadingTest: React.FC = () => {\n  const {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    totalRecords,\n    approach,\n    refetch\n  } = useOfficeDataEnhanced();\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" flexDirection=\"column\" alignItems=\"center\" p={4}>\n        <CircularProgress size={60} />\n        <Typography variant=\"h6\" sx={{ mt: 2 }}>\n          Loading office data with comprehensive pagination...\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n          This may take a moment as we fetch ALL records from the database\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={4}>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n        <Typography variant=\"body2\">\n          Failed to load office data. Please check the console for detailed error information.\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Calculate statistics\n  const letterDistribution: { [key: string]: number } = {};\n  offices.forEach(office => {\n    const firstLetter = office.name.charAt(0).toUpperCase();\n    letterDistribution[firstLetter] = (letterDistribution[firstLetter] || 0) + 1;\n  });\n\n  const sortedOfficeNames = offices.map(o => o.name).sort();\n  const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n  const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n\n  // Top regions by office count\n  const regionCounts = regions.map(region => ({\n    name: region.name,\n    count: offices.filter(o => o.region === region.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 5);\n\n  // Top divisions by office count\n  const divisionCounts = divisions.map(division => ({\n    name: division.name,\n    count: offices.filter(o => o.division === division.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n  return (\n    <Box p={4}>\n      <Typography variant=\"h4\" gutterBottom>\n        Office Loading Test - Enhanced Pagination\n      </Typography>\n      \n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\n        This test verifies that the enhanced office loading system can fetch ALL records from the Supabase database,\n        overcoming the default 1000-record pagination limit.\n      </Typography>\n\n      {/* Summary Cards */}\n      <Box display=\"flex\" gap={3} sx={{ mb: 4, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Total Records\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalRecords.toLocaleString()}\n              </Typography>\n              <Chip\n                label={approach}\n                size=\"small\"\n                color={totalRecords > 1000 ? \"success\" : \"warning\"}\n                sx={{ mt: 1 }}\n              />\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Regions\n              </Typography>\n              <Typography variant=\"h4\">\n                {regions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Divisions\n              </Typography>\n              <Typography variant=\"h4\">\n                {divisions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Offices\n              </Typography>\n              <Typography variant=\"h4\">\n                {offices.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n      </Box>\n\n      {/* Verification Results */}\n      <Box display=\"flex\" gap={3} sx={{ flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Verification Results\n            </Typography>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Records exceed 1000 limit:\n              </Typography>\n              <Chip\n                label={totalRecords > 1000 ? \"✅ YES\" : \"❌ NO\"}\n                color={totalRecords > 1000 ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Alphabetical Range:\n              </Typography>\n              <Typography variant=\"body2\">\n                First: \"{sortedOfficeNames[0] || 'N/A'}\"\n              </Typography>\n              <Typography variant=\"body2\">\n                Last: \"{sortedOfficeNames[sortedOfficeNames.length - 1] || 'N/A'}\"\n              </Typography>\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Tirupur Division Found:\n              </Typography>\n              <Chip\n                label={tirupurDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={tirupurDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {tirupurDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{tirupurDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Coimbatore Division Found:\n              </Typography>\n              <Chip\n                label={coimbatoreDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={coimbatoreDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {coimbatoreDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{coimbatoreDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Letter Distribution\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {Object.keys(letterDistribution).sort().map(letter => (\n                <Box key={letter} display=\"flex\" justifyContent=\"space-between\" sx={{ mb: 1 }}>\n                  <Typography variant=\"body2\">{letter}:</Typography>\n                  <Typography variant=\"body2\">{letterDistribution[letter]} offices</Typography>\n                </Box>\n              ))}\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      <Box display=\"flex\" gap={3} sx={{ mt: 3, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 5 Regions by Office Count\n            </Typography>\n            <List dense>\n              {regionCounts.map((region, index) => (\n                <React.Fragment key={region.name}>\n                  <ListItem>\n                    <ListItemText\n                      primary={region.name}\n                      secondary={`${region.count} offices`}\n                    />\n                  </ListItem>\n                  {index < regionCounts.length - 1 && <Divider />}\n                </React.Fragment>\n              ))}\n            </List>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 10 Divisions by Office Count\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              <List dense>\n                {divisionCounts.map((division, index) => (\n                  <React.Fragment key={division.name}>\n                    <ListItem>\n                      <ListItemText\n                        primary={division.name}\n                        secondary={`${division.count} offices`}\n                      />\n                    </ListItem>\n                    {index < divisionCounts.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      {/* Success Message */}\n      {totalRecords > 1000 && (\n        <Alert severity=\"success\" sx={{ mt: 3 }}>\n          <Typography variant=\"h6\">\n            🎉 Success! Enhanced Office Loading is Working\n          </Typography>\n          <Typography variant=\"body2\">\n            The system successfully loaded {totalRecords.toLocaleString()} office records, \n            which exceeds the default 1000-record Supabase limit. This confirms that the \n            comprehensive pagination solution is working correctly.\n          </Typography>\n        </Alert>\n      )}\n    </Box>\n  );\n};\n\nexport default OfficeLoadingTest;\n", "import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Sidebar from '../shared/Sidebar';\nimport StatsCards from '../shared/StatsCards';\nimport PageBuilder from './business/PageBuilder';\nimport OfficeLoadingTest from './business/OfficeLoadingTest';\n\nconst AdminPage: React.FC = () => {\n  const { currentUser } = useAuth();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState<any>(null);\n  const [showOfficeTest, setShowOfficeTest] = useState<boolean>(false);\n\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        const userRef = doc(db, 'employees', currentUser.uid);\n        const userSnap = await getDoc(userRef);\n        if (userSnap.exists()) {\n          setUserData(userSnap.data());\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n      <div className=\"main-content\">\n        <div className=\"page-title\">\n          Admin Dashboard\n          <button\n            onClick={() => setShowOfficeTest(!showOfficeTest)}\n            style={{\n              marginLeft: '20px',\n              padding: '8px 16px',\n              backgroundColor: showOfficeTest ? '#dc3545' : '#007bff',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {showOfficeTest ? 'Hide Office Test' : 'Show Office Loading Test'}\n          </button>\n        </div>\n        <StatsCards />\n        {showOfficeTest ? <OfficeLoadingTest /> : <PageBuilder />}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPage;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDividerUtilityClass(slot) {\n  return generateUtilityClass('MuiDivider', slot);\n}\nconst dividerClasses = generateUtilityClasses('MuiDivider', ['root', 'absolute', 'fullWidth', 'inset', 'middle', 'flexItem', 'light', 'vertical', 'withChildren', 'withChildrenVertical', 'textAlignRight', 'textAlignLeft', 'wrapper', 'wrapperVertical']);\nexport default dividerClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardContentUtilityClass(slot) {\n  return generateUtilityClass('MuiCardContent', slot);\n}\nconst cardContentClasses = generateUtilityClasses('MuiCardContent', ['root']);\nexport default cardContentClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getCardContentUtilityClass } from \"./cardContentClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardContentUtilityClass, classes);\n};\nconst CardContentRoot = styled('div', {\n  name: 'MuiCardContent',\n  slot: 'Root'\n})({\n  padding: 16,\n  '&:last-child': {\n    paddingBottom: 24\n  }\n});\nconst CardContent = /*#__PURE__*/React.forwardRef(function CardContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardContent'\n  });\n  const {\n    className,\n    component = 'div',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardContentRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardContent;"], "names": ["getCardUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "CardRoot", "styled", "Paper", "name", "overflow", "React", "inProps", "ref", "props", "useDefaultProps", "className", "raised", "other", "ownerState", "classes", "composeClasses", "root", "useUtilityClasses", "_jsx", "clsx", "elevation", "undefined", "getListItemTextUtilityClass", "stats", "title", "value", "StatsCards", "children", "map", "stat", "index", "_jsxs", "_ref", "isOpen", "onClose", "onClick", "e", "stopPropagation", "isMainCard", "cardId", "allCategories", "card", "find", "c", "id", "parentId", "isLeafCard", "some", "organizeCards", "list", "roots", "for<PERSON>ach", "item", "_map$item$parentId$ch", "push", "getAllDescendantIds", "descendants", "filter", "child", "concat", "useCardManagement", "categories", "setCategories", "selected<PERSON><PERSON>", "setSelectedCard", "newCardId", "setNewCardId", "newCardTitle", "setNewCardTitle", "actionType", "setActionType", "setIsLoading", "setError", "setSuccess", "setShowConfirmModal", "setIsAddingNewCard", "setPageConfig", "setFields", "setEditingCard", "setShowEditModal", "setCardToDelete", "setShowDeleteConfirmModal", "fetchCategories", "useCallback", "async", "fetchedCategories", "getDocs", "collection", "db", "docs", "doc", "data", "err", "console", "error", "handleAddNewCard", "doc<PERSON>ef", "getDoc", "exists", "checkDuplicateId", "handleConfirmCreate", "_categories$find", "parentIdToSet", "newPath", "path", "replace", "cardRef", "icon", "generatedIcon", "color", "generatedColor", "hash", "split", "reduce", "acc", "char", "charCodeAt", "icons", "FaFolder", "FaFileAlt", "FaCog", "FaFolderOpen", "colors", "length", "generateCardStyle", "setDoc", "lastUpdated", "Date", "toISOString", "fields", "isPage", "pageId", "setTimeout", "handleEditCard", "handleUpdateCard", "editingCard", "updateDoc", "handleDeleteClick", "handleConfirmDelete", "batch", "writeBatch", "allDescendants", "idsToDelete", "delete", "commit", "onCardChange", "onActionChange", "isLoading", "onCreateAction", "onWebPageAction", "renderCardOptions", "cards", "level", "arguments", "flatMap", "displayTitle", "trim", "toLowerCase", "includes", "style", "paddingLeft", "repeat", "onChange", "newSelectedCard", "target", "disabled", "newAction", "_Fragment", "onEditCard", "onDeleteCard", "selectedCate<PERSON><PERSON>", "FaEdit", "FaTrash", "_field$options2", "field", "onUpdate", "onRemove", "handleOptionChange", "optIndex", "key", "newOptions", "options", "handleDefaultValueChange", "type", "newDefaultValue", "checked", "defaultValue", "label", "htmlFor", "placeholder", "required", "min", "parseFloat", "max", "opt", "_field$options", "_", "i", "removeOption", "addOption", "String", "Boolean", "Array", "isArray", "join", "s", "buttonText", "sectionTitle", "pageConfig", "onAddField", "onUpdateField", "onRemoveField", "onSave", "onPreview", "loading", "FieldConfigItem", "FaPlus", "FaSave", "REPORT_FREQUENCIES", "<PERSON><PERSON><PERSON><PERSON>", "setIsOpen", "useState", "dropdownRef", "useRef", "useEffect", "handleClickOutside", "event", "current", "contains", "document", "addEventListener", "removeEventListener", "isAllSelected", "isIndeterminate", "backgroundColor", "borderColor", "getDisplayText", "selectedOption", "option", "maxHeight", "overflowY", "input", "indeterminate", "handleSelectAll", "handleCheckboxChange", "optionId", "selectedRegions", "selectedDivisions", "selectedOffices", "selectedFrequency", "onRegionsChange", "onDivisionsChange", "onOfficesChange", "onFrequencyChange", "regions", "divisions", "offices", "refetch", "useOfficeDataSimple", "setRegions", "setDivisions", "setOffices", "setLoading", "fetchOfficeData", "log", "allData", "OfficeService", "fetchAllOfficeData", "distinctRegions", "row", "Region", "region", "array", "indexOf", "sort", "regionsArray", "regionName", "distinctDivisions", "division", "Division", "findIndex", "x", "a", "b", "localeCompare", "divisionsArray", "officesArray", "facilityId", "useOfficeData", "selectedRegionNames", "regionId", "_regions$find", "r", "availableDivisions", "selectedDivisionNames", "divisionId", "_divisions$find", "d", "availableOffices", "office", "validDivisions", "validOffices", "officeId", "o", "role", "CheckboxDropdown", "frequency", "PageBuilder", "_state$categories$fin", "_state$categories$fin2", "state", "usePageBuilderState", "availableDynamicFields", "setAvailableDynamicFields", "success", "isAddingNewCard", "showConfirmModal", "showEditModal", "cardToDelete", "showDeleteConfirmModal", "isPreviewOpen", "setIsPreviewOpen", "previewContent", "setPreviewContent", "setSelectedRegions", "setSelectedDivisions", "setSelectedOffices", "setSelectedFrequency", "cardManagement", "pageConfiguration", "fetchDynamicFormFields", "formId", "formConfigRef", "formConfigSnap", "formConfigData", "loadPageConfig", "docSnap", "supabasePageService", "supabaseError", "selectedRegion", "selectedDivision", "selectedOffice", "addField", "newField", "now", "addFieldFromDynamic", "dynamicField", "columns", "buttonType", "onClickAction", "warn", "updateField", "updatedField", "<PERSON><PERSON><PERSON>s", "removeField", "handleSave", "cleanedFields", "cleanedField", "updatedPageConfig", "savePromises", "catch", "Error", "message", "savePageConfig", "Promise", "all", "handlePreview", "alert", "generatedPreview", "fieldHtml", "usePageConfiguration", "CardSelector", "cardIsLeaf", "cardIsMain", "action", "handleCreateAction", "handleWebPageAction", "Modal", "CardManagement", "ReportConfiguration", "PageBuilderContent", "dangerouslySetInnerHTML", "__html", "getListItemUtilityClass", "getListItemSecondaryActionClassesUtilityClass", "ListItemSecondaryActionRoot", "overridesResolver", "styles", "disableGutters", "position", "right", "top", "transform", "variants", "ListItemSecondaryAction", "context", "ListContext", "slots", "mui<PERSON><PERSON>", "ListItemRoot", "dense", "alignItems", "alignItemsFlexStart", "divider", "gutters", "disablePadding", "padding", "hasSecondaryAction", "secondaryAction", "memoTheme", "theme", "display", "justifyContent", "textDecoration", "width", "boxSizing", "textAlign", "_ref2", "paddingTop", "paddingBottom", "_ref3", "_ref4", "paddingRight", "_ref5", "_ref6", "listItemButtonClasses", "_ref7", "borderBottom", "vars", "palette", "backgroundClip", "_ref8", "button", "transition", "transitions", "create", "duration", "shortest", "hover", "_ref9", "ListItemContainer", "childrenProp", "component", "componentProp", "components", "componentsProps", "ContainerComponent", "ContainerProps", "ContainerClassName", "slotProps", "childContext", "listItemRef", "toArray", "isMuiElement", "container", "handleRef", "useForkRef", "Root", "rootProps", "componentProps", "Component", "Provider", "as", "isHostComponent", "pop", "ListItemTextRoot", "listItemTextClasses", "primary", "secondary", "inset", "multiline", "flex", "min<PERSON><PERSON><PERSON>", "marginTop", "marginBottom", "typographyClasses", "disableTypography", "primaryProp", "primaryTypographyProps", "secondaryProp", "secondaryTypographyProps", "externalForwardedProps", "RootSlot", "rootSlotProps", "useSlot", "elementType", "PrimarySlot", "primarySlotProps", "Typography", "SecondarySlot", "secondarySlotProps", "variant", "DividerRoot", "absolute", "light", "orientation", "vertical", "flexItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withChildrenVertical", "textAlignRight", "textAlignLeft", "margin", "flexShrink", "borderWidth", "borderStyle", "borderBottomWidth", "bottom", "left", "dividerChannel", "alpha", "marginLeft", "spacing", "marginRight", "height", "borderRightWidth", "alignSelf", "border", "borderTopStyle", "borderLeftStyle", "content", "borderTop", "flexDirection", "borderLeft", "DividerWrapper", "wrapper", "wrapperVertical", "whiteSpace", "Divider", "getDividerUtilityClass", "muiSkipListHighlight", "useOfficeDataEnhanced", "totalRecords", "setTotalRecords", "approach", "setApproach", "allOfficeData", "uniqueRegions", "Set", "add", "from", "uniqueDivisions", "Map", "set", "entries", "divisionName", "sortedNames", "letterCounts", "firstLetter", "char<PERSON>t", "toUpperCase", "Object", "keys", "letter", "tirupurDivision", "coimbatoreDivision", "regionOffices", "count", "slice", "logOfficeStatistics", "OfficeLoadingTest", "Box", "p", "CircularProgress", "size", "sx", "mt", "<PERSON><PERSON>", "severity", "mb", "letterDistribution", "sortedOfficeNames", "regionCounts", "divisionCounts", "gutterBottom", "paragraph", "gap", "flexWrap", "Card", "<PERSON><PERSON><PERSON><PERSON>", "toLocaleString", "Chip", "List", "ListItem", "ListItemText", "AdminPage", "currentUser", "useAuth", "userData", "setUserData", "useNavigate", "showOfficeTest", "setShowOfficeTest", "userRef", "uid", "userSnap", "fetchUserData", "Sidebar", "borderRadius", "cursor", "fontSize", "getCardContentUtilityClass", "CardContentRoot"], "sourceRoot": ""}