{"version": 3, "file": "static/js/875.8f961f57.chunk.js", "mappings": "4NAEO,SAASA,EAAoBC,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,EACoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,S,aCOvD,MASMC,GAAWC,EAAAA,EAAAA,IAAOC,EAAAA,EAAO,CAC7BC,KAAM,UACNN,KAAM,QAFSI,CAGd,CACDG,SAAU,WAyDZ,EAvD0BC,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,aAEF,UACJO,EAAS,OACTC,GAAS,KACNC,GACDJ,EACEK,EAAa,IACdL,EACHG,UAEIG,EA7BkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoBpB,EAAqBkB,EAAQ,EAsB1CG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKlB,EAAU,CACjCU,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BU,UAAWT,EAAS,OAAIU,EACxBd,IAAKA,EACLM,WAAYA,KACTD,GAEP,G,kEC/CO,SAASU,EAA4BzB,GAC1C,OAAOC,EAAAA,EAAAA,IAAqB,kBAAmBD,EACjD,CACA,MACA,GAD4BE,EAAAA,EAAAA,GAAuB,kBAAmB,CAAC,OAAQ,YAAa,QAAS,QAAS,UAAW,a,+DCFzH,MAAMwB,EAAQ,CACZ,CAAEC,MAAO,cAAeC,MAAO,QAC/B,CAAED,MAAO,aAAcC,MAAO,gBAC9B,CAAED,MAAO,oBAAqBC,MAAO,MACrC,CAAED,MAAO,MAAOC,MAAO,oBAgBzB,EAb6BC,KAEzBR,EAAAA,EAAAA,KAAA,OAAKR,UAAU,aAAYiB,SACxBJ,EAAMK,KAAI,CAACC,EAAMC,KAChBC,EAAAA,EAAAA,MAAA,OAAKrB,UAAW,kBAAkBoB,IAAQH,SAAA,EACxCT,EAAAA,EAAAA,KAAA,MAAAS,SAAKE,EAAKL,SACVN,EAAAA,EAAAA,KAAA,KAAGR,UAAU,aAAYiB,SAAEE,EAAKJ,UAFcK,M,kECZjD,SAASE,EAAuBnC,GACrC,OAAOC,EAAAA,EAAAA,IAAqB,aAAcD,EAC5C,CACA,MACA,GADuBE,EAAAA,EAAAA,GAAuB,aAAc,CAAC,OAAQ,WAAY,YAAa,QAAS,SAAU,WAAY,QAAS,WAAY,eAAgB,uBAAwB,iBAAkB,gBAAiB,UAAW,mB,mICKxO,MAkBA,EAlBoCkC,IAA2C,IAA1C,OAAEC,EAAM,QAAEC,EAAO,MAAEX,EAAK,SAAEG,GAAUM,EACvE,OAAKC,GAGHhB,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAgB0B,QAASD,EAAQR,UAC9CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAgB0B,QAASC,GAAKA,EAAEC,kBAAkBX,SAAA,EAC/DI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BT,EAAAA,EAAAA,KAAA,MAAAS,SAAKH,KACLN,EAAAA,EAAAA,KAAA,UAAQR,UAAU,eAAe0B,QAASD,EAAQR,SAAC,aAErDT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,aAAYiB,SACxBA,SAVW,IAaZ,E,cCpBH,MAeMY,EAAaA,CAACC,EAAgBC,KACzC,MAAMC,EAAOD,EAAcE,MAAKC,GAAKA,EAAEC,KAAOL,IAC9C,QAAOE,IAAQA,EAAKI,QAAgB,EAIzBC,EAAaA,CAACP,EAAgBC,KACjCA,EAAcO,MAAKJ,GAAKA,EAAEE,WAAaN,IAIpCS,EAAiBC,IAC5B,MAAMtB,EAAmC,CAAC,EACpCuB,EAAoB,GAW1B,OAVAD,EAAKE,SAAQC,IACXzB,EAAIyB,EAAKR,IAAM,IAAKQ,EAAM1B,SAAU,GAAI,IAE1CuB,EAAKE,SAAQC,IAC+B,IAADC,EAArCD,EAAKP,UAAYlB,EAAIyB,EAAKP,UACD,QAA3BQ,EAAA1B,EAAIyB,EAAKP,UAAUnB,gBAAQ,IAAA2B,GAA3BA,EAA6BC,KAAK3B,EAAIyB,EAAKR,KAE3CM,EAAMI,KAAK3B,EAAIyB,EAAKR,IACtB,IAEKM,CAAK,EAIDK,EAAsBA,CAACV,EAAkBL,KACpD,IAAIgB,EAAwB,GAC5B,MAAM9B,EAAWc,EAAciB,QAAOd,GAAKA,EAAEE,WAAaA,IAC1D,IAAK,MAAMa,KAAShC,EAClB8B,EAAYF,KAAKI,EAAMd,IACvBY,EAAcA,EAAYG,OAAOJ,EAAoBG,EAAMd,GAAIJ,IAEjE,OAAOgB,CAAW,ECxBPI,EAAqBrD,IAChC,MAAM,WACJsD,EAAU,cACVC,EAAa,aACbC,EAAY,gBACZC,EAAe,UACfC,EAAS,aACTC,EAAY,aACZC,EAAY,gBACZC,EAAe,WACfC,EAAU,cACVC,EAAa,aACbC,EAAY,SACZC,EAAQ,WACRC,EAAU,oBACVC,EAAmB,mBACnBC,EAAkB,cAClBC,EAAa,UACbC,EAAS,eACTC,EAAc,iBACdC,EAAgB,gBAChBC,EAAe,0BACfC,GACE1E,EAEE2E,GAAkBC,EAAAA,EAAAA,cAAYC,UAClCb,GAAa,GACb,IACE,MACMc,SADsBC,EAAAA,EAAAA,KAAQC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,gBACXC,KAAK9D,KAAI+D,IAAG,CAAO9C,GAAI8C,EAAI9C,MAAO8C,EAAIC,WAC9E7B,EAAcuB,EAChB,CAAE,MAAOO,GACPpB,EAAS,+BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,IACC,CAACT,EAAeS,EAAcC,IA+IjC,MAAO,CACLU,kBACAa,iBAzIuBX,UACvB,IAAKnB,IAAcE,EAEjB,YADAK,EAAS,qCAGXD,GAAa,GAEb,QAbuBa,WACvB,MAAMY,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc5C,GAErC,aADsBqD,EAAAA,EAAAA,IAAOD,IACdE,QAAQ,EASGC,CAAiBlC,GAIzC,OAFAO,EAAS,+DACTD,GAAa,GAGfA,GAAa,GACbG,GAAoB,EAAK,EA6HzB0B,oBA1H0BhB,UAAa,IAADiB,EACtC,IAAKpC,IAAcE,EAGf,OAFAK,EAAS,6CACTE,GAAoB,GAGxB,IAAI4B,EAA+B,KAChB,qBAAfjC,GAAqCN,EACvCuC,EAAgBvC,EACQ,qBAAfM,EACTiC,EAAgB,KACPvC,GAA+B,qBAAfM,EACvBiC,EAAgBvC,EACRA,GAA+B,qBAAfM,IACxBiC,EAAgB,MAGpB,MACMC,EAAU,GADGD,EAA4D,QAA/CD,EAAGxC,EAAWnB,MAAKC,GAAKA,EAAEC,KAAO0D,WAAc,IAAAD,OAAA,EAA5CA,EAA8CG,KAAO,iBACvDvC,IAAYwC,QAAQ,OAAQ,KAE7D,IACElC,GAAa,GACbG,GAAoB,GACpB,MAAMgC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcvB,IAC9B0C,KAAMC,EAAeC,MAAOC,GD/GRvF,KAChC,MAAMwF,EAAOxF,EACVyF,MAAM,IACNC,QAAO,CAACC,EAAKC,IAASD,EAAMC,EAAKC,WAAW,IAAI,GAE7CC,EAAQ,CAACC,EAAAA,IAAUC,EAAAA,IAAWC,EAAAA,IAAOC,EAAAA,KACrCC,EAAS,CAAC,UAAW,UAAW,UAAW,UAAW,WAK5D,MAAO,CAAEf,KAHIU,EAAMN,EAAOM,EAAMM,QAGjBd,MAFDa,EAAOX,EAAOW,EAAOC,QAEb,ECoGqCC,CAAkBzD,SAEnE0D,EAAAA,EAAAA,IAAOnB,EAAS,CACpB9D,GAAIqB,EACJ1C,MAAO4C,EACPqC,KAAMD,EACN1D,SAAUyD,EACVwB,aAAa,IAAIC,MAAOC,cACxBrB,KAAMC,EAAc1G,KACpB2G,MAAOC,EACPmB,OAAQ,GACRC,QAAQ,EACRC,OAAQlE,UAGJiB,IAENhB,EAAa,IACbE,EAAgB,IAChBO,GAAmB,GACnBL,EAAc,IACdN,EAAgBC,GAChBQ,EAAW,WAAWN,qCACtBiE,YAAW,IAAM3D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPpB,EAAS,yDACTqB,QAAQC,MAAM,uBAAwBF,EACxC,CAAC,QACCrB,GAAa,EACf,GAqEA8D,eAlEsB5F,IACtBqC,EAAerC,GACf2B,EAAgB3B,EAAKlB,OACrBwD,GAAiB,EAAK,EAgEtBuD,iBA7DuBlD,UACvB,MAAMmD,EAAc1E,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,IAClD,GAAKwE,GAAgBpE,EACrB,IACEI,GAAa,GACb,MAAMmC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc+C,EAAY3F,UAC5C4F,EAAAA,EAAAA,IAAU9B,EAAS,CAAEnF,MAAO4C,EAAc2D,aAAa,IAAIC,MAAOC,sBAClE9C,IACNH,GAAiB,GACjBD,EAAe,MACfV,EAAgB,IAChBK,EAAW,gCACX2D,YAAW,IAAM3D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,GA4CAkE,kBAzCyBlG,IACzByC,EAAgBzC,GAChB0C,GAA0B,EAAK,EAwC/ByD,oBArC0BtD,UAC1B,GAAKrB,EAAL,CACAQ,GAAa,GACb,IACE,MAAMoE,GAAQC,EAAAA,EAAAA,IAAWpD,EAAAA,IACnBqD,EAAiBtF,EAAoBQ,EAAcF,GACnDiF,EAAc,CAAC/E,KAAiB8E,GAEtC,IAAK,MAAMjG,KAAMkG,EACfH,EAAMI,QAAOrD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc5C,IACnC+F,EAAMI,QAAOrD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAAS5C,UAE1B+F,EAAMK,eACN9D,IAEND,GAA0B,GAC1BD,EAAgB,MAChBhB,EAAgB,IAChBY,EAAc,MACdC,EAAU,IACVJ,EAAW,yDACX2D,YAAW,IAAM3D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,CA1ByB,CA0BzB,EAWD,E,cC1LI,MCsEP,EAvFkDvC,IAS3C,IAT4C,WACjD6B,EAAU,aACVE,EAAY,aACZkF,EAAY,WACZ5E,EAAU,eACV6E,EAAc,UACdC,EAAS,eACTC,EAAc,gBACdC,GACDrH,EACC,MAAMsH,EAAoB,SAACC,GAAwD,IAArCC,EAAKC,UAAA9B,OAAA,QAAAvG,IAAAqI,UAAA,GAAAA,UAAA,GAAG,EACpD,OAAOF,EAAMG,SAAQjH,IAEnB,IAAIkH,EAAelH,EAAKlB,MAGxB,OAAKoI,GAAwC,KAAxBA,EAAaC,QAAkC,cAAjBD,EAK5C,EACL1I,EAAAA,EAAAA,KAAA,UAAsBO,MAAOiB,EAAKG,GAAIiH,MAAO,CAAEC,YAAwB,GAARN,EAAH,MAAoB9H,SAC7E,GAAG,KAAKqI,OAAOP,MAAUG,KADflH,EAAKG,OAGdH,EAAKf,UAAYe,EAAKf,SAASiG,OAAS,EAAI2B,EAAkB7G,EAAKf,SAAU8H,EAAQ,GAAK,KAR9F3D,QAAQmE,KAAK,qCAAqCvH,EAAKG,MAChD,GAQR,GAEL,EAkBA,OACEd,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAeiB,SAAA,EAC5BI,EAAAA,EAAAA,MAAA,UACEN,MAAOuC,EACPkG,SApBoB7H,IACxB,MAAM8H,EAAkB9H,EAAE+H,OAAO3I,MACjCyH,EAAaiB,EAAgB,EAmBzBzJ,UAAU,cACV2J,SAAUjB,EAAUzH,SAAA,EAEpBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAEyH,EAAY,qBAAuB,gCACpDG,EAAkBtG,EAAca,QAGnC5C,EAAAA,EAAAA,KAAA,OAAKR,UAAU,4BAA2BiB,UACxCI,EAAAA,EAAAA,MAAA,UACEN,MAAO6C,EACP4F,SA1BoB7H,IAC1B,MAAMiI,EAAYjI,EAAE+H,OAAO3I,MAC3B0H,EAAemB,GAEG,qBAAdA,GAAkD,qBAAdA,EACtCjB,IACuB,kBAAdiB,GACThB,GACF,EAmBM5I,UAAU,8BAA6BiB,SAAA,EAEvCT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,sBACjBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,mBAAmB4I,WAAYrG,EAAarC,SAAC,2BAG1DqC,IACCjC,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,EACET,EAAAA,EAAAA,KAAA,UAAQO,MAAM,mBAAkBE,SAAC,0BAGjCT,EAAAA,EAAAA,KAAA,UACEO,MAAM,gBACN4I,UAAWtH,EAAWiB,EAAcF,IAAevB,EAAWyB,EAAcF,GAAYnC,SACzF,mDAOL,ECpDV,EAnCsDM,IAK/C,IALgD,aACrD+B,EAAY,WACZF,EAAU,WACV0G,EAAU,aACVC,GACDxI,EACC,MAAMyI,EAAmB5G,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,IAEvD,OAAK0G,GAKH3I,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kBAAiBiB,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,oBAAkB+I,EAAiBlJ,MAAM,QAC7CO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMoI,EAAWE,GAC1BhK,UAAU,kDACV2J,UAAWrG,EAAarC,SAAA,CAEvBtB,EAAAA,cAAoBsK,EAAAA,KAAoC,iBAE3D5I,EAAAA,EAAAA,MAAA,UACEK,QAASA,IAAMqI,EAAazG,GAC5BtD,UAAU,8CACV2J,UAAWrG,EAAarC,SAAA,CAEvBtB,EAAAA,cAAoBuK,EAAAA,KAAqC,0BAnBzD,IAsBD,ECyNV,EAxPwD3I,IAKjD,IAAD4I,EAAA,IALmD,MACvDC,EAAK,MACLhJ,EAAK,SACLiJ,EAAQ,SACRC,GACD/I,EACC,MAAMgJ,EAAqBA,CAACC,EAAkBzJ,EAAe0J,KAC3D,MAAMC,EAAa,IAAKN,EAAMO,SAAW,IACzCD,EAAWF,GAAY,IAAKE,EAAWF,GAAW,CAACC,GAAM1J,GACzDsJ,EAASjJ,EAAO,IAAKgJ,EAAOO,QAASD,GAAa,EAa9CE,EAA4BjJ,IAChC,MAAM,MAAEZ,EAAK,KAAE8J,GAASlJ,EAAE+H,OAC1B,IAAIoB,EAAuB/J,EACd,aAAT8J,IACFC,EAAmBnJ,EAAE+H,OAA4BqB,SAEnDV,EAASjJ,EAAO,IAAKgJ,EAAOY,aAAcF,GAAkB,EAG9D,OACEzJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,8BAA6BiB,SAAA,EAC1CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gEAA+DiB,SAAA,EAC5ET,EAAAA,EAAAA,KAAA,UAAAS,SAASmJ,EAAMa,OAAS,kBAAyB,KAAGb,EAAMS,KAAK,KAC/DxJ,EAAAA,EAAAA,MAAA,UAAQK,QAASA,IAAM4I,EAASlJ,GAAQpB,UAAU,wBAAuBiB,SAAA,CACtEtB,EAAAA,cAAoBuK,EAAAA,KAAqC,iBAG9D7I,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,YAAWiB,SAAA,EAExBI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,cAAc9J,IAASpB,UAAU,aAAYiB,SAAC,YAC9DI,EAAAA,EAAAA,MAAA,UACEc,GAAI,cAAcf,IAClBpB,UAAU,eACVe,MAAOqJ,EAAMS,KACbrB,SAAW7H,GAAM0I,EAASjJ,EAAO,IAC5BgJ,EACHS,KAAMlJ,EAAE+H,OAAO3I,MACf4J,QAAwB,aAAfP,EAAMS,MAAsC,UAAfT,EAAMS,MAAmC,mBAAfT,EAAMS,UAA4BlK,EAAYyJ,EAAMO,QACpHQ,YAA4B,YAAff,EAAMS,MAAqC,WAAfT,EAAMS,UAAoBlK,EAAYyJ,EAAMe,cACpFlK,SAAA,EAEHT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,UACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,cACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,YACvBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,UACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,cACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,QAAOE,SAAC,iBACtBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,uBACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,iBAAgBE,SAAC,oBAC/BT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,YACvBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,iBACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,UAASE,SAAC,oBACxBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,kBAI3BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,eAAe9J,IAASpB,UAAU,aAAYiB,SAAC,aAC/DT,EAAAA,EAAAA,KAAA,SACE2B,GAAI,eAAef,IACnByJ,KAAK,OACL7K,UAAU,eACVe,MAAOqJ,EAAMa,MACbzB,SAAW7H,GAAM0I,EAASjJ,EAAO,IAAIgJ,EAAOa,MAAOtJ,EAAE+H,OAAO3I,QAC5DqK,UAAQ,OAIX,CAAC,OAAQ,WAAY,SAAU,QAAQC,SAASjB,EAAMS,QACrDxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,qBAAqB9J,IAASpB,UAAU,aAAYiB,SAAC,mBACrET,EAAAA,EAAAA,KAAA,SACE2B,GAAI,qBAAqBf,IACzByJ,KAAK,OACL7K,UAAU,eACVe,MAAOqJ,EAAMe,aAAe,GAC5B3B,SAAW7H,GAAM0I,EAASjJ,EAAO,IAAIgJ,EAAOe,YAAaxJ,EAAE+H,OAAO3I,aAKxD,WAAfqJ,EAAMS,OACLxJ,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,EACEI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,aAAa9J,IAASpB,UAAU,aAAYiB,SAAC,iBAC7DT,EAAAA,EAAAA,KAAA,SACE2B,GAAI,aAAaf,IACjByJ,KAAK,SACL7K,UAAU,eACVe,WAAqBJ,IAAdyJ,EAAMkB,IAAoB,GAAKlB,EAAMkB,IAC5C9B,SAAW7H,GAAM0I,EAASjJ,EAAO,IAAIgJ,EAAOkB,IAAwB,KAAnB3J,EAAE+H,OAAO3I,WAAeJ,EAAY4K,WAAW5J,EAAE+H,OAAO3I,eAG7GM,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,aAAa9J,IAASpB,UAAU,aAAYiB,SAAC,iBAC7DT,EAAAA,EAAAA,KAAA,SACE2B,GAAI,aAAaf,IACjByJ,KAAK,SACL7K,UAAU,eACVe,WAAqBJ,IAAdyJ,EAAMoB,IAAoB,GAAKpB,EAAMoB,IAC5ChC,SAAW7H,GAAM0I,EAASjJ,EAAO,IAAIgJ,EAAOoB,IAAwB,KAAnB7J,EAAE+H,OAAO3I,WAAeJ,EAAY4K,WAAW5J,EAAE+H,OAAO3I,iBAMhH,CAAC,WAAY,QAAS,kBAAkBsK,SAASjB,EAAMS,QACtDxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kCAAiCiB,SAAA,EAC9CT,EAAAA,EAAAA,KAAA,SAAOR,UAAU,aAAYiB,SAAC,cAChB,QADiCkJ,EAC9CC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAejJ,KAAI,CAACuK,EAAKjB,KACxBnJ,EAAAA,EAAAA,MAAA,OAAoBrB,UAAU,mBAAkBiB,SAAA,EAC9CT,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACL7K,UAAU,eACVmL,YAAY,eACZpK,MAAO0K,EAAIR,MACXzB,SAAW7H,GAAM4I,EAAmBC,EAAU7I,EAAE+H,OAAO3I,MAAO,YAEhEP,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACL7K,UAAU,eACVmL,YAAY,eACZpK,MAAO0K,EAAI1K,MACXyI,SAAW7H,GAAM4I,EAAmBC,EAAU7I,EAAE+H,OAAO3I,MAAO,YAEhEP,EAAAA,EAAAA,KAAA,UAAQqK,KAAK,SAASnJ,QAASA,IAzHvB8I,KAAsB,IAADkB,EACzC,MAAMhB,EAA0B,QAAhBgB,EAAGtB,EAAMO,eAAO,IAAAe,OAAA,EAAbA,EAAe1I,QAAO,CAAC2I,EAAGC,IAAMA,IAAMpB,IACzDH,EAASjJ,EAAO,IAAKgJ,EAAOO,QAASD,GAAa,EAuHDmB,CAAarB,GAAWxK,UAAU,yBAAwBiB,SAAC,aAfxFuJ,MAoBZhK,EAAAA,EAAAA,KAAA,UAAQqK,KAAK,SAASnJ,QAnIdoK,KAChB,MAAMpB,EAAa,IAAKN,EAAMO,SAAW,GAAK,CAAEM,MAAO,GAAIlK,MAAO,KAClEsJ,EAASjJ,EAAO,IAAKgJ,EAAOO,QAASD,GAAa,EAiIA1K,UAAU,2BAA0BiB,SAAC,kBAOlF,CAAC,OAAQ,WAAY,SAAU,QAAQoK,SAASjB,EAAMS,QACnDxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACvBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,uBAAuB9J,IAASpB,UAAU,aAAYiB,SAAC,qBACvET,EAAAA,EAAAA,KAAA,SACI2B,GAAI,uBAAuBf,IAC3ByJ,KAAqB,WAAfT,EAAMS,KAAoB,SAA0B,SAAfT,EAAMS,KAAkB,OAAS,OAC5E7K,UAAU,eACVe,WAA8BJ,IAAvByJ,EAAMY,aAA6B,GAAKe,OAAO3B,EAAMY,cAC5DxB,SAAUoB,QAKL,aAAfR,EAAMS,MAAsC,WAAfT,EAAMS,QACjCxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,wBAAuBiB,SAAA,EAClCT,EAAAA,EAAAA,KAAA,SACI2B,GAAI,uBAAuBf,IAC3ByJ,KAAK,WACL7K,UAAU,mBACV+K,QAASiB,QAAQ5B,EAAMY,cACvBxB,SAAUoB,KAEdpK,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,uBAAuB9J,IAASpB,UAAU,mBAAkBiB,SAAC,yBAIpF,CAAC,WAAY,SAASoK,SAASjB,EAAMS,OAAST,EAAMO,SAAWP,EAAMO,QAAQzD,OAAS,IAClF7F,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACxBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,uBAAuB9J,IAASpB,UAAU,aAAYiB,SAAC,qBACvEI,EAAAA,EAAAA,MAAA,UACIc,GAAI,uBAAuBf,IAC3BpB,UAAU,eACVe,WAA8BJ,IAAvByJ,EAAMY,aAA6B,GAAKe,OAAO3B,EAAMY,cAC5DxB,SAAUoB,EAAyB3J,SAAA,EAEnCT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,yBAChBmJ,EAAMO,QAAQzJ,KAAIuK,IAAOjL,EAAAA,EAAAA,KAAA,UAAwBO,MAAO0K,EAAI1K,MAAME,SAAEwK,EAAIR,OAAlCQ,EAAI1K,eAKvC,mBAAfqJ,EAAMS,OACHxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACvBT,EAAAA,EAAAA,KAAA,SAAOR,UAAU,aAAYiB,SAAC,wCAC9BT,EAAAA,EAAAA,KAAA,SACIqK,KAAK,OACL7K,UAAU,eACVe,MAAOkL,MAAMC,QAAQ9B,EAAMY,cAAgBZ,EAAMY,aAAamB,KAAK,KAAO,GAC1E3C,SAAW7H,GAAM0I,EAASjJ,EAAO,IAAIgJ,EAAOY,aAAcrJ,EAAE+H,OAAO3I,MAAMwF,MAAM,KAAKrF,KAAIkL,GAAKA,EAAEjD,SAAQnG,QAAOoJ,GAAKA,MACnHjB,YAAY,qBAKR,WAAff,EAAMS,OACLxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,qBAAqB9J,IAASpB,UAAU,aAAYiB,SAAC,mBACrET,EAAAA,EAAAA,KAAA,SACE2B,GAAI,qBAAqBf,IACzByJ,KAAK,OACL7K,UAAU,eACVe,MAAOqJ,EAAMiC,YAAc,GAC3B7C,SAAW7H,GAAM0I,EAASjJ,EAAO,IAAIgJ,EAAOiC,WAAY1K,EAAE+H,OAAO3I,aAKvD,YAAfqJ,EAAMS,OACLxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,uBAAuB9J,IAASpB,UAAU,aAAYiB,SAAC,qBACvET,EAAAA,EAAAA,KAAA,SACE2B,GAAI,uBAAuBf,IAC3ByJ,KAAK,OACL7K,UAAU,eACVe,MAAOqJ,EAAMkC,cAAgB,GAC7B9C,SAAW7H,GAAM0I,EAASjJ,EAAO,IAAIgJ,EAAOkC,aAAc3K,EAAE+H,OAAO3I,cAMvE,CAAC,SAAU,WAAWsK,SAASjB,EAAMS,QACrCxJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,wBAAuBiB,SAAA,EACpCT,EAAAA,EAAAA,KAAA,SACE2B,GAAI,kBAAkBf,IACtByJ,KAAK,WACL7K,UAAU,mBACV+K,UAAWX,EAAMgB,SACjB5B,SAAW7H,GAAM0I,EAASjJ,EAAO,IAAIgJ,EAAOgB,SAAUzJ,EAAE+H,OAAOqB,aAEjEvK,EAAAA,EAAAA,KAAA,SAAO0K,QAAS,kBAAkB9J,IAASpB,UAAU,mBAAkBiB,SAAC,sBAI1E,EC/LV,EAhD8DM,IASvD,IATwD,WAC7DgL,EAAU,OACV/E,EAAM,WACNgF,EAAU,cACVC,EAAa,cACbC,EAAa,OACbC,EAAM,UACNC,EAAS,QACTC,GACDtL,EACC,OACEF,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kBAAiBiB,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,2BAAyBsL,EAAWzL,UAExCN,EAAAA,EAAAA,KAAA,MAAAS,SAAI,yBACHuG,EAAOtG,KAAI,CAACkJ,EAAOhJ,KAClBZ,EAAAA,EAAAA,KAACsM,EAAe,CAEd1C,MAAOA,EACPhJ,MAAOA,EACPiJ,SAAUoC,EACVnC,SAAUoC,GAJLtC,EAAMjI,IAAMf,MAQrBC,EAAAA,EAAAA,MAAA,UAAQK,QAAS8K,EAAYxM,UAAU,oBAAmBiB,SAAA,CACvDtB,EAAAA,cAAoBoN,EAAAA,KAAoC,iBAG3D1L,EAAAA,EAAAA,MAAA,UACEK,QAASiL,EACT3M,UAAU,4BACV2J,SAAUkD,IAAYN,GAAgC,IAAlB/E,EAAON,OAAajG,SAAA,CAEvDtB,EAAAA,cAAoBqN,EAAAA,KAAoC,IAAEH,EAAU,YAAc,8BAGrFrM,EAAAA,EAAAA,KAAA,UACEkB,QAASkL,EACT5M,UAAU,8BACV2J,UAAW4C,GAAgC,IAAlB/E,EAAON,OAAajG,SAC9C,mBAGG,ECuCGgM,EAAwC,CACnD,CAAElM,MAAO,QAASkK,MAAO,SACzB,CAAElK,MAAO,SAAUkK,MAAO,UAC1B,CAAElK,MAAO,UAAWkK,MAAO,Y,cCxFtB,MCkJP,EA/I0D1J,IAQnD,IARoD,GACzDY,EAAE,MACF8I,EAAK,QACLN,EAAO,eACPuC,EAAc,SACd1D,EAAQ,SACRG,GAAW,EAAK,YAChBwB,EAAc,wBACf5J,EACC,MAAOC,EAAQ2L,IAAaC,EAAAA,EAAAA,WAAS,GAC/BC,GAAcC,EAAAA,EAAAA,QAAuB,OAG3CC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAsBC,IACtBJ,EAAYK,UAAYL,EAAYK,QAAQC,SAASF,EAAM/D,SAC7DyD,GAAU,EACZ,EAIF,OADAS,SAASC,iBAAiB,YAAaL,GAChC,KACLI,SAASE,oBAAoB,YAAaN,EAAmB,CAC9D,GACA,IAEH,MA+BMO,EAAgBb,EAAehG,SAAWyD,EAAQzD,QAAUyD,EAAQzD,OAAS,EAC7E8G,EAAkBd,EAAehG,OAAS,GAAKgG,EAAehG,OAASyD,EAAQzD,OAErF,OACE7F,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAO6J,QAAS/I,EAAInC,UAAU,aAAYiB,SAAA,CAAEgK,EAAM,QAClD5J,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,WAAWH,IAAKwN,EAAYpM,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACE2B,GAAIA,EACJnC,UAAW,+DAA8D2J,EAAW,WAAa,IACjGkB,KAAK,SACLnJ,QAASA,KAAOiI,GAAYwD,GAAW3L,GACvCmI,SAAUA,EACVP,MAAO,CACL6E,gBAAiBtE,EAAW,UAAY,QACxCuE,YAAa,WACbjN,UAEFT,EAAAA,EAAAA,KAAA,QAAMR,UAAqC,IAA1BkN,EAAehG,OAAe,aAAe,GAAGjG,SA7BlDkN,MACrB,GAA8B,IAA1BjB,EAAehG,OACjB,OAAOiE,EACF,GAA8B,IAA1B+B,EAAehG,OAAc,CACtC,MAAMkH,EAAiBzD,EAAQ1I,MAAKoM,GAAUA,EAAOlM,KAAO+K,EAAe,KAC3E,OAAqB,OAAdkB,QAAc,IAAdA,OAAc,EAAdA,EAAgB3O,OAAQ0L,CACjC,CACE,MAAO,GAAG+B,EAAehG,iBAC3B,EAsBSiH,OAIJ3M,IAAWmI,IACVtI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,2BAA2BoJ,MAAO,CAAEkF,UAAW,QAASC,UAAW,QAAStN,SAAA,CAExF0J,EAAQzD,OAAS,IAChB7F,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,EACET,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeiB,UAC5BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SACER,UAAU,mBACV6K,KAAK,WACL1I,GAAI,GAAGA,eACP4I,QAASgD,EACTlO,IAAM2O,IACAA,IAAOA,EAAMC,cAAgBT,EAAe,EAElDxE,SA3DIkF,KAClBxB,EAAehG,SAAWyD,EAAQzD,OAEpCsC,EAAS,IAGTA,EAASmB,EAAQzJ,KAAImN,GAAUA,EAAOlM,KACxC,KAsDgBd,EAAAA,EAAAA,MAAA,SAAOrB,UAAU,2BAA2BkL,QAAS,GAAG/I,eAAgBlB,SAAA,CAAC,eAC1D0J,EAAQzD,OAAO,aAIlC1G,EAAAA,EAAAA,KAAA,MAAIR,UAAU,wBAKjB2K,EAAQzJ,KAAImN,IACX7N,EAAAA,EAAAA,KAAA,OAAqBR,UAAU,gBAAeiB,UAC5CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SACER,UAAU,mBACV6K,KAAK,WACL1I,GAAI,GAAGA,KAAMkM,EAAOlM,KACpB4I,QAASmC,EAAe7B,SAASgD,EAAOlM,IACxCqH,SAAUA,KAAMmF,OAzFJC,EAyFyBP,EAAOlM,QAxFxD+K,EAAe7B,SAASuD,GAE1BpF,EAAS0D,EAAelK,QAAOb,GAAMA,IAAOyM,KAG5CpF,EAAS,IAAI0D,EAAgB0B,KANHA,KAyFoC,KAElDpO,EAAAA,EAAAA,KAAA,SAAOR,UAAU,mBAAmBkL,QAAS,GAAG/I,KAAMkM,EAAOlM,KAAKlB,SAC/DoN,EAAO5O,WAVJ4O,EAAOlM,MAgBC,IAAnBwI,EAAQzD,SACP1G,EAAAA,EAAAA,KAAA,OAAKR,UAAU,2BAA0BiB,UACvCT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,iCAQbiM,EAAehG,OAAS,IACvB7F,EAAAA,EAAAA,MAAA,SAAOrB,UAAU,0BAAyBiB,SAAA,CACvCiM,EAAehG,OAAO,OAAKyD,EAAQzD,OAAO,iBAG3C,ECyBV,EArKgE3F,IASzD,IAT0D,gBAC/DsN,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,GACD7N,EAEC,MAAM,QAAE8N,EAAO,UAAEC,EAAS,QAAEC,EAAO,QAAE1C,EAAO,MAAExH,EAAK,QAAEmK,GFbpBC,MACjC,MAAOJ,EAASK,IAActC,EAAAA,EAAAA,UAAmB,KAC1CkC,EAAWK,IAAgBvC,EAAAA,EAAAA,UAAqB,KAChDmC,EAASK,IAAcxC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAkB,IACzC/H,EAAOtB,IAAYqJ,EAAAA,EAAAA,UAAwB,MAE5C0C,EAAkBnL,UACtB,IACEkL,GAAW,GACX9L,EAAS,MAETqB,QAAQ2K,IAAI,0EAGZ,MAAMC,QAAgBC,EAAAA,EAAcC,qBAEpC9K,QAAQ2K,IAAI,sCAAkCC,EAAQ9I,OAAQ,kBAG9D,MAAMiJ,EAAyB,OAAPH,QAAO,IAAPA,OAAO,EAAPA,EACpB9O,KAAIkP,GAAOA,EAAIC,SAChBrN,QAAO,CAACsN,EAAQlP,EAAOmP,IAAUA,EAAMC,QAAQF,KAAYlP,IAC3D4B,QAAQsN,GAAuC,MAAVA,GAAoC,KAAlBA,EAAOnH,SAC9DsH,OAIGC,GAAwC,OAAfP,QAAe,IAAfA,OAAe,EAAfA,EAAiBjP,KAAIyP,IAAU,CAC5DxO,GAAIwO,EAAWC,cAAc5K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzEvG,KAAMkR,QACD,GAGDE,EAA2B,OAAPb,QAAO,IAAPA,OAAO,EAAPA,EACtB9O,KAAIkP,IAAG,CAAOE,OAAQF,EAAIC,OAAQS,SAAUV,EAAIW,aACjD/N,QAAO,CAACL,EAAMvB,EAAOmP,IACpBA,EAAMS,WAAUC,GAAKA,EAAEX,SAAW3N,EAAK2N,QAAUW,EAAEH,WAAanO,EAAKmO,aAAc1P,IAEpF4B,QAAQL,GACQ,MAAfA,EAAK2N,QAAmC,MAAjB3N,EAAKmO,UACL,KAAvBnO,EAAK2N,OAAOnH,QAA0C,KAAzBxG,EAAKmO,SAAS3H,SAE5CsH,MAAK,CAACS,EAAGC,IAAMD,EAAEZ,OAAOc,cAAcD,EAAEb,SAAWY,EAAEJ,SAASM,cAAcD,EAAEL,YAE3EO,GAA8C,OAAjBR,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmB3P,KAAIyB,IAAI,CAC5DR,GAAIQ,EAAKmO,SAASF,cAAc5K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC5EvG,KAAMkD,EAAKmO,SACXR,OAAQ3N,EAAK2N,aACR,GAGDgB,GAAgC,OAAPtB,QAAO,IAAPA,OAAO,EAAPA,EAC3BhN,QAAOoN,GAAOA,EAAI,gBAAkBA,EAAIC,QAAUD,EAAIW,WACvD7P,KAAIkP,IAAG,CACNjO,GAAIiO,EAAI,eACR3Q,KAAM2Q,EAAI,eACVE,OAAQF,EAAIC,QAAU,GACtBS,SAAUV,EAAIW,UAAY,GAC1BQ,WAAYnB,EAAI,qBACX,GAITV,EAAWgB,GACXf,EAAa0B,GACbzB,EAAW0B,EAEb,CAAE,MAAOnM,GACPC,QAAQC,MAAM,8BAAqBF,GACnCpB,EAAS,iDACT2L,EAAW,IACXC,EAAa,IACbC,EAAW,GACb,CAAC,QACCC,GAAW,EACb,GAQF,OAJAtC,EAAAA,EAAAA,YAAU,KACRuC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA1C,UACAxH,QACAmK,QAASM,EACV,EE9EgE0B,GAG3DC,EAAsB5C,EAAgB3N,KAAIwQ,IAAQ,IAAAC,EAAA,OAClB,QADkBA,EACtDtC,EAAQpN,MAAK2P,GAAKA,EAAEzP,KAAOuP,WAAS,IAAAC,OAAA,EAApCA,EAAsClS,IAAI,IAC1CuD,OAAOgJ,SAEH6F,EAAqBhD,EAAgB3H,OAAS,EAChDoI,EAAUtM,QAAO8N,GAAYW,EAAoBpG,SAASyF,EAASR,UACnEhB,EAGEwC,EAAwBhD,EAAkB5N,KAAI6Q,IAAU,IAAAC,EAAA,OACpB,QADoBA,EAC5D1C,EAAUrN,MAAKgQ,GAAKA,EAAE9P,KAAO4P,WAAW,IAAAC,OAAA,EAAxCA,EAA0CvS,IAAI,IAC9CuD,OAAOgJ,SAEHkG,EAAmBpD,EAAkB5H,OAAS,EAChDqI,EAAQvM,QAAOmP,GACbV,EAAoBpG,SAAS8G,EAAO7B,SACpCwB,EAAsBzG,SAAS8G,EAAOrB,YAExCjC,EAAgB3H,OAAS,EACvBqI,EAAQvM,QAAOmP,GAAUV,EAAoBpG,SAAS8G,EAAO7B,UAC7Df,EAiCN,OA9BAhC,EAAAA,EAAAA,YAAU,KACR,GAAIsB,EAAgB3H,OAAS,EAAG,CAE9B,MAAMkL,EAAiBtD,EAAkB9L,QAAO+O,IAC9C,MAAMjB,EAAWxB,EAAUrN,MAAKgQ,GAAKA,EAAE9P,KAAO4P,IAC9C,OAAOjB,GAAYW,EAAoBpG,SAASyF,EAASR,OAAO,IAG9D8B,EAAelL,SAAW4H,EAAkB5H,QAC9CgI,EAAkBkD,EAEtB,IACC,CAACvD,EAAiBC,EAAmBQ,EAAWmC,EAAqBvC,KAExE3B,EAAAA,EAAAA,YAAU,KACR,GAAIuB,EAAkB5H,OAAS,EAAG,CAEhC,MAAMmL,EAAetD,EAAgB/L,QAAOsP,IAC1C,MAAMH,EAAS5C,EAAQtN,MAAKsQ,GAAKA,EAAEpQ,KAAOmQ,IAC1C,OAAOH,GACAV,EAAoBpG,SAAS8G,EAAO7B,SACpCwB,EAAsBzG,SAAS8G,EAAOrB,SAAS,IAGpDuB,EAAanL,SAAW6H,EAAgB7H,QAC1CiI,EAAgBkD,EAEpB,IACC,CAACvD,EAAmBC,EAAiBQ,EAASkC,EAAqBK,EAAuB3C,KAG3F9N,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,iCAAgCiB,SAAA,EAC7CT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,yBAEH4L,IACCrM,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBiB,UAC/BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,4BAA2BiB,SAAA,EACxCT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,wCAAwCwS,KAAK,SAAQvR,UAClET,EAAAA,EAAAA,KAAA,QAAMR,UAAU,kBAAiBiB,SAAC,iBAC9B,8BAMXoE,IACChE,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,qBAAoBiB,SAAA,EACjCT,EAAAA,EAAAA,KAAA,UAAAS,SAAQ,WAAe,IAAEoE,GACzB7E,EAAAA,EAAAA,KAAA,UACER,UAAU,qCACV0B,QAAS8N,EAAQvO,SAClB,cAMH4L,IAAYxH,IACZhE,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,MAAKiB,SAAA,EAClBT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAACiS,EAAgB,CACftQ,GAAG,gBACH8I,MAAM,iBACNN,QAAS0E,EACTnC,eAAgB2B,EAChBrF,SAAUyF,EACVtF,SAAUkD,EACV1B,YAAY,4BAIhB3K,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAACiS,EAAgB,CACftQ,GAAG,kBACH8I,MAAM,mBACNN,QAASkH,EACT3E,eAAgB4B,EAChBtF,SAAU0F,EACVvF,SAAqC,IAA3BkF,EAAgB3H,QAAgB2F,EAC1C1B,YAAY,8BAIhB3K,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAACiS,EAAgB,CACftQ,GAAG,gBACH8I,MAAM,iBACNN,QAASuH,EACThF,eAAgB6B,EAChBvF,SAAU2F,EACVxF,SAAuC,IAA7BmF,EAAkB5H,QAAgB2F,EAC5C1B,YAAY,4BAIhB3K,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAO6J,QAAQ,mBAAmBlL,UAAU,aAAYiB,SAAA,CAAC,sBACrCT,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaiB,SAAC,UAElDI,EAAAA,EAAAA,MAAA,UACEc,GAAG,mBACHnC,UAAW,gBAAgBgP,EAAmC,GAAf,cAC/CjO,MAAOiO,EACPxF,SAAW7H,GAAMyN,EAAkBzN,EAAE+H,OAAO3I,OAC5C4I,SAAUkD,EACVzB,UAAQ,EAAAnK,SAAA,EAERT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,2BAChBgM,EAAmB/L,KAAIwR,IACtBlS,EAAAA,EAAAA,KAAA,UAA8BO,MAAO2R,EAAU3R,MAAME,SAClDyR,EAAUzH,OADAyH,EAAU3R,aAKzBiO,IACAxO,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBiB,SAAC,4CAQxC,ECrKJ0R,EAAc,4BC4ZpB,EAtZ8BC,KAAO,IAADC,EAAAC,EAElC,MAAMC,EChB2BC,MACjC,MAAO5P,EAAYC,IAAiB+J,EAAAA,EAAAA,UAAqB,KAClD9J,EAAcC,IAAmB6J,EAAAA,EAAAA,UAAiB,KAClDb,EAAYpI,IAAiBiJ,EAAAA,EAAAA,UAA4B,OACzD5F,EAAQpD,IAAagJ,EAAAA,EAAAA,UAAsB,KAC3C6F,EAAwBC,IAA6B9F,EAAAA,EAAAA,UAA6B,KAClF1E,EAAW5E,IAAgBsJ,EAAAA,EAAAA,WAAkB,IAC7CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAS,IAChC/H,EAAOtB,IAAYqJ,EAAAA,EAAAA,UAAwB,OAC3C+F,EAASnP,IAAcoJ,EAAAA,EAAAA,UAAwB,OAE/CgG,EAAiBlP,IAAsBkJ,EAAAA,EAAAA,WAAkB,IACzD5J,EAAWC,IAAgB2J,EAAAA,EAAAA,UAAiB,KAC5C1J,EAAcC,IAAmByJ,EAAAA,EAAAA,UAAiB,KAClDiG,EAAkBpP,IAAuBmJ,EAAAA,EAAAA,WAAkB,IAE3DtF,EAAazD,IAAkB+I,EAAAA,EAAAA,UAA0B,OACzDkG,EAAehP,IAAoB8I,EAAAA,EAAAA,WAAkB,IAErDmG,EAAchP,IAAmB6I,EAAAA,EAAAA,UAAwB,OACzDoG,EAAwBhP,IAA6B4I,EAAAA,EAAAA,WAAkB,IAEvExJ,EAAYC,IAAiBuJ,EAAAA,EAAAA,UAAiB,KAG9CqG,EAAeC,IAAoBtG,EAAAA,EAAAA,WAAS,IAC5CuG,EAAgBC,IAAqBxG,EAAAA,EAAAA,UAAS,KAG9CyB,EAAiBgF,IAAsBzG,EAAAA,EAAAA,UAAmB,KAC1D0B,EAAmBgF,IAAwB1G,EAAAA,EAAAA,UAAmB,KAC9D2B,EAAiBgF,IAAsB3G,EAAAA,EAAAA,UAAmB,KAC1D4B,EAAmBgF,IAAwB5G,EAAAA,EAAAA,UAAiB,IAEnE,MAAO,CAELhK,aACAE,eACAiJ,aACA/E,SACAyL,yBACAvK,YACAmE,UACAxH,QACA8N,UACAC,kBACA5P,YACAE,eACA2P,mBACAvL,cACAwL,gBACAC,eACAC,yBACA5P,aACA6P,gBACAE,iBACA9E,kBACAC,oBACAC,kBACAC,oBAGA3L,gBACAE,kBACAY,gBACAC,YACA8O,4BACApP,eACA+L,aACA9L,WACAC,aACAE,qBACAT,eACAE,kBACAM,sBACAI,iBACAC,mBACAC,kBACAC,4BACAX,gBACA6P,mBACAE,oBACAC,qBACAC,uBACAC,qBACAC,uBACD,EDtEahB,GAKRiB,EAAiB9Q,EAAkB,CACvCC,WAAY2P,EAAM3P,WAClBC,cAAe0P,EAAM1P,cACrBC,aAAcyP,EAAMzP,aACpBC,gBAAiBwP,EAAMxP,gBACvBC,UAAWuP,EAAMvP,UACjBC,aAAcsP,EAAMtP,aACpBC,aAAcqP,EAAMrP,aACpBC,gBAAiBoP,EAAMpP,gBACvBC,WAAYmP,EAAMnP,WAClBC,cAAekP,EAAMlP,cACrBC,aAAciP,EAAMjP,aACpBC,SAAUgP,EAAMhP,SAChBC,WAAY+O,EAAM/O,WAClBC,oBAAqB8O,EAAM9O,oBAC3BC,mBAAoB6O,EAAM7O,mBAC1BC,cAAe4O,EAAM5O,cACrBC,UAAW2O,EAAM3O,UACjBC,eAAgB0O,EAAM1O,eACtBC,iBAAkByO,EAAMzO,iBACxBC,gBAAiBwO,EAAMxO,gBACvBC,0BAA2BuO,EAAMvO,4BAG7B0P,EVjB6BpU,KACnC,MAAM,WACJsD,EAAU,aACVE,EAAY,WACZiJ,EAAU,cACVpI,EAAa,OACbqD,EAAM,UACNpD,EAAS,0BACT8O,EAAyB,WACzBrD,EAAU,SACV9L,EAAQ,WACRC,EAAU,kBACV4P,EAAiB,iBACjBF,EAAgB,gBAChB7E,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,mBACjB6E,EAAkB,qBAClBC,EAAoB,mBACpBC,EAAkB,qBAClBC,GACElU,EAmUJ,MAAO,CACLqU,wBAlU6BzP,EAAAA,EAAAA,cAAYC,UACzC,GAAKyP,EAAL,CACAhP,QAAQ2K,IAAI,4CAA4CqE,KACxD,IACE,MAAMC,GAAgBpP,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,cAAeqP,GACvCE,QAAuB9O,EAAAA,EAAAA,IAAO6O,GACpC,GAAIC,EAAe7O,SAAU,CAC3B,MAAM8O,EAAiBD,EAAepP,OACtCgO,EAA0BqB,EAAe/M,QAAU,IACnDpC,QAAQ2K,IAAI,0BAA2BwE,EAAe/M,OACxD,MACEpC,QAAQ2K,IAAI,mDAAmDqE,KAC/DlB,EAA0B,GAE9B,CAAE,MAAO/N,GACPC,QAAQC,MAAM,sCAAuCF,GACrDpB,EAAS,wCACTmP,EAA0B,GAC5B,CAjBmB,CAiBnB,GACC,CAACA,EAA2BnP,IAgT7ByQ,gBA9SqB9P,EAAAA,EAAAA,cAAYC,UACjC,GAAK7C,EAAL,CAIAsD,QAAQ2K,IAAI,qCAAqCjO,KACjD+N,GAAW,GACX9L,EAAS,MACT,IAEE,MAAMwB,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASjD,GAC1B2S,QAAgBjP,EAAAA,EAAAA,IAAOD,GAE7B,IAAIL,EAA0B,KAE9B,GAAIuP,EAAQhP,SAEVP,EAAOuP,EAAQvP,YAGf,IACEA,QAAawP,EAAAA,EAAoBF,eAAe1S,EAClD,CAAE,MAAO6S,GACP,CAIJ,GAAIzP,EAAM,CACRf,EAAce,GACdd,EAAUc,EAAKsC,QAAU,IAGzB,MAAMoN,EAAe1P,EAAK2J,kBAAoB3J,EAAK2P,eAAiB,CAAC3P,EAAK2P,gBAAkB,IACtFC,EAAiB5P,EAAK4J,oBAAsB5J,EAAK6P,iBAAmB,CAAC7P,EAAK6P,kBAAoB,IAC9FC,EAAe9P,EAAK6J,kBAAoB7J,EAAK+P,eAAiB,CAAC/P,EAAK+P,gBAAkB,IACtFC,EAAiBhQ,EAAK8J,mBAAqB,GAGlB,IAA3BH,EAAgB3H,QAAgB0N,EAAa1N,OAAS,GACxD2M,EAAmBe,GAEY,IAA7B9F,EAAkB5H,QAAgB4N,EAAe5N,OAAS,GAC5D4M,EAAqBgB,GAEQ,IAA3B/F,EAAgB7H,QAAgB8N,EAAa9N,OAAS,GACxD6M,EAAmBiB,IAEhBhG,GAAqBkG,GACxBlB,EAAqBkB,EAEzB,KAAO,CAEL,MAAMlT,EAAOoB,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOL,IAC3CqC,EAAc,CACZhC,GAAIL,EACJhB,OAAW,OAAJkB,QAAI,IAAJA,OAAI,EAAJA,EAAMlB,QAAS,WACtB0G,OAAQ,GACRH,aAAa,IAAIC,MAAOC,gBAE1BnD,EAAU,IAEVyP,EAAmB,IACnBC,EAAqB,IACrBC,EAAmB,IACnBC,EAAqB,GACvB,CACF,CAAE,MAAO7O,GACPpB,EAAS,sCACTqB,QAAQC,MAAMF,GACdhB,EAAc,MACdC,EAAU,GACZ,CAAC,QACCyL,GAAW,EACb,CArEA,MAFEzK,QAAQ2K,IAAI,uCAuEd,GACC,CAAC3M,EAAYyM,EAAY9L,EAAUI,EAAeC,EAAWyP,EAAoBC,EAAsBC,EAAoBC,EAAsBnF,EAAiBC,EAAmBC,EAAiBC,IAqOvMmG,SAnOeA,KACf,MAAMC,EAAsB,CAC1BjT,GAAI,SAASmF,KAAK+N,QAClBxK,KAAM,OACNI,MAAO,YACPE,YAAa,GACbR,QAAS,GACTS,UAAU,EACVkF,OAAQ,GACRQ,SAAU,GACVqB,OAAQ,IAEV/N,EAAU,IAAIoD,EAAQ4N,GAAU,EAwNhCE,oBArN2BC,IAC3BnQ,QAAQ2K,IAAI,mCAAoCwF,GAChD,MAAMH,EAAsB,CAC1BjT,GAAIoT,EAAapT,GACjB0I,KAAM0K,EAAa1K,KACnBI,MAAOsK,EAAatK,MACpBE,YAAaoK,EAAapK,YAC1BR,QAAS4K,EAAa5K,QAAU4K,EAAa5K,QAAQzJ,KAAKuK,GACrC,kBAARA,EACF,CAAER,MAAOQ,EAAK1K,MAAO0K,GAErB,CAAER,MAAOQ,EAAIR,MAAOlK,MAAO0K,EAAI1K,cAErCJ,EACLyK,SAAUmK,EAAanK,SACvBJ,aAAcuK,EAAavK,aAC3BM,IAAKiK,EAAajK,IAClBE,IAAK+J,EAAa/J,IAClBc,kBAAc3L,EACd6U,aAAS7U,EACT0L,gBAAY1L,EACZ8U,gBAAY9U,EACZ+U,mBAAe/U,EACfI,WAAOJ,GAGT,GAAI6G,EAAOlF,MAAK8H,GAASA,EAAMjI,KAAOiT,EAASjT,KAI3C,OAHAiD,QAAQmE,KAAK,iCAAiC6L,EAASjT,yBACvD4B,EAAS,kBAAkBqR,EAASjT,sDACpCwF,YAAW,IAAM5D,EAAS,OAAO,KAIrCqB,QAAQ2K,IAAI,6BAA8BqF,GAC1ChR,EAAU,IAAIoD,EAAQ4N,IACtBpR,EAAW,gBAAgBoR,EAASnK,iCACpCtD,YAAW,IAAM3D,EAAW,OAAO,IAAK,EAkLxC2R,YA/KkBA,CAACvU,EAAewU,KAClC,MAAMC,EAAgB,IAAIrO,GAC1BqO,EAAczU,GAASwU,EACvBxR,EAAUyR,EAAc,EA6KxBC,YA1KmB1U,IACnBgD,EAAUoD,EAAOxE,QAAO,CAAC2I,EAAGC,IAAMA,IAAMxK,IAAO,EA0K/C2U,WAvKiBpR,UACjB,GAAKrB,GAAiBiJ,EAMtB,GAAKyC,EAAL,CAKAa,GAAW,GACXzK,QAAQ2K,IAAI,oDAAqDzM,GACjE8B,QAAQ2K,IAAI,sBAAuBvI,GACnCpC,QAAQ2K,IAAI,oBAAqBf,GAEjC,IAAK,IAADpJ,EACF,MAAMoQ,EAAgBxO,EAAOtG,KAAIkJ,IAC/B,MAAM6L,EAAoB,CAAC,EAC3B,IAAK,MAAMxL,KAAOL,OACGzJ,IAAfyJ,EAAMK,GACRwL,EAAaxL,GAAOL,EAAMK,GAE1BwL,EAAaxL,GAAO,KAGxB,OAAOwL,CAAY,IAGfC,EAAgC,IACjC3J,EACHpK,GAAImB,EACJxC,OAAkD,QAA3C8E,EAAAxC,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,WAAa,IAAAsC,OAAA,EAA3CA,EAA6C9E,QAASyL,EAAWzL,MACxE0G,OAAQwO,EACR3O,aAAa,IAAIC,MAAOC,cACxBsH,kBACAC,oBACAC,kBACAC,qBAIImH,EAAe,GAGrBA,EAAatT,MACXuE,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASzB,GAAe4S,GACpCE,OAAMjR,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAIkR,MAAM,yBAAyBlR,EAAImR,UAAU,KAK7DH,EAAatT,KACX6R,EAAAA,EAAoB6B,eAAeL,GAChCE,OAAMjR,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAIkR,MAAM,yBAAyBlR,EAAImR,UAAU,WAKvDE,QAAQC,IAAIN,GAElBhS,EAAc+R,GACdlS,EAAW,0CACX2D,YAAW,IAAM3D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPC,QAAQC,MAAM,qCAAsCF,GACpDpB,EAAS,sCAAsCoB,aAAekR,MAAQlR,EAAImR,QAAU,kBACtF,CAAC,QACCzG,GAAW,EACb,CAjEA,MAFE9L,EAAS,+EANTA,EAAS,mDAyEX,EA6FA2S,cA1FoBA,KACpB,IAAKnK,GAAgC,IAAlB/E,EAAON,OAExB,YADAyP,MAAM,+CAIR,MAAMC,EAAmB,eACjBrK,EAAWzL,qCAEb0G,EAAOtG,KAAIkJ,IAAU,IAADsB,EAAAvB,EACpB,IAAI0M,EAAY,GAChB,OAAQzM,EAAMS,MACZ,IAAK,OACL,IAAK,SACL,IAAK,OACL,IAAK,WACHgM,EAAY,gGAEoBzM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8CACnDhB,EAAMS,2CAA2CT,EAAMe,aAAe,OAAOf,EAAMgB,SAAW,WAAa,gDAG9H,MACF,IAAK,WACHyL,EAAY,gGAEoBzM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8DACnChB,EAAMgB,SAAW,WAAa,oDACjChB,EAAMa,wCACjB,QAAbS,EAAAtB,EAAMO,eAAO,IAAAe,OAAA,EAAbA,EAAexK,KAAImN,GAAU,kBAAkBA,EAAOtN,UAAUsN,EAAOpD,mBAAkBkB,KAAK,MAAO,0EAI7G,MACF,IAAK,WACH0K,EAAY,0HAE8CzM,EAAMjI,OAAOiI,EAAMgB,SAAW,WAAa,iEAC1DhB,EAAMjI,OAAOiI,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,qDAG9F,MACF,IAAK,QACHyL,EAAY,gGAEoBzM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,kCACnD,QAAbjB,EAAAC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAejJ,KAAI,CAACmN,EAAQzC,IAAM,4HAEqBxB,EAAMjI,WAAWiI,EAAMjI,MAAMyJ,aAAayC,EAAOtN,UAAUqJ,EAAMgB,SAAW,WAAa,mEACvGhB,EAAMjI,MAAMyJ,MAAMyC,EAAOpD,kEAEjEkB,KAAK,MAAO,6CAGnB,MACF,IAAK,UACH0K,EAAY,8FAEmBzM,EAAMkC,cAAgB,yNAMrD,MACF,IAAK,SACHuK,EAAY,wEAC2CzM,EAAMiC,YAAc,oCAE3E,MACF,QACEwK,EAAY,8BAA8BzM,EAAMS,WAEpD,OAAOgM,CAAS,IACf1K,KAAK,2BAIZyH,EAAkBgD,GAClBlD,GAAiB,EAAK,EAYvB,EUjVyBoD,CAAqB,CAC7C1T,WAAY2P,EAAM3P,WAClBE,aAAcyP,EAAMzP,aACpBiJ,WAAYwG,EAAMxG,WAClBpI,cAAe4O,EAAM5O,cACrBqD,OAAQuL,EAAMvL,OACdpD,UAAW2O,EAAM3O,UACjB8O,0BAA2BH,EAAMG,0BACjCrD,WAAYkD,EAAMlD,WAClB9L,SAAUgP,EAAMhP,SAChBC,WAAY+O,EAAM/O,WAClB4P,kBAAmBb,EAAMa,kBACzBF,iBAAkBX,EAAMW,iBACxB7E,gBAAiBkE,EAAMlE,gBACvBC,kBAAmBiE,EAAMjE,kBACzBC,gBAAiBgE,EAAMhE,gBACvBC,kBAAmB+D,EAAM/D,kBACzB6E,mBAAoBd,EAAMc,mBAC1BC,qBAAsBf,EAAMe,qBAC5BC,mBAAoBhB,EAAMgB,mBAC1BC,qBAAsBjB,EAAMiB,wBAI9BzG,EAAAA,EAAAA,YAAU,KACR0G,EAAexP,kBAGf,MAAMsS,EDjD4BC,MACpC,IACE,MAAMC,EAASC,aAAaC,QAAQxE,GACpC,GAAIsE,EACF,OAAOG,KAAKC,MAAMJ,EAEtB,CAAE,MAAO5R,GACPD,QAAQmE,KAAK,wDAAyDlE,EACxE,CACA,OAAO,IAAI,ECwCe2R,GACpBD,IACEA,EAAgBlI,gBAAgB3H,OAAS,GAC3C6L,EAAMc,mBAAmBkD,EAAgBlI,iBAEvCkI,EAAgBjI,kBAAkB5H,OAAS,GAC7C6L,EAAMe,qBAAqBiD,EAAgBjI,mBAEzCiI,EAAgBhI,gBAAgB7H,OAAS,GAC3C6L,EAAMgB,mBAAmBgD,EAAgBhI,iBAEvCgI,EAAgB/H,mBAClB+D,EAAMiB,qBAAqB+C,EAAgB/H,mBAE/C,GACC,KAGHzB,EAAAA,EAAAA,YAAU,KACR,MAAM+J,EAAa,CACjBzI,gBAAiBkE,EAAMlE,gBACvBC,kBAAmBiE,EAAMjE,kBACzBC,gBAAiBgE,EAAMhE,gBACvBC,kBAAmB+D,EAAM/D,oBAIvBsI,EAAWzI,gBAAgB3H,OAAS,GACpCoQ,EAAWxI,kBAAkB5H,OAAS,GACtCoQ,EAAWvI,gBAAgB7H,OAAS,GACpCoQ,EAAWtI,oBD1FoBsI,KACrC,IACEJ,aAAaK,QAAQ5E,EAAayE,KAAKI,UAAUF,GACnD,CAAE,MAAOjS,GACPD,QAAQmE,KAAK,sDAAuDlE,EACtE,GCsFIoS,CAAuBH,EACzB,GACC,CAACvE,EAAMlE,gBAAiBkE,EAAMjE,kBAAmBiE,EAAMhE,gBAAiBgE,EAAM/D,qBAGjFzB,EAAAA,EAAAA,YAAU,KACJwF,EAAMzP,cAAgBjB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,aAAoC,kBAArB2P,EAAMnP,YACvIsQ,EAAkBM,eAAezB,EAAMzP,cACvC4Q,EAAkBC,uBAAuBpB,EAAMzP,gBACtCyP,EAAMzP,cAAkBjB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAevB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,aAAqC,kBAArB2P,EAAMnP,WAItImP,EAAMzP,eAChByP,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,IAChB2O,EAAMG,0BAA0B,IAChCH,EAAMlP,cAAc,MAPpBkP,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,IAChB2O,EAAMG,0BAA0B,KAQ9BH,EAAMzP,cAAqC,kBAArByP,EAAMnP,YAC5BmP,EAAMG,0BAA0B,GACpC,GACC,CAACH,EAAMzP,aAAcyP,EAAM3P,WAAY2P,EAAMnP,aA+EhD,OACEpD,EAAAA,EAAAA,KAAAqJ,EAAAA,SAAA,CAAA5I,UACEI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,CAC1B8R,EAAM1N,QAAS7E,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeiB,SAAE8R,EAAM1N,QACrD0N,EAAMI,UACL3S,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kBAAiBiB,SAC7B8R,EAAMI,WAGX3S,EAAAA,EAAAA,KAAA,MAAAS,SAAI,2BAEJT,EAAAA,EAAAA,KAACkX,EAAY,CACXtU,WAAY2P,EAAM3P,WAClBE,aAAcyP,EAAMzP,aACpBkF,aA1FkB1G,IAGxB,GAFAiR,EAAMxP,gBAAgBzB,GACtBiR,EAAMlP,cAAc,IACf/B,EAGE,CACH,MAAM6V,EAAatV,EAAWP,EAAQiR,EAAM3P,YACtCwU,EAAa/V,EAAWC,EAAQiR,EAAM3P,YACxCuU,IAAcC,IACd7E,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,IAExB,MATI2O,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,GAQpB,EA8EMR,WAAYmP,EAAMnP,WAClB6E,eA5EoBoP,IAC1B9E,EAAMlP,cAAcgU,EAAO,EA4ErBnP,UAAWqK,EAAMrK,UACjBC,eA1EmBmP,KACzB/E,EAAMtP,aAAa,IACnBsP,EAAMpP,gBAAgB,IACtBoP,EAAM7O,oBAAmB,EAAK,EAwExB0E,gBArEoBmP,KACtBhF,EAAMzP,cAAgBjB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,YAClH8Q,EAAkBM,eAAezB,EAAMzP,cAC9ByP,EAAMzP,eACfyP,EAAMhP,SAAS,sFACfgP,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,IAClB,IAkEK2O,EAAMK,kBACL5S,EAAAA,EAAAA,KAACwX,EAAK,CACJxW,OAAQuR,EAAMK,gBACd3R,QAASA,KACPsR,EAAM7O,oBAAmB,GACzB6O,EAAMlP,cAAc,IACpBkP,EAAMtP,aAAa,IACnBsP,EAAMpP,gBAAgB,GAAG,EAE3B7C,MACuB,qBAArBiS,EAAMnP,WAAoC,yBAC1CmP,EAAMzP,cAAqC,qBAArByP,EAAMnP,WAAoC,4BAAmF,QAAnFiP,EAA4BE,EAAM3P,WAAWnB,MAAKC,GAAKA,EAAEC,KAAO4Q,EAAMzP,sBAAa,IAAAuP,OAAA,EAAvDA,EAAyD/R,SACrJ,oBACDG,UAEDI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAeiB,SAAA,EAC5BT,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACLM,YAAY,oCACZpK,MAAOgS,EAAMvP,UACbgG,SAAW7H,GAAMoR,EAAMtP,aAAa9B,EAAE+H,OAAO3I,MAAM6P,cAAc5K,QAAQ,OAAQ,MACjFhG,UAAU,uBAEZQ,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACLM,YAAY,eACZpK,MAAOgS,EAAMrP,aACb8F,SAAW7H,GAAMoR,EAAMpP,gBAAgBhC,EAAE+H,OAAO3I,OAChDf,UAAU,uBAEZqB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEkB,QAASuS,EAAetO,oBACxBgE,SAAUoJ,EAAMrK,YAAcqK,EAAMvP,YAAcuP,EAAMrP,aACxD1D,UAAU,kBAAiBiB,SAE1B8R,EAAMrK,UAAY,cAAgB,6BAErClI,EAAAA,EAAAA,KAAA,UAAQkB,QAASA,KACfqR,EAAM7O,oBAAmB,GACzB6O,EAAMlP,cAAc,IACpBkP,EAAMtP,aAAa,IACnBsP,EAAMpP,gBAAgB,GAAG,EACxB3D,UAAU,oBAAmBiB,SAAC,mBASxC8R,EAAMzP,eACLjC,EAAAA,EAAAA,MAAAwI,EAAAA,SAAA,CAAA5I,SAAA,GAE0B,kBAArB8R,EAAMnP,YAAkCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,aAAe2P,EAAMxG,cACxJ/L,EAAAA,EAAAA,KAACyX,EAAc,CACb3U,aAAcyP,EAAMzP,aACpBF,WAAY2P,EAAM3P,WAClB0G,WAAYmK,EAAerM,eAC3BmC,aAAckK,EAAejM,oBAKX,kBAArB+K,EAAMnP,YAAkCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,cACjI5C,EAAAA,EAAAA,KAAC0X,EAAmB,CAClBrJ,gBAAiBkE,EAAMlE,gBACvBC,kBAAmBiE,EAAMjE,kBACzBC,gBAAiBgE,EAAMhE,gBACvBC,kBAAmB+D,EAAM/D,kBACzBC,gBArIeI,IAC3B,MAAM8I,EAAkBpF,EAAMlE,gBAC9BkE,EAAMc,mBAAmBxE,GAIrB+H,KAAKI,UAAUnI,KAAa+H,KAAKI,UAAUW,KAC7CpF,EAAMe,qBAAqB,IAC3Bf,EAAMgB,mBAAmB,IAC3B,EA6HY7E,kBA1HiBI,IAC7B,MAAM8I,EAAoBrF,EAAMjE,kBAChCiE,EAAMe,qBAAqBxE,GAIvB8H,KAAKI,UAAUlI,KAAe8H,KAAKI,UAAUY,IAC/CrF,EAAMgB,mBAAmB,GAC3B,EAmHY5E,gBAhHeI,IAC3BwD,EAAMgB,mBAAmBxE,EAAQ,EAgHrBH,kBA7GiBsD,IAC7BK,EAAMiB,qBAAqBtB,EAAU,IAiHP,kBAArBK,EAAMnP,YAAkCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,aAAe2P,EAAMxG,aACtJ/L,EAAAA,EAAAA,KAAC6X,EAAkB,CACjB9L,WAAYwG,EAAMxG,WAClB/E,OAAQuL,EAAMvL,OACdgF,WAAY0H,EAAkBiB,SAC9B1I,cAAeyH,EAAkByB,YACjCjJ,cAAewH,EAAkB4B,YACjCnJ,OAAQuH,EAAkB6B,WAC1BnJ,UAAWsH,EAAkBwC,cAC7B7J,QAASkG,EAAMlG,UAKG,kBAArBkG,EAAMnP,cAAoCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,aAAevB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,eAClI5C,EAAAA,EAAAA,KAAA,OAAKR,UAAU,wDAAuDiB,SAAC,iLAInD,kBAArB8R,EAAMnP,aAAmCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAC7E5C,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kDAAiDiB,SAAC,6IAOrE8R,EAAMzP,cAAqC,KAArByP,EAAMnP,aAC5BvC,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gDAA+CiB,SAAA,EAC5DT,EAAAA,EAAAA,KAAA,KAAAS,SAAG,+FACHT,EAAAA,EAAAA,KAAA,KAAAS,SAAG,wJAKN8R,EAAMO,eAAiBP,EAAMjL,cAC5BzG,EAAAA,EAAAA,MAAC2W,EAAK,CACJxW,OAAQuR,EAAMO,cACd7R,QAASA,KACPsR,EAAMzO,kBAAiB,GACvByO,EAAMpP,gBAAgB,IACtBoP,EAAM1O,eAAe,KAAK,EAE5BvD,MAAO,gBAAgBiS,EAAMjL,YAAYhH,QAAQG,SAAA,EAEjDT,EAAAA,EAAAA,KAAA,SACEqK,KAAK,OACL9J,MAAOgS,EAAMrP,aACb8F,SAAW7H,GAAMoR,EAAMpP,gBAAgBhC,EAAE+H,OAAO3I,OAChDoK,YAAY,mBACZnL,UAAU,uBAEZqB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEkB,QAASuS,EAAepM,iBACxB7H,UAAU,kBACV2J,SAAUoJ,EAAMrK,YAAcqK,EAAMrP,aAAayF,OAAOlI,SAEvD8R,EAAMrK,UAAY,cAAgB,kBAErClI,EAAAA,EAAAA,KAAA,UACEkB,QAASA,KACPqR,EAAMzO,kBAAiB,GACvByO,EAAMpP,gBAAgB,IACtBoP,EAAM1O,eAAe,KAAK,EAE5BrE,UAAU,oBAAmBiB,SAC9B,iBAON8R,EAAMS,wBAA0BT,EAAMQ,eACrClS,EAAAA,EAAAA,MAAC2W,EAAK,CACJxW,OAAQuR,EAAMS,uBACd/R,QAASA,IAAMsR,EAAMvO,2BAA0B,GAC/C1D,MAAM,mBAAkBG,SAAA,EAExBI,EAAAA,EAAAA,MAAA,KAAAJ,SAAA,CAAG,+CAAoG,QAAxD6R,EAACC,EAAM3P,WAAWnB,MAAKC,GAAKA,EAAEC,KAAO4Q,EAAMQ,sBAAa,IAAAT,OAAA,EAAvDA,EAAyDhS,MAAM,qGAC/GO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEkB,QAASuS,EAAehM,oBACxBjI,UAAU,iBACV2J,SAAUoJ,EAAMrK,UAAUzH,SAEzB8R,EAAMrK,UAAY,cAAgB,oBAErClI,EAAAA,EAAAA,KAAA,UACEkB,QAASA,IAAMqR,EAAMvO,2BAA0B,GAC/CxE,UAAU,oBAAmBiB,SAC9B,kBAQPT,EAAAA,EAAAA,KAACwX,EAAK,CACJxW,OAAQuR,EAAMU,cACdhS,QAASA,IAAMsR,EAAMW,kBAAiB,GACtC5S,MAAM,eAAcG,UAEpBT,EAAAA,EAAAA,KAAA,OAAK8X,wBAAyB,CAAEC,OAAQxF,EAAMY,wBAGjD,E,2MElaA,SAAS6E,EAAwBrZ,GACtC,OAAOC,EAAAA,EAAAA,IAAqB,cAAeD,EAC7C,EACwBE,EAAAA,EAAAA,GAAuB,cAAe,CAAC,OAAQ,YAAa,QAAS,sBAAuB,UAAW,UAAW,UAAW,oBCArJ,MACA,GAD8BA,EAAAA,EAAAA,GAAuB,oBAAqB,CAAC,OAAQ,eAAgB,QAAS,sBAAuB,WAAY,UAAW,UAAW,aCH9J,SAASoZ,EAA8CtZ,GAC5D,OAAOC,EAAAA,EAAAA,IAAqB,6BAA8BD,EAC5D,EACuCE,EAAAA,EAAAA,GAAuB,6BAA8B,CAAC,OAAQ,mBAArG,MCgBMqZ,GAA8BnZ,EAAAA,EAAAA,IAAO,MAAO,CAChDE,KAAM,6BACNN,KAAM,OACNwZ,kBAAmBA,CAAC7Y,EAAO8Y,KACzB,MAAM,WACJzY,GACEL,EACJ,MAAO,CAAC8Y,EAAOtY,KAAMH,EAAW0Y,gBAAkBD,EAAOC,eAAe,GAPxCtZ,CASjC,CACDuZ,SAAU,WACVC,MAAO,GACPC,IAAK,MACLC,UAAW,mBACXC,SAAU,CAAC,CACTpZ,MAAOyB,IAAA,IAAC,WACNpB,GACDoB,EAAA,OAAKpB,EAAW0Y,cAAc,EAC/BzP,MAAO,CACL2P,MAAO,OAUPI,EAAuCxZ,EAAAA,YAAiB,SAAiCC,EAASC,GACtG,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,gCAEF,UACJO,KACGE,GACDJ,EACEsZ,EAAUzZ,EAAAA,WAAiB0Z,EAAAA,GAC3BlZ,EAAa,IACdL,EACH+Y,eAAgBO,EAAQP,gBAEpBzY,EArDkBD,KACxB,MAAM,eACJ0Y,EAAc,QACdzY,GACED,EACEmZ,EAAQ,CACZhZ,KAAM,CAAC,OAAQuY,GAAkB,mBAEnC,OAAOxY,EAAAA,EAAAA,GAAeiZ,EAAOb,EAA+CrY,EAAQ,EA6CpEG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKkY,EAA6B,CACpD1Y,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BG,WAAYA,EACZN,IAAKA,KACFK,GAEP,IAuBAiZ,EAAwBI,QAAU,0BAClC,WCtDaC,IAAeja,EAAAA,EAAAA,IAAO,MAAO,CACxCE,KAAM,cACNN,KAAM,OACNwZ,kBAzB+BA,CAAC7Y,EAAO8Y,KACvC,MAAM,WACJzY,GACEL,EACJ,MAAO,CAAC8Y,EAAOtY,KAAMH,EAAWsZ,OAASb,EAAOa,MAAiC,eAA1BtZ,EAAWuZ,YAA+Bd,EAAOe,oBAAqBxZ,EAAWyZ,SAAWhB,EAAOgB,SAAUzZ,EAAW0Y,gBAAkBD,EAAOiB,SAAU1Z,EAAW2Z,gBAAkBlB,EAAOmB,QAAS5Z,EAAW6Z,oBAAsBpB,EAAOqB,gBAAgB,GAkB7R1a,EAIzB2a,EAAAA,EAAAA,IAAU3Y,IAAA,IAAC,MACZ4Y,GACD5Y,EAAA,MAAM,CACL6Y,QAAS,OACTC,eAAgB,aAChBX,WAAY,SACZZ,SAAU,WACVwB,eAAgB,OAChBC,MAAO,OACPC,UAAW,aACXC,UAAW,OACXvB,SAAU,CAAC,CACTpZ,MAAO4a,IAAA,IAAC,WACNva,GACDua,EAAA,OAAMva,EAAW2Z,cAAc,EAChC1Q,MAAO,CACLuR,WAAY,EACZC,cAAe,IAEhB,CACD9a,MAAO+a,IAAA,IAAC,WACN1a,GACD0a,EAAA,OAAM1a,EAAW2Z,gBAAkB3Z,EAAWsZ,KAAK,EACpDrQ,MAAO,CACLuR,WAAY,EACZC,cAAe,IAEhB,CACD9a,MAAOgb,IAAA,IAAC,WACN3a,GACD2a,EAAA,OAAM3a,EAAW2Z,iBAAmB3Z,EAAW0Y,cAAc,EAC9DzP,MAAO,CACLC,YAAa,GACb0R,aAAc,KAEf,CACDjb,MAAOkb,IAAA,IAAC,WACN7a,GACD6a,EAAA,OAAM7a,EAAW2Z,kBAAoB3Z,EAAW8Z,eAAe,EAChE7Q,MAAO,CAGL2R,aAAc,KAEf,CACDjb,MAAOmb,IAAA,IAAC,WACN9a,GACD8a,EAAA,QAAO9a,EAAW8Z,eAAe,EAClC7Q,MAAO,CACL,CAAC,QAAQ8R,EAAsB5a,QAAS,CACtCya,aAAc,MAGjB,CACDjb,MAAO,CACL4Z,WAAY,cAEdtQ,MAAO,CACLsQ,WAAY,eAEb,CACD5Z,MAAOqb,IAAA,IAAC,WACNhb,GACDgb,EAAA,OAAKhb,EAAWyZ,OAAO,EACxBxQ,MAAO,CACLgS,aAAc,cAAcjB,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,UACzD2B,eAAgB,gBAEjB,CACDzb,MAAO0b,IAAA,IAAC,WACNrb,GACDqb,EAAA,OAAKrb,EAAWsb,MAAM,EACvBrS,MAAO,CACLsS,WAAYvB,EAAMwB,YAAYC,OAAO,mBAAoB,CACvDC,SAAU1B,EAAMwB,YAAYE,SAASC,WAEvC,UAAW,CACTxB,eAAgB,OAChBrM,iBAAkBkM,EAAMkB,MAAQlB,GAAOmB,QAAQzD,OAAOkE,MAEtD,uBAAwB,CACtB9N,gBAAiB,kBAItB,CACDnO,MAAOkc,IAAA,IAAC,WACN7b,GACD6b,EAAA,OAAK7b,EAAW6Z,kBAAkB,EACnC5Q,MAAO,CAGL2R,aAAc,MAGnB,KACKkB,IAAoB1c,EAAAA,EAAAA,IAAO,KAAM,CACrCE,KAAM,cACNN,KAAM,aAFkBI,CAGvB,CACDuZ,SAAU,aAiPZ,GA3O8BnZ,EAAAA,YAAiB,SAAkBC,EAASC,GACxE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,iBAEF,WACJia,EAAa,SACbzY,SAAUib,EAAY,UACtBlc,EACAmc,UAAWC,EAAa,WACxBC,EAAa,CAAC,EAAC,gBACfC,EAAkB,CAAC,EAAC,mBACpBC,EAAqB,KACrBC,gBACExc,UAAWyc,KACRD,GACD,CAAC,EAAC,MACN/C,GAAQ,EAAK,eACbZ,GAAiB,EAAK,eACtBiB,GAAiB,EAAK,QACtBF,GAAU,EAAK,gBACfK,EAAe,UACfyC,EAAY,CAAC,EAAC,MACdpD,EAAQ,CAAC,KACNpZ,GACDJ,EACEsZ,EAAUzZ,EAAAA,WAAiB0Z,EAAAA,GAC3BsD,EAAehd,EAAAA,SAAc,KAAM,CACvC8Z,MAAOA,GAASL,EAAQK,QAAS,EACjCC,aACAb,oBACE,CAACa,EAAYN,EAAQK,MAAOA,EAAOZ,IACjC+D,EAAcjd,EAAAA,OAAa,MAC3BsB,EAAWtB,EAAAA,SAAekd,QAAQX,GAGlClC,EAAqB/Y,EAASiG,SAAU4V,EAAAA,EAAAA,GAAa7b,EAASA,EAASiG,OAAS,GAAI,CAAC,4BACrF/G,EAAa,IACdL,EACH4Z,aACAD,MAAOkD,EAAalD,MACpBZ,iBACAiB,iBACAF,UACAI,sBAEI5Z,EA5KkBD,KACxB,MAAM,WACJuZ,EAAU,QACVtZ,EAAO,MACPqZ,EAAK,eACLZ,EAAc,eACdiB,EAAc,QACdF,EAAO,mBACPI,GACE7Z,EACEmZ,EAAQ,CACZhZ,KAAM,CAAC,OAAQmZ,GAAS,SAAUZ,GAAkB,WAAYiB,GAAkB,UAAWF,GAAW,UAA0B,eAAfF,GAA+B,sBAAuBM,GAAsB,mBAC/L+C,UAAW,CAAC,cAEd,OAAO1c,EAAAA,EAAAA,GAAeiZ,EAAOd,EAAyBpY,EAAQ,EA8J9CG,CAAkBJ,GAC5B6c,GAAYC,EAAAA,EAAAA,GAAWL,EAAa/c,GACpCqd,EAAO5D,EAAMhZ,MAAQ+b,EAAWa,MAAQ1D,GACxC2D,EAAYT,EAAUpc,MAAQgc,EAAgBhc,MAAQ,CAAC,EACvD8c,EAAiB,CACrBpd,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAM6c,EAAUnd,UAAWA,MAChDE,GAEL,IAAImd,EAAYjB,GAAiB,KAGjC,OAAIpC,GAEFqD,EAAaD,EAAejB,WAAcC,EAAwBiB,EAAR,MAG/B,OAAvBd,IACgB,OAAdc,EACFA,EAAY,MAC0B,OAA7BD,EAAejB,YACxBiB,EAAejB,UAAY,SAGX3b,EAAAA,EAAAA,KAAK6Y,EAAAA,EAAYiE,SAAU,CAC7Cvc,MAAO4b,EACP1b,UAAuBI,EAAAA,EAAAA,MAAM4a,GAAmB,CAC9CsB,GAAIhB,EACJvc,WAAWS,EAAAA,EAAAA,GAAKL,EAAQ2c,UAAWN,GACnC5c,IAAKmd,EACL7c,WAAYA,KACTqc,EACHvb,SAAU,EAAcT,EAAAA,EAAAA,KAAK0c,EAAM,IAC9BC,OACEK,EAAAA,EAAAA,GAAgBN,IAAS,CAC5BK,GAAIF,EACJld,WAAY,IACPA,KACAgd,EAAUhd,gBAGdid,EACHnc,SAAUA,IACRA,EAASwc,aAICjd,EAAAA,EAAAA,KAAK6Y,EAAAA,EAAYiE,SAAU,CAC7Cvc,MAAO4b,EACP1b,UAAuBI,EAAAA,EAAAA,MAAM6b,EAAM,IAC9BC,EACHI,GAAIF,EACJxd,IAAKmd,OACAQ,EAAAA,EAAAA,GAAgBN,IAAS,CAC5B/c,WAAY,IACPA,KACAgd,EAAUhd,gBAGdid,EACHnc,SAAU,CAACA,EAAUgZ,IAAgCzZ,EAAAA,EAAAA,KAAK2Y,GAAyB,CACjFlY,SAAUgZ,QAIlB,I,qCCxPA,MAeMyD,IAAmBne,EAAAA,EAAAA,IAAO,MAAO,CACrCE,KAAM,kBACNN,KAAM,OACNwZ,kBAAmBA,CAAC7Y,EAAO8Y,KACzB,MAAM,WACJzY,GACEL,EACJ,MAAO,CAAC,CACN,CAAC,MAAM6d,GAAAA,EAAoBC,WAAYhF,EAAOgF,SAC7C,CACD,CAAC,MAAMD,GAAAA,EAAoBE,aAAcjF,EAAOiF,WAC/CjF,EAAOtY,KAAMH,EAAW2d,OAASlF,EAAOkF,MAAO3d,EAAWyd,SAAWzd,EAAW0d,WAAajF,EAAOmF,UAAW5d,EAAWsZ,OAASb,EAAOa,MAAM,GAX9Hla,CAatB,CACDye,KAAM,WACNC,SAAU,EACVC,UAAW,EACXC,aAAc,EACd,CAAC,IAAIC,GAAAA,EAAkB9d,iBAAiBqd,GAAAA,EAAoBC,YAAa,CACvExD,QAAS,SAEX,CAAC,IAAIgE,GAAAA,EAAkB9d,iBAAiBqd,GAAAA,EAAoBE,cAAe,CACzEzD,QAAS,SAEXlB,SAAU,CAAC,CACTpZ,MAAOyB,IAAA,IAAC,WACNpB,GACDoB,EAAA,OAAKpB,EAAWyd,SAAWzd,EAAW0d,SAAS,EAChDzU,MAAO,CACL8U,UAAW,EACXC,aAAc,IAEf,CACDre,MAAO4a,IAAA,IAAC,WACNva,GACDua,EAAA,OAAKva,EAAW2d,KAAK,EACtB1U,MAAO,CACLC,YAAa,QAiKnB,GA7JkC1J,EAAAA,YAAiB,SAAsBC,EAASC,GAChF,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,qBAEF,SACJwB,EAAQ,UACRjB,EAAS,kBACTqe,GAAoB,EAAK,MACzBP,GAAQ,EACRF,QAASU,EAAW,uBACpBC,EACAV,UAAWW,EAAa,yBACxBC,EAAwB,MACxBnF,EAAQ,CAAC,EAAC,UACVoD,EAAY,CAAC,KACVxc,GACDJ,GACE,MACJ2Z,GACE9Z,EAAAA,WAAiB0Z,EAAAA,GACrB,IAAIuE,EAAyB,MAAfU,EAAsBA,EAAcrd,EAC9C4c,EAAYW,EAChB,MAAMre,EAAa,IACdL,EACHue,oBACAP,QACAF,UAAWA,EACXC,YAAaA,EACbpE,SAEIrZ,EAvFkBD,KACxB,MAAM,QACJC,EAAO,MACP0d,EAAK,QACLF,EAAO,UACPC,EAAS,MACTpE,GACEtZ,EACEmZ,EAAQ,CACZhZ,KAAM,CAAC,OAAQwd,GAAS,QAASrE,GAAS,QAASmE,GAAWC,GAAa,aAC3ED,QAAS,CAAC,WACVC,UAAW,CAAC,cAEd,OAAOxd,EAAAA,EAAAA,GAAeiZ,EAAO1Y,GAAAA,EAA6BR,EAAQ,EA0ElDG,CAAkBJ,GAC5Bue,EAAyB,CAC7BpF,QACAoD,UAAW,CACTkB,QAASW,EACTV,UAAWY,KACR/B,KAGAiC,EAAUC,IAAiBC,EAAAA,GAAAA,GAAQ,OAAQ,CAChD7e,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9B8e,YAAapB,GACbgB,uBAAwB,IACnBA,KACAxe,GAELC,aACAN,SAEKkf,EAAaC,IAAoBH,EAAAA,GAAAA,GAAQ,UAAW,CACzD7e,UAAWI,EAAQwd,QACnBkB,YAAaG,EAAAA,EACbP,yBACAve,gBAEK+e,EAAeC,IAAsBN,EAAAA,GAAAA,GAAQ,YAAa,CAC/D7e,UAAWI,EAAQyd,UACnBiB,YAAaG,EAAAA,EACbP,yBACAve,eAkBF,OAhBe,MAAXyd,GAAmBA,EAAQ/S,OAASoU,EAAAA,GAAeZ,IACrDT,GAAuBpd,EAAAA,EAAAA,KAAKue,EAAa,CACvCK,QAAS3F,EAAQ,QAAU,QAC3B0C,UAAW6C,GAAkBI,aAAUze,EAAY,UAChDqe,EACH/d,SAAU2c,KAGG,MAAbC,GAAqBA,EAAUhT,OAASoU,EAAAA,GAAeZ,IACzDR,GAAyBrd,EAAAA,EAAAA,KAAK0e,EAAe,CAC3CE,QAAS,QACThZ,MAAO,mBACJ+Y,EACHle,SAAU4c,MAGMxc,EAAAA,EAAAA,MAAMsd,EAAU,IAC/BC,EACH3d,SAAU,CAAC2c,EAASC,IAExB,I,0BC3IA,MAiBMwB,IAAc9f,EAAAA,EAAAA,IAAO,MAAO,CAChCE,KAAM,aACNN,KAAM,OACNwZ,kBAAmBA,CAAC7Y,EAAO8Y,KACzB,MAAM,WACJzY,GACEL,EACJ,MAAO,CAAC8Y,EAAOtY,KAAMH,EAAWmf,UAAY1G,EAAO0G,SAAU1G,EAAOzY,EAAWif,SAAUjf,EAAWof,OAAS3G,EAAO2G,MAAkC,aAA3Bpf,EAAWqf,aAA8B5G,EAAO6G,SAAUtf,EAAWuf,UAAY9G,EAAO8G,SAAUvf,EAAWc,UAAY2X,EAAO+G,aAAcxf,EAAWc,UAAuC,aAA3Bd,EAAWqf,aAA8B5G,EAAOgH,qBAA+C,UAAzBzf,EAAWsa,WAAoD,aAA3Bta,EAAWqf,aAA8B5G,EAAOiH,eAAyC,SAAzB1f,EAAWsa,WAAmD,aAA3Bta,EAAWqf,aAA8B5G,EAAOkH,cAAc,GAP3hBvgB,EASjB2a,EAAAA,EAAAA,IAAU3Y,IAAA,IAAC,MACZ4Y,GACD5Y,EAAA,MAAM,CACLwe,OAAQ,EAERC,WAAY,EACZC,YAAa,EACbC,YAAa,QACbhS,aAAciM,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,QAC3CuG,kBAAmB,OACnBjH,SAAU,CAAC,CACTpZ,MAAO,CACLwf,UAAU,GAEZlW,MAAO,CACL0P,SAAU,WACVsH,OAAQ,EACRC,KAAM,EACN9F,MAAO,SAER,CACDza,MAAO,CACLyf,OAAO,GAETnW,MAAO,CACL8E,YAAaiM,EAAMkB,KAAO,QAAQlB,EAAMkB,KAAKC,QAAQgF,0BAA2BC,EAAAA,GAAAA,IAAMpG,EAAMmB,QAAQ1B,QAAS,OAE9G,CACD9Z,MAAO,CACLsf,QAAS,SAEXhW,MAAO,CACLoX,WAAY,KAEb,CACD1gB,MAAO,CACLsf,QAAS,SACTI,YAAa,cAEfpW,MAAO,CACLoX,WAAYrG,EAAMsG,QAAQ,GAC1BC,YAAavG,EAAMsG,QAAQ,KAE5B,CACD3gB,MAAO,CACLsf,QAAS,SACTI,YAAa,YAEfpW,MAAO,CACL8U,UAAW/D,EAAMsG,QAAQ,GACzBtC,aAAchE,EAAMsG,QAAQ,KAE7B,CACD3gB,MAAO,CACL0f,YAAa,YAEfpW,MAAO,CACLuX,OAAQ,OACRR,kBAAmB,EACnBS,iBAAkB,SAEnB,CACD9gB,MAAO,CACL4f,UAAU,GAEZtW,MAAO,CACLyX,UAAW,UACXF,OAAQ,SAET,CACD7gB,MAAO4a,IAAA,IAAC,WACNva,GACDua,EAAA,QAAOva,EAAWc,QAAQ,EAC3BmI,MAAO,CACLgR,QAAS,OACTK,UAAW,SACXqG,OAAQ,EACRC,eAAgB,QAChBC,gBAAiB,QACjB,sBAAuB,CACrBC,QAAS,KACTJ,UAAW,YAGd,CACD/gB,MAAO+a,IAAA,IAAC,WACN1a,GACD0a,EAAA,OAAK1a,EAAWc,UAAuC,aAA3Bd,EAAWqf,WAA0B,EAClEpW,MAAO,CACL,sBAAuB,CACrBmR,MAAO,OACP2G,UAAW,eAAe/G,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,UACvDmH,eAAgB,aAGnB,CACDjhB,MAAOgb,IAAA,IAAC,WACN3a,GACD2a,EAAA,MAAgC,aAA3B3a,EAAWqf,aAA8Brf,EAAWc,QAAQ,EAClEmI,MAAO,CACL+X,cAAe,SACf,sBAAuB,CACrBR,OAAQ,OACRS,WAAY,eAAejH,EAAMkB,MAAQlB,GAAOmB,QAAQ1B,UACxDoH,gBAAiB,aAGpB,CACDlhB,MAAOkb,IAAA,IAAC,WACN7a,GACD6a,EAAA,MAA8B,UAAzB7a,EAAWsa,WAAoD,aAA3Bta,EAAWqf,WAA0B,EAC/EpW,MAAO,CACL,YAAa,CACXmR,MAAO,OAET,WAAY,CACVA,MAAO,SAGV,CACDza,MAAOmb,IAAA,IAAC,WACN9a,GACD8a,EAAA,MAA8B,SAAzB9a,EAAWsa,WAAmD,aAA3Bta,EAAWqf,WAA0B,EAC9EpW,MAAO,CACL,YAAa,CACXmR,MAAO,OAET,WAAY,CACVA,MAAO,UAId,KACK8G,IAAiB9hB,EAAAA,EAAAA,IAAO,OAAQ,CACpCE,KAAM,aACNN,KAAM,UACNwZ,kBAAmBA,CAAC7Y,EAAO8Y,KACzB,MAAM,WACJzY,GACEL,EACJ,MAAO,CAAC8Y,EAAO0I,QAAoC,aAA3BnhB,EAAWqf,aAA8B5G,EAAO2I,gBAAgB,GAPrEhiB,EASpB2a,EAAAA,EAAAA,IAAUiB,IAAA,IAAC,MACZhB,GACDgB,EAAA,MAAM,CACLf,QAAS,eACT/Q,YAAa,QAAQ8Q,EAAMsG,QAAQ,YACnC1F,aAAc,QAAQZ,EAAMsG,QAAQ,YACpCe,WAAY,SACZtI,SAAU,CAAC,CACTpZ,MAAO,CACL0f,YAAa,YAEfpW,MAAO,CACLuR,WAAY,QAAQR,EAAMsG,QAAQ,YAClC7F,cAAe,QAAQT,EAAMsG,QAAQ,eAG1C,KACKgB,GAAuB9hB,EAAAA,YAAiB,SAAiBC,EAASC,GACtE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,gBAEF,SACJ6f,GAAW,EAAK,SAChBre,EAAQ,UACRjB,EAAS,YACTwf,EAAc,aAAY,UAC1BrD,GAAYlb,GAA4B,aAAhBue,EAA6B,MAAQ,MAAI,SACjEE,GAAW,EAAK,MAChBH,GAAQ,EAAK,KACb/M,GAAqB,OAAd2J,EAAqB,iBAAcxb,GAAS,UACnD8Z,EAAY,SAAQ,QACpB2E,EAAU,eACPlf,GACDJ,EACEK,EAAa,IACdL,EACHwf,WACAnD,YACAuD,WACAH,QACAC,cACAhN,OACAiI,YACA2E,WAEIhf,EAtNkBD,KACxB,MAAM,SACJmf,EAAQ,SACRre,EAAQ,QACRb,EAAO,SACPsf,EAAQ,MACRH,EAAK,YACLC,EAAW,UACX/E,EAAS,QACT2E,GACEjf,EACEmZ,EAAQ,CACZhZ,KAAM,CAAC,OAAQgf,GAAY,WAAYF,EAASG,GAAS,QAAyB,aAAhBC,GAA8B,WAAYE,GAAY,WAAYze,GAAY,eAAgBA,GAA4B,aAAhBue,GAA8B,uBAAsC,UAAd/E,GAAyC,aAAhB+E,GAA8B,iBAAgC,SAAd/E,GAAwC,aAAhB+E,GAA8B,iBACjW8B,QAAS,CAAC,UAA2B,aAAhB9B,GAA8B,oBAErD,OAAOnf,EAAAA,EAAAA,GAAeiZ,EAAOhY,GAAAA,EAAwBlB,EAAQ,EAuM7CG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAK6e,GAAa,CACpC9B,GAAIpB,EACJnc,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BwS,KAAMA,EACN3S,IAAKA,EACLM,WAAYA,EACZ,mBAA6B,cAATqS,GAAuC,OAAd2J,GAAsC,aAAhBqD,OAA4C7e,EAAd6e,KAC9Ftf,EACHe,SAAUA,GAAwBT,EAAAA,EAAAA,KAAK6gB,GAAgB,CACrDrhB,UAAWI,EAAQkhB,QACnBnhB,WAAYA,EACZc,SAAUA,IACP,MAET,IAMIwgB,KACFA,GAAQC,sBAAuB,GAiEjC,YCtSaC,GAAwBA,KACnC,MAAOtS,EAASK,IAActC,EAAAA,EAAAA,UAAmB,KAC1CkC,EAAWK,IAAgBvC,EAAAA,EAAAA,UAAqB,KAChDmC,EAASK,IAAcxC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAkB,IACzC/H,EAAOtB,IAAYqJ,EAAAA,EAAAA,UAAwB,OAC3CwU,EAAcC,IAAmBzU,EAAAA,EAAAA,UAAiB,IAClD0U,EAAUC,IAAe3U,EAAAA,EAAAA,UAAiB,IAE3C0C,EAAkBnL,UACtB,IACEkL,GAAW,GACX9L,EAAS,MAETqB,QAAQ2K,IAAI,mFAGZ,MAAMiS,QAAsB/R,EAAAA,EAAcC,qBAK1C,GAHA9K,QAAQ2K,IAAI,wDAAoDiS,EAAc9a,OAAQ,WACtF2a,EAAgBG,EAAc9a,QAED,IAAzB8a,EAAc9a,OAMhB,OALA9B,QAAQ2K,IAAI,+DACZL,EAAW,IACXC,EAAa,IACbC,EAAW,SACXmS,EAAY,WAKd,MAAME,EAAgB,IAAIC,IAC1BF,EAActf,SAAQyP,IAChBA,EAAO9B,QAAU8B,EAAO9B,OAAOlH,QACjC8Y,EAAcE,IAAIhQ,EAAO9B,OAAOlH,OAClC,IAGF,MAAMuH,EAAyBzE,MAAMmW,KAAKH,GACvCxR,OACAvP,KAAIyP,IAAU,CACbxO,GAAIwO,EAAWC,cAAc5K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzEvG,KAAMkR,MAGVvL,QAAQ2K,IAAI,yDAAgDW,EAAaxJ,QAGzE,MAAMmb,EAAkB,IAAIC,IAC5BN,EAActf,SAAQyP,IAChBA,EAAOpB,UAAYoB,EAAOpB,SAAS5H,QAAUgJ,EAAO9B,QAAU8B,EAAO9B,OAAOlH,QAC9EkZ,EAAgBE,IAAIpQ,EAAOpB,SAAS5H,OAAQgJ,EAAO9B,OAAOlH,OAC5D,IAGF,MAAMkI,EAA6BpF,MAAMmW,KAAKC,EAAgBG,WAC3D/R,MAAK,CAAAlP,EAAAmZ,KAAA,IAAExJ,GAAE3P,GAAG4P,GAAEuJ,EAAA,OAAKxJ,EAAEE,cAAcD,EAAE,IACrCjQ,KAAI2Z,IAAA,IAAE4H,EAAc9R,GAAWkK,EAAA,MAAM,CACpC1Y,GAAIsgB,EAAa7R,cAAc5K,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC3EvG,KAAMgjB,EACNnS,OAAQK,EACT,IAEHvL,QAAQ2K,IAAI,2DAAkDsB,EAAenK,QAG7E,MAAMoK,EAAyB0Q,EAC5Bhf,QAAOmP,GAAUA,EAAO,gBAAkBA,EAAO,eAAehJ,SAChEjI,KAAIiR,IAAM,CACThQ,GAAIgQ,EAAO,eACX1S,KAAM0S,EAAO,eACb7B,OAAQ6B,EAAO9B,QAAU,GACzBS,SAAUqB,EAAOpB,UAAY,GAC7BQ,WAAYY,EAAO,mBAGvB/M,QAAQ2K,IAAI,yDAAgDuB,EAAapK,QA8C/E,SACE8a,EACA3S,EACAC,EACAC,GAQA,GANAnK,QAAQ2K,IAAI,wEACZ3K,QAAQ2K,IAAI,oDAA0CiS,EAAc9a,UACpE9B,QAAQ2K,IAAI,0DAAgDV,EAAQnI,UACpE9B,QAAQ2K,IAAI,4DAAkDT,EAAUpI,UACxE9B,QAAQ2K,IAAI,0DAAgDR,EAAQrI,UAEhEqI,EAAQrI,OAAS,EAAG,CAEtB,MAAMwb,EAAcnT,EAAQrO,KAAIqR,GAAKA,EAAE9S,OAAMgR,OAC7CrL,QAAQ2K,IAAI,8DAAoD2S,EAAY,OAC5Etd,QAAQ2K,IAAI,6DAAmD2S,EAAYA,EAAYxb,OAAS,OAGhG,MAAMyb,EAA0C,CAAC,EACjDpT,EAAQ7M,SAAQyP,IACd,MAAMyQ,EAAczQ,EAAO1S,KAAKojB,OAAO,GAAGC,cAC1CH,EAAaC,IAAgBD,EAAaC,IAAgB,GAAK,CAAC,IAGlExd,QAAQ2K,IAAI,4DACZgT,OAAOC,KAAKL,GAAclS,OAAO/N,SAAQugB,IACvC7d,QAAQ2K,IAAI,uCAA6BkT,MAAWN,EAAaM,aAAkB,IAIrF,MAAMC,EAAkB3T,EAAQtN,MAAKsQ,GAAKA,EAAE9S,KAAKmR,cAAcvF,SAAS,sBAClE8X,EAAqB5T,EAAQtN,MAAKsQ,GAAKA,EAAE9S,KAAKmR,cAAcvF,SAAS,yBAsB3E,GApBAjG,QAAQ2K,IAAI,sEAA4DmT,KACxE9d,QAAQ2K,IAAI,yEAA+DoT,KAEvED,GACF9d,QAAQ2K,IAAI,gEAAsDmT,EAAgBzjB,SAEhF0jB,GACF/d,QAAQ2K,IAAI,mEAAyDoT,EAAmB1jB,SAItF4P,EAAQnI,OAAS,IACnB9B,QAAQ2K,IAAI,sDACZV,EAAQ3M,SAAQ4N,IACd,MAAM8S,EAAgB7T,EAAQvM,QAAOuP,GAAKA,EAAEjC,SAAWA,EAAO7Q,OAC9D2F,QAAQ2K,IAAI,uCAA6BO,EAAO7Q,SAAS2jB,EAAclc,iBAAiB,KAKxFoI,EAAUpI,OAAS,EAAG,CACxB9B,QAAQ2K,IAAI,yEACWT,EAAUpO,KAAI4P,IAAQ,CAC3CrR,KAAMqR,EAASrR,KACf4jB,MAAO9T,EAAQvM,QAAOuP,GAAKA,EAAEzB,WAAaA,EAASrR,OAAMyH,WACvDuJ,MAAK,CAACS,EAAGC,IAAMA,EAAEkS,MAAQnS,EAAEmS,QAAOC,MAAM,EAAG,IAEhC5gB,SAAQoO,IACrB1L,QAAQ2K,IAAI,uCAA6Be,EAASrR,SAASqR,EAASuS,gBAAgB,GAExF,CACF,CAEAje,QAAQ2K,IAAI,6DACd,CA/GMwT,CAAoBvB,EAAetR,EAAcW,EAAgBC,GAGjE5B,EAAWgB,GACXf,EAAa0B,GACbzB,EAAW0B,GACXyQ,EAAY,uBAEZ3c,QAAQ2K,IAAI,yDAEd,CAAE,MAAO5K,GACPC,QAAQC,MAAM,uCAAmCF,GACjDpB,EAAS,iDACT2L,EAAW,IACXC,EAAa,IACbC,EAAW,IACXiS,EAAgB,GAChBE,EAAY,QACd,CAAC,QACClS,GAAW,EACb,GAQF,OAJAtC,EAAAA,EAAAA,YAAU,KACRuC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA1C,UACAxH,QACAmK,QAASM,EACT8R,eACAE,WACD,EA4EH,MCwFA,GAvRoC0B,KAClC,MAAM,QACJnU,EAAO,UACPC,EAAS,QACTC,EAAO,QACP1C,EAAO,MACPxH,EAAK,aACLuc,EAAY,SACZE,EAAQ,QACRtS,GACEmS,KAEJ,GAAI9U,EACF,OACExL,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACrJ,QAAQ,OAAO+G,cAAc,SAASzH,WAAW,SAASgK,EAAG,EAAEziB,SAAA,EAClET,EAAAA,EAAAA,KAACmjB,EAAAA,EAAgB,CAACC,KAAM,MACxBpjB,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKyE,GAAI,CAAEC,GAAI,GAAI7iB,SAAC,0DAGxCT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,QAAQhZ,MAAM,iBAAiByd,GAAI,CAAEC,GAAI,GAAI7iB,SAAC,wEAOxE,GAAIoE,EACF,OACEhE,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACC,EAAG,EAAEziB,SAAA,EACRT,EAAAA,EAAAA,KAACujB,EAAAA,EAAK,CAACC,SAAS,QAAQH,GAAI,CAAEI,GAAI,GAAIhjB,SACnCoE,KAEH7E,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,QAAOne,SAAC,4FAQlC,MAAMijB,EAAgD,CAAC,EACvD3U,EAAQ7M,SAAQyP,IACd,MAAMyQ,EAAczQ,EAAO1S,KAAKojB,OAAO,GAAGC,cAC1CoB,EAAmBtB,IAAgBsB,EAAmBtB,IAAgB,GAAK,CAAC,IAG9E,MAAMuB,EAAoB5U,EAAQrO,KAAIqR,GAAKA,EAAE9S,OAAMgR,OAC7CyS,EAAkB3T,EAAQtN,MAAKsQ,GAAKA,EAAE9S,KAAKmR,cAAcvF,SAAS,sBAClE8X,EAAqB5T,EAAQtN,MAAKsQ,GAAKA,EAAE9S,KAAKmR,cAAcvF,SAAS,yBAGrE+Y,EAAe/U,EAAQnO,KAAIoP,IAAM,CACrC7Q,KAAM6Q,EAAO7Q,KACb4jB,MAAO9T,EAAQvM,QAAOuP,GAAKA,EAAEjC,SAAWA,EAAO7Q,OAAMyH,WACnDuJ,MAAK,CAACS,EAAGC,IAAMA,EAAEkS,MAAQnS,EAAEmS,QAAOC,MAAM,EAAG,GAGzCe,EAAiB/U,EAAUpO,KAAI4P,IAAQ,CAC3CrR,KAAMqR,EAASrR,KACf4jB,MAAO9T,EAAQvM,QAAOuP,GAAKA,EAAEzB,WAAaA,EAASrR,OAAMyH,WACvDuJ,MAAK,CAACS,EAAGC,IAAMA,EAAEkS,MAAQnS,EAAEmS,QAAOC,MAAM,EAAG,IAE/C,OACEjiB,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACC,EAAG,EAAEziB,SAAA,EACRT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAArjB,SAAC,+CAItCT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,QAAQhZ,MAAM,iBAAiBme,WAAS,EAAAtjB,SAAC,uKAM7DI,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACrJ,QAAQ,OAAOoK,IAAK,EAAGX,GAAI,CAAEI,GAAI,EAAGQ,SAAU,QAASxjB,SAAA,EAC1DT,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOhd,UAC5BT,EAAAA,EAAAA,KAACkkB,EAAAA,EAAI,CAAAzjB,UACHI,EAAAA,EAAAA,MAACsjB,EAAAA,EAAW,CAAA1jB,SAAA,EACVT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKhZ,MAAM,UAASnF,SAAC,mBAGzCT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAIne,SACrB2gB,EAAagD,oBAEhBpkB,EAAAA,EAAAA,KAACqkB,EAAAA,EAAI,CACH5Z,MAAO6W,EACP8B,KAAK,QACLxd,MAAOwb,EAAe,IAAO,UAAY,UACzCiC,GAAI,CAAEC,GAAI,aAMlBtjB,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOhd,UAC5BT,EAAAA,EAAAA,KAACkkB,EAAAA,EAAI,CAAAzjB,UACHI,EAAAA,EAAAA,MAACsjB,EAAAA,EAAW,CAAA1jB,SAAA,EACVT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKhZ,MAAM,UAASnF,SAAC,aAGzCT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAIne,SACrBoO,EAAQnI,iBAMjB1G,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOhd,UAC5BT,EAAAA,EAAAA,KAACkkB,EAAAA,EAAI,CAAAzjB,UACHI,EAAAA,EAAAA,MAACsjB,EAAAA,EAAW,CAAA1jB,SAAA,EACVT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKhZ,MAAM,UAASnF,SAAC,eAGzCT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAIne,SACrBqO,EAAUpI,iBAMnB1G,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOhd,UAC5BT,EAAAA,EAAAA,KAACkkB,EAAAA,EAAI,CAAAzjB,UACHI,EAAAA,EAAAA,MAACsjB,EAAAA,EAAW,CAAA1jB,SAAA,EACVT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKhZ,MAAM,UAASnF,SAAC,aAGzCT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAIne,SACrBsO,EAAQrI,oBAQnB7F,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACrJ,QAAQ,OAAOoK,IAAK,EAAGX,GAAI,CAAEY,SAAU,QAASxjB,SAAA,EACnDT,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOhd,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACqkB,GAAI,CAAEH,EAAG,GAAIziB,SAAA,EAClBT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAArjB,SAAC,0BAItCI,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EACjBT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,YAAWne,SAAC,gCAGhCT,EAAAA,EAAAA,KAACqkB,EAAAA,EAAI,CACH5Z,MAAO2W,EAAe,IAAO,aAAU,YACvCxb,MAAOwb,EAAe,IAAO,UAAY,QACzCgC,KAAK,cAITviB,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EACjBT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,YAAWne,SAAC,yBAGhCI,EAAAA,EAAAA,MAAC4d,EAAAA,EAAU,CAACG,QAAQ,QAAOne,SAAA,CAAC,WACjBkjB,EAAkB,IAAM,MAAM,QAEzC9iB,EAAAA,EAAAA,MAAC4d,EAAAA,EAAU,CAACG,QAAQ,QAAOne,SAAA,CAAC,UAClBkjB,EAAkBA,EAAkBjd,OAAS,IAAM,MAAM,WAIrE7F,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EACjBT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,YAAWne,SAAC,6BAGhCT,EAAAA,EAAAA,KAACqkB,EAAAA,EAAI,CACH5Z,MAAOiY,EAAkB,aAAU,YACnC9c,MAAO8c,EAAkB,UAAY,QACrCU,KAAK,UAENV,IACC7hB,EAAAA,EAAAA,MAAC4d,EAAAA,EAAU,CAACG,QAAQ,QAAQyE,GAAI,CAAEC,GAAI,GAAI7iB,SAAA,CAAC,IACvCiiB,EAAgBzjB,KAAK,WAK7B4B,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EACjBT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,YAAWne,SAAC,gCAGhCT,EAAAA,EAAAA,KAACqkB,EAAAA,EAAI,CACH5Z,MAAOkY,EAAqB,aAAU,YACtC/c,MAAO+c,EAAqB,UAAY,QACxCS,KAAK,UAENT,IACC9hB,EAAAA,EAAAA,MAAC4d,EAAAA,EAAU,CAACG,QAAQ,QAAQyE,GAAI,CAAEC,GAAI,GAAI7iB,SAAA,CAAC,IACvCkiB,EAAmB1jB,KAAK,gBAOpCe,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOhd,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACqkB,GAAI,CAAEH,EAAG,GAAIziB,SAAA,EAClBT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAArjB,SAAC,yBAGtCT,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACI,GAAI,CAAEvV,UAAW,IAAK5O,SAAU,QAASuB,SAC3C8hB,OAAOC,KAAKkB,GAAoBzT,OAAOvP,KAAI+hB,IAC1C5hB,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAAcrJ,QAAQ,OAAOC,eAAe,gBAAgBwJ,GAAI,CAAEI,GAAI,GAAIhjB,SAAA,EAC5EI,EAAAA,EAAAA,MAAC4d,EAAAA,EAAU,CAACG,QAAQ,QAAOne,SAAA,CAAEgiB,EAAO,QACpC5hB,EAAAA,EAAAA,MAAC4d,EAAAA,EAAU,CAACG,QAAQ,QAAOne,SAAA,CAAEijB,EAAmBjB,GAAQ,gBAFhDA,gBAUpB5hB,EAAAA,EAAAA,MAACoiB,EAAAA,EAAG,CAACrJ,QAAQ,OAAOoK,IAAK,EAAGX,GAAI,CAAEC,GAAI,EAAGW,SAAU,QAASxjB,SAAA,EAC1DT,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOhd,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACqkB,GAAI,CAAEH,EAAG,GAAIziB,SAAA,EAClBT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAArjB,SAAC,mCAGtCT,EAAAA,EAAAA,KAACskB,EAAAA,EAAI,CAACrL,OAAK,EAAAxY,SACRmjB,EAAaljB,KAAI,CAACoP,EAAQlP,KACzBC,EAAAA,EAAAA,MAAC1B,EAAAA,SAAc,CAAAsB,SAAA,EACbT,EAAAA,EAAAA,KAACukB,GAAQ,CAAA9jB,UACPT,EAAAA,EAAAA,KAACwkB,GAAY,CACXpH,QAAStN,EAAO7Q,KAChBoe,UAAW,GAAGvN,EAAO+S,oBAGxBjiB,EAAQgjB,EAAald,OAAS,IAAK1G,EAAAA,EAAAA,KAACihB,GAAO,MAPzBnR,EAAO7Q,gBAcpCe,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOhd,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACqkB,GAAI,CAAEH,EAAG,GAAIziB,SAAA,EAClBT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAArjB,SAAC,sCAGtCT,EAAAA,EAAAA,KAACijB,EAAAA,EAAG,CAACI,GAAI,CAAEvV,UAAW,IAAK5O,SAAU,QAASuB,UAC5CT,EAAAA,EAAAA,KAACskB,EAAAA,EAAI,CAACrL,OAAK,EAAAxY,SACRojB,EAAenjB,KAAI,CAAC4P,EAAU1P,KAC7BC,EAAAA,EAAAA,MAAC1B,EAAAA,SAAc,CAAAsB,SAAA,EACbT,EAAAA,EAAAA,KAACukB,GAAQ,CAAA9jB,UACPT,EAAAA,EAAAA,KAACwkB,GAAY,CACXpH,QAAS9M,EAASrR,KAClBoe,UAAW,GAAG/M,EAASuS,oBAG1BjiB,EAAQijB,EAAend,OAAS,IAAK1G,EAAAA,EAAAA,KAACihB,GAAO,MAP3B3Q,EAASrR,oBAiBzCmiB,EAAe,MACdvgB,EAAAA,EAAAA,MAAC0iB,EAAAA,EAAK,CAACC,SAAS,UAAUH,GAAI,CAAEC,GAAI,GAAI7iB,SAAA,EACtCT,EAAAA,EAAAA,KAACye,EAAAA,EAAU,CAACG,QAAQ,KAAIne,SAAC,8DAGzBI,EAAAA,EAAAA,MAAC4d,EAAAA,EAAU,CAACG,QAAQ,QAAOne,SAAA,CAAC,kCACM2gB,EAAagD,iBAAiB,gKAMhE,EC9OV,GAhD4BK,KAC1B,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,MAEjBC,EAAUC,KADAC,EAAAA,EAAAA,OACelY,EAAAA,EAAAA,UAAc,QACvCmY,EAAgBC,IAAqBpY,EAAAA,EAAAA,WAAkB,GAe9D,OAbAG,EAAAA,EAAAA,YAAU,KACc5I,WACpB,GAAIugB,EAAa,CACf,MAAMO,GAAUxgB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,YAAamgB,EAAYQ,KAC3CC,QAAiBngB,EAAAA,EAAAA,IAAOigB,GAC1BE,EAASlgB,UACX4f,EAAYM,EAASzgB,OAEzB,GAEF0gB,EAAe,GACd,CAACV,KAGF7jB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,sBAAqBiB,SAAA,EAClCT,EAAAA,EAAAA,KAACqlB,EAAAA,EAAO,CAACT,SAAUA,KACnB/jB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,CAAC,mBAE1BT,EAAAA,EAAAA,KAAA,UACEkB,QAASA,IAAM8jB,GAAmBD,GAClCnc,MAAO,CACLoX,WAAY,OACZzG,QAAS,WACT9L,gBAAiBsX,EAAiB,UAAY,UAC9Cnf,MAAO,QACP0a,OAAQ,OACRgF,aAAc,MACdC,OAAQ,UACRC,SAAU,QACV/kB,SAEDskB,EAAiB,mBAAqB,iCAG3C/kB,EAAAA,EAAAA,KAACQ,EAAAA,EAAU,IACVukB,GAAiB/kB,EAAAA,EAAAA,KAACgjB,GAAiB,KAAMhjB,EAAAA,EAAAA,KAACoS,EAAW,SAEpD,C,4GCpDH,SAASqT,EAA2B9mB,GACzC,OAAOC,EAAAA,EAAAA,IAAqB,iBAAkBD,EAChD,EAC2BE,EAAAA,EAAAA,GAAuB,iBAAkB,CAAC,S,aCKrE,MASM6mB,GAAkB3mB,EAAAA,EAAAA,IAAO,MAAO,CACpCE,KAAM,iBACNN,KAAM,QAFgBI,CAGrB,CACDwa,QAAS,GACT,eAAgB,CACda,cAAe,MAqDnB,EAlDiCjb,EAAAA,YAAiB,SAAqBC,EAASC,GAC9E,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,oBAEF,UACJO,EAAS,UACTmc,EAAY,SACTjc,GACDJ,EACEK,EAAa,IACdL,EACHqc,aAEI/b,EAhCkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoB2lB,EAA4B7lB,EAAQ,EAyBjDG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAK0lB,EAAiB,CACxC3I,GAAIpB,EACJnc,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BG,WAAYA,EACZN,IAAKA,KACFK,GAEP,G", "sources": ["../node_modules/@mui/material/esm/Card/cardClasses.js", "../node_modules/@mui/material/esm/Card/Card.js", "../node_modules/@mui/material/esm/ListItemText/listItemTextClasses.js", "components/shared/StatsCards.tsx", "../node_modules/@mui/material/esm/Divider/dividerClasses.js", "components/shared/Modal.tsx", "components/admin/business/utils/cardUtils.ts", "components/admin/business/hooks/useCardManagement.ts", "components/admin/business/hooks/usePageConfiguration.ts", "components/admin/business/components/CardSelector.tsx", "components/admin/business/components/CardManagement.tsx", "components/admin/business/components/FieldConfigItem.tsx", "components/admin/business/components/PageBuilderContent.tsx", "components/admin/business/types/PageBuilderTypes.ts", "components/admin/business/hooks/useOfficeDataSimple.ts", "components/admin/business/components/CheckboxDropdown.tsx", "components/admin/business/components/ReportConfiguration.tsx", "components/admin/business/utils/dropdownPersistence.ts", "components/admin/business/PageBuilder.tsx", "components/admin/business/hooks/usePageBuilderState.ts", "../node_modules/@mui/material/esm/ListItem/listItemClasses.js", "../node_modules/@mui/material/esm/ListItemButton/listItemButtonClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/listItemSecondaryActionClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/ListItemSecondaryAction.js", "../node_modules/@mui/material/esm/ListItem/ListItem.js", "../node_modules/@mui/material/esm/ListItemText/ListItemText.js", "../node_modules/@mui/material/esm/Divider/Divider.js", "components/admin/business/hooks/useOfficeDataEnhanced.ts", "components/admin/business/OfficeLoadingTest.tsx", "components/admin/AdminPage.tsx", "../node_modules/@mui/material/esm/CardContent/cardContentClasses.js", "../node_modules/@mui/material/esm/CardContent/CardContent.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root']);\nexport default cardClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getCardUtilityClass } from \"./cardClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardUtilityClass, classes);\n};\nconst CardRoot = styled(Paper, {\n  name: 'MuiCard',\n  slot: 'Root'\n})({\n  overflow: 'hidden'\n});\nconst Card = /*#__PURE__*/React.forwardRef(function Card(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCard'\n  });\n  const {\n    className,\n    raised = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    raised\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardRoot, {\n    className: clsx(classes.root, className),\n    elevation: raised ? 8 : undefined,\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Card.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the card will use raised styling.\n   * @default false\n   */\n  raised: chainPropTypes(PropTypes.bool, props => {\n    if (props.raised && props.variant === 'outlined') {\n      return new Error('MUI: Combining `raised={true}` with `variant=\"outlined\"` has no effect.');\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Card;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemTextUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemText', slot);\n}\nconst listItemTextClasses = generateUtilityClasses('MuiListItemText', ['root', 'multiline', 'dense', 'inset', 'primary', 'secondary']);\nexport default listItemTextClasses;", "import React from 'react';\nimport './StatsCards.css';\n\nconst stats = [\n  { title: 'SB Accounts', value: 123456 },\n  { title: 'BD Revenue', value: '₹24,343' },\n  { title: 'No. Aadhaar Trans', value: 1259 },\n  { title: 'PLI', value: '₹99,99,999' }\n];\n\nconst StatsCards: React.FC = () => {\n  return (\n    <div className=\"stats-grid\">\n      {stats.map((stat, index) => (\n        <div className={`stat-card card-${index}`} key={index}>\n          <h3>{stat.title}</h3>\n          <p className=\"stat-value\">{stat.value}</p>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default StatsCards;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDividerUtilityClass(slot) {\n  return generateUtilityClass('MuiDivider', slot);\n}\nconst dividerClasses = generateUtilityClasses('MuiDivider', ['root', 'absolute', 'fullWidth', 'inset', 'middle', 'flexItem', 'light', 'vertical', 'withChildren', 'withChildrenVertical', 'textAlignRight', 'textAlignLeft', 'wrapper', 'wrapperVertical']);\nexport default dividerClasses;", "import React from 'react';\nimport './Modal.css';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n}\n\nconst Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={e => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2>{title}</h2>\n          <button className=\"close-button\" onClick={onClose}>&times;</button>\n        </div>\n        <div className=\"modal-body\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;", "import { <PERSON>a<PERSON>older, FaFile<PERSON>lt, Fa<PERSON>og, FaFolderOpen } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\n// Helper function to generate card style (icon and color)\nexport const generateCardStyle = (title: string) => {\n  const hash = title\n    .split('')\n    .reduce((acc, char) => acc + char.charCodeAt(0), 0);\n  \n  const icons = [FaFolder, FaFileAlt, FaCog, FaFolderOpen]; // Add more icons if needed\n  const colors = ['#FFC107', '#2196F3', '#4CAF50', '#E91E63', '#9C27B0'];\n\n  const icon = icons[hash % icons.length];\n  const color = colors[hash % colors.length];\n  \n  return { icon, color };\n};\n\n// Helper function to check if a card is a main card\nexport const isMainCard = (cardId: string, allCategories: Category[]): boolean => {\n  const card = allCategories.find(c => c.id === cardId);\n  return card ? !card.parentId : false;\n};\n\n// Helper function to check if a card is a leaf card\nexport const isLeafCard = (cardId: string, allCategories: Category[]): boolean => {\n  return !allCategories.some(c => c.parentId === cardId);\n};\n\n// Helper function to organize cards into a tree structure\nexport const organizeCards = (list: Category[]): Category[] => {\n  const map: { [key: string]: Category } = {};\n  const roots: Category[] = [];\n  list.forEach(item => {\n    map[item.id] = { ...item, children: [] }; \n  });\n  list.forEach(item => {\n    if (item.parentId && map[item.parentId]) {\n      map[item.parentId].children?.push(map[item.id]);\n    } else {\n      roots.push(map[item.id]);\n    }\n  });\n  return roots;\n};\n\n// Helper function to get all descendant IDs\nexport const getAllDescendantIds = (parentId: string, allCategories: Category[]): string[] => {\n  let descendants: string[] = [];\n  const children = allCategories.filter(c => c.parentId === parentId);\n  for (const child of children) {\n    descendants.push(child.id);\n    descendants = descendants.concat(getAllDescendantIds(child.id, allCategories));\n  }\n  return descendants;\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc, collection, getDocs, writeBatch, deleteDoc, updateDoc } from 'firebase/firestore';\nimport { Category } from '../types/PageBuilderTypes';\nimport { generateCardStyle, getAllDescendantIds } from '../utils/cardUtils';\n\ninterface UseCardManagementProps {\n  categories: Category[];\n  setCategories: (categories: Category[]) => void;\n  selectedCard: string;\n  setSelectedCard: (card: string) => void;\n  newCardId: string;\n  setNewCardId: (id: string) => void;\n  newCardTitle: string;\n  setNewCardTitle: (title: string) => void;\n  actionType: string;\n  setActionType: (type: string) => void;\n  setIsLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setShowConfirmModal: (show: boolean) => void;\n  setIsAddingNewCard: (adding: boolean) => void;\n  setPageConfig: (config: any) => void;\n  setFields: (fields: any[]) => void;\n  setEditingCard: (card: Category | null) => void;\n  setShowEditModal: (show: boolean) => void;\n  setCardToDelete: (id: string | null) => void;\n  setShowDeleteConfirmModal: (show: boolean) => void;\n}\n\nexport const useCardManagement = (props: UseCardManagementProps) => {\n  const {\n    categories,\n    setCategories,\n    selectedCard,\n    setSelectedCard,\n    newCardId,\n    setNewCardId,\n    newCardTitle,\n    setNewCardTitle,\n    actionType,\n    setActionType,\n    setIsLoading,\n    setError,\n    setSuccess,\n    setShowConfirmModal,\n    setIsAddingNewCard,\n    setPageConfig,\n    setFields,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n  } = props;\n\n  const fetchCategories = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const querySnapshot = await getDocs(collection(db, 'categories'));\n      const fetchedCategories = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Category));\n      setCategories(fetchedCategories);\n    } catch (err) {\n      setError('Failed to fetch categories.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [setCategories, setIsLoading, setError]);\n\n  const checkDuplicateId = async (id: string): Promise<boolean> => {\n    const docRef = doc(db, 'categories', id);\n    const docSnap = await getDoc(docRef);\n    return docSnap.exists();\n  };\n\n  const handleAddNewCard = async () => {\n    if (!newCardId || !newCardTitle) {\n      setError('Report ID and Title are required.');\n      return;\n    }\n    setIsLoading(true);\n    const isDuplicate = await checkDuplicateId(newCardId);\n    if (isDuplicate) {\n      setError('This Report ID already exists. Please use a unique ID.');\n      setIsLoading(false);\n      return;\n    }\n    setIsLoading(false);\n    setShowConfirmModal(true);\n  };\n\n  const handleConfirmCreate = async () => {\n    if (!newCardId || !newCardTitle) {\n        setError('Report ID and Title cannot be empty.');\n        setShowConfirmModal(false);\n        return;\n    }\n    let parentIdToSet: string | null = null;\n    if (actionType === 'createNestedCard' && selectedCard) {\n      parentIdToSet = selectedCard;\n    } else if (actionType === 'addNewCardGlobal') {\n      parentIdToSet = null; \n    } else if (selectedCard && actionType !== 'addNewCardGlobal') {\n        parentIdToSet = selectedCard;\n    } else if (!selectedCard && actionType !== 'createNestedCard') { \n        parentIdToSet = null;\n    }\n\n    const parentPath = parentIdToSet ? categories.find(c => c.id === parentIdToSet)?.path : '/categories';\n    const newPath = `${parentPath}/${newCardId}`.replace(/\\/+/g, '/');\n\n    try {\n      setIsLoading(true);\n      setShowConfirmModal(false);\n      const cardRef = doc(db, 'categories', newCardId);\n      const { icon: generatedIcon, color: generatedColor } = generateCardStyle(newCardTitle);\n      \n      await setDoc(cardRef, {\n        id: newCardId,\n        title: newCardTitle,\n        path: newPath,\n        parentId: parentIdToSet,\n        lastUpdated: new Date().toISOString(),\n        icon: generatedIcon.name,\n        color: generatedColor,\n        fields: [],\n        isPage: true,\n        pageId: newCardId,\n      });\n  \n      await fetchCategories(); \n      \n      setNewCardId('');\n      setNewCardTitle('');\n      setIsAddingNewCard(false);\n      setActionType(''); \n      setSelectedCard(newCardId);\n      setSuccess(`Report \"${newCardTitle}\" has been created successfully!`);\n      setTimeout(() => setSuccess(null), 3000);\n      \n    } catch (err) {\n      setError('Error creating new report. Check console for details.');\n      console.error('Error creating card:', err);\n    } finally {\n      setIsLoading(false); \n    }\n  };\n\n  const handleEditCard = (card: Category) => {\n    setEditingCard(card);\n    setNewCardTitle(card.title);\n    setShowEditModal(true);\n  };\n\n  const handleUpdateCard = async () => {\n    const editingCard = categories.find(c => c.id === selectedCard);\n    if (!editingCard || !newCardTitle) return;\n    try {\n      setIsLoading(true);\n      const cardRef = doc(db, 'categories', editingCard.id);\n      await updateDoc(cardRef, { title: newCardTitle, lastUpdated: new Date().toISOString() });\n      await fetchCategories();\n      setShowEditModal(false);\n      setEditingCard(null);\n      setNewCardTitle('');\n      setSuccess('Report updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to update report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDeleteClick = (cardId: string) => {\n    setCardToDelete(cardId);\n    setShowDeleteConfirmModal(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (!selectedCard) return;\n    setIsLoading(true);\n    try {\n      const batch = writeBatch(db);\n      const allDescendants = getAllDescendantIds(selectedCard, categories);\n      const idsToDelete = [selectedCard, ...allDescendants];\n\n      for (const id of idsToDelete) {\n        batch.delete(doc(db, 'categories', id));\n        batch.delete(doc(db, 'pages', id));\n      }\n      await batch.commit();\n      await fetchCategories();\n\n      setShowDeleteConfirmModal(false);\n      setCardToDelete(null);\n      setSelectedCard('');\n      setPageConfig(null);\n      setFields([]);\n      setSuccess('Report and all its nested items deleted successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to delete report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return {\n    fetchCategories,\n    handleAddNewCard,\n    handleConfirmCreate,\n    handleEditCard,\n    handleUpdateCard,\n    handleDeleteClick,\n    handleConfirmDelete,\n  };\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc } from 'firebase/firestore';\nimport { FormField, PageConfig, Category } from '../types/PageBuilderTypes';\nimport { <PERSON><PERSON>ield as DynamicFormField, FormConfig as DynamicFormConfig, FormFieldOption } from '../../../shared/DynamicForm';\nimport { supabasePageService } from '../services/supabasePageService';\n\ninterface UsePageConfigurationProps {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  setPageConfig: (config: PageConfig | null) => void;\n  fields: FormField[];\n  setFields: (fields: FormField[]) => void;\n  setAvailableDynamicFields: (fields: DynamicFormField[]) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setPreviewContent: (content: string) => void;\n  setIsPreviewOpen: (open: boolean) => void;\n  // New dropdown values - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  // Setters for dropdown values\n  setSelectedRegions: (regions: string[]) => void;\n  setSelectedDivisions: (divisions: string[]) => void;\n  setSelectedOffices: (offices: string[]) => void;\n  setSelectedFrequency: (frequency: string) => void;\n}\n\nexport const usePageConfiguration = (props: UsePageConfigurationProps) => {\n  const {\n    categories,\n    selectedCard,\n    pageConfig,\n    setPageConfig,\n    fields,\n    setFields,\n    setAvailableDynamicFields,\n    setLoading,\n    setError,\n    setSuccess,\n    setPreviewContent,\n    setIsPreviewOpen,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  } = props;\n\n  const fetchDynamicFormFields = useCallback(async (formId: string) => {\n    if (!formId) return;\n    console.log(`Fetching dynamic form fields for formId: ${formId}`);\n    try {\n      const formConfigRef = doc(db, 'formConfigs', formId);\n      const formConfigSnap = await getDoc(formConfigRef);\n      if (formConfigSnap.exists()) {\n        const formConfigData = formConfigSnap.data() as DynamicFormConfig;\n        setAvailableDynamicFields(formConfigData.fields || []);\n        console.log('Fetched dynamic fields:', formConfigData.fields);\n      } else {\n        console.log(`No dynamic form configuration found for formId: ${formId}`);\n        setAvailableDynamicFields([]);\n      }\n    } catch (err) {\n      console.error('Error fetching dynamic form fields:', err);\n      setError('Failed to fetch dynamic form fields.');\n      setAvailableDynamicFields([]);\n    }\n  }, [setAvailableDynamicFields, setError]);\n\n  const loadPageConfig = useCallback(async (cardId: string) => {\n    if (!cardId) {\n      console.log('loadPageConfig called with no cardId');\n      return;\n    }\n    console.log(`loadPageConfig called for cardId: ${cardId}`);\n    setLoading(true);\n    setError(null);\n    try {\n      // Try loading from Firebase first\n      const docRef = doc(db, 'pages', cardId);\n      const docSnap = await getDoc(docRef);\n\n      let data: PageConfig | null = null;\n\n      if (docSnap.exists()) {\n        // Loading from Firebase\n        data = docSnap.data() as PageConfig;\n      } else {\n        // If not found in Firebase, try Supabase\n        try {\n          data = await supabasePageService.loadPageConfig(cardId);\n        } catch (supabaseError) {\n          // Not found in either database, will create new config\n        }\n      }\n\n      if (data) {\n        setPageConfig(data);\n        setFields(data.fields || []);\n        // Load saved dropdown values - handle both old single values and new arrays\n        // Only set if current state is empty to preserve user selections\n        const savedRegions = data.selectedRegions || (data.selectedRegion ? [data.selectedRegion] : []);\n        const savedDivisions = data.selectedDivisions || (data.selectedDivision ? [data.selectedDivision] : []);\n        const savedOffices = data.selectedOffices || (data.selectedOffice ? [data.selectedOffice] : []);\n        const savedFrequency = data.selectedFrequency || '';\n\n        // Only update if current selections are empty (preserve user's current selections)\n        if (selectedRegions.length === 0 && savedRegions.length > 0) {\n          setSelectedRegions(savedRegions);\n        }\n        if (selectedDivisions.length === 0 && savedDivisions.length > 0) {\n          setSelectedDivisions(savedDivisions);\n        }\n        if (selectedOffices.length === 0 && savedOffices.length > 0) {\n          setSelectedOffices(savedOffices);\n        }\n        if (!selectedFrequency && savedFrequency) {\n          setSelectedFrequency(savedFrequency);\n        }\n      } else {\n        // Create new page config\n        const card = categories.find(c => c.id === cardId);\n        setPageConfig({\n          id: cardId,\n          title: card?.title || 'New Page',\n          fields: [],\n          lastUpdated: new Date().toISOString(),\n        });\n        setFields([]);\n        // Reset dropdown values for new page\n        setSelectedRegions([]);\n        setSelectedDivisions([]);\n        setSelectedOffices([]);\n        setSelectedFrequency('');\n      }\n    } catch (err) {\n      setError('Failed to load page configuration.');\n      console.error(err);\n      setPageConfig(null);\n      setFields([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [categories, setLoading, setError, setPageConfig, setFields, setSelectedRegions, setSelectedDivisions, setSelectedOffices, setSelectedFrequency, selectedRegions, selectedDivisions, selectedOffices, selectedFrequency]);\n\n  const addField = () => {\n    const newField: FormField = {\n      id: `field_${Date.now()}`,\n      type: 'text',\n      label: 'New Field',\n      placeholder: '',\n      options: [],\n      required: false,\n      region: '',\n      division: '',\n      office: '',\n    };\n    setFields([...fields, newField]);\n  };\n\n  const addFieldFromDynamic = (dynamicField: DynamicFormField) => {\n    console.log('Attempting to add dynamic field:', dynamicField);\n    const newField: FormField = {\n      id: dynamicField.id,\n      type: dynamicField.type,\n      label: dynamicField.label,\n      placeholder: dynamicField.placeholder,\n      options: dynamicField.options ? dynamicField.options.map((opt: string | FormFieldOption) => {\n        if (typeof opt === 'string') {\n          return { label: opt, value: opt };\n        } else {\n          return { label: opt.label, value: opt.value };\n        }\n      }) : undefined,\n      required: dynamicField.required,\n      defaultValue: dynamicField.defaultValue,\n      min: dynamicField.min,\n      max: dynamicField.max,\n      sectionTitle: undefined,\n      columns: undefined,\n      buttonText: undefined,\n      buttonType: undefined,\n      onClickAction: undefined,\n      value: undefined,\n    };\n\n    if (fields.some(field => field.id === newField.id)) {\n        console.warn(`Duplicate field ID detected: \"${newField.id}\". Field not added.`);\n        setError(`Field with ID \"${newField.id}\" already exists in the page configuration.`);\n        setTimeout(() => setError(null), 3000);\n        return;\n    }\n\n    console.log('Adding new field to state:', newField);\n    setFields([...fields, newField]);\n    setSuccess(`Added field \"${newField.label}\" to page configuration.`);\n    setTimeout(() => setSuccess(null), 3000);\n  };\n\n  const updateField = (index: number, updatedField: FormField) => {\n    const updatedFields = [...fields];\n    updatedFields[index] = updatedField;\n    setFields(updatedFields);\n  };\n\n  const removeField = (index: number) => {\n    setFields(fields.filter((_, i) => i !== index));\n  };\n\n  const handleSave = async () => {\n    if (!selectedCard || !pageConfig) {\n      setError('No report selected or page configuration loaded.');\n      return;\n    }\n\n    // Validate that report frequency is selected\n    if (!selectedFrequency) {\n      setError('Report frequency is required. Please select a frequency before saving.');\n      return;\n    }\n\n    setLoading(true);\n    console.log('Attempting to save page configuration for cardId:', selectedCard);\n    console.log('Fields being saved:', fields);\n    console.log('Report frequency:', selectedFrequency);\n\n    try {\n      const cleanedFields = fields.map(field => {\n        const cleanedField: any = {};\n        for (const key in field) {\n          if (field[key] !== undefined) {\n            cleanedField[key] = field[key];\n          } else {\n            cleanedField[key] = null;\n          }\n        }\n        return cleanedField;\n      });\n\n      const updatedPageConfig: PageConfig = {\n        ...pageConfig,\n        id: selectedCard,\n        title: categories.find(c => c.id === selectedCard)?.title || pageConfig.title,\n        fields: cleanedFields,\n        lastUpdated: new Date().toISOString(),\n        selectedRegions,\n        selectedDivisions,\n        selectedOffices,\n        selectedFrequency,\n      };\n\n      // Save to both Firebase and Supabase\n      const savePromises = [];\n\n      // Save to Firebase\n      savePromises.push(\n        setDoc(doc(db, 'pages', selectedCard), updatedPageConfig)\n          .catch(err => {\n            console.error('Firebase save failed:', err);\n            throw new Error(`Firebase save failed: ${err.message}`);\n          })\n      );\n\n      // Save to Supabase\n      savePromises.push(\n        supabasePageService.savePageConfig(updatedPageConfig)\n          .catch(err => {\n            console.error('Supabase save failed:', err);\n            throw new Error(`Supabase save failed: ${err.message}`);\n          })\n      );\n\n      // Wait for both saves to complete\n      await Promise.all(savePromises);\n\n      setPageConfig(updatedPageConfig);\n      setSuccess('Page configuration saved successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n\n    } catch (err) {\n      console.error('Failed to save page configuration:', err);\n      setError(`Failed to save page configuration: ${err instanceof Error ? err.message : 'Unknown error'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePreview = () => {\n    if (!pageConfig || fields.length === 0) {\n      alert('No page configuration or fields to preview.');\n      return;\n    }\n\n    const generatedPreview = `\n      <h1>${pageConfig.title}</h1>\n      <form>\n        ${fields.map(field => {\n          let fieldHtml = '';\n          switch (field.type) {\n            case 'text':\n            case 'number':\n            case 'date':\n            case 'textarea':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <input type=\"${field.type}\" class=\"form-control\" placeholder=\"${field.placeholder || ''}\" ${field.required ? 'required' : ''} />\n                </div>\n              `;\n              break;\n            case 'dropdown':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <select class=\"form-control\" ${field.required ? 'required' : ''}>\n                    <option value=\"\">Select ${field.label}</option>\n                    ${field.options?.map(option => `<option value=\"${option.value}\">${option.label}</option>`).join('') || ''}\n                  </select>\n                </div>\n              `;\n              break;\n            case 'checkbox':\n              fieldHtml = `\n                <div class=\"form-check mb-3\">\n                  <input type=\"checkbox\" class=\"form-check-input\" id=\"${field.id}\" ${field.required ? 'required' : ''} />\n                  <label class=\"form-check-label\" for=\"${field.id}\">${field.label}${field.required ? ' *' : ''}</label>\n                </div>\n              `;\n              break;\n            case 'radio':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  ${field.options?.map((option, i) => `\n                    <div class=\"form-check\">\n                      <input class=\"form-check-input\" type=\"radio\" name=\"${field.id}\" id=\"${field.id}-${i}\" value=\"${option.value}\" ${field.required ? 'required' : ''}>\n                      <label class=\"form-check-label\" for=\"${field.id}-${i}\">${option.label}</label>\n                    </div>\n                  `).join('') || ''}\n                </div>\n              `;\n              break;\n            case 'section':\n              fieldHtml = `\n                <div class=\"card mt-3 mb-3\">\n                  <div class=\"card-header\">${field.sectionTitle || 'Section'}</div>\n                  <div class=\"card-body\">\n                    <p>Fields for this section would appear here in the actual form.</p>\n                  </div>\n                </div>\n              `;\n              break;\n            case 'button':\n              fieldHtml = `\n                <button type=\"button\" class=\"btn btn-primary mt-3\">${field.buttonText || 'Button'}</button>\n              `;\n              break;\n            default:\n              fieldHtml = `<p>Unsupported field type: ${field.type}</p>`;\n          }\n          return fieldHtml;\n        }).join('')}\n      </form>\n    `;\n\n    setPreviewContent(generatedPreview);\n    setIsPreviewOpen(true);\n  };\n\n  return {\n    fetchDynamicFormFields,\n    loadPageConfig,\n    addField,\n    addFieldFromDynamic,\n    updateField,\n    removeField,\n    handleSave,\n    handlePreview,\n  };\n};\n", "import React from 'react';\nimport { Category } from '../types/PageBuilderTypes';\nimport { organizeCards, isLeafCard, isMainCard } from '../utils/cardUtils';\n\ninterface CardSelectorProps {\n  categories: Category[];\n  selectedCard: string;\n  onCardChange: (cardId: string) => void;\n  actionType: string;\n  onActionChange: (action: string) => void;\n  isLoading: boolean;\n  onCreateAction: () => void;\n  onWebPageAction: () => void;\n}\n\nconst CardSelector: React.FC<CardSelectorProps> = ({\n  categories,\n  selectedCard,\n  onCardChange,\n  actionType,\n  onActionChange,\n  isLoading,\n  onCreateAction,\n  onWebPageAction,\n}) => {\n  const renderCardOptions = (cards: Category[], level = 0): React.ReactElement[] => {\n    return cards.flatMap(card => {\n      // Handle undefined or empty titles gracefully\n      let displayTitle = card.title;\n\n      // If title is undefined, null, or empty, skip the entry\n      if (!displayTitle || displayTitle.trim() === '' || displayTitle === 'undefined') {\n        console.warn(`Skipping undefined entry with ID: ${card.id}`);\n        return [];\n      }\n\n      return [\n        <option key={card.id} value={card.id} style={{ paddingLeft: `${level * 20}px` }}>\n          {`${'--'.repeat(level)} ${displayTitle}`}\n        </option>,\n        ...(card.children && card.children.length > 0 ? renderCardOptions(card.children, level + 1) : []),\n      ];\n    });\n  };\n\n  const handleCardChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newSelectedCard = e.target.value;\n    onCardChange(newSelectedCard);\n  };\n\n  const handleActionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newAction = e.target.value;\n    onActionChange(newAction);\n    \n    if (newAction === 'createNestedCard' || newAction === 'addNewCardGlobal') {\n      onCreateAction();\n    } else if (newAction === 'createWebPage') {\n      onWebPageAction();\n    }\n  };\n\n  return (\n    <div className=\"card-selector\">\n      <select\n        value={selectedCard}\n        onChange={handleCardChange}\n        className=\"form-select\"\n        disabled={isLoading}\n      >\n        <option value=\"\">{isLoading ? 'Loading Reports...' : 'Select or Create New Report'}</option>\n        {renderCardOptions(organizeCards(categories))}\n      </select>\n\n      <div className=\"action-dropdown-container\">\n        <select\n          value={actionType}\n          onChange={handleActionChange}\n          className=\"form-select action-dropdown\"\n        >\n          <option value=\"\">Select Action...</option>\n          <option value=\"addNewCardGlobal\" disabled={!!selectedCard}>\n            Create New Main Report\n          </option>\n          {selectedCard && (\n            <>\n              <option value=\"createNestedCard\">\n                Create Nested Report\n              </option>\n              <option\n                value=\"createWebPage\"\n                disabled={!isLeafCard(selectedCard, categories) || isMainCard(selectedCard, categories)}\n              >\n                Create/Edit Web Page for this Report\n              </option>\n            </>\n          )}\n        </select>\n      </div>\n    </div>\n  );\n};\n\nexport default CardSelector;\n", "import React from 'react';\nimport { FaEdit, FaTrash } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\ninterface CardManagementProps {\n  selectedCard: string;\n  categories: Category[];\n  onEditCard: (card: Category) => void;\n  onDeleteCard: (cardId: string) => void;\n}\n\nconst CardManagement: React.FC<CardManagementProps> = ({\n  selectedCard,\n  categories,\n  onEditCard,\n  onDeleteCard,\n}) => {\n  const selectedCategory = categories.find(c => c.id === selectedCard);\n\n  if (!selectedCategory) {\n    return null;\n  }\n\n  return (\n    <div className=\"card-management\">\n      <h3>Report Details: \"{selectedCategory.title}\"</h3>\n      <div className=\"card-actions\">\n        <button\n          onClick={() => onEditCard(selectedCategory)}\n          className=\"edit-button btn btn-outline-primary btn-sm me-2\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaEdit as React.ComponentType<any>)} Edit Name\n        </button>\n        <button\n          onClick={() => onDeleteCard(selectedCard)}\n          className=\"delete-button btn btn-outline-danger btn-sm\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaTrash as React.ComponentType<any>)} Delete Report\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CardManagement;\n", "import React from 'react';\nimport { FaTrash } from 'react-icons/fa';\nimport { FormField } from '../types/PageBuilderTypes';\n\ninterface FieldConfigItemProps {\n  field: FormField;\n  index: number;\n  onUpdate: (index: number, field: FormField) => void;\n  onRemove: (index: number) => void;\n}\n\nconst FieldConfigItem: React.FC<FieldConfigItemProps> = ({\n  field,\n  index,\n  onUpdate,\n  onRemove,\n}) => {\n  const handleOptionChange = (optIndex: number, value: string, key: 'label' | 'value') => {\n    const newOptions = [...(field.options || [])];\n    newOptions[optIndex] = { ...newOptions[optIndex], [key]: value };\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const addOption = () => {\n    const newOptions = [...(field.options || []), { label: '', value: '' }];\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const removeOption = (optIndex: number) => {\n    const newOptions = field.options?.filter((_, i) => i !== optIndex);\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const handleDefaultValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { value, type } = e.target;\n    let newDefaultValue: any = value;\n    if (type === 'checkbox') {\n      newDefaultValue = (e.target as HTMLInputElement).checked;\n    }\n    onUpdate(index, { ...field, defaultValue: newDefaultValue });\n  };\n\n  return (\n    <div className=\"field-config-item card mb-3\">\n      <div className=\"card-header d-flex justify-content-between align-items-center\">\n        <strong>{field.label || 'Unnamed Field'}</strong> ({field.type})\n        <button onClick={() => onRemove(index)} className=\"btn btn-danger btn-sm\">\n          {React.createElement(FaTrash as React.ComponentType<any>)} Remove\n        </button>\n      </div>\n      <div className=\"card-body\">\n        {/* Field Type Selector */}\n        <div className=\"form-group\">\n          <label htmlFor={`field-type-${index}`} className=\"form-label\">Type: </label>\n          <select\n            id={`field-type-${index}`}\n            className=\"form-control\"\n            value={field.type}\n            onChange={(e) => onUpdate(index, {\n              ...field, \n              type: e.target.value as FormField['type'], \n              options: field.type !== 'dropdown' && field.type !== 'radio' && field.type !== 'checkbox-group' ? undefined : field.options, \n              placeholder: field.type === 'section' || field.type === 'button' ? undefined : field.placeholder \n            })}\n          >\n            <option value=\"text\">Text</option>\n            <option value=\"textarea\">Textarea</option>\n            <option value=\"number\">Number</option>\n            <option value=\"date\">Date</option>\n            <option value=\"dropdown\">Dropdown</option>\n            <option value=\"radio\">Radio Group</option>\n            <option value=\"checkbox\">Checkbox (Single)</option>\n            <option value=\"checkbox-group\">Checkbox Group</option>\n            <option value=\"switch\">Switch</option>\n            <option value=\"file\">File Upload</option>\n            <option value=\"section\">Section Header</option>\n            <option value=\"button\">Button</option>\n          </select>\n        </div>\n\n        <div className=\"form-group\">\n          <label htmlFor={`field-label-${index}`} className=\"form-label\">Label: </label>\n          <input\n            id={`field-label-${index}`}\n            type=\"text\"\n            className=\"form-control\"\n            value={field.label}\n            onChange={(e) => onUpdate(index, {...field, label: e.target.value})}\n            required\n          />\n        </div>\n\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-placeholder-${index}`} className=\"form-label\">Placeholder: </label>\n            <input\n              id={`field-placeholder-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.placeholder || ''}\n              onChange={(e) => onUpdate(index, {...field, placeholder: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'number' && (\n          <>\n            <div className=\"form-group\">\n              <label htmlFor={`field-min-${index}`} className=\"form-label\">Min Value: </label>\n              <input\n                id={`field-min-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.min === undefined ? '' : field.min}\n                onChange={(e) => onUpdate(index, {...field, min: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor={`field-max-${index}`} className=\"form-label\">Max Value: </label>\n              <input\n                id={`field-max-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.max === undefined ? '' : field.max}\n                onChange={(e) => onUpdate(index, {...field, max: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n          </>\n        )}\n\n        {['dropdown', 'radio', 'checkbox-group'].includes(field.type) && (\n          <div className=\"form-group field-options-config\">\n            <label className=\"form-label\">Options: </label>\n            {field.options?.map((opt, optIndex) => (\n              <div key={optIndex} className=\"input-group mb-2\">\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Label\"\n                  value={opt.label}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'label')}\n                />\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Value\"\n                  value={opt.value}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'value')}\n                />\n                <button type=\"button\" onClick={() => removeOption(optIndex)} className=\"btn btn-outline-danger\">\n                  Remove\n                </button>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addOption} className=\"btn btn-secondary btn-sm\">\n              Add Option\n            </button>\n          </div>\n        )}\n\n        {/* Default Value - Type specific handling */}\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n            <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <input\n                    id={`field-default-value-${index}`}\n                    type={field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n            </div>\n        )}\n\n        {(field.type === 'checkbox' || field.type === 'switch') && (\n            <div className=\"form-group form-check\">\n                <input\n                    id={`field-default-value-${index}`}\n                    type=\"checkbox\"\n                    className=\"form-check-input\"\n                    checked={Boolean(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n                <label htmlFor={`field-default-value-${index}`} className=\"form-check-label\">Default Checked: </label>\n            </div>\n        )}\n\n        {['dropdown', 'radio'].includes(field.type) && field.options && field.options.length > 0 && (\n             <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <select\n                    id={`field-default-value-${index}`}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                >\n                    <option value=\"\">-- Select Default --</option>\n                    {field.options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}\n                </select>\n            </div>\n        )}\n\n        {field.type === 'checkbox-group' && (\n            <div className=\"form-group\">\n                <label className=\"form-label\">Default Values (comma-separated): </label>\n                <input\n                    type=\"text\"\n                    className=\"form-control\"\n                    value={Array.isArray(field.defaultValue) ? field.defaultValue.join(',') : ''}\n                    onChange={(e) => onUpdate(index, {...field, defaultValue: e.target.value.split(',').map(s => s.trim()).filter(s => s)})}\n                    placeholder=\"value1,value2\"\n                />\n            </div>\n        )}\n\n        {field.type === 'button' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-button-text-${index}`} className=\"form-label\">Button Text: </label>\n            <input\n              id={`field-button-text-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.buttonText || ''}\n              onChange={(e) => onUpdate(index, {...field, buttonText: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'section' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-section-title-${index}`} className=\"form-label\">Section Title: </label>\n            <input\n              id={`field-section-title-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.sectionTitle || ''}\n              onChange={(e) => onUpdate(index, {...field, sectionTitle: e.target.value})}\n            />\n          </div>\n        )}\n\n        {/* Required Checkbox (excluding button and section) */}\n        {!['button', 'section'].includes(field.type) && (\n          <div className=\"form-group form-check\">\n            <input\n              id={`field-required-${index}`}\n              type=\"checkbox\"\n              className=\"form-check-input\"\n              checked={!!field.required}\n              onChange={(e) => onUpdate(index, {...field, required: e.target.checked})}\n            />\n            <label htmlFor={`field-required-${index}`} className=\"form-check-label\"> Required</label>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FieldConfigItem;\n", "import React from 'react';\nimport { FaPlus, FaSave } from 'react-icons/fa';\nimport { FormField, PageConfig } from '../types/PageBuilderTypes';\nimport FieldConfigItem from './FieldConfigItem';\n\ninterface PageBuilderContentProps {\n  pageConfig: PageConfig;\n  fields: FormField[];\n  onAddField: () => void;\n  onUpdateField: (index: number, field: FormField) => void;\n  onRemoveField: (index: number) => void;\n  onSave: () => void;\n  onPreview: () => void;\n  loading: boolean;\n}\n\nconst PageBuilderContent: React.FC<PageBuilderContentProps> = ({\n  pageConfig,\n  fields,\n  onAddField,\n  onUpdateField,\n  onRemoveField,\n  onSave,\n  onPreview,\n  loading,\n}) => {\n  return (\n    <div className=\"builder-content\">\n      <h4>Page Configuration for: {pageConfig.title}</h4>\n      \n      <h5>Current Page Fields:</h5>\n      {fields.map((field, index) => (\n        <FieldConfigItem\n          key={field.id || index}\n          field={field}\n          index={index}\n          onUpdate={onUpdateField}\n          onRemove={onRemoveField}\n        />\n      ))}\n      \n      <button onClick={onAddField} className=\"btn btn-info mt-3\">\n        {React.createElement(FaPlus as React.ComponentType<any>)} Add Field\n      </button>\n      \n      <button \n        onClick={onSave} \n        className=\"btn btn-success mt-3 ms-2\" \n        disabled={loading || !pageConfig || fields.length === 0}\n      >\n        {React.createElement(FaSave as React.ComponentType<any>)} {loading ? 'Saving...' : 'Save Page Configuration'}\n      </button>\n      \n      <button \n        onClick={onPreview} \n        className=\"btn btn-secondary mt-3 ms-2\" \n        disabled={!pageConfig || fields.length === 0}\n      >\n        Preview Page\n      </button>\n    </div>\n  );\n};\n\nexport default PageBuilderContent;\n", "// Interfaces for PageBuilder component\n\nexport interface FormFieldOption {\n  label: string;\n  value: string;\n}\n\n// NOTE: This FormField interface is for the PageBuilder's internal state\n// and represents the configuration being built for a specific 'page'.\n// It's slightly different from the DynamicFormField used by the DynamicForm component.\nexport interface FormField {\n  id: string;\n  type: 'text' | 'textarea' | 'number' | 'date' | 'dropdown' | 'radio' | 'checkbox' | 'checkbox-group' | 'section' | 'button' | 'file' | 'switch';\n  label: string;\n  placeholder?: string;\n  region?: string;\n  division?: string;\n  office?: string;\n  options?: FormFieldOption[]; // For dropdown, radio, checkbox\n  required?: boolean;\n  value?: any; // Current value of the field (might not be used in builder, but kept for consistency)\n  // For section type\n  sectionTitle?: string;\n  columns?: number; // Number of columns for fields within the section\n  // For button type\n  buttonText?: string;\n  buttonType?: string;\n  onClickAction?: string;\n  defaultValue?: any;\n  min?: number;\n  max?: number;\n  [key: string]: any; // Add this index signature\n}\n\nexport interface PageConfig {\n  id: string;\n  title: string;\n  fields: FormField[];\n  lastUpdated: string;\n  isPage?: boolean; // New field\n  pageId?: string;\n  // Report configuration - updated to support both old single values and new arrays\n  selectedRegion?: string; // Keep for backward compatibility\n  selectedDivision?: string; // Keep for backward compatibility\n  selectedOffice?: string; // Keep for backward compatibility\n  selectedRegions?: string[]; // New array-based selections\n  selectedDivisions?: string[]; // New array-based selections\n  selectedOffices?: string[]; // New array-based selections\n  selectedFrequency?: string;\n}\n\nexport interface Category {\n  id: string;\n  title: string;\n  path: string; // e.g., /categories/parent-id/child-id\n  parentId: string | null;\n  children?: Category[];\n  icon?: string; // Icon name (e.g., 'FaFolder')\n  color?: string; // Color for the icon/card\n  fields?: FormField[]; // If storing form fields directly on category for some reason\n  lastUpdated?: string;\n  isPage: boolean; // New field\n  pageId: string; \n}\n\nexport interface PageBuilderState {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  fields: FormField[];\n  availableDynamicFields: any[];\n  isLoading: boolean;\n  loading: boolean;\n  error: string | null;\n  success: string | null;\n  isAddingNewCard: boolean;\n  newCardId: string;\n  newCardTitle: string;\n  showConfirmModal: boolean;\n  editingCard: Category | null;\n  showEditModal: boolean;\n  cardToDelete: string | null;\n  showDeleteConfirmModal: boolean;\n  actionType: string;\n  isPreviewOpen: boolean;\n  previewContent: string;\n  // New dropdown states - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n}\n\n// Report frequency options\nexport interface ReportFrequency {\n  value: string;\n  label: string;\n}\n\nexport const REPORT_FREQUENCIES: ReportFrequency[] = [\n  { value: 'daily', label: 'Daily' },\n  { value: 'weekly', label: 'Weekly' },\n  { value: 'monthly', label: 'Monthly' }\n];\n\n// Location hierarchy interfaces - matching Supabase table structure\nexport interface Region {\n  id: string;\n  name: string;\n}\n\nexport interface Division {\n  id: string;\n  name: string;\n  region: string; // matches the Region column in Supabase\n}\n\nexport interface Office {\n  id: string; // Now uses office name instead of facility ID\n  name: string;\n  region: string; // matches the Region column in Supabase\n  division: string; // matches the Division column in Supabase\n  facilityId?: string; // Keep facility ID for reference/mapping\n}\n\n// Supabase office record interface (matches actual table structure)\nexport interface SupabaseOfficeRecord {\n  'Facility ID': string;\n  Region: string;\n  Division: string;\n  'Office name': string;\n}\n", "import { useState, useEffect } from 'react';\nimport { supabase } from '../../../../config/supabaseClient';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n}\n\nexport const useOfficeDataSimple = (): UseOfficeDataReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataSimple: Fetching with enhanced pagination...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allData = await OfficeService.fetchAllOfficeData();\n\n      console.log('✅ useOfficeDataSimple: Fetched', allData.length, 'office records');\n\n      // Process regions exactly like SQL: SELECT DISTINCT \"Region\" FROM offices ORDER BY \"Region\"\n      const distinctRegions = allData\n        ?.map(row => row.Region)\n        .filter((region, index, array) => array.indexOf(region) === index)\n        .filter((region): region is string => region != null && region.trim() !== '') // Type guard to ensure string\n        .sort();\n\n      // Process regions successfully\n\n      const regionsArray: Region[] = distinctRegions?.map(regionName => ({\n        id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: regionName,\n      })) || [];\n\n      // Process divisions exactly like SQL: SELECT DISTINCT \"Region\", \"Division\" FROM offices ORDER BY \"Region\", \"Division\"\n      const distinctDivisions = allData\n        ?.map(row => ({ region: row.Region, division: row.Division }))\n        .filter((item, index, array) =>\n          array.findIndex(x => x.region === item.region && x.division === item.division) === index\n        )\n        .filter((item): item is { region: string; division: string } =>\n          item.region != null && item.division != null &&\n          item.region.trim() !== '' && item.division.trim() !== ''\n        )\n        .sort((a, b) => a.region.localeCompare(b.region) || a.division.localeCompare(b.division));\n\n      const divisionsArray: Division[] = distinctDivisions?.map(item => ({\n        id: item.division.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: item.division,\n        region: item.region,\n      })) || [];\n\n      // Process all offices - USE OFFICE NAME AS ID instead of Facility ID\n      const officesArray: Office[] = allData\n        ?.filter(row => row['Office name'] && row.Region && row.Division)\n        .map(row => ({\n          id: row['Office name'], // ✅ FIXED: Use office name as ID for form targeting\n          name: row['Office name'],\n          region: row.Region || '',\n          division: row.Division || '',\n          facilityId: row['Office name'], // Use office name as facility ID for consistency\n        })) || [];\n\n      // Data processing completed successfully\n\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n\n    } catch (err) {\n      console.error('🚨 SIMPLE: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n  };\n};\n", "import React, { useState, useRef, useEffect } from 'react';\n\ninterface Option {\n  id: string;\n  name: string;\n}\n\ninterface CheckboxDropdownProps {\n  id: string;\n  label: string;\n  options: Option[];\n  selectedValues: string[];\n  onChange: (values: string[]) => void;\n  disabled?: boolean;\n  placeholder?: string;\n}\n\nconst CheckboxDropdown: React.FC<CheckboxDropdownProps> = ({\n  id,\n  label,\n  options,\n  selectedValues,\n  onChange,\n  disabled = false,\n  placeholder = \"-- Select Options --\"\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleCheckboxChange = (optionId: string) => {\n    if (selectedValues.includes(optionId)) {\n      // Remove from selection\n      onChange(selectedValues.filter(id => id !== optionId));\n    } else {\n      // Add to selection\n      onChange([...selectedValues, optionId]);\n    }\n  };\n\n  const handleSelectAll = () => {\n    if (selectedValues.length === options.length) {\n      // Deselect all\n      onChange([]);\n    } else {\n      // Select all\n      onChange(options.map(option => option.id));\n    }\n  };\n\n  const getDisplayText = () => {\n    if (selectedValues.length === 0) {\n      return placeholder;\n    } else if (selectedValues.length === 1) {\n      const selectedOption = options.find(option => option.id === selectedValues[0]);\n      return selectedOption?.name || placeholder;\n    } else {\n      return `${selectedValues.length} selected`;\n    }\n  };\n\n  const isAllSelected = selectedValues.length === options.length && options.length > 0;\n  const isIndeterminate = selectedValues.length > 0 && selectedValues.length < options.length;\n\n  return (\n    <div className=\"form-group\">\n      <label htmlFor={id} className=\"form-label\">{label}:</label>\n      <div className=\"dropdown\" ref={dropdownRef}>\n        <button\n          id={id}\n          className={`btn btn-outline-secondary dropdown-toggle w-100 text-start ${disabled ? 'disabled' : ''}`}\n          type=\"button\"\n          onClick={() => !disabled && setIsOpen(!isOpen)}\n          disabled={disabled}\n          style={{ \n            backgroundColor: disabled ? '#e9ecef' : 'white',\n            borderColor: '#ced4da'\n          }}\n        >\n          <span className={selectedValues.length === 0 ? 'text-muted' : ''}>\n            {getDisplayText()}\n          </span>\n        </button>\n        \n        {isOpen && !disabled && (\n          <div className=\"dropdown-menu show w-100\" style={{ maxHeight: '300px', overflowY: 'auto' }}>\n            {/* Select All Option */}\n            {options.length > 1 && (\n              <>\n                <div className=\"dropdown-item\">\n                  <div className=\"form-check\">\n                    <input\n                      className=\"form-check-input\"\n                      type=\"checkbox\"\n                      id={`${id}-select-all`}\n                      checked={isAllSelected}\n                      ref={(input) => {\n                        if (input) input.indeterminate = isIndeterminate;\n                      }}\n                      onChange={handleSelectAll}\n                    />\n                    <label className=\"form-check-label fw-bold\" htmlFor={`${id}-select-all`}>\n                      Select All ({options.length})\n                    </label>\n                  </div>\n                </div>\n                <hr className=\"dropdown-divider\" />\n              </>\n            )}\n            \n            {/* Individual Options */}\n            {options.map(option => (\n              <div key={option.id} className=\"dropdown-item\">\n                <div className=\"form-check\">\n                  <input\n                    className=\"form-check-input\"\n                    type=\"checkbox\"\n                    id={`${id}-${option.id}`}\n                    checked={selectedValues.includes(option.id)}\n                    onChange={() => handleCheckboxChange(option.id)}\n                  />\n                  <label className=\"form-check-label\" htmlFor={`${id}-${option.id}`}>\n                    {option.name}\n                  </label>\n                </div>\n              </div>\n            ))}\n            \n            {options.length === 0 && (\n              <div className=\"dropdown-item text-muted\">\n                <em>No options available</em>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n      \n      {/* Selected count indicator */}\n      {selectedValues.length > 0 && (\n        <small className=\"text-muted mt-1 d-block\">\n          {selectedValues.length} of {options.length} selected\n        </small>\n      )}\n    </div>\n  );\n};\n\nexport default CheckboxDropdown;\n", "import React, { useEffect } from 'react';\nimport { REPORT_FREQUENCIES } from '../types/PageBuilderTypes';\nimport { useOfficeDataSimple as useOfficeData } from '../hooks/useOfficeDataSimple';\nimport CheckboxDropdown from './CheckboxDropdown';\n\ninterface ReportConfigurationProps {\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  onRegionsChange: (regions: string[]) => void;\n  onDivisionsChange: (divisions: string[]) => void;\n  onOfficesChange: (offices: string[]) => void;\n  onFrequencyChange: (frequency: string) => void;\n}\n\nconst ReportConfiguration: React.FC<ReportConfigurationProps> = ({\n  selectedRegions,\n  selectedDivisions,\n  selectedOffices,\n  selectedFrequency,\n  onRegionsChange,\n  onDivisionsChange,\n  onOfficesChange,\n  onFrequencyChange,\n}) => {\n  // Use custom hook to fetch office data from Supabase\n  const { regions, divisions, offices, loading, error, refetch } = useOfficeData();\n\n  // Filter divisions based on selected regions\n  const selectedRegionNames = selectedRegions.map(regionId =>\n    regions.find(r => r.id === regionId)?.name\n  ).filter(Boolean);\n\n  const availableDivisions = selectedRegions.length > 0\n    ? divisions.filter(division => selectedRegionNames.includes(division.region))\n    : divisions; // Show all divisions if no regions selected\n\n  // Filter offices based on selected divisions\n  const selectedDivisionNames = selectedDivisions.map(divisionId =>\n    divisions.find(d => d.id === divisionId)?.name\n  ).filter(Boolean);\n\n  const availableOffices = selectedDivisions.length > 0\n    ? offices.filter(office =>\n        selectedRegionNames.includes(office.region) &&\n        selectedDivisionNames.includes(office.division)\n      )\n    : selectedRegions.length > 0\n      ? offices.filter(office => selectedRegionNames.includes(office.region))\n      : offices; // Show all offices if no filters applied\n\n  // Reset dependent selections when parent selections change\n  useEffect(() => {\n    if (selectedRegions.length > 0) {\n      // Remove divisions that don't belong to selected regions\n      const validDivisions = selectedDivisions.filter(divisionId => {\n        const division = divisions.find(d => d.id === divisionId);\n        return division && selectedRegionNames.includes(division.region);\n      });\n\n      if (validDivisions.length !== selectedDivisions.length) {\n        onDivisionsChange(validDivisions);\n      }\n    }\n  }, [selectedRegions, selectedDivisions, divisions, selectedRegionNames, onDivisionsChange]);\n\n  useEffect(() => {\n    if (selectedDivisions.length > 0) {\n      // Remove offices that don't belong to selected regions/divisions\n      const validOffices = selectedOffices.filter(officeId => {\n        const office = offices.find(o => o.id === officeId);\n        return office &&\n               selectedRegionNames.includes(office.region) &&\n               selectedDivisionNames.includes(office.division);\n      });\n\n      if (validOffices.length !== selectedOffices.length) {\n        onOfficesChange(validOffices);\n      }\n    }\n  }, [selectedDivisions, selectedOffices, offices, selectedRegionNames, selectedDivisionNames, onOfficesChange]);\n\n  return (\n    <div className=\"report-configuration mt-3 mb-3\">\n      <h5>Report Configuration</h5>\n\n      {loading && (\n        <div className=\"alert alert-info\">\n          <div className=\"d-flex align-items-center\">\n            <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n            Loading office data...\n          </div>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"alert alert-danger\">\n          <strong>Error:</strong> {error}\n          <button\n            className=\"btn btn-sm btn-outline-danger ms-2\"\n            onClick={refetch}\n          >\n            Retry\n          </button>\n        </div>\n      )}\n\n      {!loading && !error && (\n        <div className=\"row\">\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"region-select\"\n              label=\"Select Regions\"\n              options={regions}\n              selectedValues={selectedRegions}\n              onChange={onRegionsChange}\n              disabled={loading}\n              placeholder=\"-- Select Regions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"division-select\"\n              label=\"Select Divisions\"\n              options={availableDivisions}\n              selectedValues={selectedDivisions}\n              onChange={onDivisionsChange}\n              disabled={selectedRegions.length === 0 || loading}\n              placeholder=\"-- Select Divisions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"office-select\"\n              label=\"Select Offices\"\n              options={availableOffices}\n              selectedValues={selectedOffices}\n              onChange={onOfficesChange}\n              disabled={selectedDivisions.length === 0 || loading}\n              placeholder=\"-- Select Offices --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <div className=\"form-group\">\n              <label htmlFor=\"frequency-select\" className=\"form-label\">\n                Report Frequency: <span className=\"text-danger\">*</span>\n              </label>\n              <select\n                id=\"frequency-select\"\n                className={`form-select ${!selectedFrequency ? 'is-invalid' : ''}`}\n                value={selectedFrequency}\n                onChange={(e) => onFrequencyChange(e.target.value)}\n                disabled={loading}\n                required\n              >\n                <option value=\"\">-- Select Frequency --</option>\n                {REPORT_FREQUENCIES.map(frequency => (\n                  <option key={frequency.value} value={frequency.value}>\n                    {frequency.label}\n                  </option>\n                ))}\n              </select>\n              {!selectedFrequency && (\n                <div className=\"invalid-feedback\">\n                  Report frequency is required.\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ReportConfiguration;\n", "/**\n * Utility functions for persisting dropdown selections in localStorage\n * This helps maintain selected values across component re-renders and page refreshes\n */\n\ninterface DropdownSelections {\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n}\n\nconst STORAGE_KEY = 'admin_dropdown_selections';\n\n/**\n * Save dropdown selections to localStorage\n */\nexport const saveDropdownSelections = (selections: DropdownSelections): void => {\n  try {\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(selections));\n  } catch (error) {\n    console.warn('Failed to save dropdown selections to localStorage:', error);\n  }\n};\n\n/**\n * Load dropdown selections from localStorage\n */\nexport const loadDropdownSelections = (): DropdownSelections | null => {\n  try {\n    const stored = localStorage.getItem(STORAGE_KEY);\n    if (stored) {\n      return JSON.parse(stored);\n    }\n  } catch (error) {\n    console.warn('Failed to load dropdown selections from localStorage:', error);\n  }\n  return null;\n};\n\n/**\n * Clear dropdown selections from localStorage\n */\nexport const clearDropdownSelections = (): void => {\n  try {\n    localStorage.removeItem(STORAGE_KEY);\n  } catch (error) {\n    console.warn('Failed to clear dropdown selections from localStorage:', error);\n  }\n};\n\n/**\n * Check if selections have changed\n */\nexport const hasSelectionsChanged = (\n  current: DropdownSelections,\n  previous: DropdownSelections\n): boolean => {\n  return (\n    JSON.stringify(current.selectedRegions) !== JSON.stringify(previous.selectedRegions) ||\n    JSON.stringify(current.selectedDivisions) !== JSON.stringify(previous.selectedDivisions) ||\n    JSON.stringify(current.selectedOffices) !== JSON.stringify(previous.selectedOffices) ||\n    current.selectedFrequency !== previous.selectedFrequency\n  );\n};\n", "import React, { useEffect } from 'react';\nimport Modal from '../../shared/Modal';\nimport './PageBuilder.css';\n\n// Import refactored components and hooks\nimport { usePageBuilderState } from './hooks/usePageBuilderState';\nimport { useCardManagement } from './hooks/useCardManagement';\nimport { usePageConfiguration } from './hooks/usePageConfiguration';\nimport CardSelector from './components/CardSelector';\nimport CardManagement from './components/CardManagement';\nimport PageBuilderContent from './components/PageBuilderContent';\nimport ReportConfiguration from './components/ReportConfiguration';\n// Debug components removed - Supabase integration working\nimport { isLeafCard, isMainCard } from './utils/cardUtils';\nimport { saveDropdownSelections, loadDropdownSelections } from './utils/dropdownPersistence';\n\n// All interfaces and utilities are now imported from separate files\n\nconst PageBuilder: React.FC = () => {\n  // Use custom hooks for state management\n  const state = usePageBuilderState();\n\n  // Debug mode removed - using working SQL-based implementation\n\n  // Initialize custom hooks\n  const cardManagement = useCardManagement({\n    categories: state.categories,\n    setCategories: state.setCategories,\n    selectedCard: state.selectedCard,\n    setSelectedCard: state.setSelectedCard,\n    newCardId: state.newCardId,\n    setNewCardId: state.setNewCardId,\n    newCardTitle: state.newCardTitle,\n    setNewCardTitle: state.setNewCardTitle,\n    actionType: state.actionType,\n    setActionType: state.setActionType,\n    setIsLoading: state.setIsLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setShowConfirmModal: state.setShowConfirmModal,\n    setIsAddingNewCard: state.setIsAddingNewCard,\n    setPageConfig: state.setPageConfig,\n    setFields: state.setFields,\n    setEditingCard: state.setEditingCard,\n    setShowEditModal: state.setShowEditModal,\n    setCardToDelete: state.setCardToDelete,\n    setShowDeleteConfirmModal: state.setShowDeleteConfirmModal,\n  });\n\n  const pageConfiguration = usePageConfiguration({\n    categories: state.categories,\n    selectedCard: state.selectedCard,\n    pageConfig: state.pageConfig,\n    setPageConfig: state.setPageConfig,\n    fields: state.fields,\n    setFields: state.setFields,\n    setAvailableDynamicFields: state.setAvailableDynamicFields,\n    setLoading: state.setLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setPreviewContent: state.setPreviewContent,\n    setIsPreviewOpen: state.setIsPreviewOpen,\n    selectedRegions: state.selectedRegions,\n    selectedDivisions: state.selectedDivisions,\n    selectedOffices: state.selectedOffices,\n    selectedFrequency: state.selectedFrequency,\n    setSelectedRegions: state.setSelectedRegions,\n    setSelectedDivisions: state.setSelectedDivisions,\n    setSelectedOffices: state.setSelectedOffices,\n    setSelectedFrequency: state.setSelectedFrequency,\n  });\n\n  // Initialize data on component mount\n  useEffect(() => {\n    cardManagement.fetchCategories();\n\n    // Load saved dropdown selections from localStorage\n    const savedSelections = loadDropdownSelections();\n    if (savedSelections) {\n      if (savedSelections.selectedRegions.length > 0) {\n        state.setSelectedRegions(savedSelections.selectedRegions);\n      }\n      if (savedSelections.selectedDivisions.length > 0) {\n        state.setSelectedDivisions(savedSelections.selectedDivisions);\n      }\n      if (savedSelections.selectedOffices.length > 0) {\n        state.setSelectedOffices(savedSelections.selectedOffices);\n      }\n      if (savedSelections.selectedFrequency) {\n        state.setSelectedFrequency(savedSelections.selectedFrequency);\n      }\n    }\n  }, []);\n\n  // Save dropdown selections to localStorage whenever they change\n  useEffect(() => {\n    const selections = {\n      selectedRegions: state.selectedRegions,\n      selectedDivisions: state.selectedDivisions,\n      selectedOffices: state.selectedOffices,\n      selectedFrequency: state.selectedFrequency,\n    };\n\n    // Only save if at least one selection is made\n    if (selections.selectedRegions.length > 0 ||\n        selections.selectedDivisions.length > 0 ||\n        selections.selectedOffices.length > 0 ||\n        selections.selectedFrequency) {\n      saveDropdownSelections(selections);\n    }\n  }, [state.selectedRegions, state.selectedDivisions, state.selectedOffices, state.selectedFrequency]);\n\n  // Handle card and action changes\n  useEffect(() => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.actionType === 'createWebPage') {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n      pageConfiguration.fetchDynamicFormFields(state.selectedCard);\n    } else if (state.selectedCard && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && state.actionType === 'createWebPage') {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n    } else if (!state.selectedCard) {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n      state.setActionType('');\n    }\n\n    if (state.selectedCard && state.actionType !== 'createWebPage') {\n        state.setAvailableDynamicFields([]);\n    }\n  }, [state.selectedCard, state.categories, state.actionType]);\n\n  // Event handlers for UI interactions\n  const handleCardChange = (cardId: string) => {\n    state.setSelectedCard(cardId);\n    state.setActionType('');\n    if (!cardId) {\n        state.setPageConfig(null);\n        state.setFields([]);\n    } else {\n        const cardIsLeaf = isLeafCard(cardId, state.categories);\n        const cardIsMain = isMainCard(cardId, state.categories);\n        if(!cardIsLeaf || cardIsMain) {\n            state.setPageConfig(null);\n            state.setFields([]);\n        }\n    }\n  };\n\n  const handleActionChange = (action: string) => {\n    state.setActionType(action);\n  };\n\n  const handleCreateAction = () => {\n    state.setNewCardId('');\n    state.setNewCardTitle('');\n    state.setIsAddingNewCard(true);\n  };\n\n  const handleWebPageAction = () => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories)) {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n    } else if (state.selectedCard) {\n      state.setError('Web page can only be created/edited for a final nested report (not a main report).');\n      state.setPageConfig(null);\n      state.setFields([]);\n    }\n  };\n\n  // Event handlers for report configuration dropdowns - updated for arrays\n  const handleRegionsChange = (regions: string[]) => {\n    const previousRegions = state.selectedRegions;\n    state.setSelectedRegions(regions);\n\n    // Only reset dependent dropdowns if regions actually changed\n    // This prevents losing selections when component re-renders\n    if (JSON.stringify(regions) !== JSON.stringify(previousRegions)) {\n      state.setSelectedDivisions([]);\n      state.setSelectedOffices([]);\n    }\n  };\n\n  const handleDivisionsChange = (divisions: string[]) => {\n    const previousDivisions = state.selectedDivisions;\n    state.setSelectedDivisions(divisions);\n\n    // Only reset dependent dropdown if divisions actually changed\n    // This prevents losing selections when component re-renders\n    if (JSON.stringify(divisions) !== JSON.stringify(previousDivisions)) {\n      state.setSelectedOffices([]);\n    }\n  };\n\n  const handleOfficesChange = (offices: string[]) => {\n    state.setSelectedOffices(offices);\n  };\n\n  const handleFrequencyChange = (frequency: string) => {\n    state.setSelectedFrequency(frequency);\n  };\n\n  // All card management functions are now handled by the useCardManagement hook\n\n  // All page builder functions are now handled by the usePageConfiguration hook\n\n  // All rendering functions are now handled by separate components\n  \n  // All field rendering is now handled by the FieldConfigItem component\n\n  return (\n    <>\n      <div className=\"page-builder\">\n        {state.error && <div className=\"error-message\">{state.error}</div>}\n        {state.success && (\n          <div className=\"success-message\">\n            {state.success}\n          </div>\n        )}\n        <h2>Report & Page Builder</h2>\n\n        <CardSelector\n          categories={state.categories}\n          selectedCard={state.selectedCard}\n          onCardChange={handleCardChange}\n          actionType={state.actionType}\n          onActionChange={handleActionChange}\n          isLoading={state.isLoading}\n          onCreateAction={handleCreateAction}\n          onWebPageAction={handleWebPageAction}\n        />\n\n        {/* Modal for adding/creating new card */}\n        {state.isAddingNewCard && (\n          <Modal\n            isOpen={state.isAddingNewCard}\n            onClose={() => {\n              state.setIsAddingNewCard(false);\n              state.setActionType('');\n              state.setNewCardId('');\n              state.setNewCardTitle('');\n            }}\n            title={\n              state.actionType === 'addNewCardGlobal' ? \"Create New Main Report\" :\n              state.selectedCard && state.actionType === 'createNestedCard' ? `Add Nested Report under \"${state.categories.find(c => c.id === state.selectedCard)?.title}\"` :\n              \"Create New Report\"\n            }\n          >\n            <div className=\"new-card-form\">\n              <input\n                type=\"text\"\n                placeholder=\"Report ID (e.g., 'new-report-id')\"\n                value={state.newCardId}\n                onChange={(e) => state.setNewCardId(e.target.value.toLowerCase().replace(/\\s+/g, '-'))}\n                className=\"form-control mb-2\"\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Report Title\"\n                value={state.newCardTitle}\n                onChange={(e) => state.setNewCardTitle(e.target.value)}\n                className=\"form-control mb-2\"\n              />\n              <div className=\"form-buttons modal-buttons\">\n                <button\n                  onClick={cardManagement.handleConfirmCreate}\n                  disabled={state.isLoading || !state.newCardId || !state.newCardTitle}\n                  className=\"btn btn-primary\"\n                >\n                  {state.isLoading ? 'Creating...' : 'Confirm & Create Report'}\n                </button>\n                <button onClick={() => {\n                  state.setIsAddingNewCard(false);\n                  state.setActionType('');\n                  state.setNewCardId('');\n                  state.setNewCardTitle('');\n                }} className=\"btn btn-secondary\">\n                  Cancel\n                </button>\n              </div>\n            </div>\n          </Modal>\n        )}\n\n        {/* Conditional Rendering for Card Management OR Page Builder OR Warnings */}\n        {state.selectedCard && (\n          <>\n            {/* Card Management Section */}\n            {!(state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig) && (\n              <CardManagement\n                selectedCard={state.selectedCard}\n                categories={state.categories}\n                onEditCard={cardManagement.handleEditCard}\n                onDeleteCard={cardManagement.handleDeleteClick}\n              />\n            )}\n\n            {/* Report Configuration Dropdowns */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && (\n              <ReportConfiguration\n                selectedRegions={state.selectedRegions}\n                selectedDivisions={state.selectedDivisions}\n                selectedOffices={state.selectedOffices}\n                selectedFrequency={state.selectedFrequency}\n                onRegionsChange={handleRegionsChange}\n                onDivisionsChange={handleDivisionsChange}\n                onOfficesChange={handleOfficesChange}\n                onFrequencyChange={handleFrequencyChange}\n              />\n            )}\n\n            {/* Page Builder Content */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig && (\n              <PageBuilderContent\n                pageConfig={state.pageConfig}\n                fields={state.fields}\n                onAddField={pageConfiguration.addField}\n                onUpdateField={pageConfiguration.updateField}\n                onRemoveField={pageConfiguration.removeField}\n                onSave={pageConfiguration.handleSave}\n                onPreview={pageConfiguration.handlePreview}\n                loading={state.loading}\n              />\n            )}\n\n            {/* Warning Messages */}\n            {state.actionType === 'createWebPage' && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && (\n              <div className=\"warning-message mt-3 p-2 bg-warning text-dark rounded\">\n                Page configuration is only available for final nested reports (which are not main reports). Please select an appropriate nested report to configure its page, or create one.\n              </div>\n            )}\n            {state.actionType !== 'createWebPage' && !isLeafCard(state.selectedCard, state.categories) && (\n              <div className=\"info-message mt-3 p-2 bg-info text-dark rounded\">\n                This is a parent report. You can create nested reports under it or select an existing nested report to manage or configure its page.\n              </div>\n            )}\n          </>\n        )}\n\n        {!state.selectedCard && state.actionType === '' && (\n          <div className=\"info-message mt-3 p-3 bg-light border rounded\">\n            <p>Select a report from the dropdown to manage it or configure its web page (if applicable).</p>\n            <p>If no reports exist, or to create a new top-level report, choose \"Create New Main Report\" from the action dropdown after clearing any selection.</p>\n          </div>\n        )}\n\n        {/* Modals for Edit and Delete Confirmation */}\n        {state.showEditModal && state.editingCard && (\n          <Modal\n            isOpen={state.showEditModal}\n            onClose={() => {\n              state.setShowEditModal(false);\n              state.setNewCardTitle('');\n              state.setEditingCard(null);\n            }}\n            title={`Edit Report: ${state.editingCard.title}`}\n          >\n            <input\n              type=\"text\"\n              value={state.newCardTitle}\n              onChange={(e) => state.setNewCardTitle(e.target.value)}\n              placeholder=\"New Report Title\"\n              className=\"form-control mb-2\"\n            />\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleUpdateCard}\n                className=\"btn btn-primary\"\n                disabled={state.isLoading || !state.newCardTitle.trim()}\n              >\n                {state.isLoading ? 'Updating...' : 'Update Title'}\n              </button>\n              <button\n                onClick={() => {\n                  state.setShowEditModal(false);\n                  state.setNewCardTitle('');\n                  state.setEditingCard(null);\n                }}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {state.showDeleteConfirmModal && state.cardToDelete && (\n          <Modal\n            isOpen={state.showDeleteConfirmModal}\n            onClose={() => state.setShowDeleteConfirmModal(false)}\n            title=\"Confirm Deletion\"\n          >\n            <p>Are you sure you want to delete the report \"{state.categories.find(c => c.id === state.cardToDelete)?.title}\" and ALL its nested reports and associated page configurations? This action cannot be undone.</p>\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleConfirmDelete}\n                className=\"btn btn-danger\"\n                disabled={state.isLoading}\n              >\n                {state.isLoading ? 'Deleting...' : 'Confirm Delete'}\n              </button>\n              <button\n                onClick={() => state.setShowDeleteConfirmModal(false)}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {/* Preview Modal */}\n        <Modal\n          isOpen={state.isPreviewOpen}\n          onClose={() => state.setIsPreviewOpen(false)}\n          title=\"Page Preview\"\n        >\n          <div dangerouslySetInnerHTML={{ __html: state.previewContent }} />\n        </Modal>\n      </div>\n    </>\n  );\n};\n\nexport default PageBuilder;\n\n\n", "import { useState } from 'react';\nimport { Category, FormField, PageConfig } from '../types/PageBuilderTypes';\nimport { FormField as DynamicFormField } from '../../../shared/DynamicForm';\n\nexport const usePageBuilderState = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [selectedCard, setSelectedCard] = useState<string>('');\n  const [pageConfig, setPageConfig] = useState<PageConfig | null>(null);\n  const [fields, setFields] = useState<FormField[]>([]);\n  const [availableDynamicFields, setAvailableDynamicFields] = useState<DynamicFormField[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const [isAddingNewCard, setIsAddingNewCard] = useState<boolean>(false);\n  const [newCardId, setNewCardId] = useState<string>('');\n  const [newCardTitle, setNewCardTitle] = useState<string>('');\n  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);\n\n  const [editingCard, setEditingCard] = useState<Category | null>(null);\n  const [showEditModal, setShowEditModal] = useState<boolean>(false);\n\n  const [cardToDelete, setCardToDelete] = useState<string | null>(null);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<boolean>(false);\n\n  const [actionType, setActionType] = useState<string>('');\n\n  // State for Preview Modal\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false);\n  const [previewContent, setPreviewContent] = useState('');\n\n  // New dropdown states - updated to arrays for multiple selections\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\n  const [selectedDivisions, setSelectedDivisions] = useState<string[]>([]);\n  const [selectedOffices, setSelectedOffices] = useState<string[]>([]);\n  const [selectedFrequency, setSelectedFrequency] = useState<string>('');\n\n  return {\n    // State values\n    categories,\n    selectedCard,\n    pageConfig,\n    fields,\n    availableDynamicFields,\n    isLoading,\n    loading,\n    error,\n    success,\n    isAddingNewCard,\n    newCardId,\n    newCardTitle,\n    showConfirmModal,\n    editingCard,\n    showEditModal,\n    cardToDelete,\n    showDeleteConfirmModal,\n    actionType,\n    isPreviewOpen,\n    previewContent,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n\n    // State setters\n    setCategories,\n    setSelectedCard,\n    setPageConfig,\n    setFields,\n    setAvailableDynamicFields,\n    setIsLoading,\n    setLoading,\n    setError,\n    setSuccess,\n    setIsAddingNewCard,\n    setNewCardId,\n    setNewCardTitle,\n    setShowConfirmModal,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n    setActionType,\n    setIsPreviewOpen,\n    setPreviewContent,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  };\n};\n", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemUtilityClass(slot) {\n  return generateUtilityClass('MuiListItem', slot);\n}\nconst listItemClasses = generateUtilityClasses('MuiListItem', ['root', 'container', 'dense', 'alignItemsFlexStart', 'divider', 'gutters', 'padding', 'secondaryAction']);\nexport default listItemClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemButton', slot);\n}\nconst listItemButtonClasses = generateUtilityClasses('MuiListItemButton', ['root', 'focusVisible', 'dense', 'alignItemsFlexStart', 'disabled', 'divider', 'gutters', 'selected']);\nexport default listItemButtonClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemSecondaryActionClassesUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemSecondaryAction', slot);\n}\nconst listItemSecondaryActionClasses = generateUtilityClasses('MuiListItemSecondaryAction', ['root', 'disableGutters']);\nexport default listItemSecondaryActionClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemSecondaryActionClassesUtilityClass } from \"./listItemSecondaryActionClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disableGutters,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getListItemSecondaryActionClassesUtilityClass, classes);\n};\nconst ListItemSecondaryActionRoot = styled('div', {\n  name: 'MuiListItemSecondaryAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.disableGutters && styles.disableGutters];\n  }\n})({\n  position: 'absolute',\n  right: 16,\n  top: '50%',\n  transform: 'translateY(-50%)',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.disableGutters,\n    style: {\n      right: 0\n    }\n  }]\n});\n\n/**\n * Must be used as the last child of ListItem to function properly.\n *\n * @deprecated Use the `secondaryAction` prop in the `ListItem` component instead. This component will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n */\nconst ListItemSecondaryAction = /*#__PURE__*/React.forwardRef(function ListItemSecondaryAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemSecondaryAction'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const ownerState = {\n    ...props,\n    disableGutters: context.disableGutters\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemSecondaryActionRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemSecondaryAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or selection control.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nListItemSecondaryAction.muiName = 'ListItemSecondaryAction';\nexport default ListItemSecondaryAction;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport isMuiElement from \"../utils/isMuiElement.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemUtilityClass } from \"./listItemClasses.js\";\nimport { listItemButtonClasses } from \"../ListItemButton/index.js\";\nimport ListItemSecondaryAction from \"../ListItemSecondaryAction/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.dense && styles.dense, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart, ownerState.divider && styles.divider, !ownerState.disableGutters && styles.gutters, !ownerState.disablePadding && styles.padding, ownerState.hasSecondaryAction && styles.secondaryAction];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes,\n    dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  } = ownerState;\n  const slots = {\n    root: ['root', dense && 'dense', !disableGutters && 'gutters', !disablePadding && 'padding', divider && 'divider', alignItems === 'flex-start' && 'alignItemsFlexStart', hasSecondaryAction && 'secondaryAction'],\n    container: ['container']\n  };\n  return composeClasses(slots, getListItemUtilityClass, classes);\n};\nexport const ListItemRoot = styled('div', {\n  name: 'MuiListItem',\n  slot: 'Root',\n  overridesResolver\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'flex-start',\n  alignItems: 'center',\n  position: 'relative',\n  textDecoration: 'none',\n  width: '100%',\n  boxSizing: 'border-box',\n  textAlign: 'left',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding,\n    style: {\n      paddingTop: 8,\n      paddingBottom: 8\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && ownerState.dense,\n    style: {\n      paddingTop: 4,\n      paddingBottom: 4\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !ownerState.disableGutters,\n    style: {\n      paddingLeft: 16,\n      paddingRight: 16\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !!ownerState.secondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.secondaryAction,\n    style: {\n      [`& > .${listItemButtonClasses.root}`]: {\n        paddingRight: 48\n      }\n    }\n  }, {\n    props: {\n      alignItems: 'flex-start'\n    },\n    style: {\n      alignItems: 'flex-start'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.divider,\n    style: {\n      borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,\n      backgroundClip: 'padding-box'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.button,\n    style: {\n      transition: theme.transitions.create('background-color', {\n        duration: theme.transitions.duration.shortest\n      }),\n      '&:hover': {\n        textDecoration: 'none',\n        backgroundColor: (theme.vars || theme).palette.action.hover,\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: 'transparent'\n        }\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hasSecondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }]\n})));\nconst ListItemContainer = styled('li', {\n  name: 'MuiListItem',\n  slot: 'Container'\n})({\n  position: 'relative'\n});\n\n/**\n * Uses an additional container component if `ListItemSecondaryAction` is the last child.\n */\nconst ListItem = /*#__PURE__*/React.forwardRef(function ListItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItem'\n  });\n  const {\n    alignItems = 'center',\n    children: childrenProp,\n    className,\n    component: componentProp,\n    components = {},\n    componentsProps = {},\n    ContainerComponent = 'li',\n    ContainerProps: {\n      className: ContainerClassName,\n      ...ContainerProps\n    } = {},\n    dense = false,\n    disableGutters = false,\n    disablePadding = false,\n    divider = false,\n    secondaryAction,\n    slotProps = {},\n    slots = {},\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const childContext = React.useMemo(() => ({\n    dense: dense || context.dense || false,\n    alignItems,\n    disableGutters\n  }), [alignItems, context.dense, dense, disableGutters]);\n  const listItemRef = React.useRef(null);\n  const children = React.Children.toArray(childrenProp);\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  const hasSecondaryAction = children.length && isMuiElement(children[children.length - 1], ['ListItemSecondaryAction']);\n  const ownerState = {\n    ...props,\n    alignItems,\n    dense: childContext.dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleRef = useForkRef(listItemRef, ref);\n  const Root = slots.root || components.Root || ListItemRoot;\n  const rootProps = slotProps.root || componentsProps.root || {};\n  const componentProps = {\n    className: clsx(classes.root, rootProps.className, className),\n    ...other\n  };\n  let Component = componentProp || 'li';\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  if (hasSecondaryAction) {\n    // Use div by default.\n    Component = !componentProps.component && !componentProp ? 'div' : Component;\n\n    // Avoid nesting of li > li.\n    if (ContainerComponent === 'li') {\n      if (Component === 'li') {\n        Component = 'div';\n      } else if (componentProps.component === 'li') {\n        componentProps.component = 'div';\n      }\n    }\n    return /*#__PURE__*/_jsx(ListContext.Provider, {\n      value: childContext,\n      children: /*#__PURE__*/_jsxs(ListItemContainer, {\n        as: ContainerComponent,\n        className: clsx(classes.container, ContainerClassName),\n        ref: handleRef,\n        ownerState: ownerState,\n        ...ContainerProps,\n        children: [/*#__PURE__*/_jsx(Root, {\n          ...rootProps,\n          ...(!isHostComponent(Root) && {\n            as: Component,\n            ownerState: {\n              ...ownerState,\n              ...rootProps.ownerState\n            }\n          }),\n          ...componentProps,\n          children: children\n        }), children.pop()]\n      })\n    });\n  }\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsxs(Root, {\n      ...rootProps,\n      as: Component,\n      ref: handleRef,\n      ...(!isHostComponent(Root) && {\n        ownerState: {\n          ...ownerState,\n          ...rootProps.ownerState\n        }\n      }),\n      ...componentProps,\n      children: [children, secondaryAction && /*#__PURE__*/_jsx(ListItemSecondaryAction, {\n        children: secondaryAction\n      })]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Defines the `align-items` style property.\n   * @default 'center'\n   */\n  alignItems: PropTypes.oneOf(['center', 'flex-start']),\n  /**\n   * The content of the component if a `ListItemSecondaryAction` is used it must\n   * be the last child.\n   */\n  children: chainPropTypes(PropTypes.node, props => {\n    const children = React.Children.toArray(props.children);\n\n    // React.Children.toArray(props.children).findLastIndex(isListItemSecondaryAction)\n    let secondaryActionIndex = -1;\n    for (let i = children.length - 1; i >= 0; i -= 1) {\n      const child = children[i];\n      if (isMuiElement(child, ['ListItemSecondaryAction'])) {\n        secondaryActionIndex = i;\n        break;\n      }\n    }\n\n    //  is ListItemSecondaryAction the last child of ListItem\n    if (secondaryActionIndex !== -1 && secondaryActionIndex !== children.length - 1) {\n      return new Error('MUI: You used an element after ListItemSecondaryAction. ' + 'For ListItem to detect that it has a secondary action ' + 'you must pass it as the last child to ListItem.');\n    }\n    return null;\n  }),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The container component used when a `ListItemSecondaryAction` is the last child.\n   * @default 'li'\n   * @deprecated Use the `component` or `slots.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerComponent: elementTypeAcceptingRef,\n  /**\n   * Props applied to the container component if used.\n   * @default {}\n   * @deprecated Use the `slotProps.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerProps: PropTypes.object,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used.\n   * The prop defaults to the value inherited from the parent List component.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * If `true`, all padding is removed.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * If `true`, a 1px light border is added to the bottom of the list item.\n   * @default false\n   */\n  divider: PropTypes.bool,\n  /**\n   * The element to display at the end of ListItem.\n   */\n  secondaryAction: PropTypes.node,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItem;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography, { typographyClasses } from \"../Typography/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport listItemTextClasses, { getListItemTextUtilityClass } from \"./listItemTextClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    inset,\n    primary,\n    secondary,\n    dense\n  } = ownerState;\n  const slots = {\n    root: ['root', inset && 'inset', dense && 'dense', primary && secondary && 'multiline'],\n    primary: ['primary'],\n    secondary: ['secondary']\n  };\n  return composeClasses(slots, getListItemTextUtilityClass, classes);\n};\nconst ListItemTextRoot = styled('div', {\n  name: 'MuiListItemText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${listItemTextClasses.primary}`]: styles.primary\n    }, {\n      [`& .${listItemTextClasses.secondary}`]: styles.secondary\n    }, styles.root, ownerState.inset && styles.inset, ownerState.primary && ownerState.secondary && styles.multiline, ownerState.dense && styles.dense];\n  }\n})({\n  flex: '1 1 auto',\n  minWidth: 0,\n  marginTop: 4,\n  marginBottom: 4,\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.primary})`]: {\n    display: 'block'\n  },\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.secondary})`]: {\n    display: 'block'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.primary && ownerState.secondary,\n    style: {\n      marginTop: 6,\n      marginBottom: 6\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.inset,\n    style: {\n      paddingLeft: 56\n    }\n  }]\n});\nconst ListItemText = /*#__PURE__*/React.forwardRef(function ListItemText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemText'\n  });\n  const {\n    children,\n    className,\n    disableTypography = false,\n    inset = false,\n    primary: primaryProp,\n    primaryTypographyProps,\n    secondary: secondaryProp,\n    secondaryTypographyProps,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const {\n    dense\n  } = React.useContext(ListContext);\n  let primary = primaryProp != null ? primaryProp : children;\n  let secondary = secondaryProp;\n  const ownerState = {\n    ...props,\n    disableTypography,\n    inset,\n    primary: !!primary,\n    secondary: !!secondary,\n    dense\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      primary: primaryTypographyProps,\n      secondary: secondaryTypographyProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: ListItemTextRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    ref\n  });\n  const [PrimarySlot, primarySlotProps] = useSlot('primary', {\n    className: classes.primary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SecondarySlot, secondarySlotProps] = useSlot('secondary', {\n    className: classes.secondary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  if (primary != null && primary.type !== Typography && !disableTypography) {\n    primary = /*#__PURE__*/_jsx(PrimarySlot, {\n      variant: dense ? 'body2' : 'body1',\n      component: primarySlotProps?.variant ? undefined : 'span',\n      ...primarySlotProps,\n      children: primary\n    });\n  }\n  if (secondary != null && secondary.type !== Typography && !disableTypography) {\n    secondary = /*#__PURE__*/_jsx(SecondarySlot, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      ...secondarySlotProps,\n      children: secondary\n    });\n  }\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [primary, secondary]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Alias for the `primary` prop.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the children won't be wrapped by a Typography component.\n   * This can be useful to render an alternative Typography variant by wrapping\n   * the `children` (or `primary`) text, and optional `secondary` text\n   * with the Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * If `true`, the children are indented.\n   * This should be used if there is no left avatar or left icon.\n   * @default false\n   */\n  inset: PropTypes.bool,\n  /**\n   * The main content element.\n   */\n  primary: PropTypes.node,\n  /**\n   * These props will be forwarded to the primary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.primary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  primaryTypographyProps: PropTypes.object,\n  /**\n   * The secondary content element.\n   */\n  secondary: PropTypes.node,\n  /**\n   * These props will be forwarded to the secondary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.secondary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  secondaryTypographyProps: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    primary: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    secondary: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    primary: PropTypes.elementType,\n    root: PropTypes.elementType,\n    secondary: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemText;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDividerUtilityClass } from \"./dividerClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    absolute,\n    children,\n    classes,\n    flexItem,\n    light,\n    orientation,\n    textAlign,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', absolute && 'absolute', variant, light && 'light', orientation === 'vertical' && 'vertical', flexItem && 'flexItem', children && 'withChildren', children && orientation === 'vertical' && 'withChildrenVertical', textAlign === 'right' && orientation !== 'vertical' && 'textAlignRight', textAlign === 'left' && orientation !== 'vertical' && 'textAlignLeft'],\n    wrapper: ['wrapper', orientation === 'vertical' && 'wrapperVertical']\n  };\n  return composeClasses(slots, getDividerUtilityClass, classes);\n};\nconst DividerRoot = styled('div', {\n  name: 'MuiDivider',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.absolute && styles.absolute, styles[ownerState.variant], ownerState.light && styles.light, ownerState.orientation === 'vertical' && styles.vertical, ownerState.flexItem && styles.flexItem, ownerState.children && styles.withChildren, ownerState.children && ownerState.orientation === 'vertical' && styles.withChildrenVertical, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && styles.textAlignRight, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && styles.textAlignLeft];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 0,\n  // Reset browser default style.\n  flexShrink: 0,\n  borderWidth: 0,\n  borderStyle: 'solid',\n  borderColor: (theme.vars || theme).palette.divider,\n  borderBottomWidth: 'thin',\n  variants: [{\n    props: {\n      absolute: true\n    },\n    style: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      width: '100%'\n    }\n  }, {\n    props: {\n      light: true\n    },\n    style: {\n      borderColor: theme.vars ? `rgba(${theme.vars.palette.dividerChannel} / 0.08)` : alpha(theme.palette.divider, 0.08)\n    }\n  }, {\n    props: {\n      variant: 'inset'\n    },\n    style: {\n      marginLeft: 72\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'horizontal'\n    },\n    style: {\n      marginLeft: theme.spacing(2),\n      marginRight: theme.spacing(2)\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'vertical'\n    },\n    style: {\n      marginTop: theme.spacing(1),\n      marginBottom: theme.spacing(1)\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      height: '100%',\n      borderBottomWidth: 0,\n      borderRightWidth: 'thin'\n    }\n  }, {\n    props: {\n      flexItem: true\n    },\n    style: {\n      alignSelf: 'stretch',\n      height: 'auto'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.children,\n    style: {\n      display: 'flex',\n      textAlign: 'center',\n      border: 0,\n      borderTopStyle: 'solid',\n      borderLeftStyle: 'solid',\n      '&::before, &::after': {\n        content: '\"\"',\n        alignSelf: 'center'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.children && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before, &::after': {\n        width: '100%',\n        borderTop: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderTopStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.orientation === 'vertical' && ownerState.children,\n    style: {\n      flexDirection: 'column',\n      '&::before, &::after': {\n        height: '100%',\n        borderLeft: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderLeftStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '90%'\n      },\n      '&::after': {\n        width: '10%'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '10%'\n      },\n      '&::after': {\n        width: '90%'\n      }\n    }\n  }]\n})));\nconst DividerWrapper = styled('span', {\n  name: 'MuiDivider',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.wrapper, ownerState.orientation === 'vertical' && styles.wrapperVertical];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-block',\n  paddingLeft: `calc(${theme.spacing(1)} * 1.2)`,\n  paddingRight: `calc(${theme.spacing(1)} * 1.2)`,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      paddingTop: `calc(${theme.spacing(1)} * 1.2)`,\n      paddingBottom: `calc(${theme.spacing(1)} * 1.2)`\n    }\n  }]\n})));\nconst Divider = /*#__PURE__*/React.forwardRef(function Divider(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDivider'\n  });\n  const {\n    absolute = false,\n    children,\n    className,\n    orientation = 'horizontal',\n    component = children || orientation === 'vertical' ? 'div' : 'hr',\n    flexItem = false,\n    light = false,\n    role = component !== 'hr' ? 'separator' : undefined,\n    textAlign = 'center',\n    variant = 'fullWidth',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    absolute,\n    component,\n    flexItem,\n    light,\n    orientation,\n    role,\n    textAlign,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DividerRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    role: role,\n    ref: ref,\n    ownerState: ownerState,\n    \"aria-orientation\": role === 'separator' && (component !== 'hr' || orientation === 'vertical') ? orientation : undefined,\n    ...other,\n    children: children ? /*#__PURE__*/_jsx(DividerWrapper, {\n      className: classes.wrapper,\n      ownerState: ownerState,\n      children: children\n    }) : null\n  });\n});\n\n/**\n * The following flag is used to ensure that this component isn't tabbable i.e.\n * does not get highlight/focus inside of MUI List.\n */\nif (Divider) {\n  Divider.muiSkipListHighlight = true;\n}\nprocess.env.NODE_ENV !== \"production\" ? Divider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Absolutely position the element.\n   * @default false\n   */\n  absolute: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, a vertical divider will have the correct height when used in flex container.\n   * (By default, a vertical divider will have a calculated height of `0px` if it is the child of a flex container.)\n   * @default false\n   */\n  flexItem: PropTypes.bool,\n  /**\n   * If `true`, the divider will have a lighter color.\n   * @default false\n   * @deprecated Use <Divider sx={{ opacity: 0.6 }} /> (or any opacity or color) instead. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  light: PropTypes.bool,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The text alignment.\n   * @default 'center'\n   */\n  textAlign: PropTypes.oneOf(['center', 'left', 'right']),\n  /**\n   * The variant to use.\n   * @default 'fullWidth'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['fullWidth', 'inset', 'middle']), PropTypes.string])\n} : void 0;\nexport default Divider;", "import { useState, useEffect } from 'react';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataEnhancedReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n  totalRecords: number;\n  approach: string;\n}\n\n/**\n * Enhanced office data hook with comprehensive pagination\n * Mirrors the successful Flutter implementation to overcome 1000-record limit\n */\nexport const useOfficeDataEnhanced = (): UseOfficeDataEnhancedReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [totalRecords, setTotalRecords] = useState<number>(0);\n  const [approach, setApproach] = useState<string>('');\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataEnhanced: Starting comprehensive office data fetch...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allOfficeData = await OfficeService.fetchAllOfficeData();\n      \n      console.log('✅ useOfficeDataEnhanced: Fetched office records:', allOfficeData.length, 'records');\n      setTotalRecords(allOfficeData.length);\n\n      if (allOfficeData.length === 0) {\n        console.log('⚠️ useOfficeDataEnhanced: No office records found');\n        setRegions([]);\n        setDivisions([]);\n        setOffices([]);\n        setApproach('no-data');\n        return;\n      }\n\n      // Process regions - get unique regions\n      const uniqueRegions = new Set<string>();\n      allOfficeData.forEach(office => {\n        if (office.Region && office.Region.trim()) {\n          uniqueRegions.add(office.Region.trim());\n        }\n      });\n\n      const regionsArray: Region[] = Array.from(uniqueRegions)\n        .sort()\n        .map(regionName => ({\n          id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed regions:', regionsArray.length);\n\n      // Process divisions - get unique divisions with their regions\n      const uniqueDivisions = new Map<string, string>();\n      allOfficeData.forEach(office => {\n        if (office.Division && office.Division.trim() && office.Region && office.Region.trim()) {\n          uniqueDivisions.set(office.Division.trim(), office.Region.trim());\n        }\n      });\n\n      const divisionsArray: Division[] = Array.from(uniqueDivisions.entries())\n        .sort(([a], [b]) => a.localeCompare(b))\n        .map(([divisionName, regionName]) => ({\n          id: divisionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: divisionName,\n          region: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed divisions:', divisionsArray.length);\n\n      // Process offices - use office name as ID for consistency\n      const officesArray: Office[] = allOfficeData\n        .filter(office => office['Office name'] && office['Office name'].trim())\n        .map(office => ({\n          id: office['Office name'], // Use office name as ID for form targeting\n          name: office['Office name'],\n          region: office.Region || '',\n          division: office.Division || '',\n          facilityId: office['Office name'], // Keep for reference\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed offices:', officesArray.length);\n\n      // Log comprehensive statistics\n      logOfficeStatistics(allOfficeData, regionsArray, divisionsArray, officesArray);\n\n      // Set the processed data\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n      setApproach('enhanced-pagination');\n\n      console.log('✅ useOfficeDataEnhanced: Data processing complete');\n\n    } catch (err) {\n      console.error('❌ useOfficeDataEnhanced: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n      setTotalRecords(0);\n      setApproach('error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n    totalRecords,\n    approach,\n  };\n};\n\n/**\n * Log comprehensive statistics about the processed office data\n */\nfunction logOfficeStatistics(\n  allOfficeData: any[],\n  regions: Region[],\n  divisions: Division[],\n  offices: Office[]\n): void {\n  console.log('📊 useOfficeDataEnhanced: === COMPREHENSIVE STATISTICS ===');\n  console.log(`📊 useOfficeDataEnhanced: Raw records: ${allOfficeData.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed regions: ${regions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed divisions: ${divisions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed offices: ${offices.length}`);\n\n  if (offices.length > 0) {\n    // Alphabetical range\n    const sortedNames = offices.map(o => o.name).sort();\n    console.log(`📊 useOfficeDataEnhanced: Office range - First: \"${sortedNames[0]}\"`);\n    console.log(`📊 useOfficeDataEnhanced: Office range - Last: \"${sortedNames[sortedNames.length - 1]}\"`);\n\n    // Letter distribution\n    const letterCounts: { [key: string]: number } = {};\n    offices.forEach(office => {\n      const firstLetter = office.name.charAt(0).toUpperCase();\n      letterCounts[firstLetter] = (letterCounts[firstLetter] || 0) + 1;\n    });\n\n    console.log('📊 useOfficeDataEnhanced: Letter distribution:');\n    Object.keys(letterCounts).sort().forEach(letter => {\n      console.log(`📊 useOfficeDataEnhanced: ${letter}: ${letterCounts[letter]} offices`);\n    });\n\n    // Check for specific offices\n    const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n    const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n    \n    console.log(`📊 useOfficeDataEnhanced: Contains \"Tirupur division\": ${!!tirupurDivision}`);\n    console.log(`📊 useOfficeDataEnhanced: Contains \"Coimbatore division\": ${!!coimbatoreDivision}`);\n\n    if (tirupurDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Tirupur division: \"${tirupurDivision.name}\"`);\n    }\n    if (coimbatoreDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Coimbatore division: \"${coimbatoreDivision.name}\"`);\n    }\n\n    // Region breakdown\n    if (regions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Regions found:');\n      regions.forEach(region => {\n        const regionOffices = offices.filter(o => o.region === region.name);\n        console.log(`📊 useOfficeDataEnhanced: ${region.name}: ${regionOffices.length} offices`);\n      });\n    }\n\n    // Division breakdown\n    if (divisions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Top 10 divisions by office count:');\n      const divisionCounts = divisions.map(division => ({\n        name: division.name,\n        count: offices.filter(o => o.division === division.name).length\n      })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n      divisionCounts.forEach(division => {\n        console.log(`📊 useOfficeDataEnhanced: ${division.name}: ${division.count} offices`);\n      });\n    }\n  }\n\n  console.log('📊 useOfficeDataEnhanced: === END STATISTICS ===');\n}\n\nexport default useOfficeDataEnhanced;\n", "import React from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n  Chip,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport { useOfficeDataEnhanced } from './hooks/useOfficeDataEnhanced';\n\n/**\n * Test component to verify enhanced office loading functionality\n * This component displays comprehensive statistics about the loaded office data\n */\nconst OfficeLoadingTest: React.FC = () => {\n  const {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    totalRecords,\n    approach,\n    refetch\n  } = useOfficeDataEnhanced();\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" flexDirection=\"column\" alignItems=\"center\" p={4}>\n        <CircularProgress size={60} />\n        <Typography variant=\"h6\" sx={{ mt: 2 }}>\n          Loading office data with comprehensive pagination...\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n          This may take a moment as we fetch ALL records from the database\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={4}>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n        <Typography variant=\"body2\">\n          Failed to load office data. Please check the console for detailed error information.\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Calculate statistics\n  const letterDistribution: { [key: string]: number } = {};\n  offices.forEach(office => {\n    const firstLetter = office.name.charAt(0).toUpperCase();\n    letterDistribution[firstLetter] = (letterDistribution[firstLetter] || 0) + 1;\n  });\n\n  const sortedOfficeNames = offices.map(o => o.name).sort();\n  const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n  const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n\n  // Top regions by office count\n  const regionCounts = regions.map(region => ({\n    name: region.name,\n    count: offices.filter(o => o.region === region.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 5);\n\n  // Top divisions by office count\n  const divisionCounts = divisions.map(division => ({\n    name: division.name,\n    count: offices.filter(o => o.division === division.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n  return (\n    <Box p={4}>\n      <Typography variant=\"h4\" gutterBottom>\n        Office Loading Test - Enhanced Pagination\n      </Typography>\n      \n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\n        This test verifies that the enhanced office loading system can fetch ALL records from the Supabase database,\n        overcoming the default 1000-record pagination limit.\n      </Typography>\n\n      {/* Summary Cards */}\n      <Box display=\"flex\" gap={3} sx={{ mb: 4, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Total Records\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalRecords.toLocaleString()}\n              </Typography>\n              <Chip\n                label={approach}\n                size=\"small\"\n                color={totalRecords > 1000 ? \"success\" : \"warning\"}\n                sx={{ mt: 1 }}\n              />\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Regions\n              </Typography>\n              <Typography variant=\"h4\">\n                {regions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Divisions\n              </Typography>\n              <Typography variant=\"h4\">\n                {divisions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Offices\n              </Typography>\n              <Typography variant=\"h4\">\n                {offices.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n      </Box>\n\n      {/* Verification Results */}\n      <Box display=\"flex\" gap={3} sx={{ flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Verification Results\n            </Typography>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Records exceed 1000 limit:\n              </Typography>\n              <Chip\n                label={totalRecords > 1000 ? \"✅ YES\" : \"❌ NO\"}\n                color={totalRecords > 1000 ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Alphabetical Range:\n              </Typography>\n              <Typography variant=\"body2\">\n                First: \"{sortedOfficeNames[0] || 'N/A'}\"\n              </Typography>\n              <Typography variant=\"body2\">\n                Last: \"{sortedOfficeNames[sortedOfficeNames.length - 1] || 'N/A'}\"\n              </Typography>\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Tirupur Division Found:\n              </Typography>\n              <Chip\n                label={tirupurDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={tirupurDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {tirupurDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{tirupurDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Coimbatore Division Found:\n              </Typography>\n              <Chip\n                label={coimbatoreDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={coimbatoreDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {coimbatoreDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{coimbatoreDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Letter Distribution\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {Object.keys(letterDistribution).sort().map(letter => (\n                <Box key={letter} display=\"flex\" justifyContent=\"space-between\" sx={{ mb: 1 }}>\n                  <Typography variant=\"body2\">{letter}:</Typography>\n                  <Typography variant=\"body2\">{letterDistribution[letter]} offices</Typography>\n                </Box>\n              ))}\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      <Box display=\"flex\" gap={3} sx={{ mt: 3, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 5 Regions by Office Count\n            </Typography>\n            <List dense>\n              {regionCounts.map((region, index) => (\n                <React.Fragment key={region.name}>\n                  <ListItem>\n                    <ListItemText\n                      primary={region.name}\n                      secondary={`${region.count} offices`}\n                    />\n                  </ListItem>\n                  {index < regionCounts.length - 1 && <Divider />}\n                </React.Fragment>\n              ))}\n            </List>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 10 Divisions by Office Count\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              <List dense>\n                {divisionCounts.map((division, index) => (\n                  <React.Fragment key={division.name}>\n                    <ListItem>\n                      <ListItemText\n                        primary={division.name}\n                        secondary={`${division.count} offices`}\n                      />\n                    </ListItem>\n                    {index < divisionCounts.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      {/* Success Message */}\n      {totalRecords > 1000 && (\n        <Alert severity=\"success\" sx={{ mt: 3 }}>\n          <Typography variant=\"h6\">\n            🎉 Success! Enhanced Office Loading is Working\n          </Typography>\n          <Typography variant=\"body2\">\n            The system successfully loaded {totalRecords.toLocaleString()} office records, \n            which exceeds the default 1000-record Supabase limit. This confirms that the \n            comprehensive pagination solution is working correctly.\n          </Typography>\n        </Alert>\n      )}\n    </Box>\n  );\n};\n\nexport default OfficeLoadingTest;\n", "import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Sidebar from '../shared/Sidebar';\nimport StatsCards from '../shared/StatsCards';\nimport PageBuilder from './business/PageBuilder';\nimport OfficeLoadingTest from './business/OfficeLoadingTest';\n\nconst AdminPage: React.FC = () => {\n  const { currentUser } = useAuth();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState<any>(null);\n  const [showOfficeTest, setShowOfficeTest] = useState<boolean>(false);\n\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        const userRef = doc(db, 'employees', currentUser.uid);\n        const userSnap = await getDoc(userRef);\n        if (userSnap.exists()) {\n          setUserData(userSnap.data());\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n      <div className=\"main-content\">\n        <div className=\"page-title\">\n          Admin Dashboard\n          <button\n            onClick={() => setShowOfficeTest(!showOfficeTest)}\n            style={{\n              marginLeft: '20px',\n              padding: '8px 16px',\n              backgroundColor: showOfficeTest ? '#dc3545' : '#007bff',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {showOfficeTest ? 'Hide Office Test' : 'Show Office Loading Test'}\n          </button>\n        </div>\n        <StatsCards />\n        {showOfficeTest ? <OfficeLoadingTest /> : <PageBuilder />}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPage;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardContentUtilityClass(slot) {\n  return generateUtilityClass('MuiCardContent', slot);\n}\nconst cardContentClasses = generateUtilityClasses('MuiCardContent', ['root']);\nexport default cardContentClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getCardContentUtilityClass } from \"./cardContentClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardContentUtilityClass, classes);\n};\nconst CardContentRoot = styled('div', {\n  name: 'MuiCardContent',\n  slot: 'Root'\n})({\n  padding: 16,\n  '&:last-child': {\n    paddingBottom: 24\n  }\n});\nconst CardContent = /*#__PURE__*/React.forwardRef(function CardContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardContent'\n  });\n  const {\n    className,\n    component = 'div',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardContentRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardContent;"], "names": ["getCardUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "CardRoot", "styled", "Paper", "name", "overflow", "React", "inProps", "ref", "props", "useDefaultProps", "className", "raised", "other", "ownerState", "classes", "composeClasses", "root", "useUtilityClasses", "_jsx", "clsx", "elevation", "undefined", "getListItemTextUtilityClass", "stats", "title", "value", "StatsCards", "children", "map", "stat", "index", "_jsxs", "getDividerUtilityClass", "_ref", "isOpen", "onClose", "onClick", "e", "stopPropagation", "isMainCard", "cardId", "allCategories", "card", "find", "c", "id", "parentId", "isLeafCard", "some", "organizeCards", "list", "roots", "for<PERSON>ach", "item", "_map$item$parentId$ch", "push", "getAllDescendantIds", "descendants", "filter", "child", "concat", "useCardManagement", "categories", "setCategories", "selected<PERSON><PERSON>", "setSelectedCard", "newCardId", "setNewCardId", "newCardTitle", "setNewCardTitle", "actionType", "setActionType", "setIsLoading", "setError", "setSuccess", "setShowConfirmModal", "setIsAddingNewCard", "setPageConfig", "setFields", "setEditingCard", "setShowEditModal", "setCardToDelete", "setShowDeleteConfirmModal", "fetchCategories", "useCallback", "async", "fetchedCategories", "getDocs", "collection", "db", "docs", "doc", "data", "err", "console", "error", "handleAddNewCard", "doc<PERSON>ef", "getDoc", "exists", "checkDuplicateId", "handleConfirmCreate", "_categories$find", "parentIdToSet", "newPath", "path", "replace", "cardRef", "icon", "generatedIcon", "color", "generatedColor", "hash", "split", "reduce", "acc", "char", "charCodeAt", "icons", "FaFolder", "FaFileAlt", "FaCog", "FaFolderOpen", "colors", "length", "generateCardStyle", "setDoc", "lastUpdated", "Date", "toISOString", "fields", "isPage", "pageId", "setTimeout", "handleEditCard", "handleUpdateCard", "editingCard", "updateDoc", "handleDeleteClick", "handleConfirmDelete", "batch", "writeBatch", "allDescendants", "idsToDelete", "delete", "commit", "onCardChange", "onActionChange", "isLoading", "onCreateAction", "onWebPageAction", "renderCardOptions", "cards", "level", "arguments", "flatMap", "displayTitle", "trim", "style", "paddingLeft", "repeat", "warn", "onChange", "newSelectedCard", "target", "disabled", "newAction", "_Fragment", "onEditCard", "onDeleteCard", "selectedCate<PERSON><PERSON>", "FaEdit", "FaTrash", "_field$options2", "field", "onUpdate", "onRemove", "handleOptionChange", "optIndex", "key", "newOptions", "options", "handleDefaultValueChange", "type", "newDefaultValue", "checked", "defaultValue", "label", "htmlFor", "placeholder", "required", "includes", "min", "parseFloat", "max", "opt", "_field$options", "_", "i", "removeOption", "addOption", "String", "Boolean", "Array", "isArray", "join", "s", "buttonText", "sectionTitle", "pageConfig", "onAddField", "onUpdateField", "onRemoveField", "onSave", "onPreview", "loading", "FieldConfigItem", "FaPlus", "FaSave", "REPORT_FREQUENCIES", "<PERSON><PERSON><PERSON><PERSON>", "setIsOpen", "useState", "dropdownRef", "useRef", "useEffect", "handleClickOutside", "event", "current", "contains", "document", "addEventListener", "removeEventListener", "isAllSelected", "isIndeterminate", "backgroundColor", "borderColor", "getDisplayText", "selectedOption", "option", "maxHeight", "overflowY", "input", "indeterminate", "handleSelectAll", "handleCheckboxChange", "optionId", "selectedRegions", "selectedDivisions", "selectedOffices", "selectedFrequency", "onRegionsChange", "onDivisionsChange", "onOfficesChange", "onFrequencyChange", "regions", "divisions", "offices", "refetch", "useOfficeDataSimple", "setRegions", "setDivisions", "setOffices", "setLoading", "fetchOfficeData", "log", "allData", "OfficeService", "fetchAllOfficeData", "distinctRegions", "row", "Region", "region", "array", "indexOf", "sort", "regionsArray", "regionName", "toLowerCase", "distinctDivisions", "division", "Division", "findIndex", "x", "a", "b", "localeCompare", "divisionsArray", "officesArray", "facilityId", "useOfficeData", "selectedRegionNames", "regionId", "_regions$find", "r", "availableDivisions", "selectedDivisionNames", "divisionId", "_divisions$find", "d", "availableOffices", "office", "validDivisions", "validOffices", "officeId", "o", "role", "CheckboxDropdown", "frequency", "STORAGE_KEY", "PageBuilder", "_state$categories$fin", "_state$categories$fin2", "state", "usePageBuilderState", "availableDynamicFields", "setAvailableDynamicFields", "success", "isAddingNewCard", "showConfirmModal", "showEditModal", "cardToDelete", "showDeleteConfirmModal", "isPreviewOpen", "setIsPreviewOpen", "previewContent", "setPreviewContent", "setSelectedRegions", "setSelectedDivisions", "setSelectedOffices", "setSelectedFrequency", "cardManagement", "pageConfiguration", "fetchDynamicFormFields", "formId", "formConfigRef", "formConfigSnap", "formConfigData", "loadPageConfig", "docSnap", "supabasePageService", "supabaseError", "savedRegions", "selectedRegion", "savedDivisions", "selectedDivision", "savedOffices", "selectedOffice", "savedFrequency", "addField", "newField", "now", "addFieldFromDynamic", "dynamicField", "columns", "buttonType", "onClickAction", "updateField", "updatedField", "<PERSON><PERSON><PERSON>s", "removeField", "handleSave", "cleanedFields", "cleanedField", "updatedPageConfig", "savePromises", "catch", "Error", "message", "savePageConfig", "Promise", "all", "handlePreview", "alert", "generatedPreview", "fieldHtml", "usePageConfiguration", "savedSelections", "loadDropdownSelections", "stored", "localStorage", "getItem", "JSON", "parse", "selections", "setItem", "stringify", "saveDropdownSelections", "CardSelector", "cardIsLeaf", "cardIsMain", "action", "handleCreateAction", "handleWebPageAction", "Modal", "CardManagement", "ReportConfiguration", "previousRegions", "previousDivisions", "PageBuilderContent", "dangerouslySetInnerHTML", "__html", "getListItemUtilityClass", "getListItemSecondaryActionClassesUtilityClass", "ListItemSecondaryActionRoot", "overridesResolver", "styles", "disableGutters", "position", "right", "top", "transform", "variants", "ListItemSecondaryAction", "context", "ListContext", "slots", "mui<PERSON><PERSON>", "ListItemRoot", "dense", "alignItems", "alignItemsFlexStart", "divider", "gutters", "disablePadding", "padding", "hasSecondaryAction", "secondaryAction", "memoTheme", "theme", "display", "justifyContent", "textDecoration", "width", "boxSizing", "textAlign", "_ref2", "paddingTop", "paddingBottom", "_ref3", "_ref4", "paddingRight", "_ref5", "_ref6", "listItemButtonClasses", "_ref7", "borderBottom", "vars", "palette", "backgroundClip", "_ref8", "button", "transition", "transitions", "create", "duration", "shortest", "hover", "_ref9", "ListItemContainer", "childrenProp", "component", "componentProp", "components", "componentsProps", "ContainerComponent", "ContainerProps", "ContainerClassName", "slotProps", "childContext", "listItemRef", "toArray", "isMuiElement", "container", "handleRef", "useForkRef", "Root", "rootProps", "componentProps", "Component", "Provider", "as", "isHostComponent", "pop", "ListItemTextRoot", "listItemTextClasses", "primary", "secondary", "inset", "multiline", "flex", "min<PERSON><PERSON><PERSON>", "marginTop", "marginBottom", "typographyClasses", "disableTypography", "primaryProp", "primaryTypographyProps", "secondaryProp", "secondaryTypographyProps", "externalForwardedProps", "RootSlot", "rootSlotProps", "useSlot", "elementType", "PrimarySlot", "primarySlotProps", "Typography", "SecondarySlot", "secondarySlotProps", "variant", "DividerRoot", "absolute", "light", "orientation", "vertical", "flexItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withChildrenVertical", "textAlignRight", "textAlignLeft", "margin", "flexShrink", "borderWidth", "borderStyle", "borderBottomWidth", "bottom", "left", "dividerChannel", "alpha", "marginLeft", "spacing", "marginRight", "height", "borderRightWidth", "alignSelf", "border", "borderTopStyle", "borderLeftStyle", "content", "borderTop", "flexDirection", "borderLeft", "DividerWrapper", "wrapper", "wrapperVertical", "whiteSpace", "Divider", "muiSkipListHighlight", "useOfficeDataEnhanced", "totalRecords", "setTotalRecords", "approach", "setApproach", "allOfficeData", "uniqueRegions", "Set", "add", "from", "uniqueDivisions", "Map", "set", "entries", "divisionName", "sortedNames", "letterCounts", "firstLetter", "char<PERSON>t", "toUpperCase", "Object", "keys", "letter", "tirupurDivision", "coimbatoreDivision", "regionOffices", "count", "slice", "logOfficeStatistics", "OfficeLoadingTest", "Box", "p", "CircularProgress", "size", "sx", "mt", "<PERSON><PERSON>", "severity", "mb", "letterDistribution", "sortedOfficeNames", "regionCounts", "divisionCounts", "gutterBottom", "paragraph", "gap", "flexWrap", "Card", "<PERSON><PERSON><PERSON><PERSON>", "toLocaleString", "Chip", "List", "ListItem", "ListItemText", "AdminPage", "currentUser", "useAuth", "userData", "setUserData", "useNavigate", "showOfficeTest", "setShowOfficeTest", "userRef", "uid", "userSnap", "fetchUserData", "Sidebar", "borderRadius", "cursor", "fontSize", "getCardContentUtilityClass", "CardContentRoot"], "sourceRoot": ""}