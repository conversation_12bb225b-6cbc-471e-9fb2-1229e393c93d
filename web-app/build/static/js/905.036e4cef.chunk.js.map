{"version": 3, "file": "static/js/905.036e4cef.chunk.js", "mappings": "iMAMO,MAAMA,EAUX,2CAAaC,GACX,IAQE,GANAC,QAAQC,IAAI,6EACZD,QAAQC,IAAI,mDAA0CC,KAAKC,gBAC3DH,QAAQC,IAAI,6DAAoDC,KAAKE,sBACrEJ,QAAQC,IAAI,qDAA4CC,KAAKG,kBAGzDH,KAAKC,gBAAgD,OAA9BD,KAAKE,qBAE9B,OADAJ,QAAQC,IAAI,uEAA8DC,KAAKE,sBACxEF,KAAKE,qBAGdJ,QAAQC,IAAI,gGAEZ,MAAMK,EAAOC,EAAAA,EAAKC,YAClB,IAAKF,EAEH,OADAN,QAAQC,IAAI,oDACL,EAGTD,QAAQC,IAAI,wDAA+CK,EAAKG,KAGhE,MAAMC,QAAgBC,EAAAA,EAAAA,KAAOC,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAaP,EAAKG,MAEvD,IAAKC,EAAQI,SAEX,OADAd,QAAQC,IAAI,0DACL,EAGT,MAAMc,EAAWL,EAAQM,OACnBC,EAAqB,OAARF,QAAQ,IAARA,OAAQ,EAARA,EAAUE,WAE7B,IAAKA,GAAoC,KAAtBA,EAAWC,OAE5B,OADAlB,QAAQC,IAAI,6DACL,EAGTD,QAAQC,IAAI,mDAA0C,IAAIgB,MAG1D,MAAME,EAAUF,EAAWC,OACrBE,EAAYD,EAAQE,cACpBC,EAAiBF,EAAUG,SAAS,YAsB1C,OApBAvB,QAAQC,IAAI,mDAA0C,IAAIgB,MAC1DjB,QAAQC,IAAI,+CAAsC,IAAIkB,MACtDnB,QAAQC,IAAI,iDAAwC,IAAImB,MACxDpB,QAAQC,IAAI,4DAAmDqB,GAC/DtB,QAAQC,IAAI,mDAA0CqB,EAAiB,mCAAqC,2BAG5GpB,KAAKG,iBAAmBY,EACxBf,KAAKE,qBAAuBkB,EAC5BpB,KAAKsB,eAAiB,IAAIC,KAE1BzB,QAAQC,IAAI,+DAAsDC,KAAKG,kBACvEL,QAAQC,IAAI,mEAA0DC,KAAKE,sBAEvEkB,EACFtB,QAAQC,IAAI,+FAEZD,QAAQC,IAAI,+FAGPqB,CAET,CAAE,MAAOI,GAEP,OADA1B,QAAQ0B,MAAM,+DAA2DA,IAClE,CACT,CACF,CAKA,qCAAaC,GACX,IAIE,GAHA3B,QAAQC,IAAI,yEAGRC,KAAKC,gBAA4C,OAA1BD,KAAKG,iBAE9B,OADAL,QAAQC,IAAI,gEAAuDC,KAAKG,kBACjEH,KAAKG,iBAGdL,QAAQC,IAAI,mFACZD,QAAQC,IAAI,mDAA0CC,KAAKC,gBAC3DH,QAAQC,IAAI,qDAA4CC,KAAKG,kBAE7D,MAAMC,EAAOC,EAAAA,EAAKC,YAClB,IAAKF,EAEH,OADAN,QAAQC,IAAI,mDACL,KAGTD,QAAQC,IAAI,wDAA+CK,EAAKG,KAChET,QAAQC,IAAI,0DAAiDK,EAAKsB,OAElE5B,QAAQC,IAAI,+EACZ,MAAMS,QAAgBC,EAAAA,EAAAA,KAAOC,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAaP,EAAKG,MAEvD,IAAKC,EAAQI,SAGX,OAFAd,QAAQC,IAAI,sFACZD,QAAQC,IAAI,qDAA4C,aAAaK,EAAKG,OACnE,KAGTT,QAAQC,IAAI,qDACZ,MAAMc,EAAWL,EAAQM,OACzBhB,QAAQC,IAAI,qDAA4Cc,GAExD,MAAME,EAAqB,OAARF,QAAQ,IAARA,OAAQ,EAARA,EAAUE,WAiB7B,OAhBAjB,QAAQC,IAAI,4DAAmDgB,GAC/DjB,QAAQC,IAAI,8DAAqDgB,GACjEjB,QAAQC,IAAI,oEAAyE,MAAdgB,GAGvEjB,QAAQC,IAAI,0DAAiD4B,OAAOC,KAAKf,GAAY,CAAC,IACtFf,QAAQC,IAAI,2DAA0D,OAARc,QAAQ,IAARA,OAAQ,EAARA,EAAUE,YACxEjB,QAAQC,IAAI,4DAA2D,OAARc,QAAQ,IAARA,OAAQ,EAARA,EAAUgB,aACzE/B,QAAQC,IAAI,uDAAsD,OAARc,QAAQ,IAARA,OAAQ,EAARA,EAAUiB,QACpEhC,QAAQC,IAAI,uDAAsD,OAARc,QAAQ,IAARA,OAAQ,EAARA,EAAUkB,QAGpE/B,KAAKG,iBAAmBY,GAAc,KACtCf,KAAKsB,eAAiB,IAAIC,KAE1BzB,QAAQC,IAAI,gEAAuDgB,GAAc,QAC1EA,GAAc,IAEvB,CAAE,MAAOS,GAGP,OAFA1B,QAAQ0B,MAAM,2DAAuDA,GACrE1B,QAAQ0B,MAAM,+CAA2CA,GAClD,IACT,CACF,CAKA,8BAAaQ,GAOX,IACElC,QAAQC,IAAI,8DAGZC,KAAKiC,aAEL,MAAMlB,QAAmBf,KAAKyB,2BAC9B3B,QAAQC,IAAI,mDAA0CgB,GAGtD,IAkBImB,EACAC,EACAC,EApBAhB,GAAiB,EAQrB,OAPIL,GAAoC,KAAtBA,EAAWC,SAC3BI,EAAiBL,EAAWC,OAAOG,cAAcE,SAAS,YAC1DvB,QAAQC,IAAI,yDAAgDqB,GAC5DtB,QAAQC,IAAI,4DAAmD,IAAIgB,EAAWC,OAAOG,kBACrFrB,QAAQC,IAAI,wDAA+CqB,IAGxDL,GAcDK,GACFc,EAAc,WACdC,EAAa,gBACbC,EAAc,oGAEdF,EAAc,SACdC,EAAa,SACbC,EAAc,8DAGhBtC,QAAQC,IAAI,gDAAuC,CACjDgB,aACAK,iBACAc,cACAC,eAGK,CACLpB,aACAK,iBACAc,cACAC,aACAC,gBAnCO,CACLrB,WAAY,KACZK,gBAAgB,EAChBc,YAAa,OACbC,WAAY,OACZC,YAAa,kCAiCnB,CAAE,MAAOZ,GAEP,OADA1B,QAAQ0B,MAAM,2DAAuDA,GAC9D,CACLT,WAAY,KACZK,gBAAgB,EAChBc,YAAa,QACbC,WAAY,SACZC,YAAa,mCAEjB,CACF,CAKA,uCAAaC,GACXvC,QAAQC,IAAI,2EAGZC,KAAKiC,aAGL,MAAMK,QAAetC,KAAKH,iCAG1B,OADAC,QAAQC,IAAI,4DAAmDuC,GACxDA,CACT,CAKA,iBAAOL,GACLjC,KAAKG,iBAAmB,KACxBH,KAAKE,qBAAuB,KAC5BF,KAAKsB,eAAiB,KACtBxB,QAAQC,IAAI,0DACd,CAKA,mBAAeE,GACb,IAAKD,KAAKsB,eACR,OAAO,EAKT,OAFY,IAAIC,MACOgB,UAAYvC,KAAKsB,eAAeiB,UACnCvC,KAAKwC,YAC3B,CAKA,iCAAaC,GACX,IACE,MAAMC,QAAmB1C,KAAKgC,oBAE9B,OAAKU,EAAW3B,WAIZ2B,EAAWtB,eACN,6KAEA,mHAAmHsB,EAAW3B,eAN9H,iCASX,CAAE,MAAOS,GACP,MAAO,gCACT,CACF,CAKA,oCAAamB,GAOX,IACE7C,QAAQC,IAAI,mDAGZ,MAAMK,EAAOC,EAAAA,EAAKC,YAClB,IAAKF,EAEH,OADAN,QAAQC,IAAI,yCACL,CACLgB,WAAY,KACZE,QAAS,KACTC,UAAW,KACX0B,kBAAkB,EAClBC,yBAAyB,GAI7B,MAAMrC,QAAgBC,EAAAA,EAAAA,KAAOC,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAaP,EAAKG,MACvD,IAAKC,EAAQI,SAEX,OADAd,QAAQC,IAAI,+CACL,CACLgB,WAAY,KACZE,QAAS,KACTC,UAAW,KACX0B,kBAAkB,EAClBC,yBAAyB,GAI7B,MAAMhC,EAAWL,EAAQM,OACnBC,EAAqB,OAARF,QAAQ,IAARA,OAAQ,EAARA,EAAUE,WAI7B,GAFAjB,QAAQC,IAAI,6CAAoCgB,IAE3CA,EAEH,OADAjB,QAAQC,IAAI,qDACL,CACLgB,WAAY,KACZE,QAAS,KACTC,UAAW,KACX0B,kBAAkB,EAClBC,yBAAyB,GAI7B,MAAM5B,EAAUF,EAAWC,OACrBE,EAAYD,EAAQE,cACpByB,EAAmB1B,EAAUG,SAAS,YAU5C,OARAvB,QAAQC,IAAI,sCAA6B,IAAIgB,MAC7CjB,QAAQC,IAAI,qCAA4B,IAAIkB,MAC5CnB,QAAQC,IAAI,uCAA8B,IAAImB,MAC9CpB,QAAQC,IAAI,kDAAyC6C,GACrD9C,QAAQC,IAAI,uDAA8C6C,GAE1D9C,QAAQC,IAAI,uDAEL,CACLgB,aACAE,UACAC,YACA0B,mBACAC,wBAAyBD,EAG7B,CAAE,MAAOpB,GAEP,OADA1B,QAAQ0B,MAAM,6BAAyBA,GAChC,CACLT,WAAY,KACZE,QAAS,KACTC,UAAW,KACX0B,kBAAkB,EAClBC,yBAAyB,EAE7B,CACF,CAKA,8BAAaC,GACX,IACEhD,QAAQC,IAAI,uEAEZ,MAAM2C,QAAmB1C,KAAKgC,oBAE9BlC,QAAQC,IAAI,4BAAmB2C,EAAW3B,YAC1CjB,QAAQC,IAAI,iCAAwB2C,EAAWtB,gBAC/CtB,QAAQC,IAAI,6BAAoB2C,EAAWR,aAC3CpC,QAAQC,IAAI,4BAAmB2C,EAAWP,YAC1CrC,QAAQC,IAAI,4BAAmB2C,EAAWN,aAE1C,MAAMW,QAA0B/C,KAAKyC,uBACrC3C,QAAQC,IAAI,mCAA0BgD,GAEtCjD,QAAQC,IAAI,mDAEd,CAAE,MAAOyB,GACP1B,QAAQ0B,MAAM,2DAAuDA,EACvE,CACF,EA/YW5B,EACIO,iBAAkC,KADtCP,EAEIM,qBAAuC,KAF3CN,EAGI0B,eAA8B,KAHlC1B,EAIa4C,aAAe,KA8YzC,U,uBC/XA,MAkcA,EAlcqCQ,KACnC,MAAM,YAAE1C,IAAgB2C,EAAAA,EAAAA,MACjBC,EAAcC,IAAmBC,EAAAA,EAAAA,UAA8B,OAC/DC,EAAWC,IAAgBF,EAAAA,EAAAA,WAAS,IACpCvC,EAAU0C,IAAeH,EAAAA,EAAAA,UAAc,MAG9CI,EAAAA,WAAgB,KACQC,WACpB,GAAInD,EACF,IACE,MAAME,QAAgBC,EAAAA,EAAAA,KAAOC,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAaL,EAAYC,MAC1DC,EAAQI,UACV2C,EAAY/C,EAAQM,OAExB,CAAE,MAAOU,GACP1B,QAAQ0B,MAAM,wCAAyCA,EACzD,CACF,EAEFkC,EAAe,GACd,CAACpD,IAoKJ,OACEqD,EAAAA,EAAAA,MAAA,OAAKC,UAAU,sBAAqBC,SAAA,EAClCC,EAAAA,EAAAA,KAACC,EAAAA,EAAO,CAAClD,SAAUA,KACnBiD,EAAAA,EAAAA,KAAA,OAAKF,UAAU,eAAcC,UAC3BF,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CAAEC,QAAS,OAAQC,SAAU,SAAUC,OAAQ,UAAWN,SAAA,EACpEF,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CAAEI,QAAS,OAAQC,eAAgB,gBAAiBC,WAAY,SAAUC,aAAc,QAASV,SAAA,EAC3GC,EAAAA,EAAAA,KAAA,MAAIE,MAAO,CAAEG,OAAQ,EAAGK,MAAO,WAAYX,SAAC,wCAC5CF,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CAAEI,QAAS,OAAQK,IAAK,OAAQC,SAAU,QAASb,SAAA,EAC7DC,EAAAA,EAAAA,KAAA,UACEa,QAhEK1C,KACjBrC,EAAsBqC,aACtBnC,QAAQC,IAAI,mCAAoB,EA+DpBiE,MAAO,CACLC,QAAS,iBACTW,gBAAiB,UACjBJ,MAAO,QACPK,OAAQ,OACRC,aAAc,MACdC,OAAQ,UACRC,WAAY,OACZnB,SACH,oCAGDC,EAAAA,EAAAA,KAAA,UACEa,QAzEOlB,UACnB3D,QAAQC,IAAI,oDACZ,IACE,MAAMuC,QAAe1C,EAAsByC,6BAC3CvC,QAAQC,IAAI,qCAA4BuC,GAGxC,MAAMI,QAAmB9C,EAAsBoC,oBAC/ClC,QAAQC,IAAI,kCAAyB2C,GAErCuC,MAAM,2CAA2C3C,mBAAwB4C,KAAKC,UAAUzC,EAAY,KAAM,KAC5G,CAAE,MAAOlB,GACP1B,QAAQ0B,MAAM,8BAA0BA,GACxCyD,MAAM,yBAAyBzD,IACjC,GA4DYwC,MAAO,CACLC,QAAS,iBACTW,gBAAiB,UACjBJ,MAAO,QACPK,OAAQ,OACRC,aAAc,MACdC,OAAQ,UACRC,WAAY,OACZnB,SACH,gCAGDC,EAAAA,EAAAA,KAAA,UACEa,QAtEYS,KACxBtF,QAAQC,IAAI,mDAEM,CAChB,sBACA,mBACA,kBACA,mBACA,aACA,YACA,WACA,eACA,sBACA,sBACA,0BAGQsF,SAAQC,IAChB,MAAMrE,EAAUqE,EAAStE,OACnBE,EAAYD,EAAQE,cACpBmB,EAASpB,EAAUG,SAAS,YAElCvB,QAAQC,IAAI,uBAAauF,MACzBxF,QAAQC,IAAI,gBAAgBkB,MAC5BnB,QAAQC,IAAI,kBAAkBmB,MAC9BpB,QAAQC,IAAI,4BAA4BuC,KACxCxC,QAAQC,IAAI,wBAAuBuC,EAAS,kCAAoC,iCAChFxC,QAAQC,IAAI,GAAG,IAGjBD,QAAQC,IAAI,sDAA4C,EAyC5CiE,MAAO,CACLC,QAAS,iBACTW,gBAAiB,UACjBJ,MAAO,QACPK,OAAQ,OACRC,aAAc,MACdC,OAAQ,UACRC,WAAY,OACZnB,SACH,sCAGDC,EAAAA,EAAAA,KAAA,UACEa,QArNOlB,UACnBH,GAAa,GACbxD,QAAQC,IAAI,iDAEZ,IACE,MAAMwF,EAAwB,CAC5BC,WAAW,IAAIjE,MAAOkE,cACtBC,oBAAqBpF,EACrBqF,SAAoB,OAAXrF,QAAW,IAAXA,OAAW,EAAXA,EAAaC,MAAO,KAC7BqF,WAAsB,OAAXtF,QAAW,IAAXA,OAAW,EAAXA,EAAaoB,QAAS,KACjCmE,eAAe,EACfC,YAAa,KACb/E,WAAY,KACZgF,eAAgB,YAChBC,gBAAiB,GACjBnD,yBAAyB,EACzBH,WAAY,MAOd,GAJA5C,QAAQC,IAAI,0CAAiCwF,EAAQG,mBACrD5F,QAAQC,IAAI,gCAAuBwF,EAAQI,SAC3C7F,QAAQC,IAAI,kCAAyBwF,EAAQK,WAEzCtF,EAAa,CAEfR,QAAQC,IAAI,kEACZ,MAAMS,QAAgBC,EAAAA,EAAAA,KAAOC,EAAAA,EAAAA,IAAIC,EAAAA,GAAI,YAAaL,EAAYC,MAK9D,GAHAgF,EAAQM,cAAgBrF,EAAQI,SAChCd,QAAQC,IAAI,4CAAmCwF,EAAQM,eAEnDrF,EAAQI,SAAU,CAAC,IAADqF,EACpBV,EAAQO,YAActF,EAAQM,OAC9ByE,EAAQS,gBAAkBrE,OAAOC,KAAK2D,EAAQO,aAAe,CAAC,GAE9DhG,QAAQC,IAAI,qCAA4BwF,EAAQO,aAChDhG,QAAQC,IAAI,qCAA4BwF,EAAQS,iBAGhDT,EAAQxE,YAAgC,QAAnBkF,EAAAV,EAAQO,mBAAW,IAAAG,OAAA,EAAnBA,EAAqBlF,aAAc,KACxDwE,EAAQQ,sBAAwBR,EAAQxE,WAExCjB,QAAQC,IAAI,6CAAoCwF,EAAQxE,YACxDjB,QAAQC,IAAI,wCAA+BwF,EAAQQ,gBAGnDjG,QAAQC,IAAI,wDAGZH,EAAsBqC,aAGtB,MAAMiE,QAA0BtG,EAAsB6B,2BACtD3B,QAAQC,IAAI,2CAAkCmG,GAG1CX,EAAQxE,aACVjB,QAAQC,IAAI,0DACZD,QAAQC,IAAI,4CAAmC,IAAIwF,EAAQxE,eAC3DjB,QAAQC,IAAI,2CAAkC,IAAIwF,EAAQxE,WAAWC,WACrElB,QAAQC,IAAI,6CAAoC,IAAIwF,EAAQxE,WAAWC,OAAOG,kBAC9ErB,QAAQC,IAAI,4CAAmCwF,EAAQxE,WAAWC,OAAOG,cAAcE,SAAS,aAChGvB,QAAQC,IAAI,oDAA2CwF,EAAQxE,WAAWC,OAAOG,cAAcE,SAAS,aACxGvB,QAAQC,IAAI,+DAId,MAAMoG,QAAmBvG,EAAsB+C,0BAC/C7C,QAAQC,IAAI,4CAAmCoG,GAG/CZ,EAAQ1C,8BAAgCjD,EAAsBC,iCAC9DC,QAAQC,IAAI,iDAAwCwF,EAAQ1C,yBAG5D0C,EAAQ7C,iBAAmB9C,EAAsBoC,oBACjDlC,QAAQC,IAAI,mCAA0BwF,EAAQ7C,YAG7C6C,EAAgBY,WAAaA,CAChC,CACF,CAEAhD,EAAgBoC,GAChBzF,QAAQC,IAAI,oDAEd,CAAE,MAAOyB,GACP1B,QAAQ0B,MAAM,yCAAgCA,GAC9C2B,EAAgB,CACdqC,WAAW,IAAIjE,MAAOkE,cACtBC,oBAAqBpF,EACrBqF,SAAoB,OAAXrF,QAAW,IAAXA,OAAW,EAAXA,EAAaC,MAAO,KAC7BqF,WAAsB,OAAXtF,QAAW,IAAXA,OAAW,EAAXA,EAAaoB,QAAS,KACjCmE,eAAe,EACfC,YAAa,KACb/E,WAAY,KACZgF,eAAgB,QAChBC,gBAAiB,GACjBnD,yBAAyB,EACzBH,WAAY,KACZlB,MAAOA,aAAiB4E,MAAQ5E,EAAM6E,QAAUC,OAAO9E,IAE3D,CAAC,QACC8B,GAAa,EACf,GA8GYiD,SAAUlD,EACVW,MAAO,CACLC,QAAS,iBACTW,gBAAiBvB,EAAY,UAAY,UACzCmB,MAAO,QACPK,OAAQ,OACRC,aAAc,MACdC,OAAQ1B,EAAY,cAAgB,UACpC2B,WAAY,OACZnB,SAEDR,EAAY,0BAAkB,uCAKrCM,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CACVY,gBAAiB,UACjBC,OAAQ,oBACRC,aAAc,OACdb,QAAS,SACTM,aAAc,QACdV,SAAA,EACAC,EAAAA,EAAAA,KAAA,MAAIE,MAAO,CAAEG,OAAQ,aAAcK,MAAO,WAAYX,SAAC,qCACvDF,EAAAA,EAAAA,MAAA,MAAIK,MAAO,CAAEG,OAAQ,EAAGqC,YAAa,UAAW3C,SAAA,EAC9CC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0EACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,yDACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,qDACJC,EAAAA,EAAAA,KAAA,MAAAD,SAAI,0DAIPX,IACCS,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CACVY,gBAAiB,QACjBC,OAAQ,oBACRC,aAAc,OACdb,QAAS,UACTJ,SAAA,EACAC,EAAAA,EAAAA,KAAA,MAAIE,MAAO,CAAEG,OAAQ,eAAgBK,MAAO,WAAYX,SAAC,gCAEzDF,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CAAEI,QAAS,OAAQK,IAAK,QAASZ,SAAA,EAC3CF,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CAAEI,QAAS,OAAQqC,oBAAqB,YAAahC,IAAK,SAAUH,WAAY,SAAUT,SAAA,EACpGC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,gBACRC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CAAE0C,WAAY,aAAc7C,SAAEX,EAAasC,aAExD1B,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,yBACRC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CAAEQ,MAAOtB,EAAawC,kBAAoB,UAAY,WAAY7B,SAC5EX,EAAawC,kBAAoB,aAAU,eAG9C5B,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,eACRC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CAAE0C,WAAY,aAAc7C,SAAEX,EAAayC,SAAW,UAEnE7B,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,iBACRC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CAAE0C,WAAY,aAAc7C,SAAEX,EAAa0C,WAAa,UAErE9B,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,sBACRC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CAAEQ,MAAOtB,EAAa2C,cAAgB,UAAY,WAAYhC,SACxEX,EAAa2C,cAAgB,aAAU,eAG1C/B,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,kBACRC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CACX0C,WAAY,YACZlC,MAAOtB,EAAanC,WAAa,UAAY,UAC7CiE,WAAY,QACZnB,SACCX,EAAanC,YAAc,oBAG9B+C,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,uBACRC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CAAE0C,WAAY,aAAc7C,SAAEX,EAAa6C,kBAExDjC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,gCACRC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CAAEQ,MAAOtB,EAAaL,wBAA0B,UAAY,WAAYgB,SAClFX,EAAaL,wBAA0B,+CAAuC,iDAKjFK,EAAqBiD,aACrBxC,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CACV2C,UAAW,OACX1C,QAAS,OACTW,gBAAiB,UACjBC,OAAQ,oBACRC,aAAc,OACdjB,SAAA,EACAC,EAAAA,EAAAA,KAAA,UAAQE,MAAO,CAAEQ,MAAO,WAAYX,SAAC,+DACrCF,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CAAE2C,UAAW,SAAUvC,QAAS,OAAQqC,oBAAqB,YAAahC,IAAK,SAAUmC,SAAU,YAAa/C,SAAA,EAC1HC,EAAAA,EAAAA,KAAA,QAAAD,SAAM,eACNF,EAAAA,EAAAA,MAAA,QAAMK,MAAO,CAAE0C,WAAY,aAAc7C,SAAA,CAAC,IAAGX,EAAqBiD,WAAWpF,WAAW,QAExF+C,EAAAA,EAAAA,KAAA,QAAAD,SAAM,cACNF,EAAAA,EAAAA,MAAA,QAAMK,MAAO,CAAE0C,WAAY,aAAc7C,SAAA,CAAC,IAAGX,EAAqBiD,WAAWlF,QAAQ,QAErF6C,EAAAA,EAAAA,KAAA,QAAAD,SAAM,gBACNF,EAAAA,EAAAA,MAAA,QAAMK,MAAO,CAAE0C,WAAY,aAAc7C,SAAA,CAAC,IAAGX,EAAqBiD,WAAWjF,UAAU,QAEvF4C,EAAAA,EAAAA,KAAA,QAAAD,SAAM,2BACNC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CACX0C,WAAY,YACZlC,MAAQtB,EAAqBiD,WAAWvD,iBAAmB,UAAY,UACvEoC,WAAY,QACZnB,SACEX,EAAqBiD,WAAWvD,iBAAmB,cAAW,kBAGlEkB,EAAAA,EAAAA,KAAA,QAAAD,SAAM,sBACNC,EAAAA,EAAAA,KAAA,QAAME,MAAO,CACXgB,WAAY,OACZR,MAAQtB,EAAqBiD,WAAWtD,wBAA0B,UAAY,WAC9EgB,SACEX,EAAqBiD,WAAWtD,wBAA0B,+BAAuB,wCAM3FiB,EAAAA,EAAAA,KAAA,OAAKE,MAAO,CAAEI,QAAS,OAAQqC,oBAAqB,YAAahC,IAAK,SAAUH,WAAY,WAG3FpB,EAAa8C,gBAAgBa,OAAS,IACrClD,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,0BACRC,EAAAA,EAAAA,KAAA,OAAKE,MAAO,CACV2C,UAAW,SACX1C,QAAS,UACTW,gBAAiB,UACjBE,aAAc,MACd4B,WAAY,YACZE,SAAU,YACV/C,SACCX,EAAa8C,gBAAgBc,KAAK,WAKxC5D,EAAa4C,cACZnC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,oBACRC,EAAAA,EAAAA,KAAA,OAAKE,MAAO,CACV2C,UAAW,SACX1C,QAAS,UACTW,gBAAiB,UACjBE,aAAc,MACd8B,SAAU,UACVG,SAAU,OACVC,UAAW,SACXnD,SACCqB,KAAKC,UAAUjC,EAAa4C,YAAa,KAAM,QAKrD5C,EAAaR,aACZiB,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,+BACRC,EAAAA,EAAAA,KAAA,OAAKE,MAAO,CACV2C,UAAW,SACX1C,QAAS,UACTW,gBAAiB,UACjBE,aAAc,MACd8B,SAAU,UACVG,SAAU,QACVlD,SACCqB,KAAKC,UAAUjC,EAAaR,WAAY,KAAM,QAKpDQ,EAAa1B,QACZmC,EAAAA,EAAAA,MAAA,OAAAE,SAAA,EACEC,EAAAA,EAAAA,KAAA,UAAQE,MAAO,CAAEQ,MAAO,WAAYX,SAAC,YACrCC,EAAAA,EAAAA,KAAA,OAAKE,MAAO,CACV2C,UAAW,SACX1C,QAAS,UACTW,gBAAiB,UACjBJ,MAAO,UACPM,aAAc,MACd4B,WAAY,YACZE,SAAU,YACV/C,SACCX,EAAa1B,kBAQ1BmC,EAAAA,EAAAA,MAAA,OAAKK,MAAO,CACV2C,UAAW,OACX1C,QAAS,SACTW,gBAAiB,UACjBE,aAAc,QACdjB,SAAA,EACAC,EAAAA,EAAAA,KAAA,MAAIE,MAAO,CAAEG,OAAQ,aAAcK,MAAO,WAAYX,SAAC,uCACvDF,EAAAA,EAAAA,MAAA,MAAIK,MAAO,CAAEG,OAAQ,EAAGqC,YAAa,UAAW3C,SAAA,EAC9CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,yBAA6B,0EACzCF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,4BAAgC,uEAC5CF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,uBAA2B,8DACvCF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,kBAAsB,8DAClCF,EAAAA,EAAAA,MAAA,MAAAE,SAAA,EAAIC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,2BAA+B,qDAK/C,C", "sources": ["services/reportsRoutingService.ts", "components/Reports/ReportsDebugScreen.tsx"], "sourcesContent": ["import { auth, db } from '../config/firebase';\nimport { doc, getDoc } from 'firebase/firestore';\n\n/**\n * Service to determine which type of reports screen to show based on user's office\n */\nexport class ReportsRoutingService {\n  private static cachedUserOffice: string | null = null;\n  private static cachedIsDivisionUser: boolean | null = null;\n  private static cacheTimestamp: Date | null = null;\n  private static readonly CACHE_EXPIRY = 30 * 60 * 1000; // 30 minutes\n\n  /**\n   * Determines if the current user should see the comprehensive reports (Division users)\n   * or the simple office-only reports (non-Division users)\n   */\n  static async shouldShowComprehensiveReports(): Promise<boolean> {\n    try {\n      // For debugging: always log cache status\n      console.log('📋 ReportsRoutingService: shouldShowComprehensiveReports called');\n      console.log('📋 ReportsRoutingService: Cache valid:', this.isCacheValid());\n      console.log('📋 ReportsRoutingService: Cached isDivisionUser:', this.cachedIsDivisionUser);\n      console.log('📋 ReportsRoutingService: Cached office:', this.cachedUserOffice);\n\n      // Check cache first\n      if (this.isCacheValid() && this.cachedIsDivisionUser !== null) {\n        console.log('📋 ReportsRoutingService: Using cached result: isDivision=', this.cachedIsDivisionUser);\n        return this.cachedIsDivisionUser;\n      }\n\n      console.log('📋 ReportsRoutingService: Cache invalid or empty, determining fresh report type...');\n\n      const user = auth.currentUser;\n      if (!user) {\n        console.log('❌ ReportsRoutingService: No user logged in');\n        return false;\n      }\n\n      console.log('📋 ReportsRoutingService: Current user UID:', user.uid);\n\n      // Get user's office from Firebase\n      const userDoc = await getDoc(doc(db, 'employees', user.uid));\n\n      if (!userDoc.exists()) {\n        console.log('❌ ReportsRoutingService: User document not found');\n        return false;\n      }\n\n      const userData = userDoc.data();\n      const officeName = userData?.officeName as string | undefined;\n\n      if (!officeName || officeName.trim() === '') {\n        console.log('❌ ReportsRoutingService: User office name not found');\n        return false;\n      }\n\n      console.log('📋 ReportsRoutingService: User office:', `\"${officeName}\"`);\n\n      // Check if office name ends with 'Division' (case-insensitive)\n      const trimmed = officeName.trim();\n      const lowercase = trimmed.toLowerCase();\n      const isDivisionUser = lowercase.endsWith('division');\n\n      console.log('📋 ReportsRoutingService: Office name:', `\"${officeName}\"`);\n      console.log('📋 ReportsRoutingService: Trimmed:', `\"${trimmed}\"`);\n      console.log('📋 ReportsRoutingService: Lowercase:', `\"${lowercase}\"`);\n      console.log('📋 ReportsRoutingService: Ends with \"division\":', isDivisionUser);\n      console.log('📋 ReportsRoutingService: Office type:', isDivisionUser ? 'DIVISION (Comprehensive Reports)' : 'OFFICE (Simple Reports)');\n\n      // Cache the results\n      this.cachedUserOffice = officeName;\n      this.cachedIsDivisionUser = isDivisionUser;\n      this.cacheTimestamp = new Date();\n\n      console.log('📋 ReportsRoutingService: Cached results - Office:', this.cachedUserOffice);\n      console.log('📋 ReportsRoutingService: Cached results - isDivision:', this.cachedIsDivisionUser);\n\n      if (isDivisionUser) {\n        console.log('✅ ReportsRoutingService: User is Division-level → Report Screen 1 (Comprehensive)');\n      } else {\n        console.log('✅ ReportsRoutingService: User is Office-level → Report Screen 2 (Table View Only)');\n      }\n\n      return isDivisionUser;\n\n    } catch (error) {\n      console.error('❌ ReportsRoutingService: Error determining report type:', error);\n      return false; // Default to simple reports on error\n    }\n  }\n\n  /**\n   * Gets the current user's office name\n   */\n  static async getCurrentUserOfficeName(): Promise<string | null> {\n    try {\n      console.log('🔍 ReportsRoutingService: getCurrentUserOfficeName() called');\n\n      // Use cached value if available and valid\n      if (this.isCacheValid() && this.cachedUserOffice !== null) {\n        console.log('📋 ReportsRoutingService: Using cached office name:', this.cachedUserOffice);\n        return this.cachedUserOffice;\n      }\n\n      console.log('🔍 ReportsRoutingService: Cache invalid or empty, fetching fresh data');\n      console.log('🔍 ReportsRoutingService: Cache valid:', this.isCacheValid());\n      console.log('🔍 ReportsRoutingService: Cached office:', this.cachedUserOffice);\n\n      const user = auth.currentUser;\n      if (!user) {\n        console.log('❌ ReportsRoutingService: No user logged in');\n        return null;\n      }\n\n      console.log('🔍 ReportsRoutingService: Current user UID:', user.uid);\n      console.log('🔍 ReportsRoutingService: Current user email:', user.email);\n\n      console.log('🔍 ReportsRoutingService: Fetching user document from Firebase...');\n      const userDoc = await getDoc(doc(db, 'employees', user.uid));\n\n      if (!userDoc.exists()) {\n        console.log('❌ ReportsRoutingService: User document does not exist in employees collection');\n        console.log('🔍 ReportsRoutingService: Document path:', `employees/${user.uid}`);\n        return null;\n      }\n\n      console.log('✅ ReportsRoutingService: User document found');\n      const userData = userDoc.data();\n      console.log('🔍 ReportsRoutingService: Raw user data:', userData);\n\n      const officeName = userData?.officeName as string | undefined;\n      console.log('🔍 ReportsRoutingService: Extracted officeName:', officeName);\n      console.log('🔍 ReportsRoutingService: officeName type:', typeof officeName);\n      console.log('🔍 ReportsRoutingService: officeName is null/undefined:', officeName == null);\n\n      // Check all possible office-related fields\n      console.log('🔍 ReportsRoutingService: All user data keys:', Object.keys(userData || {}));\n      console.log('🔍 ReportsRoutingService: userData.officeName:', userData?.officeName);\n      console.log('🔍 ReportsRoutingService: userData.office_name:', userData?.office_name);\n      console.log('🔍 ReportsRoutingService: userData.office:', userData?.office);\n      console.log('🔍 ReportsRoutingService: userData.Office:', userData?.Office);\n\n      // Update cache\n      this.cachedUserOffice = officeName || null;\n      this.cacheTimestamp = new Date();\n\n      console.log('🔍 ReportsRoutingService: Final office name result:', officeName || 'NULL');\n      return officeName || null;\n\n    } catch (error) {\n      console.error('❌ ReportsRoutingService: Error getting user office:', error);\n      console.error('❌ ReportsRoutingService: Error details:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Gets detailed information about the user's office type and access level\n   */\n  static async getUserOfficeInfo(): Promise<{\n    officeName: string | null;\n    isDivisionUser: boolean;\n    accessLevel: string;\n    reportType: string;\n    description: string;\n  }> {\n    try {\n      console.log('🔍 getUserOfficeInfo: Starting fresh analysis...');\n\n      // Clear cache to ensure fresh data\n      this.clearCache();\n\n      const officeName = await this.getCurrentUserOfficeName();\n      console.log('🔍 getUserOfficeInfo: Got office name:', officeName);\n\n      // Use direct division logic test instead of cached shouldShowComprehensiveReports\n      let isDivisionUser = false;\n      if (officeName && officeName.trim() !== '') {\n        isDivisionUser = officeName.trim().toLowerCase().endsWith('division');\n        console.log('🔍 getUserOfficeInfo: Direct division check:', isDivisionUser);\n        console.log('🔍 getUserOfficeInfo: Office trimmed lowercase:', `\"${officeName.trim().toLowerCase()}\"`);\n        console.log('🔍 getUserOfficeInfo: Ends with \"division\":', isDivisionUser);\n      }\n\n      if (!officeName) {\n        return {\n          officeName: null,\n          isDivisionUser: false,\n          accessLevel: 'none',\n          reportType: 'none',\n          description: 'No office information available',\n        };\n      }\n\n      let accessLevel: string;\n      let reportType: string;\n      let description: string;\n\n      if (isDivisionUser) {\n        accessLevel = 'division';\n        reportType = 'comprehensive';\n        description = 'Report Screen 1: Summary + Submissions + Table View tabs with multi-level office hierarchy data';\n      } else {\n        accessLevel = 'office';\n        reportType = 'simple';\n        description = 'Report Screen 2: Table View only with office-specific data';\n      }\n\n      console.log('🔍 getUserOfficeInfo: Final result:', {\n        officeName,\n        isDivisionUser,\n        accessLevel,\n        reportType\n      });\n\n      return {\n        officeName,\n        isDivisionUser,\n        accessLevel,\n        reportType,\n        description,\n      };\n\n    } catch (error) {\n      console.error('❌ ReportsRoutingService: Error getting office info:', error);\n      return {\n        officeName: null,\n        isDivisionUser: false,\n        accessLevel: 'error',\n        reportType: 'simple',\n        description: 'Error loading office information',\n      };\n    }\n  }\n\n  /**\n   * Forces a fresh check bypassing all cache (for debugging)\n   */\n  static async forceRefreshDivisionStatus(): Promise<boolean> {\n    console.log('🔄 ReportsRoutingService: Force refresh - bypassing all cache');\n\n    // Clear cache completely\n    this.clearCache();\n\n    // Force fresh check\n    const result = await this.shouldShowComprehensiveReports();\n\n    console.log('🔄 ReportsRoutingService: Force refresh result:', result);\n    return result;\n  }\n\n  /**\n   * Clears the cache (useful for testing or when user data changes)\n   */\n  static clearCache(): void {\n    this.cachedUserOffice = null;\n    this.cachedIsDivisionUser = null;\n    this.cacheTimestamp = null;\n    console.log('🗑️ ReportsRoutingService: Cache cleared');\n  }\n\n  /**\n   * Checks if the cached data is still valid\n   */\n  private static isCacheValid(): boolean {\n    if (!this.cacheTimestamp) {\n      return false;\n    }\n    \n    const now = new Date();\n    const difference = now.getTime() - this.cacheTimestamp.getTime();\n    return difference < this.CACHE_EXPIRY;\n  }\n\n  /**\n   * Gets a human-readable description of the user's report access\n   */\n  static async getAccessDescription(): Promise<string> {\n    try {\n      const officeInfo = await this.getUserOfficeInfo();\n\n      if (!officeInfo.officeName) {\n        return 'No office information available';\n      }\n\n      if (officeInfo.isDivisionUser) {\n        return 'Division-level access: You can view Report Screen 1 with comprehensive reports including Summary, Submissions, and Table View tabs with multi-level office hierarchy data.';\n      } else {\n        return `Office-level access: You can view Report Screen 2 with Table View only containing data specific to your office (${officeInfo.officeName}).`;\n      }\n\n    } catch (error) {\n      return 'Error determining access level';\n    }\n  }\n\n  /**\n   * Direct test of division logic without cache (for debugging)\n   */\n  static async testDivisionLogicDirect(): Promise<{\n    officeName: string | null;\n    trimmed: string | null;\n    lowercase: string | null;\n    endsWithDivision: boolean;\n    shouldShowComprehensive: boolean;\n  }> {\n    try {\n      console.log('🧪 === DIRECT DIVISION LOGIC TEST ===');\n\n      // Get fresh data without cache\n      const user = auth.currentUser;\n      if (!user) {\n        console.log('❌ Direct test: No user logged in');\n        return {\n          officeName: null,\n          trimmed: null,\n          lowercase: null,\n          endsWithDivision: false,\n          shouldShowComprehensive: false,\n        };\n      }\n\n      const userDoc = await getDoc(doc(db, 'employees', user.uid));\n      if (!userDoc.exists()) {\n        console.log('❌ Direct test: User document not found');\n        return {\n          officeName: null,\n          trimmed: null,\n          lowercase: null,\n          endsWithDivision: false,\n          shouldShowComprehensive: false,\n        };\n      }\n\n      const userData = userDoc.data();\n      const officeName = userData?.officeName as string | undefined;\n\n      console.log('🧪 Direct test: Raw office name:', officeName);\n\n      if (!officeName) {\n        console.log('❌ Direct test: Office name is null/undefined');\n        return {\n          officeName: null,\n          trimmed: null,\n          lowercase: null,\n          endsWithDivision: false,\n          shouldShowComprehensive: false,\n        };\n      }\n\n      const trimmed = officeName.trim();\n      const lowercase = trimmed.toLowerCase();\n      const endsWithDivision = lowercase.endsWith('division');\n\n      console.log('🧪 Direct test: Original:', `\"${officeName}\"`);\n      console.log('🧪 Direct test: Trimmed:', `\"${trimmed}\"`);\n      console.log('🧪 Direct test: Lowercase:', `\"${lowercase}\"`);\n      console.log('🧪 Direct test: Ends with \"division\":', endsWithDivision);\n      console.log('🧪 Direct test: Should show comprehensive:', endsWithDivision);\n\n      console.log('🧪 === END DIRECT DIVISION LOGIC TEST ===');\n\n      return {\n        officeName,\n        trimmed,\n        lowercase,\n        endsWithDivision,\n        shouldShowComprehensive: endsWithDivision,\n      };\n\n    } catch (error) {\n      console.error('❌ Direct test: Error:', error);\n      return {\n        officeName: null,\n        trimmed: null,\n        lowercase: null,\n        endsWithDivision: false,\n        shouldShowComprehensive: false,\n      };\n    }\n  }\n\n  /**\n   * Logs detailed information about the user's report access (for debugging)\n   */\n  static async logUserAccessInfo(): Promise<void> {\n    try {\n      console.log('📋 === ReportsRoutingService: User Access Information ===');\n\n      const officeInfo = await this.getUserOfficeInfo();\n\n      console.log('📋 Office Name:', officeInfo.officeName);\n      console.log('📋 Is Division User:', officeInfo.isDivisionUser);\n      console.log('📋 Access Level:', officeInfo.accessLevel);\n      console.log('📋 Report Type:', officeInfo.reportType);\n      console.log('📋 Description:', officeInfo.description);\n\n      const accessDescription = await this.getAccessDescription();\n      console.log('📋 Access Description:', accessDescription);\n\n      console.log('📋 === End User Access Information ===');\n\n    } catch (error) {\n      console.error('❌ ReportsRoutingService: Error logging access info:', error);\n    }\n  }\n}\n\nexport default ReportsRoutingService;\n", "import React, { useState } from 'react';\nimport { useAuth } from '../../contexts/AuthContext';\nimport ReportsRoutingService from '../../services/reportsRoutingService';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport Sidebar from '../shared/Sidebar';\n\ninterface DebugResults {\n  timestamp: string;\n  userAuthenticated: boolean;\n  userUID: string | null;\n  userEmail: string | null;\n  userDocExists: boolean;\n  rawUserData: any;\n  officeName: string | null;\n  officeNameType: string;\n  allUserDataKeys: string[];\n  shouldShowComprehensive: boolean;\n  officeInfo: any;\n  error?: string;\n}\n\n/**\n * Debug screen to test and troubleshoot the reports routing logic\n */\nconst ReportsDebugScreen: React.FC = () => {\n  const { currentUser } = useAuth();\n  const [debugResults, setDebugResults] = useState<DebugResults | null>(null);\n  const [isLoading, setIsLoading] = useState(false);\n  const [userData, setUserData] = useState<any>(null);\n\n  // Fetch basic user data for sidebar\n  React.useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        try {\n          const userDoc = await getDoc(doc(db, 'employees', currentUser.uid));\n          if (userDoc.exists()) {\n            setUserData(userDoc.data());\n          }\n        } catch (error) {\n          console.error('Error fetching user data for sidebar:', error);\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  const runDebugTest = async () => {\n    setIsLoading(true);\n    console.log('🧪 === REACT ROUTING DEBUG TEST ===');\n\n    try {\n      const results: DebugResults = {\n        timestamp: new Date().toISOString(),\n        userAuthenticated: !!currentUser,\n        userUID: currentUser?.uid || null,\n        userEmail: currentUser?.email || null,\n        userDocExists: false,\n        rawUserData: null,\n        officeName: null,\n        officeNameType: 'undefined',\n        allUserDataKeys: [],\n        shouldShowComprehensive: false,\n        officeInfo: null,\n      };\n\n      console.log('🧪 Debug: User authenticated:', results.userAuthenticated);\n      console.log('🧪 Debug: User UID:', results.userUID);\n      console.log('🧪 Debug: User email:', results.userEmail);\n\n      if (currentUser) {\n        // Test direct Firebase access\n        console.log('🧪 Debug: Testing direct Firebase document access...');\n        const userDoc = await getDoc(doc(db, 'employees', currentUser.uid));\n        \n        results.userDocExists = userDoc.exists();\n        console.log('🧪 Debug: User document exists:', results.userDocExists);\n\n        if (userDoc.exists()) {\n          results.rawUserData = userDoc.data();\n          results.allUserDataKeys = Object.keys(results.rawUserData || {});\n          \n          console.log('🧪 Debug: Raw user data:', results.rawUserData);\n          console.log('🧪 Debug: All data keys:', results.allUserDataKeys);\n\n          // Extract office name\n          results.officeName = results.rawUserData?.officeName || null;\n          results.officeNameType = typeof results.officeName;\n          \n          console.log('🧪 Debug: Extracted office name:', results.officeName);\n          console.log('🧪 Debug: Office name type:', results.officeNameType);\n\n          // Test ReportsRoutingService\n          console.log('🧪 Debug: Testing ReportsRoutingService...');\n\n          // Clear cache first\n          ReportsRoutingService.clearCache();\n\n          // Test office name retrieval\n          const serviceOfficeName = await ReportsRoutingService.getCurrentUserOfficeName();\n          console.log('🧪 Debug: Service office name:', serviceOfficeName);\n\n          // Manual test of the division logic\n          if (results.officeName) {\n            console.log('🧪 Debug: === MANUAL DIVISION LOGIC TEST ===');\n            console.log('🧪 Debug: Original office name:', `\"${results.officeName}\"`);\n            console.log('🧪 Debug: Trimmed office name:', `\"${results.officeName.trim()}\"`);\n            console.log('🧪 Debug: Lowercase office name:', `\"${results.officeName.trim().toLowerCase()}\"`);\n            console.log('🧪 Debug: Ends with \"division\":', results.officeName.trim().toLowerCase().endsWith('division'));\n            console.log('🧪 Debug: Manual division check result:', results.officeName.trim().toLowerCase().endsWith('division'));\n            console.log('🧪 Debug: === END MANUAL DIVISION LOGIC TEST ===');\n          }\n\n          // Test direct division logic (bypasses cache)\n          const directTest = await ReportsRoutingService.testDivisionLogicDirect();\n          console.log('🧪 Debug: Direct division test:', directTest);\n\n          // Test comprehensive reports check\n          results.shouldShowComprehensive = await ReportsRoutingService.shouldShowComprehensiveReports();\n          console.log('🧪 Debug: Should show comprehensive:', results.shouldShowComprehensive);\n\n          // Test office info\n          results.officeInfo = await ReportsRoutingService.getUserOfficeInfo();\n          console.log('🧪 Debug: Office info:', results.officeInfo);\n\n          // Add direct test results to results object\n          (results as any).directTest = directTest;\n        }\n      }\n\n      setDebugResults(results);\n      console.log('🧪 === END REACT ROUTING DEBUG TEST ===');\n\n    } catch (error) {\n      console.error('🧪 Debug: Error during test:', error);\n      setDebugResults({\n        timestamp: new Date().toISOString(),\n        userAuthenticated: !!currentUser,\n        userUID: currentUser?.uid || null,\n        userEmail: currentUser?.email || null,\n        userDocExists: false,\n        rawUserData: null,\n        officeName: null,\n        officeNameType: 'error',\n        allUserDataKeys: [],\n        shouldShowComprehensive: false,\n        officeInfo: null,\n        error: error instanceof Error ? error.message : String(error),\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const clearCache = () => {\n    ReportsRoutingService.clearCache();\n    console.log('🗑️ Cache cleared');\n  };\n\n  const forceRefresh = async () => {\n    console.log('🔄 Force refreshing division status...');\n    try {\n      const result = await ReportsRoutingService.forceRefreshDivisionStatus();\n      console.log('🔄 Force refresh result:', result);\n\n      // Also get fresh office info\n      const officeInfo = await ReportsRoutingService.getUserOfficeInfo();\n      console.log('🔄 Fresh office info:', officeInfo);\n\n      alert(`Force refresh complete!\\nDivision User: ${result}\\nOffice Info: ${JSON.stringify(officeInfo, null, 2)}`);\n    } catch (error) {\n      console.error('❌ Force refresh error:', error);\n      alert(`Force refresh failed: ${error}`);\n    }\n  };\n\n  const testDivisionLogic = () => {\n    console.log('🧪 === MANUAL DIVISION LOGIC TEST ===');\n\n    const testCases = [\n      'Coimbatore Division',\n      'Chennai Division',\n      'Mumbai Division',\n      'Tirupur Division',\n      'Chennai RO',\n      'Mumbai BO',\n      'Delhi SO',\n      'Bangalore HO',\n      'coimbatore division', // lowercase\n      'COIMBATORE DIVISION', // uppercase\n      'Coimbatore  Division  ', // with spaces\n    ];\n\n    testCases.forEach(testCase => {\n      const trimmed = testCase.trim();\n      const lowercase = trimmed.toLowerCase();\n      const result = lowercase.endsWith('division');\n\n      console.log(`🧪 Test: \"${testCase}\"`);\n      console.log(`   Trimmed: \"${trimmed}\"`);\n      console.log(`   Lowercase: \"${lowercase}\"`);\n      console.log(`   Ends with \"division\": ${result}`);\n      console.log(`   Expected Screen: ${result ? 'Report Screen 1 (Comprehensive)' : 'Report Screen 2 (Table Only)'}`);\n      console.log('');\n    });\n\n    console.log('🧪 === END MANUAL DIVISION LOGIC TEST ===');\n  };\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n      <div className=\"main-content\">\n        <div style={{ padding: '2rem', maxWidth: '1200px', margin: '0 auto' }}>\n          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>\n            <h1 style={{ margin: 0, color: '#1E3A8A' }}>🧪 Reports Routing Debug</h1>\n            <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>\n              <button\n                onClick={clearCache}\n                style={{\n                  padding: '0.75rem 1.5rem',\n                  backgroundColor: '#6c757d',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  cursor: 'pointer',\n                  fontWeight: '500'\n                }}\n              >\n                🗑️ Clear Cache\n              </button>\n              <button\n                onClick={forceRefresh}\n                style={{\n                  padding: '0.75rem 1.5rem',\n                  backgroundColor: '#dc3545',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  cursor: 'pointer',\n                  fontWeight: '500'\n                }}\n              >\n                🔄 Force Refresh\n              </button>\n              <button\n                onClick={testDivisionLogic}\n                style={{\n                  padding: '0.75rem 1.5rem',\n                  backgroundColor: '#28a745',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  cursor: 'pointer',\n                  fontWeight: '500'\n                }}\n              >\n                🔍 Test Division Logic\n              </button>\n              <button\n                onClick={runDebugTest}\n                disabled={isLoading}\n                style={{\n                  padding: '0.75rem 1.5rem',\n                  backgroundColor: isLoading ? '#6c757d' : '#1E3A8A',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '8px',\n                  cursor: isLoading ? 'not-allowed' : 'pointer',\n                  fontWeight: '500'\n                }}\n              >\n                {isLoading ? '🔄 Testing...' : '🧪 Run Debug Test'}\n              </button>\n            </div>\n          </div>\n\n          <div style={{\n            backgroundColor: '#f8f9fa',\n            border: '1px solid #dee2e6',\n            borderRadius: '12px',\n            padding: '1.5rem',\n            marginBottom: '2rem'\n          }}>\n            <h3 style={{ margin: '0 0 1rem 0', color: '#495057' }}>📋 Debug Instructions</h3>\n            <ol style={{ margin: 0, paddingLeft: '1.5rem' }}>\n              <li>Click \"Run Debug Test\" to analyze your office data and routing logic</li>\n              <li>Check the console for detailed logs during the test</li>\n              <li>Review the results below to identify any issues</li>\n              <li>Use \"Clear Cache\" to force fresh data retrieval</li>\n            </ol>\n          </div>\n\n          {debugResults && (\n            <div style={{\n              backgroundColor: 'white',\n              border: '1px solid #dee2e6',\n              borderRadius: '12px',\n              padding: '1.5rem'\n            }}>\n              <h3 style={{ margin: '0 0 1.5rem 0', color: '#495057' }}>🔍 Debug Results</h3>\n              \n              <div style={{ display: 'grid', gap: '1rem' }}>\n                <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr', gap: '0.5rem', alignItems: 'start' }}>\n                  <strong>Timestamp:</strong>\n                  <span style={{ fontFamily: 'monospace' }}>{debugResults.timestamp}</span>\n                  \n                  <strong>User Authenticated:</strong>\n                  <span style={{ color: debugResults.userAuthenticated ? '#28a745' : '#dc3545' }}>\n                    {debugResults.userAuthenticated ? '✅ Yes' : '❌ No'}\n                  </span>\n                  \n                  <strong>User UID:</strong>\n                  <span style={{ fontFamily: 'monospace' }}>{debugResults.userUID || 'NULL'}</span>\n                  \n                  <strong>User Email:</strong>\n                  <span style={{ fontFamily: 'monospace' }}>{debugResults.userEmail || 'NULL'}</span>\n                  \n                  <strong>Document Exists:</strong>\n                  <span style={{ color: debugResults.userDocExists ? '#28a745' : '#dc3545' }}>\n                    {debugResults.userDocExists ? '✅ Yes' : '❌ No'}\n                  </span>\n                  \n                  <strong>Office Name:</strong>\n                  <span style={{ \n                    fontFamily: 'monospace',\n                    color: debugResults.officeName ? '#28a745' : '#dc3545',\n                    fontWeight: 'bold'\n                  }}>\n                    {debugResults.officeName || 'NULL/UNDEFINED'}\n                  </span>\n                  \n                  <strong>Office Name Type:</strong>\n                  <span style={{ fontFamily: 'monospace' }}>{debugResults.officeNameType}</span>\n                  \n                  <strong>Should Show Comprehensive:</strong>\n                  <span style={{ color: debugResults.shouldShowComprehensive ? '#007bff' : '#28a745' }}>\n                    {debugResults.shouldShowComprehensive ? '📊 Report Screen 1 (Comprehensive)' : '📋 Report Screen 2 (Table Only)'}\n                  </span>\n                </div>\n\n                {/* Direct Test Results */}\n                {(debugResults as any).directTest && (\n                  <div style={{\n                    marginTop: '1rem',\n                    padding: '1rem',\n                    backgroundColor: '#fff3cd',\n                    border: '1px solid #ffeaa7',\n                    borderRadius: '8px'\n                  }}>\n                    <strong style={{ color: '#856404' }}>🧪 Direct Division Logic Test (Bypasses Cache):</strong>\n                    <div style={{ marginTop: '0.5rem', display: 'grid', gridTemplateColumns: '150px 1fr', gap: '0.5rem', fontSize: '0.875rem' }}>\n                      <span>Original:</span>\n                      <span style={{ fontFamily: 'monospace' }}>\"{(debugResults as any).directTest.officeName}\"</span>\n\n                      <span>Trimmed:</span>\n                      <span style={{ fontFamily: 'monospace' }}>\"{(debugResults as any).directTest.trimmed}\"</span>\n\n                      <span>Lowercase:</span>\n                      <span style={{ fontFamily: 'monospace' }}>\"{(debugResults as any).directTest.lowercase}\"</span>\n\n                      <span>Ends with \"division\":</span>\n                      <span style={{\n                        fontFamily: 'monospace',\n                        color: (debugResults as any).directTest.endsWithDivision ? '#28a745' : '#dc3545',\n                        fontWeight: 'bold'\n                      }}>\n                        {(debugResults as any).directTest.endsWithDivision ? '✅ TRUE' : '❌ FALSE'}\n                      </span>\n\n                      <span>Expected Screen:</span>\n                      <span style={{\n                        fontWeight: 'bold',\n                        color: (debugResults as any).directTest.shouldShowComprehensive ? '#007bff' : '#28a745'\n                      }}>\n                        {(debugResults as any).directTest.shouldShowComprehensive ? '📊 Report Screen 1' : '📋 Report Screen 2'}\n                      </span>\n                    </div>\n                  </div>\n                )}\n\n                <div style={{ display: 'grid', gridTemplateColumns: '200px 1fr', gap: '0.5rem', alignItems: 'start' }}>\n                </div>\n\n                {debugResults.allUserDataKeys.length > 0 && (\n                  <div>\n                    <strong>Available Data Keys:</strong>\n                    <div style={{ \n                      marginTop: '0.5rem',\n                      padding: '0.75rem',\n                      backgroundColor: '#f8f9fa',\n                      borderRadius: '4px',\n                      fontFamily: 'monospace',\n                      fontSize: '0.875rem'\n                    }}>\n                      {debugResults.allUserDataKeys.join(', ')}\n                    </div>\n                  </div>\n                )}\n\n                {debugResults.rawUserData && (\n                  <div>\n                    <strong>Raw User Data:</strong>\n                    <pre style={{ \n                      marginTop: '0.5rem',\n                      padding: '0.75rem',\n                      backgroundColor: '#f8f9fa',\n                      borderRadius: '4px',\n                      fontSize: '0.75rem',\n                      overflow: 'auto',\n                      maxHeight: '200px'\n                    }}>\n                      {JSON.stringify(debugResults.rawUserData, null, 2)}\n                    </pre>\n                  </div>\n                )}\n\n                {debugResults.officeInfo && (\n                  <div>\n                    <strong>Office Info from Service:</strong>\n                    <pre style={{ \n                      marginTop: '0.5rem',\n                      padding: '0.75rem',\n                      backgroundColor: '#f8f9fa',\n                      borderRadius: '4px',\n                      fontSize: '0.75rem',\n                      overflow: 'auto'\n                    }}>\n                      {JSON.stringify(debugResults.officeInfo, null, 2)}\n                    </pre>\n                  </div>\n                )}\n\n                {debugResults.error && (\n                  <div>\n                    <strong style={{ color: '#dc3545' }}>Error:</strong>\n                    <div style={{ \n                      marginTop: '0.5rem',\n                      padding: '0.75rem',\n                      backgroundColor: '#f8d7da',\n                      color: '#721c24',\n                      borderRadius: '4px',\n                      fontFamily: 'monospace',\n                      fontSize: '0.875rem'\n                    }}>\n                      {debugResults.error}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n\n          <div style={{\n            marginTop: '2rem',\n            padding: '1.5rem',\n            backgroundColor: '#e3f2fd',\n            borderRadius: '12px'\n          }}>\n            <h4 style={{ margin: '0 0 1rem 0', color: '#1565c0' }}>💡 Troubleshooting Tips</h4>\n            <ul style={{ margin: 0, paddingLeft: '1.5rem' }}>\n              <li><strong>Office Name is NULL:</strong> Check if the user's document in Firebase has an 'officeName' field</li>\n              <li><strong>Document doesn't exist:</strong> User may not be properly registered in the employees collection</li>\n              <li><strong>Wrong office name:</strong> Verify the office name format and spelling in Firebase</li>\n              <li><strong>Cache issues:</strong> Use \"Clear Cache\" button to force fresh data retrieval</li>\n              <li><strong>Authentication issues:</strong> Ensure user is properly logged in</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReportsDebugScreen;\n"], "names": ["ReportsRoutingService", "shouldShowComprehensiveReports", "console", "log", "this", "isCache<PERSON><PERSON>d", "cachedIsDivisionUser", "cachedUserOffice", "user", "auth", "currentUser", "uid", "userDoc", "getDoc", "doc", "db", "exists", "userData", "data", "officeName", "trim", "trimmed", "lowercase", "toLowerCase", "isDivisionUser", "endsWith", "cacheTimestamp", "Date", "error", "getCurrentUserOfficeName", "email", "Object", "keys", "office_name", "office", "Office", "getUserOfficeInfo", "clearCache", "accessLevel", "reportType", "description", "forceRefreshDivisionStatus", "result", "getTime", "CACHE_EXPIRY", "getAccessDescription", "officeInfo", "testDivisionLogicDirect", "endsWithDivision", "shouldShowComprehensive", "logUserAccessInfo", "accessDescription", "ReportsDebugScreen", "useAuth", "debugResults", "setDebugResults", "useState", "isLoading", "setIsLoading", "setUserData", "React", "async", "fetchUserData", "_jsxs", "className", "children", "_jsx", "Sidebar", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "display", "justifyContent", "alignItems", "marginBottom", "color", "gap", "flexWrap", "onClick", "backgroundColor", "border", "borderRadius", "cursor", "fontWeight", "alert", "JSON", "stringify", "testDivisionLogic", "for<PERSON>ach", "testCase", "results", "timestamp", "toISOString", "userAuthenticated", "userUID", "userEmail", "userDocExists", "rawUserData", "officeNameType", "allUserDataKeys", "_results$rawUserData", "serviceOfficeName", "directTest", "Error", "message", "String", "disabled", "paddingLeft", "gridTemplateColumns", "fontFamily", "marginTop", "fontSize", "length", "join", "overflow", "maxHeight"], "sourceRoot": ""}