"use strict";(self.webpackChunkindia_post_web_app=self.webpackChunkindia_post_web_app||[]).push([[905],{2286:(e,o,i)=>{i.r(o),i.d(o,{default:()=>f});var s=i(5043),r=i(9066),n=i(2073),t=i(5472);class c{static async shouldShowComprehensiveReports(){try{if(console.log("\ud83d\udccb ReportsRoutingService: shouldShowComprehensiveReports called"),console.log("\ud83d\udccb ReportsRoutingService: Cache valid:",this.isCacheValid()),console.log("\ud83d\udccb ReportsRoutingService: Cached isDivisionUser:",this.cachedIsDivisionUser),console.log("\ud83d\udccb ReportsRoutingService: Cached office:",this.cachedUserOffice),this.isCacheValid()&&null!==this.cachedIsDivisionUser)return console.log("\ud83d\udccb ReportsRoutingService: Using cached result: isDivision=",this.cachedIsDivisionUser),this.cachedIsDivisionUser;console.log("\ud83d\udccb ReportsRoutingService: Cache invalid or empty, determining fresh report type...");const e=n.auth.currentUser;if(!e)return console.log("\u274c ReportsRoutingService: No user logged in"),!1;console.log("\ud83d\udccb ReportsRoutingService: Current user UID:",e.uid);const o=await(0,t.x7)((0,t.H9)(n.db,"employees",e.uid));if(!o.exists())return console.log("\u274c ReportsRoutingService: User document not found"),!1;const i=o.data(),s=null===i||void 0===i?void 0:i.officeName;if(!s||""===s.trim())return console.log("\u274c ReportsRoutingService: User office name not found"),!1;console.log("\ud83d\udccb ReportsRoutingService: User office:",`"${s}"`);const r=s.trim(),c=r.toLowerCase(),l=c.endsWith("division");return console.log("\ud83d\udccb ReportsRoutingService: Office name:",`"${s}"`),console.log("\ud83d\udccb ReportsRoutingService: Trimmed:",`"${r}"`),console.log("\ud83d\udccb ReportsRoutingService: Lowercase:",`"${c}"`),console.log('\ud83d\udccb ReportsRoutingService: Ends with "division":',l),console.log("\ud83d\udccb ReportsRoutingService: Office type:",l?"DIVISION (Comprehensive Reports)":"OFFICE (Simple Reports)"),this.cachedUserOffice=s,this.cachedIsDivisionUser=l,this.cacheTimestamp=new Date,console.log("\ud83d\udccb ReportsRoutingService: Cached results - Office:",this.cachedUserOffice),console.log("\ud83d\udccb ReportsRoutingService: Cached results - isDivision:",this.cachedIsDivisionUser),l?console.log("\u2705 ReportsRoutingService: User is Division-level \u2192 Report Screen 1 (Comprehensive)"):console.log("\u2705 ReportsRoutingService: User is Office-level \u2192 Report Screen 2 (Table View Only)"),l}catch(e){return console.error("\u274c ReportsRoutingService: Error determining report type:",e),!1}}static async getCurrentUserOfficeName(){try{if(console.log("\ud83d\udd0d ReportsRoutingService: getCurrentUserOfficeName() called"),this.isCacheValid()&&null!==this.cachedUserOffice)return console.log("\ud83d\udccb ReportsRoutingService: Using cached office name:",this.cachedUserOffice),this.cachedUserOffice;console.log("\ud83d\udd0d ReportsRoutingService: Cache invalid or empty, fetching fresh data"),console.log("\ud83d\udd0d ReportsRoutingService: Cache valid:",this.isCacheValid()),console.log("\ud83d\udd0d ReportsRoutingService: Cached office:",this.cachedUserOffice);const e=n.auth.currentUser;if(!e)return console.log("\u274c ReportsRoutingService: No user logged in"),null;console.log("\ud83d\udd0d ReportsRoutingService: Current user UID:",e.uid),console.log("\ud83d\udd0d ReportsRoutingService: Current user email:",e.email),console.log("\ud83d\udd0d ReportsRoutingService: Fetching user document from Firebase...");const o=await(0,t.x7)((0,t.H9)(n.db,"employees",e.uid));if(!o.exists())return console.log("\u274c ReportsRoutingService: User document does not exist in employees collection"),console.log("\ud83d\udd0d ReportsRoutingService: Document path:",`employees/${e.uid}`),null;console.log("\u2705 ReportsRoutingService: User document found");const i=o.data();console.log("\ud83d\udd0d ReportsRoutingService: Raw user data:",i);const s=null===i||void 0===i?void 0:i.officeName;return console.log("\ud83d\udd0d ReportsRoutingService: Extracted officeName:",s),console.log("\ud83d\udd0d ReportsRoutingService: officeName type:",typeof s),console.log("\ud83d\udd0d ReportsRoutingService: officeName is null/undefined:",null==s),console.log("\ud83d\udd0d ReportsRoutingService: All user data keys:",Object.keys(i||{})),console.log("\ud83d\udd0d ReportsRoutingService: userData.officeName:",null===i||void 0===i?void 0:i.officeName),console.log("\ud83d\udd0d ReportsRoutingService: userData.office_name:",null===i||void 0===i?void 0:i.office_name),console.log("\ud83d\udd0d ReportsRoutingService: userData.office:",null===i||void 0===i?void 0:i.office),console.log("\ud83d\udd0d ReportsRoutingService: userData.Office:",null===i||void 0===i?void 0:i.Office),this.cachedUserOffice=s||null,this.cacheTimestamp=new Date,console.log("\ud83d\udd0d ReportsRoutingService: Final office name result:",s||"NULL"),s||null}catch(e){return console.error("\u274c ReportsRoutingService: Error getting user office:",e),console.error("\u274c ReportsRoutingService: Error details:",e),null}}static async getUserOfficeInfo(){try{console.log("\ud83d\udd0d getUserOfficeInfo: Starting fresh analysis..."),this.clearCache();const e=await this.getCurrentUserOfficeName();console.log("\ud83d\udd0d getUserOfficeInfo: Got office name:",e);let o,i,s,r=!1;return e&&""!==e.trim()&&(r=e.trim().toLowerCase().endsWith("division"),console.log("\ud83d\udd0d getUserOfficeInfo: Direct division check:",r),console.log("\ud83d\udd0d getUserOfficeInfo: Office trimmed lowercase:",`"${e.trim().toLowerCase()}"`),console.log('\ud83d\udd0d getUserOfficeInfo: Ends with "division":',r)),e?(r?(o="division",i="comprehensive",s="Report Screen 1: Summary + Submissions + Table View tabs with multi-level office hierarchy data"):(o="office",i="simple",s="Report Screen 2: Table View only with office-specific data"),console.log("\ud83d\udd0d getUserOfficeInfo: Final result:",{officeName:e,isDivisionUser:r,accessLevel:o,reportType:i}),{officeName:e,isDivisionUser:r,accessLevel:o,reportType:i,description:s}):{officeName:null,isDivisionUser:!1,accessLevel:"none",reportType:"none",description:"No office information available"}}catch(e){return console.error("\u274c ReportsRoutingService: Error getting office info:",e),{officeName:null,isDivisionUser:!1,accessLevel:"error",reportType:"simple",description:"Error loading office information"}}}static async forceRefreshDivisionStatus(){console.log("\ud83d\udd04 ReportsRoutingService: Force refresh - bypassing all cache"),this.clearCache();const e=await this.shouldShowComprehensiveReports();return console.log("\ud83d\udd04 ReportsRoutingService: Force refresh result:",e),e}static clearCache(){this.cachedUserOffice=null,this.cachedIsDivisionUser=null,this.cacheTimestamp=null,console.log("\ud83d\uddd1\ufe0f ReportsRoutingService: Cache cleared")}static isCacheValid(){if(!this.cacheTimestamp)return!1;return(new Date).getTime()-this.cacheTimestamp.getTime()<this.CACHE_EXPIRY}static async getAccessDescription(){try{const e=await this.getUserOfficeInfo();return e.officeName?e.isDivisionUser?"Division-level access: You can view Report Screen 1 with comprehensive reports including Summary, Submissions, and Table View tabs with multi-level office hierarchy data.":`Office-level access: You can view Report Screen 2 with Table View only containing data specific to your office (${e.officeName}).`:"No office information available"}catch(e){return"Error determining access level"}}static async testDivisionLogicDirect(){try{console.log("\ud83e\uddea === DIRECT DIVISION LOGIC TEST ===");const e=n.auth.currentUser;if(!e)return console.log("\u274c Direct test: No user logged in"),{officeName:null,trimmed:null,lowercase:null,endsWithDivision:!1,shouldShowComprehensive:!1};const o=await(0,t.x7)((0,t.H9)(n.db,"employees",e.uid));if(!o.exists())return console.log("\u274c Direct test: User document not found"),{officeName:null,trimmed:null,lowercase:null,endsWithDivision:!1,shouldShowComprehensive:!1};const i=o.data(),s=null===i||void 0===i?void 0:i.officeName;if(console.log("\ud83e\uddea Direct test: Raw office name:",s),!s)return console.log("\u274c Direct test: Office name is null/undefined"),{officeName:null,trimmed:null,lowercase:null,endsWithDivision:!1,shouldShowComprehensive:!1};const r=s.trim(),c=r.toLowerCase(),l=c.endsWith("division");return console.log("\ud83e\uddea Direct test: Original:",`"${s}"`),console.log("\ud83e\uddea Direct test: Trimmed:",`"${r}"`),console.log("\ud83e\uddea Direct test: Lowercase:",`"${c}"`),console.log('\ud83e\uddea Direct test: Ends with "division":',l),console.log("\ud83e\uddea Direct test: Should show comprehensive:",l),console.log("\ud83e\uddea === END DIRECT DIVISION LOGIC TEST ==="),{officeName:s,trimmed:r,lowercase:c,endsWithDivision:l,shouldShowComprehensive:l}}catch(e){return console.error("\u274c Direct test: Error:",e),{officeName:null,trimmed:null,lowercase:null,endsWithDivision:!1,shouldShowComprehensive:!1}}}static async logUserAccessInfo(){try{console.log("\ud83d\udccb === ReportsRoutingService: User Access Information ===");const e=await this.getUserOfficeInfo();console.log("\ud83d\udccb Office Name:",e.officeName),console.log("\ud83d\udccb Is Division User:",e.isDivisionUser),console.log("\ud83d\udccb Access Level:",e.accessLevel),console.log("\ud83d\udccb Report Type:",e.reportType),console.log("\ud83d\udccb Description:",e.description);const o=await this.getAccessDescription();console.log("\ud83d\udccb Access Description:",o),console.log("\ud83d\udccb === End User Access Information ===")}catch(e){console.error("\u274c ReportsRoutingService: Error logging access info:",e)}}}c.cachedUserOffice=null,c.cachedIsDivisionUser=null,c.cacheTimestamp=null,c.CACHE_EXPIRY=18e5;const l=c;var a=i(1103),d=i(579);const f=()=>{const{currentUser:e}=(0,r.A)(),[o,i]=(0,s.useState)(null),[c,f]=(0,s.useState)(!1),[u,g]=(0,s.useState)(null);s.useEffect((()=>{(async()=>{if(e)try{const o=await(0,t.x7)((0,t.H9)(n.db,"employees",e.uid));o.exists()&&g(o.data())}catch(o){console.error("Error fetching user data for sidebar:",o)}})()}),[e]);return(0,d.jsxs)("div",{className:"dashboard-container",children:[(0,d.jsx)(a.A,{userData:u}),(0,d.jsx)("div",{className:"main-content",children:(0,d.jsxs)("div",{style:{padding:"2rem",maxWidth:"1200px",margin:"0 auto"},children:[(0,d.jsxs)("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"2rem"},children:[(0,d.jsx)("h1",{style:{margin:0,color:"#1E3A8A"},children:"\ud83e\uddea Reports Routing Debug"}),(0,d.jsxs)("div",{style:{display:"flex",gap:"1rem",flexWrap:"wrap"},children:[(0,d.jsx)("button",{onClick:()=>{l.clearCache(),console.log("\ud83d\uddd1\ufe0f Cache cleared")},style:{padding:"0.75rem 1.5rem",backgroundColor:"#6c757d",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontWeight:"500"},children:"\ud83d\uddd1\ufe0f Clear Cache"}),(0,d.jsx)("button",{onClick:async()=>{console.log("\ud83d\udd04 Force refreshing division status...");try{const e=await l.forceRefreshDivisionStatus();console.log("\ud83d\udd04 Force refresh result:",e);const o=await l.getUserOfficeInfo();console.log("\ud83d\udd04 Fresh office info:",o),alert(`Force refresh complete!\nDivision User: ${e}\nOffice Info: ${JSON.stringify(o,null,2)}`)}catch(e){console.error("\u274c Force refresh error:",e),alert(`Force refresh failed: ${e}`)}},style:{padding:"0.75rem 1.5rem",backgroundColor:"#dc3545",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontWeight:"500"},children:"\ud83d\udd04 Force Refresh"}),(0,d.jsx)("button",{onClick:()=>{console.log("\ud83e\uddea === MANUAL DIVISION LOGIC TEST ===");["Coimbatore Division","Chennai Division","Mumbai Division","Tirupur Division","Chennai RO","Mumbai BO","Delhi SO","Bangalore HO","coimbatore division","COIMBATORE DIVISION","Coimbatore  Division  "].forEach((e=>{const o=e.trim(),i=o.toLowerCase(),s=i.endsWith("division");console.log(`\ud83e\uddea Test: "${e}"`),console.log(`   Trimmed: "${o}"`),console.log(`   Lowercase: "${i}"`),console.log(`   Ends with "division": ${s}`),console.log("   Expected Screen: "+(s?"Report Screen 1 (Comprehensive)":"Report Screen 2 (Table Only)")),console.log("")})),console.log("\ud83e\uddea === END MANUAL DIVISION LOGIC TEST ===")},style:{padding:"0.75rem 1.5rem",backgroundColor:"#28a745",color:"white",border:"none",borderRadius:"8px",cursor:"pointer",fontWeight:"500"},children:"\ud83d\udd0d Test Division Logic"}),(0,d.jsx)("button",{onClick:async()=>{f(!0),console.log("\ud83e\uddea === REACT ROUTING DEBUG TEST ===");try{const s={timestamp:(new Date).toISOString(),userAuthenticated:!!e,userUID:(null===e||void 0===e?void 0:e.uid)||null,userEmail:(null===e||void 0===e?void 0:e.email)||null,userDocExists:!1,rawUserData:null,officeName:null,officeNameType:"undefined",allUserDataKeys:[],shouldShowComprehensive:!1,officeInfo:null};if(console.log("\ud83e\uddea Debug: User authenticated:",s.userAuthenticated),console.log("\ud83e\uddea Debug: User UID:",s.userUID),console.log("\ud83e\uddea Debug: User email:",s.userEmail),e){console.log("\ud83e\uddea Debug: Testing direct Firebase document access...");const i=await(0,t.x7)((0,t.H9)(n.db,"employees",e.uid));if(s.userDocExists=i.exists(),console.log("\ud83e\uddea Debug: User document exists:",s.userDocExists),i.exists()){var o;s.rawUserData=i.data(),s.allUserDataKeys=Object.keys(s.rawUserData||{}),console.log("\ud83e\uddea Debug: Raw user data:",s.rawUserData),console.log("\ud83e\uddea Debug: All data keys:",s.allUserDataKeys),s.officeName=(null===(o=s.rawUserData)||void 0===o?void 0:o.officeName)||null,s.officeNameType=typeof s.officeName,console.log("\ud83e\uddea Debug: Extracted office name:",s.officeName),console.log("\ud83e\uddea Debug: Office name type:",s.officeNameType),console.log("\ud83e\uddea Debug: Testing ReportsRoutingService..."),l.clearCache();const e=await l.getCurrentUserOfficeName();console.log("\ud83e\uddea Debug: Service office name:",e),s.officeName&&(console.log("\ud83e\uddea Debug: === MANUAL DIVISION LOGIC TEST ==="),console.log("\ud83e\uddea Debug: Original office name:",`"${s.officeName}"`),console.log("\ud83e\uddea Debug: Trimmed office name:",`"${s.officeName.trim()}"`),console.log("\ud83e\uddea Debug: Lowercase office name:",`"${s.officeName.trim().toLowerCase()}"`),console.log('\ud83e\uddea Debug: Ends with "division":',s.officeName.trim().toLowerCase().endsWith("division")),console.log("\ud83e\uddea Debug: Manual division check result:",s.officeName.trim().toLowerCase().endsWith("division")),console.log("\ud83e\uddea Debug: === END MANUAL DIVISION LOGIC TEST ==="));const r=await l.testDivisionLogicDirect();console.log("\ud83e\uddea Debug: Direct division test:",r),s.shouldShowComprehensive=await l.shouldShowComprehensiveReports(),console.log("\ud83e\uddea Debug: Should show comprehensive:",s.shouldShowComprehensive),s.officeInfo=await l.getUserOfficeInfo(),console.log("\ud83e\uddea Debug: Office info:",s.officeInfo),s.directTest=r}}i(s),console.log("\ud83e\uddea === END REACT ROUTING DEBUG TEST ===")}catch(s){console.error("\ud83e\uddea Debug: Error during test:",s),i({timestamp:(new Date).toISOString(),userAuthenticated:!!e,userUID:(null===e||void 0===e?void 0:e.uid)||null,userEmail:(null===e||void 0===e?void 0:e.email)||null,userDocExists:!1,rawUserData:null,officeName:null,officeNameType:"error",allUserDataKeys:[],shouldShowComprehensive:!1,officeInfo:null,error:s instanceof Error?s.message:String(s)})}finally{f(!1)}},disabled:c,style:{padding:"0.75rem 1.5rem",backgroundColor:c?"#6c757d":"#1E3A8A",color:"white",border:"none",borderRadius:"8px",cursor:c?"not-allowed":"pointer",fontWeight:"500"},children:c?"\ud83d\udd04 Testing...":"\ud83e\uddea Run Debug Test"})]})]}),(0,d.jsxs)("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"12px",padding:"1.5rem",marginBottom:"2rem"},children:[(0,d.jsx)("h3",{style:{margin:"0 0 1rem 0",color:"#495057"},children:"\ud83d\udccb Debug Instructions"}),(0,d.jsxs)("ol",{style:{margin:0,paddingLeft:"1.5rem"},children:[(0,d.jsx)("li",{children:'Click "Run Debug Test" to analyze your office data and routing logic'}),(0,d.jsx)("li",{children:"Check the console for detailed logs during the test"}),(0,d.jsx)("li",{children:"Review the results below to identify any issues"}),(0,d.jsx)("li",{children:'Use "Clear Cache" to force fresh data retrieval'})]})]}),o&&(0,d.jsxs)("div",{style:{backgroundColor:"white",border:"1px solid #dee2e6",borderRadius:"12px",padding:"1.5rem"},children:[(0,d.jsx)("h3",{style:{margin:"0 0 1.5rem 0",color:"#495057"},children:"\ud83d\udd0d Debug Results"}),(0,d.jsxs)("div",{style:{display:"grid",gap:"1rem"},children:[(0,d.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"200px 1fr",gap:"0.5rem",alignItems:"start"},children:[(0,d.jsx)("strong",{children:"Timestamp:"}),(0,d.jsx)("span",{style:{fontFamily:"monospace"},children:o.timestamp}),(0,d.jsx)("strong",{children:"User Authenticated:"}),(0,d.jsx)("span",{style:{color:o.userAuthenticated?"#28a745":"#dc3545"},children:o.userAuthenticated?"\u2705 Yes":"\u274c No"}),(0,d.jsx)("strong",{children:"User UID:"}),(0,d.jsx)("span",{style:{fontFamily:"monospace"},children:o.userUID||"NULL"}),(0,d.jsx)("strong",{children:"User Email:"}),(0,d.jsx)("span",{style:{fontFamily:"monospace"},children:o.userEmail||"NULL"}),(0,d.jsx)("strong",{children:"Document Exists:"}),(0,d.jsx)("span",{style:{color:o.userDocExists?"#28a745":"#dc3545"},children:o.userDocExists?"\u2705 Yes":"\u274c No"}),(0,d.jsx)("strong",{children:"Office Name:"}),(0,d.jsx)("span",{style:{fontFamily:"monospace",color:o.officeName?"#28a745":"#dc3545",fontWeight:"bold"},children:o.officeName||"NULL/UNDEFINED"}),(0,d.jsx)("strong",{children:"Office Name Type:"}),(0,d.jsx)("span",{style:{fontFamily:"monospace"},children:o.officeNameType}),(0,d.jsx)("strong",{children:"Should Show Comprehensive:"}),(0,d.jsx)("span",{style:{color:o.shouldShowComprehensive?"#007bff":"#28a745"},children:o.shouldShowComprehensive?"\ud83d\udcca Report Screen 1 (Comprehensive)":"\ud83d\udccb Report Screen 2 (Table Only)"})]}),o.directTest&&(0,d.jsxs)("div",{style:{marginTop:"1rem",padding:"1rem",backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"8px"},children:[(0,d.jsx)("strong",{style:{color:"#856404"},children:"\ud83e\uddea Direct Division Logic Test (Bypasses Cache):"}),(0,d.jsxs)("div",{style:{marginTop:"0.5rem",display:"grid",gridTemplateColumns:"150px 1fr",gap:"0.5rem",fontSize:"0.875rem"},children:[(0,d.jsx)("span",{children:"Original:"}),(0,d.jsxs)("span",{style:{fontFamily:"monospace"},children:['"',o.directTest.officeName,'"']}),(0,d.jsx)("span",{children:"Trimmed:"}),(0,d.jsxs)("span",{style:{fontFamily:"monospace"},children:['"',o.directTest.trimmed,'"']}),(0,d.jsx)("span",{children:"Lowercase:"}),(0,d.jsxs)("span",{style:{fontFamily:"monospace"},children:['"',o.directTest.lowercase,'"']}),(0,d.jsx)("span",{children:'Ends with "division":'}),(0,d.jsx)("span",{style:{fontFamily:"monospace",color:o.directTest.endsWithDivision?"#28a745":"#dc3545",fontWeight:"bold"},children:o.directTest.endsWithDivision?"\u2705 TRUE":"\u274c FALSE"}),(0,d.jsx)("span",{children:"Expected Screen:"}),(0,d.jsx)("span",{style:{fontWeight:"bold",color:o.directTest.shouldShowComprehensive?"#007bff":"#28a745"},children:o.directTest.shouldShowComprehensive?"\ud83d\udcca Report Screen 1":"\ud83d\udccb Report Screen 2"})]})]}),(0,d.jsx)("div",{style:{display:"grid",gridTemplateColumns:"200px 1fr",gap:"0.5rem",alignItems:"start"}}),o.allUserDataKeys.length>0&&(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"Available Data Keys:"}),(0,d.jsx)("div",{style:{marginTop:"0.5rem",padding:"0.75rem",backgroundColor:"#f8f9fa",borderRadius:"4px",fontFamily:"monospace",fontSize:"0.875rem"},children:o.allUserDataKeys.join(", ")})]}),o.rawUserData&&(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"Raw User Data:"}),(0,d.jsx)("pre",{style:{marginTop:"0.5rem",padding:"0.75rem",backgroundColor:"#f8f9fa",borderRadius:"4px",fontSize:"0.75rem",overflow:"auto",maxHeight:"200px"},children:JSON.stringify(o.rawUserData,null,2)})]}),o.officeInfo&&(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{children:"Office Info from Service:"}),(0,d.jsx)("pre",{style:{marginTop:"0.5rem",padding:"0.75rem",backgroundColor:"#f8f9fa",borderRadius:"4px",fontSize:"0.75rem",overflow:"auto"},children:JSON.stringify(o.officeInfo,null,2)})]}),o.error&&(0,d.jsxs)("div",{children:[(0,d.jsx)("strong",{style:{color:"#dc3545"},children:"Error:"}),(0,d.jsx)("div",{style:{marginTop:"0.5rem",padding:"0.75rem",backgroundColor:"#f8d7da",color:"#721c24",borderRadius:"4px",fontFamily:"monospace",fontSize:"0.875rem"},children:o.error})]})]})]}),(0,d.jsxs)("div",{style:{marginTop:"2rem",padding:"1.5rem",backgroundColor:"#e3f2fd",borderRadius:"12px"},children:[(0,d.jsx)("h4",{style:{margin:"0 0 1rem 0",color:"#1565c0"},children:"\ud83d\udca1 Troubleshooting Tips"}),(0,d.jsxs)("ul",{style:{margin:0,paddingLeft:"1.5rem"},children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Office Name is NULL:"})," Check if the user's document in Firebase has an 'officeName' field"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Document doesn't exist:"})," User may not be properly registered in the employees collection"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Wrong office name:"})," Verify the office name format and spelling in Firebase"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Cache issues:"}),' Use "Clear Cache" button to force fresh data retrieval']}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Authentication issues:"})," Ensure user is properly logged in"]})]})]})]})})]})}}}]);
//# sourceMappingURL=905.59c871f6.chunk.js.map