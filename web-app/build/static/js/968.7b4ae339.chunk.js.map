{"version": 3, "file": "static/js/968.7b4ae339.chunk.js", "mappings": "4NAEO,SAASA,EAAoBC,GAClC,OAAOC,EAAAA,EAAAA,IAAqB,UAAWD,EACzC,EACoBE,EAAAA,EAAAA,GAAuB,UAAW,CAAC,S,aCOvD,MASMC,GAAWC,EAAAA,EAAAA,IAAOC,EAAAA,EAAO,CAC7BC,KAAM,UACNN,KAAM,QAFSI,CAGd,CACDG,SAAU,WAyDZ,EAvD0BC,EAAAA,YAAiB,SAAcC,EAASC,GAChE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,aAEF,UACJO,EAAS,OACTC,GAAS,KACNC,GACDJ,EACEK,EAAa,IACdL,EACHG,UAEIG,EA7BkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoBpB,EAAqBkB,EAAQ,EAsB1CG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKlB,EAAU,CACjCU,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BU,UAAWT,EAAS,OAAIU,EACxBd,IAAKA,EACLM,WAAYA,KACTD,GAEP,G,kEC/CO,SAASU,EAA4BzB,GAC1C,OAAOC,EAAAA,EAAAA,IAAqB,kBAAmBD,EACjD,CACA,MACA,GAD4BE,EAAAA,EAAAA,GAAuB,kBAAmB,CAAC,OAAQ,YAAa,QAAS,QAAS,UAAW,a,+DCFzH,MAAMwB,EAAQ,CACZ,CAAEC,MAAO,cAAeC,MAAO,QAC/B,CAAED,MAAO,aAAcC,MAAO,gBAC9B,CAAED,MAAO,oBAAqBC,MAAO,MACrC,CAAED,MAAO,MAAOC,MAAO,oBAgBzB,EAb6BC,KAEzBR,EAAAA,EAAAA,KAAA,OAAKR,UAAU,aAAYiB,SACxBJ,EAAMK,KAAI,CAACC,EAAMC,KAChBC,EAAAA,EAAAA,MAAA,OAAKrB,UAAW,kBAAkBoB,IAAQH,SAAA,EACxCT,EAAAA,EAAAA,KAAA,MAAAS,SAAKE,EAAKL,SACVN,EAAAA,EAAAA,KAAA,KAAGR,UAAU,aAAYiB,SAAEE,EAAKJ,UAFcK,M,kECZjD,SAASE,EAAuBnC,GACrC,OAAOC,EAAAA,EAAAA,IAAqB,aAAcD,EAC5C,CACA,MACA,GADuBE,EAAAA,EAAAA,GAAuB,aAAc,CAAC,OAAQ,WAAY,YAAa,QAAS,SAAU,WAAY,QAAS,WAAY,eAAgB,uBAAwB,iBAAkB,gBAAiB,UAAW,mB,4GCHjO,SAASkC,EAA2BpC,GACzC,OAAOC,EAAAA,EAAAA,IAAqB,iBAAkBD,EAChD,EAC2BE,EAAAA,EAAAA,GAAuB,iBAAkB,CAAC,S,aCKrE,MASMmC,GAAkBjC,EAAAA,EAAAA,IAAO,MAAO,CACpCE,KAAM,iBACNN,KAAM,QAFgBI,CAGrB,CACDkC,QAAS,GACT,eAAgB,CACdC,cAAe,MAqDnB,EAlDiC/B,EAAAA,YAAiB,SAAqBC,EAASC,GAC9E,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,oBAEF,UACJO,EAAS,UACT2B,EAAY,SACTzB,GACDJ,EACEK,EAAa,IACdL,EACH6B,aAEIvB,EAhCkBD,KACxB,MAAM,QACJC,GACED,EAIJ,OAAOE,EAAAA,EAAAA,GAHO,CACZC,KAAM,CAAC,SAEoBiB,EAA4BnB,EAAQ,EAyBjDG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKgB,EAAiB,CACxCI,GAAID,EACJ3B,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BG,WAAYA,EACZN,IAAKA,KACFK,GAEP,G,mICxCA,MAkBA,EAlBoC2B,IAA2C,IAA1C,OAAEC,EAAM,QAAEC,EAAO,MAAEjB,EAAK,SAAEG,GAAUY,EACvE,OAAKC,GAGHtB,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAgBgC,QAASD,EAAQd,UAC9CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAgBgC,QAASC,GAAKA,EAAEC,kBAAkBjB,SAAA,EAC/DI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BT,EAAAA,EAAAA,KAAA,MAAAS,SAAKH,KACLN,EAAAA,EAAAA,KAAA,UAAQR,UAAU,eAAegC,QAASD,EAAQd,SAAC,aAErDT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,aAAYiB,SACxBA,SAVW,IAaZ,E,cCpBH,MAeMkB,EAAaA,CAACC,EAAgBC,KACzC,MAAMC,EAAOD,EAAcE,MAAKC,GAAKA,EAAEC,KAAOL,IAC9C,QAAOE,IAAQA,EAAKI,QAAgB,EAIzBC,EAAaA,CAACP,EAAgBC,KACjCA,EAAcO,MAAKJ,GAAKA,EAAEE,WAAaN,IAIpCS,EAAiBC,IAC5B,MAAM5B,EAAmC,CAAC,EACpC6B,EAAoB,GAW1B,OAVAD,EAAKE,SAAQC,IACX/B,EAAI+B,EAAKR,IAAM,IAAKQ,EAAMhC,SAAU,GAAI,IAE1C6B,EAAKE,SAAQC,IAC+B,IAADC,EAArCD,EAAKP,UAAYxB,EAAI+B,EAAKP,UACD,QAA3BQ,EAAAhC,EAAI+B,EAAKP,UAAUzB,gBAAQ,IAAAiC,GAA3BA,EAA6BC,KAAKjC,EAAI+B,EAAKR,KAE3CM,EAAMI,KAAKjC,EAAI+B,EAAKR,IACtB,IAEKM,CAAK,EAIDK,EAAsBA,CAACV,EAAkBL,KACpD,IAAIgB,EAAwB,GAC5B,MAAMpC,EAAWoB,EAAciB,QAAOd,GAAKA,EAAEE,WAAaA,IAC1D,IAAK,MAAMa,KAAStC,EAClBoC,EAAYF,KAAKI,EAAMd,IACvBY,EAAcA,EAAYG,OAAOJ,EAAoBG,EAAMd,GAAIJ,IAEjE,OAAOgB,CAAW,ECxBPI,EAAqB3D,IAChC,MAAM,WACJ4D,EAAU,cACVC,EAAa,aACbC,EAAY,gBACZC,EAAe,UACfC,EAAS,aACTC,EAAY,aACZC,EAAY,gBACZC,EAAe,WACfC,EAAU,cACVC,EAAa,aACbC,EAAY,SACZC,EAAQ,WACRC,EAAU,oBACVC,EAAmB,mBACnBC,EAAkB,cAClBC,EAAa,UACbC,EAAS,eACTC,EAAc,iBACdC,EAAgB,gBAChBC,EAAe,0BACfC,GACEhF,EAEEiF,GAAkBC,EAAAA,EAAAA,cAAYC,UAClCb,GAAa,GACb,IACE,MACMc,SADsBC,EAAAA,EAAAA,KAAQC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,gBACXC,KAAKpE,KAAIqE,IAAG,CAAO9C,GAAI8C,EAAI9C,MAAO8C,EAAIC,WAC9E7B,EAAcuB,EAChB,CAAE,MAAOO,GACPpB,EAAS,+BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,IACC,CAACT,EAAeS,EAAcC,IA+IjC,MAAO,CACLU,kBACAa,iBAzIuBX,UACvB,IAAKnB,IAAcE,EAEjB,YADAK,EAAS,qCAGXD,GAAa,GAEb,QAbuBa,WACvB,MAAMY,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc5C,GAErC,aADsBqD,EAAAA,EAAAA,IAAOD,IACdE,QAAQ,EASGC,CAAiBlC,GAIzC,OAFAO,EAAS,+DACTD,GAAa,GAGfA,GAAa,GACbG,GAAoB,EAAK,EA6HzB0B,oBA1H0BhB,UAAa,IAADiB,EACtC,IAAKpC,IAAcE,EAGf,OAFAK,EAAS,6CACTE,GAAoB,GAGxB,IAAI4B,EAA+B,KAChB,qBAAfjC,GAAqCN,EACvCuC,EAAgBvC,EACQ,qBAAfM,EACTiC,EAAgB,KACPvC,GAA+B,qBAAfM,EACvBiC,EAAgBvC,EACRA,GAA+B,qBAAfM,IACxBiC,EAAgB,MAGpB,MACMC,EAAU,GADGD,EAA4D,QAA/CD,EAAGxC,EAAWnB,MAAKC,GAAKA,EAAEC,KAAO0D,WAAc,IAAAD,OAAA,EAA5CA,EAA8CG,KAAO,iBACvDvC,IAAYwC,QAAQ,OAAQ,KAE7D,IACElC,GAAa,GACbG,GAAoB,GACpB,MAAMgC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcvB,IAC9B0C,KAAMC,EAAeC,MAAOC,GD/GR7F,KAChC,MAAM8F,EAAO9F,EACV+F,MAAM,IACNC,QAAO,CAACC,EAAKC,IAASD,EAAMC,EAAKC,WAAW,IAAI,GAE7CC,EAAQ,CAACC,EAAAA,IAAUC,EAAAA,IAAWC,EAAAA,IAAOC,EAAAA,KACrCC,EAAS,CAAC,UAAW,UAAW,UAAW,UAAW,WAK5D,MAAO,CAAEf,KAHIU,EAAMN,EAAOM,EAAMM,QAGjBd,MAFDa,EAAOX,EAAOW,EAAOC,QAEb,ECoGqCC,CAAkBzD,SAEnE0D,EAAAA,EAAAA,IAAOnB,EAAS,CACpB9D,GAAIqB,EACJhD,MAAOkD,EACPqC,KAAMD,EACN1D,SAAUyD,EACVwB,aAAa,IAAIC,MAAOC,cACxBrB,KAAMC,EAAchH,KACpBiH,MAAOC,EACPmB,OAAQ,GACRC,QAAQ,EACRC,OAAQlE,UAGJiB,IAENhB,EAAa,IACbE,EAAgB,IAChBO,GAAmB,GACnBL,EAAc,IACdN,EAAgBC,GAChBQ,EAAW,WAAWN,qCACtBiE,YAAW,IAAM3D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPpB,EAAS,yDACTqB,QAAQC,MAAM,uBAAwBF,EACxC,CAAC,QACCrB,GAAa,EACf,GAqEA8D,eAlEsB5F,IACtBqC,EAAerC,GACf2B,EAAgB3B,EAAKxB,OACrB8D,GAAiB,EAAK,EAgEtBuD,iBA7DuBlD,UACvB,MAAMmD,EAAc1E,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,IAClD,GAAKwE,GAAgBpE,EACrB,IACEI,GAAa,GACb,MAAMmC,GAAUhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc+C,EAAY3F,UAC5C4F,EAAAA,EAAAA,IAAU9B,EAAS,CAAEzF,MAAOkD,EAAc2D,aAAa,IAAIC,MAAOC,sBAClE9C,IACNH,GAAiB,GACjBD,EAAe,MACfV,EAAgB,IAChBK,EAAW,gCACX2D,YAAW,IAAM3D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,GA4CAkE,kBAzCyBlG,IACzByC,EAAgBzC,GAChB0C,GAA0B,EAAK,EAwC/ByD,oBArC0BtD,UAC1B,GAAKrB,EAAL,CACAQ,GAAa,GACb,IACE,MAAMoE,GAAQC,EAAAA,EAAAA,IAAWpD,EAAAA,IACnBqD,EAAiBtF,EAAoBQ,EAAcF,GACnDiF,EAAc,CAAC/E,KAAiB8E,GAEtC,IAAK,MAAMjG,KAAMkG,EACfH,EAAMI,QAAOrD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc5C,IACnC+F,EAAMI,QAAOrD,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAAS5C,UAE1B+F,EAAMK,eACN9D,IAEND,GAA0B,GAC1BD,EAAgB,MAChBhB,EAAgB,IAChBY,EAAc,MACdC,EAAU,IACVJ,EAAW,yDACX2D,YAAW,IAAM3D,EAAW,OAAO,IACrC,CAAE,MAAOmB,GACPpB,EAAS,4BACTqB,QAAQC,MAAMF,EAChB,CAAC,QACCrB,GAAa,EACf,CA1ByB,CA0BzB,EAWD,E,cC1LI,MCuFP,EAxGkDvC,IAS3C,IAT4C,WACjD6B,EAAU,aACVE,EAAY,aACZkF,EAAY,WACZ5E,EAAU,eACV6E,EAAc,UACdC,EAAS,eACTC,EAAc,gBACdC,GACDrH,EACC,MAAMsH,EAAoB,SAACC,GAAwD,IAArCC,EAAKC,UAAA9B,OAAA,QAAA7G,IAAA2I,UAAA,GAAAA,UAAA,GAAG,EACpD,OAAOF,EAAMG,SAAQjH,IAEnB,IAAIkH,EAAelH,EAAKxB,MAGxB,IAAK0I,GAAwC,KAAxBA,EAAaC,QAAkC,cAAjBD,EAA8B,CAE/E,GAAgB,QAAZlH,EAAKG,KAAgBH,EAAKG,GAAGiH,cAAcC,SAAS,OAKtD,OADAjE,QAAQkE,KAAK,qCAAqCtH,EAAKG,MAChD,GAJP+G,EAAe,KAMnB,CAGA,GAAqB,cAAjBA,EAA8B,CAChC,GAAgB,QAAZlH,EAAKG,KAAgBH,EAAKG,GAAGiH,cAAcC,SAAS,OAKtD,OADAjE,QAAQkE,KAAK,2CAA2CtH,EAAKG,MACtD,GAJP+G,EAAe,KAMnB,CAEA,MAAO,EACLhJ,EAAAA,EAAAA,KAAA,UAAsBO,MAAOuB,EAAKG,GAAIoH,MAAO,CAAEC,YAAwB,GAART,EAAH,MAAoBpI,SAC7E,GAAG,KAAK8I,OAAOV,MAAUG,KADflH,EAAKG,OAGdH,EAAKrB,UAAYqB,EAAKrB,SAASuG,OAAS,EAAI2B,EAAkB7G,EAAKrB,SAAUoI,EAAQ,GAAK,GAC/F,GAEL,EAkBA,OACEhI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAeiB,SAAA,EAC5BI,EAAAA,EAAAA,MAAA,UACEN,MAAO6C,EACPoG,SApBoB/H,IACxB,MAAMgI,EAAkBhI,EAAEiI,OAAOnJ,MACjC+H,EAAamB,EAAgB,EAmBzBjK,UAAU,cACVmK,SAAUnB,EAAU/H,SAAA,EAEpBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAE+H,EAAY,qBAAuB,gCACpDG,EAAkBtG,EAAca,QAGnClD,EAAAA,EAAAA,KAAA,OAAKR,UAAU,4BAA2BiB,UACxCI,EAAAA,EAAAA,MAAA,UACEN,MAAOmD,EACP8F,SA1BoB/H,IAC1B,MAAMmI,EAAYnI,EAAEiI,OAAOnJ,MAC3BgI,EAAeqB,GAEG,qBAAdA,GAAkD,qBAAdA,EACtCnB,IACuB,kBAAdmB,GACTlB,GACF,EAmBMlJ,UAAU,8BAA6BiB,SAAA,EAEvCT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,sBACjBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,mBAAmBoJ,WAAYvG,EAAa3C,SAAC,2BAG1D2C,IACCvC,EAAAA,EAAAA,MAAAgJ,EAAAA,SAAA,CAAApJ,SAAA,EACET,EAAAA,EAAAA,KAAA,UAAQO,MAAM,mBAAkBE,SAAC,0BAGjCT,EAAAA,EAAAA,KAAA,UACEO,MAAM,gBACNoJ,UAAWxH,EAAWiB,EAAcF,IAAevB,EAAWyB,EAAcF,GAAYzC,SACzF,mDAOL,ECrEV,EAnCsDY,IAK/C,IALgD,aACrD+B,EAAY,WACZF,EAAU,WACV4G,EAAU,aACVC,GACD1I,EACC,MAAM2I,EAAmB9G,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,IAEvD,OAAK4G,GAKHnJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kBAAiBiB,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,oBAAkBuJ,EAAiB1J,MAAM,QAC7CO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,UACEW,QAASA,IAAMsI,EAAWE,GAC1BxK,UAAU,kDACVmK,UAAWvG,EAAa3C,SAAA,CAEvBtB,EAAAA,cAAoB8K,EAAAA,KAAoC,iBAE3DpJ,EAAAA,EAAAA,MAAA,UACEW,QAASA,IAAMuI,EAAa3G,GAC5B5D,UAAU,8CACVmK,UAAWvG,EAAa3C,SAAA,CAEvBtB,EAAAA,cAAoB+K,EAAAA,KAAqC,0BAnBzD,IAsBD,ECyNV,EAxPwD7I,IAKjD,IAAD8I,EAAA,IALmD,MACvDC,EAAK,MACLxJ,EAAK,SACLyJ,EAAQ,SACRC,GACDjJ,EACC,MAAMkJ,EAAqBA,CAACC,EAAkBjK,EAAekK,KAC3D,MAAMC,EAAa,IAAKN,EAAMO,SAAW,IACzCD,EAAWF,GAAY,IAAKE,EAAWF,GAAW,CAACC,GAAMlK,GACzD8J,EAASzJ,EAAO,IAAKwJ,EAAOO,QAASD,GAAa,EAa9CE,EAA4BnJ,IAChC,MAAM,MAAElB,EAAK,KAAEsK,GAASpJ,EAAEiI,OAC1B,IAAIoB,EAAuBvK,EACd,aAATsK,IACFC,EAAmBrJ,EAAEiI,OAA4BqB,SAEnDV,EAASzJ,EAAO,IAAKwJ,EAAOY,aAAcF,GAAkB,EAG9D,OACEjK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,8BAA6BiB,SAAA,EAC1CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gEAA+DiB,SAAA,EAC5ET,EAAAA,EAAAA,KAAA,UAAAS,SAAS2J,EAAMa,OAAS,kBAAyB,KAAGb,EAAMS,KAAK,KAC/DhK,EAAAA,EAAAA,MAAA,UAAQW,QAASA,IAAM8I,EAAS1J,GAAQpB,UAAU,wBAAuBiB,SAAA,CACtEtB,EAAAA,cAAoB+K,EAAAA,KAAqC,iBAG9DrJ,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,YAAWiB,SAAA,EAExBI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,cAActK,IAASpB,UAAU,aAAYiB,SAAC,YAC9DI,EAAAA,EAAAA,MAAA,UACEoB,GAAI,cAAcrB,IAClBpB,UAAU,eACVe,MAAO6J,EAAMS,KACbrB,SAAW/H,GAAM4I,EAASzJ,EAAO,IAC5BwJ,EACHS,KAAMpJ,EAAEiI,OAAOnJ,MACfoK,QAAwB,aAAfP,EAAMS,MAAsC,UAAfT,EAAMS,MAAmC,mBAAfT,EAAMS,UAA4B1K,EAAYiK,EAAMO,QACpHQ,YAA4B,YAAff,EAAMS,MAAqC,WAAfT,EAAMS,UAAoB1K,EAAYiK,EAAMe,cACpF1K,SAAA,EAEHT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,UACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,cACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,YACvBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,UACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,cACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,QAAOE,SAAC,iBACtBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,WAAUE,SAAC,uBACzBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,iBAAgBE,SAAC,oBAC/BT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,YACvBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,OAAME,SAAC,iBACrBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,UAASE,SAAC,oBACxBT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,SAAQE,SAAC,kBAI3BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,eAAetK,IAASpB,UAAU,aAAYiB,SAAC,aAC/DT,EAAAA,EAAAA,KAAA,SACEiC,GAAI,eAAerB,IACnBiK,KAAK,OACLrL,UAAU,eACVe,MAAO6J,EAAMa,MACbzB,SAAW/H,GAAM4I,EAASzJ,EAAO,IAAIwJ,EAAOa,MAAOxJ,EAAEiI,OAAOnJ,QAC5D6K,UAAQ,OAIX,CAAC,OAAQ,WAAY,SAAU,QAAQjC,SAASiB,EAAMS,QACrDhK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,qBAAqBtK,IAASpB,UAAU,aAAYiB,SAAC,mBACrET,EAAAA,EAAAA,KAAA,SACEiC,GAAI,qBAAqBrB,IACzBiK,KAAK,OACLrL,UAAU,eACVe,MAAO6J,EAAMe,aAAe,GAC5B3B,SAAW/H,GAAM4I,EAASzJ,EAAO,IAAIwJ,EAAOe,YAAa1J,EAAEiI,OAAOnJ,aAKxD,WAAf6J,EAAMS,OACLhK,EAAAA,EAAAA,MAAAgJ,EAAAA,SAAA,CAAApJ,SAAA,EACEI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,aAAatK,IAASpB,UAAU,aAAYiB,SAAC,iBAC7DT,EAAAA,EAAAA,KAAA,SACEiC,GAAI,aAAarB,IACjBiK,KAAK,SACLrL,UAAU,eACVe,WAAqBJ,IAAdiK,EAAMiB,IAAoB,GAAKjB,EAAMiB,IAC5C7B,SAAW/H,GAAM4I,EAASzJ,EAAO,IAAIwJ,EAAOiB,IAAwB,KAAnB5J,EAAEiI,OAAOnJ,WAAeJ,EAAYmL,WAAW7J,EAAEiI,OAAOnJ,eAG7GM,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,aAAatK,IAASpB,UAAU,aAAYiB,SAAC,iBAC7DT,EAAAA,EAAAA,KAAA,SACEiC,GAAI,aAAarB,IACjBiK,KAAK,SACLrL,UAAU,eACVe,WAAqBJ,IAAdiK,EAAMmB,IAAoB,GAAKnB,EAAMmB,IAC5C/B,SAAW/H,GAAM4I,EAASzJ,EAAO,IAAIwJ,EAAOmB,IAAwB,KAAnB9J,EAAEiI,OAAOnJ,WAAeJ,EAAYmL,WAAW7J,EAAEiI,OAAOnJ,iBAMhH,CAAC,WAAY,QAAS,kBAAkB4I,SAASiB,EAAMS,QACtDhK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kCAAiCiB,SAAA,EAC9CT,EAAAA,EAAAA,KAAA,SAAOR,UAAU,aAAYiB,SAAC,cAChB,QADiC0J,EAC9CC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAezJ,KAAI,CAAC8K,EAAKhB,KACxB3J,EAAAA,EAAAA,MAAA,OAAoBrB,UAAU,mBAAkBiB,SAAA,EAC9CT,EAAAA,EAAAA,KAAA,SACE6K,KAAK,OACLrL,UAAU,eACV2L,YAAY,eACZ5K,MAAOiL,EAAIP,MACXzB,SAAW/H,GAAM8I,EAAmBC,EAAU/I,EAAEiI,OAAOnJ,MAAO,YAEhEP,EAAAA,EAAAA,KAAA,SACE6K,KAAK,OACLrL,UAAU,eACV2L,YAAY,eACZ5K,MAAOiL,EAAIjL,MACXiJ,SAAW/H,GAAM8I,EAAmBC,EAAU/I,EAAEiI,OAAOnJ,MAAO,YAEhEP,EAAAA,EAAAA,KAAA,UAAQ6K,KAAK,SAASrJ,QAASA,IAzHvBgJ,KAAsB,IAADiB,EACzC,MAAMf,EAA0B,QAAhBe,EAAGrB,EAAMO,eAAO,IAAAc,OAAA,EAAbA,EAAe3I,QAAO,CAAC4I,EAAGC,IAAMA,IAAMnB,IACzDH,EAASzJ,EAAO,IAAKwJ,EAAOO,QAASD,GAAa,EAuHDkB,CAAapB,GAAWhL,UAAU,yBAAwBiB,SAAC,aAfxF+J,MAoBZxK,EAAAA,EAAAA,KAAA,UAAQ6K,KAAK,SAASrJ,QAnIdqK,KAChB,MAAMnB,EAAa,IAAKN,EAAMO,SAAW,GAAK,CAAEM,MAAO,GAAI1K,MAAO,KAClE8J,EAASzJ,EAAO,IAAKwJ,EAAOO,QAASD,GAAa,EAiIAlL,UAAU,2BAA0BiB,SAAC,kBAOlF,CAAC,OAAQ,WAAY,SAAU,QAAQ0I,SAASiB,EAAMS,QACnDhK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACvBT,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,uBAAuBtK,IAASpB,UAAU,aAAYiB,SAAC,qBACvET,EAAAA,EAAAA,KAAA,SACIiC,GAAI,uBAAuBrB,IAC3BiK,KAAqB,WAAfT,EAAMS,KAAoB,SAA0B,SAAfT,EAAMS,KAAkB,OAAS,OAC5ErL,UAAU,eACVe,WAA8BJ,IAAvBiK,EAAMY,aAA6B,GAAKc,OAAO1B,EAAMY,cAC5DxB,SAAUoB,QAKL,aAAfR,EAAMS,MAAsC,WAAfT,EAAMS,QACjChK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,wBAAuBiB,SAAA,EAClCT,EAAAA,EAAAA,KAAA,SACIiC,GAAI,uBAAuBrB,IAC3BiK,KAAK,WACLrL,UAAU,mBACVuL,QAASgB,QAAQ3B,EAAMY,cACvBxB,SAAUoB,KAEd5K,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,uBAAuBtK,IAASpB,UAAU,mBAAkBiB,SAAC,yBAIpF,CAAC,WAAY,SAAS0I,SAASiB,EAAMS,OAAST,EAAMO,SAAWP,EAAMO,QAAQ3D,OAAS,IAClFnG,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACxBT,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,uBAAuBtK,IAASpB,UAAU,aAAYiB,SAAC,qBACvEI,EAAAA,EAAAA,MAAA,UACIoB,GAAI,uBAAuBrB,IAC3BpB,UAAU,eACVe,WAA8BJ,IAAvBiK,EAAMY,aAA6B,GAAKc,OAAO1B,EAAMY,cAC5DxB,SAAUoB,EAAyBnK,SAAA,EAEnCT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,yBAChB2J,EAAMO,QAAQjK,KAAI8K,IAAOxL,EAAAA,EAAAA,KAAA,UAAwBO,MAAOiL,EAAIjL,MAAME,SAAE+K,EAAIP,OAAlCO,EAAIjL,eAKvC,mBAAf6J,EAAMS,OACHhK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACvBT,EAAAA,EAAAA,KAAA,SAAOR,UAAU,aAAYiB,SAAC,wCAC9BT,EAAAA,EAAAA,KAAA,SACI6K,KAAK,OACLrL,UAAU,eACVe,MAAOyL,MAAMC,QAAQ7B,EAAMY,cAAgBZ,EAAMY,aAAakB,KAAK,KAAO,GAC1E1C,SAAW/H,GAAM4I,EAASzJ,EAAO,IAAIwJ,EAAOY,aAAcvJ,EAAEiI,OAAOnJ,MAAM8F,MAAM,KAAK3F,KAAIyL,GAAKA,EAAElD,SAAQnG,QAAOqJ,GAAKA,MACnHhB,YAAY,qBAKR,WAAff,EAAMS,OACLhK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,qBAAqBtK,IAASpB,UAAU,aAAYiB,SAAC,mBACrET,EAAAA,EAAAA,KAAA,SACEiC,GAAI,qBAAqBrB,IACzBiK,KAAK,OACLrL,UAAU,eACVe,MAAO6J,EAAMgC,YAAc,GAC3B5C,SAAW/H,GAAM4I,EAASzJ,EAAO,IAAIwJ,EAAOgC,WAAY3K,EAAEiI,OAAOnJ,aAKvD,YAAf6J,EAAMS,OACLhK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,uBAAuBtK,IAASpB,UAAU,aAAYiB,SAAC,qBACvET,EAAAA,EAAAA,KAAA,SACEiC,GAAI,uBAAuBrB,IAC3BiK,KAAK,OACLrL,UAAU,eACVe,MAAO6J,EAAMiC,cAAgB,GAC7B7C,SAAW/H,GAAM4I,EAASzJ,EAAO,IAAIwJ,EAAOiC,aAAc5K,EAAEiI,OAAOnJ,cAMvE,CAAC,SAAU,WAAW4I,SAASiB,EAAMS,QACrChK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,wBAAuBiB,SAAA,EACpCT,EAAAA,EAAAA,KAAA,SACEiC,GAAI,kBAAkBrB,IACtBiK,KAAK,WACLrL,UAAU,mBACVuL,UAAWX,EAAMgB,SACjB5B,SAAW/H,GAAM4I,EAASzJ,EAAO,IAAIwJ,EAAOgB,SAAU3J,EAAEiI,OAAOqB,aAEjE/K,EAAAA,EAAAA,KAAA,SAAOkL,QAAS,kBAAkBtK,IAASpB,UAAU,mBAAkBiB,SAAC,sBAI1E,EC/LV,EAhD8DY,IASvD,IATwD,WAC7DiL,EAAU,OACVhF,EAAM,WACNiF,EAAU,cACVC,EAAa,cACbC,EAAa,OACbC,EAAM,UACNC,EAAS,QACTC,GACDvL,EACC,OACER,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,kBAAiBiB,SAAA,EAC9BI,EAAAA,EAAAA,MAAA,MAAAJ,SAAA,CAAI,2BAAyB6L,EAAWhM,UAExCN,EAAAA,EAAAA,KAAA,MAAAS,SAAI,yBACH6G,EAAO5G,KAAI,CAAC0J,EAAOxJ,KAClBZ,EAAAA,EAAAA,KAAC6M,EAAe,CAEdzC,MAAOA,EACPxJ,MAAOA,EACPyJ,SAAUmC,EACVlC,SAAUmC,GAJLrC,EAAMnI,IAAMrB,MAQrBC,EAAAA,EAAAA,MAAA,UAAQW,QAAS+K,EAAY/M,UAAU,oBAAmBiB,SAAA,CACvDtB,EAAAA,cAAoB2N,EAAAA,KAAoC,iBAG3DjM,EAAAA,EAAAA,MAAA,UACEW,QAASkL,EACTlN,UAAU,4BACVmK,SAAUiD,IAAYN,GAAgC,IAAlBhF,EAAON,OAAavG,SAAA,CAEvDtB,EAAAA,cAAoB4N,EAAAA,KAAoC,IAAEH,EAAU,YAAc,8BAGrF5M,EAAAA,EAAAA,KAAA,UACEwB,QAASmL,EACTnN,UAAU,8BACVmK,UAAW2C,GAAgC,IAAlBhF,EAAON,OAAavG,SAC9C,mBAGG,ECuCGuM,EAAwC,CACnD,CAAEzM,MAAO,QAAS0K,MAAO,SACzB,CAAE1K,MAAO,SAAU0K,MAAO,UAC1B,CAAE1K,MAAO,UAAW0K,MAAO,Y,cCxFtB,MCkJP,EA/I0D5J,IAQnD,IARoD,GACzDY,EAAE,MACFgJ,EAAK,QACLN,EAAO,eACPsC,EAAc,SACdzD,EAAQ,SACRG,GAAW,EAAK,YAChBwB,EAAc,wBACf9J,EACC,MAAOC,EAAQ4L,IAAaC,EAAAA,EAAAA,WAAS,GAC/BC,GAAcC,EAAAA,EAAAA,QAAuB,OAG3CC,EAAAA,EAAAA,YAAU,KACR,MAAMC,EAAsBC,IACtBJ,EAAYK,UAAYL,EAAYK,QAAQC,SAASF,EAAM9D,SAC7DwD,GAAU,EACZ,EAIF,OADAS,SAASC,iBAAiB,YAAaL,GAChC,KACLI,SAASE,oBAAoB,YAAaN,EAAmB,CAC9D,GACA,IAEH,MA+BMO,EAAgBb,EAAejG,SAAW2D,EAAQ3D,QAAU2D,EAAQ3D,OAAS,EAC7E+G,EAAkBd,EAAejG,OAAS,GAAKiG,EAAejG,OAAS2D,EAAQ3D,OAErF,OACEnG,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAOqK,QAASjJ,EAAIzC,UAAU,aAAYiB,SAAA,CAAEwK,EAAM,QAClDpK,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,WAAWH,IAAK+N,EAAY3M,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEiC,GAAIA,EACJzC,UAAW,+DAA8DmK,EAAW,WAAa,IACjGkB,KAAK,SACLrJ,QAASA,KAAOmI,GAAYuD,GAAW5L,GACvCqI,SAAUA,EACVN,MAAO,CACL2E,gBAAiBrE,EAAW,UAAY,QACxCsE,YAAa,WACbxN,UAEFT,EAAAA,EAAAA,KAAA,QAAMR,UAAqC,IAA1ByN,EAAejG,OAAe,aAAe,GAAGvG,SA7BlDyN,MACrB,GAA8B,IAA1BjB,EAAejG,OACjB,OAAOmE,EACF,GAA8B,IAA1B8B,EAAejG,OAAc,CACtC,MAAMmH,EAAiBxD,EAAQ5I,MAAKqM,GAAUA,EAAOnM,KAAOgL,EAAe,KAC3E,OAAqB,OAAdkB,QAAc,IAAdA,OAAc,EAAdA,EAAgBlP,OAAQkM,CACjC,CACE,MAAO,GAAG8B,EAAejG,iBAC3B,EAsBSkH,OAIJ5M,IAAWqI,IACV9I,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,2BAA2B6J,MAAO,CAAEgF,UAAW,QAASC,UAAW,QAAS7N,SAAA,CAExFkK,EAAQ3D,OAAS,IAChBnG,EAAAA,EAAAA,MAAAgJ,EAAAA,SAAA,CAAApJ,SAAA,EACET,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeiB,UAC5BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SACER,UAAU,mBACVqL,KAAK,WACL5I,GAAI,GAAGA,eACP8I,QAAS+C,EACTzO,IAAMkP,IACAA,IAAOA,EAAMC,cAAgBT,EAAe,EAElDvE,SA3DIiF,KAClBxB,EAAejG,SAAW2D,EAAQ3D,OAEpCwC,EAAS,IAGTA,EAASmB,EAAQjK,KAAI0N,GAAUA,EAAOnM,KACxC,KAsDgBpB,EAAAA,EAAAA,MAAA,SAAOrB,UAAU,2BAA2B0L,QAAS,GAAGjJ,eAAgBxB,SAAA,CAAC,eAC1DkK,EAAQ3D,OAAO,aAIlChH,EAAAA,EAAAA,KAAA,MAAIR,UAAU,wBAKjBmL,EAAQjK,KAAI0N,IACXpO,EAAAA,EAAAA,KAAA,OAAqBR,UAAU,gBAAeiB,UAC5CI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBT,EAAAA,EAAAA,KAAA,SACER,UAAU,mBACVqL,KAAK,WACL5I,GAAI,GAAGA,KAAMmM,EAAOnM,KACpB8I,QAASkC,EAAe9D,SAASiF,EAAOnM,IACxCuH,SAAUA,KAAMkF,OAzFJC,EAyFyBP,EAAOnM,QAxFxDgL,EAAe9D,SAASwF,GAE1BnF,EAASyD,EAAenK,QAAOb,GAAMA,IAAO0M,KAG5CnF,EAAS,IAAIyD,EAAgB0B,KANHA,KAyFoC,KAElD3O,EAAAA,EAAAA,KAAA,SAAOR,UAAU,mBAAmB0L,QAAS,GAAGjJ,KAAMmM,EAAOnM,KAAKxB,SAC/D2N,EAAOnP,WAVJmP,EAAOnM,MAgBC,IAAnB0I,EAAQ3D,SACPhH,EAAAA,EAAAA,KAAA,OAAKR,UAAU,2BAA0BiB,UACvCT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,iCAQbwM,EAAejG,OAAS,IACvBnG,EAAAA,EAAAA,MAAA,SAAOrB,UAAU,0BAAyBiB,SAAA,CACvCwM,EAAejG,OAAO,OAAK2D,EAAQ3D,OAAO,iBAG3C,ECyBV,EArKgE3F,IASzD,IAT0D,gBAC/DuN,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,GACD9N,EAEC,MAAM,QAAE+N,EAAO,UAAEC,EAAS,QAAEC,EAAO,QAAE1C,EAAO,MAAEzH,EAAK,QAAEoK,GFbpBC,MACjC,MAAOJ,EAASK,IAActC,EAAAA,EAAAA,UAAmB,KAC1CkC,EAAWK,IAAgBvC,EAAAA,EAAAA,UAAqB,KAChDmC,EAASK,IAAcxC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAkB,IACzChI,EAAOtB,IAAYsJ,EAAAA,EAAAA,UAAwB,MAE5C0C,EAAkBpL,UACtB,IACEmL,GAAW,GACX/L,EAAS,MAETqB,QAAQ4K,IAAI,0EAGZ,MAAMC,QAAgBC,EAAAA,EAAcC,qBAEpC/K,QAAQ4K,IAAI,sCAAkCC,EAAQ/I,OAAQ,kBAG9D,MAAMkJ,EAAyB,OAAPH,QAAO,IAAPA,OAAO,EAAPA,EACpBrP,KAAIyP,GAAOA,EAAIC,SAChBtN,QAAO,CAACuN,EAAQzP,EAAO0P,IAAUA,EAAMC,QAAQF,KAAYzP,IAC3DkC,QAAQuN,GAAuC,MAAVA,GAAoC,KAAlBA,EAAOpH,SAC9DuH,OAIGC,GAAwC,OAAfP,QAAe,IAAfA,OAAe,EAAfA,EAAiBxP,KAAIgQ,IAAU,CAC5DzO,GAAIyO,EAAWxH,cAAcpD,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzE7G,KAAMyR,QACD,GAGDC,EAA2B,OAAPZ,QAAO,IAAPA,OAAO,EAAPA,EACtBrP,KAAIyP,IAAG,CAAOE,OAAQF,EAAIC,OAAQQ,SAAUT,EAAIU,aACjD/N,QAAO,CAACL,EAAM7B,EAAO0P,IACpBA,EAAMQ,WAAUC,GAAKA,EAAEV,SAAW5N,EAAK4N,QAAUU,EAAEH,WAAanO,EAAKmO,aAAchQ,IAEpFkC,QAAQL,GACQ,MAAfA,EAAK4N,QAAmC,MAAjB5N,EAAKmO,UACL,KAAvBnO,EAAK4N,OAAOpH,QAA0C,KAAzBxG,EAAKmO,SAAS3H,SAE5CuH,MAAK,CAACQ,EAAGC,IAAMD,EAAEX,OAAOa,cAAcD,EAAEZ,SAAWW,EAAEJ,SAASM,cAAcD,EAAEL,YAE3EO,GAA8C,OAAjBR,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmBjQ,KAAI+B,IAAI,CAC5DR,GAAIQ,EAAKmO,SAAS1H,cAAcpD,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC5E7G,KAAMwD,EAAKmO,SACXP,OAAQ5N,EAAK4N,aACR,GAGDe,GAAgC,OAAPrB,QAAO,IAAPA,OAAO,EAAPA,EAC3BjN,QAAOqN,GAAOA,EAAI,gBAAkBA,EAAIC,QAAUD,EAAIU,WACvDnQ,KAAIyP,IAAG,CACNlO,GAAIkO,EAAI,eACRlR,KAAMkR,EAAI,eACVE,OAAQF,EAAIC,QAAU,GACtBQ,SAAUT,EAAIU,UAAY,GAC1BQ,WAAYlB,EAAI,qBACX,GAITV,EAAWgB,GACXf,EAAayB,GACbxB,EAAWyB,EAEb,CAAE,MAAOnM,GACPC,QAAQC,MAAM,8BAAqBF,GACnCpB,EAAS,iDACT4L,EAAW,IACXC,EAAa,IACbC,EAAW,GACb,CAAC,QACCC,GAAW,EACb,GAQF,OAJAtC,EAAAA,EAAAA,YAAU,KACRuC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA1C,UACAzH,QACAoK,QAASM,EACV,EE9EgEyB,GAG3DC,EAAsB3C,EAAgBlO,KAAI8Q,IAAQ,IAAAC,EAAA,OAClB,QADkBA,EACtDrC,EAAQrN,MAAK2P,GAAKA,EAAEzP,KAAOuP,WAAS,IAAAC,OAAA,EAApCA,EAAsCxS,IAAI,IAC1C6D,OAAOiJ,SAEH4F,EAAqB/C,EAAgB5H,OAAS,EAChDqI,EAAUvM,QAAO8N,GAAYW,EAAoBpI,SAASyH,EAASP,UACnEhB,EAGEuC,EAAwB/C,EAAkBnO,KAAImR,IAAU,IAAAC,EAAA,OACpB,QADoBA,EAC5DzC,EAAUtN,MAAKgQ,GAAKA,EAAE9P,KAAO4P,WAAW,IAAAC,OAAA,EAAxCA,EAA0C7S,IAAI,IAC9C6D,OAAOiJ,SAEHiG,EAAmBnD,EAAkB7H,OAAS,EAChDsI,EAAQxM,QAAOmP,GACbV,EAAoBpI,SAAS8I,EAAO5B,SACpCuB,EAAsBzI,SAAS8I,EAAOrB,YAExChC,EAAgB5H,OAAS,EACvBsI,EAAQxM,QAAOmP,GAAUV,EAAoBpI,SAAS8I,EAAO5B,UAC7Df,EAiCN,OA9BAhC,EAAAA,EAAAA,YAAU,KACR,GAAIsB,EAAgB5H,OAAS,EAAG,CAE9B,MAAMkL,EAAiBrD,EAAkB/L,QAAO+O,IAC9C,MAAMjB,EAAWvB,EAAUtN,MAAKgQ,GAAKA,EAAE9P,KAAO4P,IAC9C,OAAOjB,GAAYW,EAAoBpI,SAASyH,EAASP,OAAO,IAG9D6B,EAAelL,SAAW6H,EAAkB7H,QAC9CiI,EAAkBiD,EAEtB,IACC,CAACtD,EAAiBC,EAAmBQ,EAAWkC,EAAqBtC,KAExE3B,EAAAA,EAAAA,YAAU,KACR,GAAIuB,EAAkB7H,OAAS,EAAG,CAEhC,MAAMmL,EAAerD,EAAgBhM,QAAOsP,IAC1C,MAAMH,EAAS3C,EAAQvN,MAAKsQ,GAAKA,EAAEpQ,KAAOmQ,IAC1C,OAAOH,GACAV,EAAoBpI,SAAS8I,EAAO5B,SACpCuB,EAAsBzI,SAAS8I,EAAOrB,SAAS,IAGpDuB,EAAanL,SAAW8H,EAAgB9H,QAC1CkI,EAAgBiD,EAEpB,IACC,CAACtD,EAAmBC,EAAiBQ,EAASiC,EAAqBK,EAAuB1C,KAG3FrO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,iCAAgCiB,SAAA,EAC7CT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,yBAEHmM,IACC5M,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBiB,UAC/BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,4BAA2BiB,SAAA,EACxCT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,wCAAwC8S,KAAK,SAAQ7R,UAClET,EAAAA,EAAAA,KAAA,QAAMR,UAAU,kBAAiBiB,SAAC,iBAC9B,8BAMX0E,IACCtE,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,qBAAoBiB,SAAA,EACjCT,EAAAA,EAAAA,KAAA,UAAAS,SAAQ,WAAe,IAAE0E,GACzBnF,EAAAA,EAAAA,KAAA,UACER,UAAU,qCACVgC,QAAS+N,EAAQ9O,SAClB,cAMHmM,IAAYzH,IACZtE,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,MAAKiB,SAAA,EAClBT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAACuS,EAAgB,CACftQ,GAAG,gBACHgJ,MAAM,iBACNN,QAASyE,EACTnC,eAAgB2B,EAChBpF,SAAUwF,EACVrF,SAAUiD,EACVzB,YAAY,4BAIhBnL,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAACuS,EAAgB,CACftQ,GAAG,kBACHgJ,MAAM,mBACNN,QAASgH,EACT1E,eAAgB4B,EAChBrF,SAAUyF,EACVtF,SAAqC,IAA3BiF,EAAgB5H,QAAgB4F,EAC1CzB,YAAY,8BAIhBnL,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBT,EAAAA,EAAAA,KAACuS,EAAgB,CACftQ,GAAG,gBACHgJ,MAAM,iBACNN,QAASqH,EACT/E,eAAgB6B,EAChBtF,SAAU0F,EACVvF,SAAuC,IAA7BkF,EAAkB7H,QAAgB4F,EAC5CzB,YAAY,4BAIhBnL,EAAAA,EAAAA,KAAA,OAAKR,UAAU,WAAUiB,UACvBI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,EACzBI,EAAAA,EAAAA,MAAA,SAAOqK,QAAQ,mBAAmB1L,UAAU,aAAYiB,SAAA,CAAC,sBACrCT,EAAAA,EAAAA,KAAA,QAAMR,UAAU,cAAaiB,SAAC,UAElDI,EAAAA,EAAAA,MAAA,UACEoB,GAAG,mBACHzC,UAAW,gBAAgBuP,EAAmC,GAAf,cAC/CxO,MAAOwO,EACPvF,SAAW/H,GAAM0N,EAAkB1N,EAAEiI,OAAOnJ,OAC5CoJ,SAAUiD,EACVxB,UAAQ,EAAA3K,SAAA,EAERT,EAAAA,EAAAA,KAAA,UAAQO,MAAM,GAAEE,SAAC,2BAChBuM,EAAmBtM,KAAI8R,IACtBxS,EAAAA,EAAAA,KAAA,UAA8BO,MAAOiS,EAAUjS,MAAME,SAClD+R,EAAUvH,OADAuH,EAAUjS,aAKzBwO,IACA/O,EAAAA,EAAAA,KAAA,OAAKR,UAAU,mBAAkBiB,SAAC,4CAQxC,ECrKJgS,EAAc,4BC4ZpB,EAtZ8BC,KAAO,IAADC,EAAAC,EAElC,MAAMC,EChB2BC,MACjC,MAAO5P,EAAYC,IAAiBgK,EAAAA,EAAAA,UAAqB,KAClD/J,EAAcC,IAAmB8J,EAAAA,EAAAA,UAAiB,KAClDb,EAAYrI,IAAiBkJ,EAAAA,EAAAA,UAA4B,OACzD7F,EAAQpD,IAAaiJ,EAAAA,EAAAA,UAAsB,KAC3C4F,EAAwBC,IAA6B7F,EAAAA,EAAAA,UAA6B,KAClF3E,EAAW5E,IAAgBuJ,EAAAA,EAAAA,WAAkB,IAC7CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAS,IAChChI,EAAOtB,IAAYsJ,EAAAA,EAAAA,UAAwB,OAC3C8F,EAASnP,IAAcqJ,EAAAA,EAAAA,UAAwB,OAE/C+F,EAAiBlP,IAAsBmJ,EAAAA,EAAAA,WAAkB,IACzD7J,EAAWC,IAAgB4J,EAAAA,EAAAA,UAAiB,KAC5C3J,EAAcC,IAAmB0J,EAAAA,EAAAA,UAAiB,KAClDgG,EAAkBpP,IAAuBoJ,EAAAA,EAAAA,WAAkB,IAE3DvF,EAAazD,IAAkBgJ,EAAAA,EAAAA,UAA0B,OACzDiG,EAAehP,IAAoB+I,EAAAA,EAAAA,WAAkB,IAErDkG,EAAchP,IAAmB8I,EAAAA,EAAAA,UAAwB,OACzDmG,EAAwBhP,IAA6B6I,EAAAA,EAAAA,WAAkB,IAEvEzJ,EAAYC,IAAiBwJ,EAAAA,EAAAA,UAAiB,KAG9CoG,EAAeC,IAAoBrG,EAAAA,EAAAA,WAAS,IAC5CsG,EAAgBC,IAAqBvG,EAAAA,EAAAA,UAAS,KAG9CyB,EAAiB+E,IAAsBxG,EAAAA,EAAAA,UAAmB,KAC1D0B,EAAmB+E,IAAwBzG,EAAAA,EAAAA,UAAmB,KAC9D2B,EAAiB+E,IAAsB1G,EAAAA,EAAAA,UAAmB,KAC1D4B,EAAmB+E,IAAwB3G,EAAAA,EAAAA,UAAiB,IAEnE,MAAO,CAELjK,aACAE,eACAkJ,aACAhF,SACAyL,yBACAvK,YACAoE,UACAzH,QACA8N,UACAC,kBACA5P,YACAE,eACA2P,mBACAvL,cACAwL,gBACAC,eACAC,yBACA5P,aACA6P,gBACAE,iBACA7E,kBACAC,oBACAC,kBACAC,oBAGA5L,gBACAE,kBACAY,gBACAC,YACA8O,4BACApP,eACAgM,aACA/L,WACAC,aACAE,qBACAT,eACAE,kBACAM,sBACAI,iBACAC,mBACAC,kBACAC,4BACAX,gBACA6P,mBACAE,oBACAC,qBACAC,uBACAC,qBACAC,uBACD,EDtEahB,GAKRiB,EAAiB9Q,EAAkB,CACvCC,WAAY2P,EAAM3P,WAClBC,cAAe0P,EAAM1P,cACrBC,aAAcyP,EAAMzP,aACpBC,gBAAiBwP,EAAMxP,gBACvBC,UAAWuP,EAAMvP,UACjBC,aAAcsP,EAAMtP,aACpBC,aAAcqP,EAAMrP,aACpBC,gBAAiBoP,EAAMpP,gBACvBC,WAAYmP,EAAMnP,WAClBC,cAAekP,EAAMlP,cACrBC,aAAciP,EAAMjP,aACpBC,SAAUgP,EAAMhP,SAChBC,WAAY+O,EAAM/O,WAClBC,oBAAqB8O,EAAM9O,oBAC3BC,mBAAoB6O,EAAM7O,mBAC1BC,cAAe4O,EAAM5O,cACrBC,UAAW2O,EAAM3O,UACjBC,eAAgB0O,EAAM1O,eACtBC,iBAAkByO,EAAMzO,iBACxBC,gBAAiBwO,EAAMxO,gBACvBC,0BAA2BuO,EAAMvO,4BAG7B0P,EVjB6B1U,KACnC,MAAM,WACJ4D,EAAU,aACVE,EAAY,WACZkJ,EAAU,cACVrI,EAAa,OACbqD,EAAM,UACNpD,EAAS,0BACT8O,EAAyB,WACzBpD,EAAU,SACV/L,EAAQ,WACRC,EAAU,kBACV4P,EAAiB,iBACjBF,EAAgB,gBAChB5E,EAAe,kBACfC,EAAiB,gBACjBC,EAAe,kBACfC,EAAiB,mBACjB4E,EAAkB,qBAClBC,EAAoB,mBACpBC,EAAkB,qBAClBC,GACExU,EAmUJ,MAAO,CACL2U,wBAlU6BzP,EAAAA,EAAAA,cAAYC,UACzC,GAAKyP,EAAL,CACAhP,QAAQ4K,IAAI,4CAA4CoE,KACxD,IACE,MAAMC,GAAgBpP,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,cAAeqP,GACvCE,QAAuB9O,EAAAA,EAAAA,IAAO6O,GACpC,GAAIC,EAAe7O,SAAU,CAC3B,MAAM8O,EAAiBD,EAAepP,OACtCgO,EAA0BqB,EAAe/M,QAAU,IACnDpC,QAAQ4K,IAAI,0BAA2BuE,EAAe/M,OACxD,MACEpC,QAAQ4K,IAAI,mDAAmDoE,KAC/DlB,EAA0B,GAE9B,CAAE,MAAO/N,GACPC,QAAQC,MAAM,sCAAuCF,GACrDpB,EAAS,wCACTmP,EAA0B,GAC5B,CAjBmB,CAiBnB,GACC,CAACA,EAA2BnP,IAgT7ByQ,gBA9SqB9P,EAAAA,EAAAA,cAAYC,UACjC,GAAK7C,EAAL,CAIAsD,QAAQ4K,IAAI,qCAAqClO,KACjDgO,GAAW,GACX/L,EAAS,MACT,IAEE,MAAMwB,GAASN,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASjD,GAC1B2S,QAAgBjP,EAAAA,EAAAA,IAAOD,GAE7B,IAAIL,EAA0B,KAE9B,GAAIuP,EAAQhP,SAEVP,EAAOuP,EAAQvP,YAGf,IACEA,QAAawP,EAAAA,EAAoBF,eAAe1S,EAClD,CAAE,MAAO6S,GACP,CAIJ,GAAIzP,EAAM,CACRf,EAAce,GACdd,EAAUc,EAAKsC,QAAU,IAGzB,MAAMoN,EAAe1P,EAAK4J,kBAAoB5J,EAAK2P,eAAiB,CAAC3P,EAAK2P,gBAAkB,IACtFC,EAAiB5P,EAAK6J,oBAAsB7J,EAAK6P,iBAAmB,CAAC7P,EAAK6P,kBAAoB,IAC9FC,EAAe9P,EAAK8J,kBAAoB9J,EAAK+P,eAAiB,CAAC/P,EAAK+P,gBAAkB,IACtFC,EAAiBhQ,EAAK+J,mBAAqB,GAGlB,IAA3BH,EAAgB5H,QAAgB0N,EAAa1N,OAAS,GACxD2M,EAAmBe,GAEY,IAA7B7F,EAAkB7H,QAAgB4N,EAAe5N,OAAS,GAC5D4M,EAAqBgB,GAEQ,IAA3B9F,EAAgB9H,QAAgB8N,EAAa9N,OAAS,GACxD6M,EAAmBiB,IAEhB/F,GAAqBiG,GACxBlB,EAAqBkB,EAEzB,KAAO,CAEL,MAAMlT,EAAOoB,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOL,IAC3CqC,EAAc,CACZhC,GAAIL,EACJtB,OAAW,OAAJwB,QAAI,IAAJA,OAAI,EAAJA,EAAMxB,QAAS,WACtBgH,OAAQ,GACRH,aAAa,IAAIC,MAAOC,gBAE1BnD,EAAU,IAEVyP,EAAmB,IACnBC,EAAqB,IACrBC,EAAmB,IACnBC,EAAqB,GACvB,CACF,CAAE,MAAO7O,GACPpB,EAAS,sCACTqB,QAAQC,MAAMF,GACdhB,EAAc,MACdC,EAAU,GACZ,CAAC,QACC0L,GAAW,EACb,CArEA,MAFE1K,QAAQ4K,IAAI,uCAuEd,GACC,CAAC5M,EAAY0M,EAAY/L,EAAUI,EAAeC,EAAWyP,EAAoBC,EAAsBC,EAAoBC,EAAsBlF,EAAiBC,EAAmBC,EAAiBC,IAqOvMkG,SAnOeA,KACf,MAAMC,EAAsB,CAC1BjT,GAAI,SAASmF,KAAK+N,QAClBtK,KAAM,OACNI,MAAO,YACPE,YAAa,GACbR,QAAS,GACTS,UAAU,EACViF,OAAQ,GACRO,SAAU,GACVqB,OAAQ,IAEV/N,EAAU,IAAIoD,EAAQ4N,GAAU,EAwNhCE,oBArN2BC,IAC3BnQ,QAAQ4K,IAAI,mCAAoCuF,GAChD,MAAMH,EAAsB,CAC1BjT,GAAIoT,EAAapT,GACjB4I,KAAMwK,EAAaxK,KACnBI,MAAOoK,EAAapK,MACpBE,YAAakK,EAAalK,YAC1BR,QAAS0K,EAAa1K,QAAU0K,EAAa1K,QAAQjK,KAAK8K,GACrC,kBAARA,EACF,CAAEP,MAAOO,EAAKjL,MAAOiL,GAErB,CAAEP,MAAOO,EAAIP,MAAO1K,MAAOiL,EAAIjL,cAErCJ,EACLiL,SAAUiK,EAAajK,SACvBJ,aAAcqK,EAAarK,aAC3BK,IAAKgK,EAAahK,IAClBE,IAAK8J,EAAa9J,IAClBc,kBAAclM,EACdmV,aAASnV,EACTiM,gBAAYjM,EACZoV,gBAAYpV,EACZqV,mBAAerV,EACfI,WAAOJ,GAGT,GAAImH,EAAOlF,MAAKgI,GAASA,EAAMnI,KAAOiT,EAASjT,KAI3C,OAHAiD,QAAQkE,KAAK,iCAAiC8L,EAASjT,yBACvD4B,EAAS,kBAAkBqR,EAASjT,sDACpCwF,YAAW,IAAM5D,EAAS,OAAO,KAIrCqB,QAAQ4K,IAAI,6BAA8BoF,GAC1ChR,EAAU,IAAIoD,EAAQ4N,IACtBpR,EAAW,gBAAgBoR,EAASjK,iCACpCxD,YAAW,IAAM3D,EAAW,OAAO,IAAK,EAkLxC2R,YA/KkBA,CAAC7U,EAAe8U,KAClC,MAAMC,EAAgB,IAAIrO,GAC1BqO,EAAc/U,GAAS8U,EACvBxR,EAAUyR,EAAc,EA6KxBC,YA1KmBhV,IACnBsD,EAAUoD,EAAOxE,QAAO,CAAC4I,EAAGC,IAAMA,IAAM/K,IAAO,EA0K/CiV,WAvKiBpR,UACjB,GAAKrB,GAAiBkJ,EAMtB,GAAKyC,EAAL,CAKAa,GAAW,GACX1K,QAAQ4K,IAAI,oDAAqD1M,GACjE8B,QAAQ4K,IAAI,sBAAuBxI,GACnCpC,QAAQ4K,IAAI,oBAAqBf,GAEjC,IAAK,IAADrJ,EACF,MAAMoQ,EAAgBxO,EAAO5G,KAAI0J,IAC/B,MAAM2L,EAAoB,CAAC,EAC3B,IAAK,MAAMtL,KAAOL,OACGjK,IAAfiK,EAAMK,GACRsL,EAAatL,GAAOL,EAAMK,GAE1BsL,EAAatL,GAAO,KAGxB,OAAOsL,CAAY,IAGfC,EAAgC,IACjC1J,EACHrK,GAAImB,EACJ9C,OAAkD,QAA3CoF,EAAAxC,EAAWnB,MAAKC,GAAKA,EAAEC,KAAOmB,WAAa,IAAAsC,OAAA,EAA3CA,EAA6CpF,QAASgM,EAAWhM,MACxEgH,OAAQwO,EACR3O,aAAa,IAAIC,MAAOC,cACxBuH,kBACAC,oBACAC,kBACAC,qBAIIkH,EAAe,GAGrBA,EAAatT,MACXuE,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,QAASzB,GAAe4S,GACpCE,OAAMjR,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAIkR,MAAM,yBAAyBlR,EAAImR,UAAU,KAK7DH,EAAatT,KACX6R,EAAAA,EAAoB6B,eAAeL,GAChCE,OAAMjR,IAEL,MADAC,QAAQC,MAAM,wBAAyBF,GACjC,IAAIkR,MAAM,yBAAyBlR,EAAImR,UAAU,WAKvDE,QAAQC,IAAIN,GAElBhS,EAAc+R,GACdlS,EAAW,0CACX2D,YAAW,IAAM3D,EAAW,OAAO,IAErC,CAAE,MAAOmB,GACPC,QAAQC,MAAM,qCAAsCF,GACpDpB,EAAS,sCAAsCoB,aAAekR,MAAQlR,EAAImR,QAAU,kBACtF,CAAC,QACCxG,GAAW,EACb,CAjEA,MAFE/L,EAAS,+EANTA,EAAS,mDAyEX,EA6FA2S,cA1FoBA,KACpB,IAAKlK,GAAgC,IAAlBhF,EAAON,OAExB,YADAyP,MAAM,+CAIR,MAAMC,EAAmB,eACjBpK,EAAWhM,qCAEbgH,EAAO5G,KAAI0J,IAAU,IAADqB,EAAAtB,EACpB,IAAIwM,EAAY,GAChB,OAAQvM,EAAMS,MACZ,IAAK,OACL,IAAK,SACL,IAAK,OACL,IAAK,WACH8L,EAAY,gGAEoBvM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8CACnDhB,EAAMS,2CAA2CT,EAAMe,aAAe,OAAOf,EAAMgB,SAAW,WAAa,gDAG9H,MACF,IAAK,WACHuL,EAAY,gGAEoBvM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,8DACnChB,EAAMgB,SAAW,WAAa,oDACjChB,EAAMa,wCACjB,QAAbQ,EAAArB,EAAMO,eAAO,IAAAc,OAAA,EAAbA,EAAe/K,KAAI0N,GAAU,kBAAkBA,EAAO7N,UAAU6N,EAAOnD,mBAAkBiB,KAAK,MAAO,0EAI7G,MACF,IAAK,WACHyK,EAAY,0HAE8CvM,EAAMnI,OAAOmI,EAAMgB,SAAW,WAAa,iEAC1DhB,EAAMnI,OAAOmI,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,qDAG9F,MACF,IAAK,QACHuL,EAAY,gGAEoBvM,EAAMa,QAAQb,EAAMgB,SAAW,KAAO,kCACnD,QAAbjB,EAAAC,EAAMO,eAAO,IAAAR,OAAA,EAAbA,EAAezJ,KAAI,CAAC0N,EAAQzC,IAAM,4HAEqBvB,EAAMnI,WAAWmI,EAAMnI,MAAM0J,aAAayC,EAAO7N,UAAU6J,EAAMgB,SAAW,WAAa,mEACvGhB,EAAMnI,MAAM0J,MAAMyC,EAAOnD,kEAEjEiB,KAAK,MAAO,6CAGnB,MACF,IAAK,UACHyK,EAAY,8FAEmBvM,EAAMiC,cAAgB,yNAMrD,MACF,IAAK,SACHsK,EAAY,wEAC2CvM,EAAMgC,YAAc,oCAE3E,MACF,QACEuK,EAAY,8BAA8BvM,EAAMS,WAEpD,OAAO8L,CAAS,IACfzK,KAAK,2BAIZwH,EAAkBgD,GAClBlD,GAAiB,EAAK,EAYvB,EUjVyBoD,CAAqB,CAC7C1T,WAAY2P,EAAM3P,WAClBE,aAAcyP,EAAMzP,aACpBkJ,WAAYuG,EAAMvG,WAClBrI,cAAe4O,EAAM5O,cACrBqD,OAAQuL,EAAMvL,OACdpD,UAAW2O,EAAM3O,UACjB8O,0BAA2BH,EAAMG,0BACjCpD,WAAYiD,EAAMjD,WAClB/L,SAAUgP,EAAMhP,SAChBC,WAAY+O,EAAM/O,WAClB4P,kBAAmBb,EAAMa,kBACzBF,iBAAkBX,EAAMW,iBACxB5E,gBAAiBiE,EAAMjE,gBACvBC,kBAAmBgE,EAAMhE,kBACzBC,gBAAiB+D,EAAM/D,gBACvBC,kBAAmB8D,EAAM9D,kBACzB4E,mBAAoBd,EAAMc,mBAC1BC,qBAAsBf,EAAMe,qBAC5BC,mBAAoBhB,EAAMgB,mBAC1BC,qBAAsBjB,EAAMiB,wBAI9BxG,EAAAA,EAAAA,YAAU,KACRyG,EAAexP,kBAGf,MAAMsS,EDjD4BC,MACpC,IACE,MAAMC,EAASC,aAAaC,QAAQxE,GACpC,GAAIsE,EACF,OAAOG,KAAKC,MAAMJ,EAEtB,CAAE,MAAO5R,GACPD,QAAQkE,KAAK,wDAAyDjE,EACxE,CACA,OAAO,IAAI,ECwCe2R,GACpBD,IACEA,EAAgBjI,gBAAgB5H,OAAS,GAC3C6L,EAAMc,mBAAmBkD,EAAgBjI,iBAEvCiI,EAAgBhI,kBAAkB7H,OAAS,GAC7C6L,EAAMe,qBAAqBiD,EAAgBhI,mBAEzCgI,EAAgB/H,gBAAgB9H,OAAS,GAC3C6L,EAAMgB,mBAAmBgD,EAAgB/H,iBAEvC+H,EAAgB9H,mBAClB8D,EAAMiB,qBAAqB+C,EAAgB9H,mBAE/C,GACC,KAGHzB,EAAAA,EAAAA,YAAU,KACR,MAAM8J,EAAa,CACjBxI,gBAAiBiE,EAAMjE,gBACvBC,kBAAmBgE,EAAMhE,kBACzBC,gBAAiB+D,EAAM/D,gBACvBC,kBAAmB8D,EAAM9D,oBAIvBqI,EAAWxI,gBAAgB5H,OAAS,GACpCoQ,EAAWvI,kBAAkB7H,OAAS,GACtCoQ,EAAWtI,gBAAgB9H,OAAS,GACpCoQ,EAAWrI,oBD1FoBqI,KACrC,IACEJ,aAAaK,QAAQ5E,EAAayE,KAAKI,UAAUF,GACnD,CAAE,MAAOjS,GACPD,QAAQkE,KAAK,sDAAuDjE,EACtE,GCsFIoS,CAAuBH,EACzB,GACC,CAACvE,EAAMjE,gBAAiBiE,EAAMhE,kBAAmBgE,EAAM/D,gBAAiB+D,EAAM9D,qBAGjFzB,EAAAA,EAAAA,YAAU,KACJuF,EAAMzP,cAAgBjB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,aAAoC,kBAArB2P,EAAMnP,YACvIsQ,EAAkBM,eAAezB,EAAMzP,cACvC4Q,EAAkBC,uBAAuBpB,EAAMzP,gBACtCyP,EAAMzP,cAAkBjB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAevB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,aAAqC,kBAArB2P,EAAMnP,WAItImP,EAAMzP,eAChByP,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,IAChB2O,EAAMG,0BAA0B,IAChCH,EAAMlP,cAAc,MAPpBkP,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,IAChB2O,EAAMG,0BAA0B,KAQ9BH,EAAMzP,cAAqC,kBAArByP,EAAMnP,YAC5BmP,EAAMG,0BAA0B,GACpC,GACC,CAACH,EAAMzP,aAAcyP,EAAM3P,WAAY2P,EAAMnP,aA+EhD,OACE1D,EAAAA,EAAAA,KAAA6J,EAAAA,SAAA,CAAApJ,UACEI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,CAC1BoS,EAAM1N,QAASnF,EAAAA,EAAAA,KAAA,OAAKR,UAAU,gBAAeiB,SAAEoS,EAAM1N,QACrD0N,EAAMI,UACLjT,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kBAAiBiB,SAC7BoS,EAAMI,WAGXjT,EAAAA,EAAAA,KAAA,MAAAS,SAAI,2BAEJT,EAAAA,EAAAA,KAACwX,EAAY,CACXtU,WAAY2P,EAAM3P,WAClBE,aAAcyP,EAAMzP,aACpBkF,aA1FkB1G,IAGxB,GAFAiR,EAAMxP,gBAAgBzB,GACtBiR,EAAMlP,cAAc,IACf/B,EAGE,CACH,MAAM6V,EAAatV,EAAWP,EAAQiR,EAAM3P,YACtCwU,EAAa/V,EAAWC,EAAQiR,EAAM3P,YACxCuU,IAAcC,IACd7E,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,IAExB,MATI2O,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,GAQpB,EA8EMR,WAAYmP,EAAMnP,WAClB6E,eA5EoBoP,IAC1B9E,EAAMlP,cAAcgU,EAAO,EA4ErBnP,UAAWqK,EAAMrK,UACjBC,eA1EmBmP,KACzB/E,EAAMtP,aAAa,IACnBsP,EAAMpP,gBAAgB,IACtBoP,EAAM7O,oBAAmB,EAAK,EAwExB0E,gBArEoBmP,KACtBhF,EAAMzP,cAAgBjB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,YAClH8Q,EAAkBM,eAAezB,EAAMzP,cAC9ByP,EAAMzP,eACfyP,EAAMhP,SAAS,sFACfgP,EAAM5O,cAAc,MACpB4O,EAAM3O,UAAU,IAClB,IAkEK2O,EAAMK,kBACLlT,EAAAA,EAAAA,KAAC8X,EAAK,CACJxW,OAAQuR,EAAMK,gBACd3R,QAASA,KACPsR,EAAM7O,oBAAmB,GACzB6O,EAAMlP,cAAc,IACpBkP,EAAMtP,aAAa,IACnBsP,EAAMpP,gBAAgB,GAAG,EAE3BnD,MACuB,qBAArBuS,EAAMnP,WAAoC,yBAC1CmP,EAAMzP,cAAqC,qBAArByP,EAAMnP,WAAoC,4BAAmF,QAAnFiP,EAA4BE,EAAM3P,WAAWnB,MAAKC,GAAKA,EAAEC,KAAO4Q,EAAMzP,sBAAa,IAAAuP,OAAA,EAAvDA,EAAyDrS,SACrJ,oBACDG,UAEDI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gBAAeiB,SAAA,EAC5BT,EAAAA,EAAAA,KAAA,SACE6K,KAAK,OACLM,YAAY,oCACZ5K,MAAOsS,EAAMvP,UACbkG,SAAW/H,GAAMoR,EAAMtP,aAAa9B,EAAEiI,OAAOnJ,MAAM2I,cAAcpD,QAAQ,OAAQ,MACjFtG,UAAU,uBAEZQ,EAAAA,EAAAA,KAAA,SACE6K,KAAK,OACLM,YAAY,eACZ5K,MAAOsS,EAAMrP,aACbgG,SAAW/H,GAAMoR,EAAMpP,gBAAgBhC,EAAEiI,OAAOnJ,OAChDf,UAAU,uBAEZqB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEwB,QAASuS,EAAetO,oBACxBkE,SAAUkJ,EAAMrK,YAAcqK,EAAMvP,YAAcuP,EAAMrP,aACxDhE,UAAU,kBAAiBiB,SAE1BoS,EAAMrK,UAAY,cAAgB,6BAErCxI,EAAAA,EAAAA,KAAA,UAAQwB,QAASA,KACfqR,EAAM7O,oBAAmB,GACzB6O,EAAMlP,cAAc,IACpBkP,EAAMtP,aAAa,IACnBsP,EAAMpP,gBAAgB,GAAG,EACxBjE,UAAU,oBAAmBiB,SAAC,mBASxCoS,EAAMzP,eACLvC,EAAAA,EAAAA,MAAAgJ,EAAAA,SAAA,CAAApJ,SAAA,GAE0B,kBAArBoS,EAAMnP,YAAkCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,aAAe2P,EAAMvG,cACxJtM,EAAAA,EAAAA,KAAC+X,EAAc,CACb3U,aAAcyP,EAAMzP,aACpBF,WAAY2P,EAAM3P,WAClB4G,WAAYiK,EAAerM,eAC3BqC,aAAcgK,EAAejM,oBAKX,kBAArB+K,EAAMnP,YAAkCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,cACjIlD,EAAAA,EAAAA,KAACgY,EAAmB,CAClBpJ,gBAAiBiE,EAAMjE,gBACvBC,kBAAmBgE,EAAMhE,kBACzBC,gBAAiB+D,EAAM/D,gBACvBC,kBAAmB8D,EAAM9D,kBACzBC,gBArIeI,IAC3B,MAAM6I,EAAkBpF,EAAMjE,gBAC9BiE,EAAMc,mBAAmBvE,GAIrB8H,KAAKI,UAAUlI,KAAa8H,KAAKI,UAAUW,KAC7CpF,EAAMe,qBAAqB,IAC3Bf,EAAMgB,mBAAmB,IAC3B,EA6HY5E,kBA1HiBI,IAC7B,MAAM6I,EAAoBrF,EAAMhE,kBAChCgE,EAAMe,qBAAqBvE,GAIvB6H,KAAKI,UAAUjI,KAAe6H,KAAKI,UAAUY,IAC/CrF,EAAMgB,mBAAmB,GAC3B,EAmHY3E,gBAhHeI,IAC3BuD,EAAMgB,mBAAmBvE,EAAQ,EAgHrBH,kBA7GiBqD,IAC7BK,EAAMiB,qBAAqBtB,EAAU,IAiHP,kBAArBK,EAAMnP,YAAkCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAAgBvB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,aAAe2P,EAAMvG,aACtJtM,EAAAA,EAAAA,KAACmY,EAAkB,CACjB7L,WAAYuG,EAAMvG,WAClBhF,OAAQuL,EAAMvL,OACdiF,WAAYyH,EAAkBiB,SAC9BzI,cAAewH,EAAkByB,YACjChJ,cAAeuH,EAAkB4B,YACjClJ,OAAQsH,EAAkB6B,WAC1BlJ,UAAWqH,EAAkBwC,cAC7B5J,QAASiG,EAAMjG,UAKG,kBAArBiG,EAAMnP,cAAoCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,aAAevB,EAAWkR,EAAMzP,aAAcyP,EAAM3P,eAClIlD,EAAAA,EAAAA,KAAA,OAAKR,UAAU,wDAAuDiB,SAAC,iLAInD,kBAArBoS,EAAMnP,aAAmCvB,EAAW0Q,EAAMzP,aAAcyP,EAAM3P,cAC7ElD,EAAAA,EAAAA,KAAA,OAAKR,UAAU,kDAAiDiB,SAAC,6IAOrEoS,EAAMzP,cAAqC,KAArByP,EAAMnP,aAC5B7C,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,gDAA+CiB,SAAA,EAC5DT,EAAAA,EAAAA,KAAA,KAAAS,SAAG,+FACHT,EAAAA,EAAAA,KAAA,KAAAS,SAAG,wJAKNoS,EAAMO,eAAiBP,EAAMjL,cAC5B/G,EAAAA,EAAAA,MAACiX,EAAK,CACJxW,OAAQuR,EAAMO,cACd7R,QAASA,KACPsR,EAAMzO,kBAAiB,GACvByO,EAAMpP,gBAAgB,IACtBoP,EAAM1O,eAAe,KAAK,EAE5B7D,MAAO,gBAAgBuS,EAAMjL,YAAYtH,QAAQG,SAAA,EAEjDT,EAAAA,EAAAA,KAAA,SACE6K,KAAK,OACLtK,MAAOsS,EAAMrP,aACbgG,SAAW/H,GAAMoR,EAAMpP,gBAAgBhC,EAAEiI,OAAOnJ,OAChD4K,YAAY,mBACZ3L,UAAU,uBAEZqB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEwB,QAASuS,EAAepM,iBACxBnI,UAAU,kBACVmK,SAAUkJ,EAAMrK,YAAcqK,EAAMrP,aAAayF,OAAOxI,SAEvDoS,EAAMrK,UAAY,cAAgB,kBAErCxI,EAAAA,EAAAA,KAAA,UACEwB,QAASA,KACPqR,EAAMzO,kBAAiB,GACvByO,EAAMpP,gBAAgB,IACtBoP,EAAM1O,eAAe,KAAK,EAE5B3E,UAAU,oBAAmBiB,SAC9B,iBAONoS,EAAMS,wBAA0BT,EAAMQ,eACrCxS,EAAAA,EAAAA,MAACiX,EAAK,CACJxW,OAAQuR,EAAMS,uBACd/R,QAASA,IAAMsR,EAAMvO,2BAA0B,GAC/ChE,MAAM,mBAAkBG,SAAA,EAExBI,EAAAA,EAAAA,MAAA,KAAAJ,SAAA,CAAG,+CAAoG,QAAxDmS,EAACC,EAAM3P,WAAWnB,MAAKC,GAAKA,EAAEC,KAAO4Q,EAAMQ,sBAAa,IAAAT,OAAA,EAAvDA,EAAyDtS,MAAM,qGAC/GO,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,6BAA4BiB,SAAA,EACzCT,EAAAA,EAAAA,KAAA,UACEwB,QAASuS,EAAehM,oBACxBvI,UAAU,iBACVmK,SAAUkJ,EAAMrK,UAAU/H,SAEzBoS,EAAMrK,UAAY,cAAgB,oBAErCxI,EAAAA,EAAAA,KAAA,UACEwB,QAASA,IAAMqR,EAAMvO,2BAA0B,GAC/C9E,UAAU,oBAAmBiB,SAC9B,kBAQPT,EAAAA,EAAAA,KAAC8X,EAAK,CACJxW,OAAQuR,EAAMU,cACdhS,QAASA,IAAMsR,EAAMW,kBAAiB,GACtClT,MAAM,eAAcG,UAEpBT,EAAAA,EAAAA,KAAA,OAAKoY,wBAAyB,CAAEC,OAAQxF,EAAMY,wBAGjD,E,2MElaA,SAAS6E,EAAwB3Z,GACtC,OAAOC,EAAAA,EAAAA,IAAqB,cAAeD,EAC7C,EACwBE,EAAAA,EAAAA,GAAuB,cAAe,CAAC,OAAQ,YAAa,QAAS,sBAAuB,UAAW,UAAW,UAAW,oBCArJ,MACA,GAD8BA,EAAAA,EAAAA,GAAuB,oBAAqB,CAAC,OAAQ,eAAgB,QAAS,sBAAuB,WAAY,UAAW,UAAW,aCH9J,SAAS0Z,EAA8C5Z,GAC5D,OAAOC,EAAAA,EAAAA,IAAqB,6BAA8BD,EAC5D,EACuCE,EAAAA,EAAAA,GAAuB,6BAA8B,CAAC,OAAQ,mBAArG,MCgBM2Z,GAA8BzZ,EAAAA,EAAAA,IAAO,MAAO,CAChDE,KAAM,6BACNN,KAAM,OACN8Z,kBAAmBA,CAACnZ,EAAOoZ,KACzB,MAAM,WACJ/Y,GACEL,EACJ,MAAO,CAACoZ,EAAO5Y,KAAMH,EAAWgZ,gBAAkBD,EAAOC,eAAe,GAPxC5Z,CASjC,CACD6Z,SAAU,WACVC,MAAO,GACPC,IAAK,MACLC,UAAW,mBACXC,SAAU,CAAC,CACT1Z,MAAO+B,IAAA,IAAC,WACN1B,GACD0B,EAAA,OAAK1B,EAAWgZ,cAAc,EAC/BtP,MAAO,CACLwP,MAAO,OAUPI,EAAuC9Z,EAAAA,YAAiB,SAAiCC,EAASC,GACtG,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,gCAEF,UACJO,KACGE,GACDJ,EACE4Z,EAAU/Z,EAAAA,WAAiBga,EAAAA,GAC3BxZ,EAAa,IACdL,EACHqZ,eAAgBO,EAAQP,gBAEpB/Y,EArDkBD,KACxB,MAAM,eACJgZ,EAAc,QACd/Y,GACED,EACEyZ,EAAQ,CACZtZ,KAAM,CAAC,OAAQ6Y,GAAkB,mBAEnC,OAAO9Y,EAAAA,EAAAA,GAAeuZ,EAAOb,EAA+C3Y,EAAQ,EA6CpEG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAKwY,EAA6B,CACpDhZ,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9BG,WAAYA,EACZN,IAAKA,KACFK,GAEP,IAuBAuZ,EAAwBI,QAAU,0BAClC,WCtDaC,IAAeva,EAAAA,EAAAA,IAAO,MAAO,CACxCE,KAAM,cACNN,KAAM,OACN8Z,kBAzB+BA,CAACnZ,EAAOoZ,KACvC,MAAM,WACJ/Y,GACEL,EACJ,MAAO,CAACoZ,EAAO5Y,KAAMH,EAAW4Z,OAASb,EAAOa,MAAiC,eAA1B5Z,EAAW6Z,YAA+Bd,EAAOe,oBAAqB9Z,EAAW+Z,SAAWhB,EAAOgB,SAAU/Z,EAAWgZ,gBAAkBD,EAAOiB,SAAUha,EAAWia,gBAAkBlB,EAAOzX,QAAStB,EAAWka,oBAAsBnB,EAAOoB,gBAAgB,GAkB7R/a,EAIzBgb,EAAAA,EAAAA,IAAU1Y,IAAA,IAAC,MACZ2Y,GACD3Y,EAAA,MAAM,CACL4Y,QAAS,OACTC,eAAgB,aAChBV,WAAY,SACZZ,SAAU,WACVuB,eAAgB,OAChBC,MAAO,OACPC,UAAW,aACXC,UAAW,OACXtB,SAAU,CAAC,CACT1Z,MAAOib,IAAA,IAAC,WACN5a,GACD4a,EAAA,OAAM5a,EAAWia,cAAc,EAChCvQ,MAAO,CACLmR,WAAY,EACZtZ,cAAe,IAEhB,CACD5B,MAAOmb,IAAA,IAAC,WACN9a,GACD8a,EAAA,OAAM9a,EAAWia,gBAAkBja,EAAW4Z,KAAK,EACpDlQ,MAAO,CACLmR,WAAY,EACZtZ,cAAe,IAEhB,CACD5B,MAAOob,IAAA,IAAC,WACN/a,GACD+a,EAAA,OAAM/a,EAAWia,iBAAmBja,EAAWgZ,cAAc,EAC9DtP,MAAO,CACLC,YAAa,GACbqR,aAAc,KAEf,CACDrb,MAAOsb,IAAA,IAAC,WACNjb,GACDib,EAAA,OAAMjb,EAAWia,kBAAoBja,EAAWma,eAAe,EAChEzQ,MAAO,CAGLsR,aAAc,KAEf,CACDrb,MAAOub,IAAA,IAAC,WACNlb,GACDkb,EAAA,QAAOlb,EAAWma,eAAe,EAClCzQ,MAAO,CACL,CAAC,QAAQyR,EAAsBhb,QAAS,CACtC6a,aAAc,MAGjB,CACDrb,MAAO,CACLka,WAAY,cAEdnQ,MAAO,CACLmQ,WAAY,eAEb,CACDla,MAAOyb,IAAA,IAAC,WACNpb,GACDob,EAAA,OAAKpb,EAAW+Z,OAAO,EACxBrQ,MAAO,CACL2R,aAAc,cAAchB,EAAMiB,MAAQjB,GAAOkB,QAAQxB,UACzDyB,eAAgB,gBAEjB,CACD7b,MAAO8b,IAAA,IAAC,WACNzb,GACDyb,EAAA,OAAKzb,EAAW0b,MAAM,EACvBhS,MAAO,CACLiS,WAAYtB,EAAMuB,YAAYC,OAAO,mBAAoB,CACvDC,SAAUzB,EAAMuB,YAAYE,SAASC,WAEvC,UAAW,CACTvB,eAAgB,OAChBnM,iBAAkBgM,EAAMiB,MAAQjB,GAAOkB,QAAQvD,OAAOgE,MAEtD,uBAAwB,CACtB3N,gBAAiB,kBAItB,CACD1O,MAAOsc,IAAA,IAAC,WACNjc,GACDic,EAAA,OAAKjc,EAAWka,kBAAkB,EACnCxQ,MAAO,CAGLsR,aAAc,MAGnB,KACKkB,IAAoB9c,EAAAA,EAAAA,IAAO,KAAM,CACrCE,KAAM,cACNN,KAAM,aAFkBI,CAGvB,CACD6Z,SAAU,aAiPZ,GA3O8BzZ,EAAAA,YAAiB,SAAkBC,EAASC,GACxE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,iBAEF,WACJua,EAAa,SACb/Y,SAAUqb,EAAY,UACtBtc,EACA2B,UAAW4a,EAAa,WACxBC,EAAa,CAAC,EAAC,gBACfC,EAAkB,CAAC,EAAC,mBACpBC,EAAqB,KACrBC,gBACE3c,UAAW4c,KACRD,GACD,CAAC,EAAC,MACN5C,GAAQ,EAAK,eACbZ,GAAiB,EAAK,eACtBiB,GAAiB,EAAK,QACtBF,GAAU,EAAK,gBACfI,EAAe,UACfuC,EAAY,CAAC,EAAC,MACdjD,EAAQ,CAAC,KACN1Z,GACDJ,EACE4Z,EAAU/Z,EAAAA,WAAiBga,EAAAA,GAC3BmD,EAAend,EAAAA,SAAc,KAAM,CACvCoa,MAAOA,GAASL,EAAQK,QAAS,EACjCC,aACAb,oBACE,CAACa,EAAYN,EAAQK,MAAOA,EAAOZ,IACjC4D,EAAcpd,EAAAA,OAAa,MAC3BsB,EAAWtB,EAAAA,SAAeqd,QAAQV,GAGlCjC,EAAqBpZ,EAASuG,SAAUyV,EAAAA,EAAAA,GAAahc,EAASA,EAASuG,OAAS,GAAI,CAAC,4BACrFrH,EAAa,IACdL,EACHka,aACAD,MAAO+C,EAAa/C,MACpBZ,iBACAiB,iBACAF,UACAG,sBAEIja,EA5KkBD,KACxB,MAAM,WACJ6Z,EAAU,QACV5Z,EAAO,MACP2Z,EAAK,eACLZ,EAAc,eACdiB,EAAc,QACdF,EAAO,mBACPG,GACEla,EACEyZ,EAAQ,CACZtZ,KAAM,CAAC,OAAQyZ,GAAS,SAAUZ,GAAkB,WAAYiB,GAAkB,UAAWF,GAAW,UAA0B,eAAfF,GAA+B,sBAAuBK,GAAsB,mBAC/L6C,UAAW,CAAC,cAEd,OAAO7c,EAAAA,EAAAA,GAAeuZ,EAAOd,EAAyB1Y,EAAQ,EA8J9CG,CAAkBJ,GAC5Bgd,GAAYC,EAAAA,EAAAA,GAAWL,EAAald,GACpCwd,EAAOzD,EAAMtZ,MAAQkc,EAAWa,MAAQvD,GACxCwD,EAAYT,EAAUvc,MAAQmc,EAAgBnc,MAAQ,CAAC,EACvDid,EAAiB,CACrBvd,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMgd,EAAUtd,UAAWA,MAChDE,GAEL,IAAIsd,EAAYjB,GAAiB,KAGjC,OAAIlC,GAEFmD,EAAaD,EAAe5b,WAAc4a,EAAwBiB,EAAR,MAG/B,OAAvBd,IACgB,OAAdc,EACFA,EAAY,MAC0B,OAA7BD,EAAe5b,YACxB4b,EAAe5b,UAAY,SAGXnB,EAAAA,EAAAA,KAAKmZ,EAAAA,EAAY8D,SAAU,CAC7C1c,MAAO+b,EACP7b,UAAuBI,EAAAA,EAAAA,MAAMgb,GAAmB,CAC9Cza,GAAI8a,EACJ1c,WAAWS,EAAAA,EAAAA,GAAKL,EAAQ8c,UAAWN,GACnC/c,IAAKsd,EACLhd,WAAYA,KACTwc,EACH1b,SAAU,EAAcT,EAAAA,EAAAA,KAAK6c,EAAM,IAC9BC,OACEI,EAAAA,EAAAA,GAAgBL,IAAS,CAC5Bzb,GAAI4b,EACJrd,WAAY,IACPA,KACAmd,EAAUnd,gBAGdod,EACHtc,SAAUA,IACRA,EAAS0c,aAICnd,EAAAA,EAAAA,KAAKmZ,EAAAA,EAAY8D,SAAU,CAC7C1c,MAAO+b,EACP7b,UAAuBI,EAAAA,EAAAA,MAAMgc,EAAM,IAC9BC,EACH1b,GAAI4b,EACJ3d,IAAKsd,OACAO,EAAAA,EAAAA,GAAgBL,IAAS,CAC5Bld,WAAY,IACPA,KACAmd,EAAUnd,gBAGdod,EACHtc,SAAU,CAACA,EAAUqZ,IAAgC9Z,EAAAA,EAAAA,KAAKiZ,GAAyB,CACjFxY,SAAUqZ,QAIlB,I,qCCxPA,MAeMsD,IAAmBre,EAAAA,EAAAA,IAAO,MAAO,CACrCE,KAAM,kBACNN,KAAM,OACN8Z,kBAAmBA,CAACnZ,EAAOoZ,KACzB,MAAM,WACJ/Y,GACEL,EACJ,MAAO,CAAC,CACN,CAAC,MAAM+d,GAAAA,EAAoBC,WAAY5E,EAAO4E,SAC7C,CACD,CAAC,MAAMD,GAAAA,EAAoBE,aAAc7E,EAAO6E,WAC/C7E,EAAO5Y,KAAMH,EAAW6d,OAAS9E,EAAO8E,MAAO7d,EAAW2d,SAAW3d,EAAW4d,WAAa7E,EAAO+E,UAAW9d,EAAW4Z,OAASb,EAAOa,MAAM,GAX9Hxa,CAatB,CACD2e,KAAM,WACNC,SAAU,EACVC,UAAW,EACXC,aAAc,EACd,CAAC,IAAIC,GAAAA,EAAkBhe,iBAAiBud,GAAAA,EAAoBC,YAAa,CACvErD,QAAS,SAEX,CAAC,IAAI6D,GAAAA,EAAkBhe,iBAAiBud,GAAAA,EAAoBE,cAAe,CACzEtD,QAAS,SAEXjB,SAAU,CAAC,CACT1Z,MAAO+B,IAAA,IAAC,WACN1B,GACD0B,EAAA,OAAK1B,EAAW2d,SAAW3d,EAAW4d,SAAS,EAChDlU,MAAO,CACLuU,UAAW,EACXC,aAAc,IAEf,CACDve,MAAOib,IAAA,IAAC,WACN5a,GACD4a,EAAA,OAAK5a,EAAW6d,KAAK,EACtBnU,MAAO,CACLC,YAAa,QAiKnB,GA7JkCnK,EAAAA,YAAiB,SAAsBC,EAASC,GAChF,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,qBAEF,SACJwB,EAAQ,UACRjB,EAAS,kBACTue,GAAoB,EAAK,MACzBP,GAAQ,EACRF,QAASU,EAAW,uBACpBC,EACAV,UAAWW,EAAa,yBACxBC,EAAwB,MACxB/E,EAAQ,CAAC,EAAC,UACViD,EAAY,CAAC,KACV3c,GACDJ,GACE,MACJia,GACEpa,EAAAA,WAAiBga,EAAAA,GACrB,IAAImE,EAAyB,MAAfU,EAAsBA,EAAcvd,EAC9C8c,EAAYW,EAChB,MAAMve,EAAa,IACdL,EACHye,oBACAP,QACAF,UAAWA,EACXC,YAAaA,EACbhE,SAEI3Z,EAvFkBD,KACxB,MAAM,QACJC,EAAO,MACP4d,EAAK,QACLF,EAAO,UACPC,EAAS,MACThE,GACE5Z,EACEyZ,EAAQ,CACZtZ,KAAM,CAAC,OAAQ0d,GAAS,QAASjE,GAAS,QAAS+D,GAAWC,GAAa,aAC3ED,QAAS,CAAC,WACVC,UAAW,CAAC,cAEd,OAAO1d,EAAAA,EAAAA,GAAeuZ,EAAOhZ,GAAAA,EAA6BR,EAAQ,EA0ElDG,CAAkBJ,GAC5Bye,EAAyB,CAC7BhF,QACAiD,UAAW,CACTiB,QAASW,EACTV,UAAWY,KACR9B,KAGAgC,EAAUC,IAAiBC,EAAAA,GAAAA,GAAQ,OAAQ,CAChD/e,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9Bgf,YAAapB,GACbgB,uBAAwB,IACnBA,KACA1e,GAELC,aACAN,SAEKof,EAAaC,IAAoBH,EAAAA,GAAAA,GAAQ,UAAW,CACzD/e,UAAWI,EAAQ0d,QACnBkB,YAAaG,EAAAA,EACbP,yBACAze,gBAEKif,EAAeC,IAAsBN,EAAAA,GAAAA,GAAQ,YAAa,CAC/D/e,UAAWI,EAAQ2d,UACnBiB,YAAaG,EAAAA,EACbP,yBACAze,eAkBF,OAhBe,MAAX2d,GAAmBA,EAAQzS,OAAS8T,EAAAA,GAAeZ,IACrDT,GAAuBtd,EAAAA,EAAAA,KAAKye,EAAa,CACvCK,QAASvF,EAAQ,QAAU,QAC3BpY,UAAWud,GAAkBI,aAAU3e,EAAY,UAChDue,EACHje,SAAU6c,KAGG,MAAbC,GAAqBA,EAAU1S,OAAS8T,EAAAA,GAAeZ,IACzDR,GAAyBvd,EAAAA,EAAAA,KAAK4e,EAAe,CAC3CE,QAAS,QACT5Y,MAAO,mBACJ2Y,EACHpe,SAAU8c,MAGM1c,EAAAA,EAAAA,MAAMwd,EAAU,IAC/BC,EACH7d,SAAU,CAAC6c,EAASC,IAExB,I,0BC3IA,MAiBMwB,IAAchgB,EAAAA,EAAAA,IAAO,MAAO,CAChCE,KAAM,aACNN,KAAM,OACN8Z,kBAAmBA,CAACnZ,EAAOoZ,KACzB,MAAM,WACJ/Y,GACEL,EACJ,MAAO,CAACoZ,EAAO5Y,KAAMH,EAAWqf,UAAYtG,EAAOsG,SAAUtG,EAAO/Y,EAAWmf,SAAUnf,EAAWsf,OAASvG,EAAOuG,MAAkC,aAA3Btf,EAAWuf,aAA8BxG,EAAOyG,SAAUxf,EAAWyf,UAAY1G,EAAO0G,SAAUzf,EAAWc,UAAYiY,EAAO2G,aAAc1f,EAAWc,UAAuC,aAA3Bd,EAAWuf,aAA8BxG,EAAO4G,qBAA+C,UAAzB3f,EAAW2a,WAAoD,aAA3B3a,EAAWuf,aAA8BxG,EAAO6G,eAAyC,SAAzB5f,EAAW2a,WAAmD,aAA3B3a,EAAWuf,aAA8BxG,EAAO8G,cAAc,GAP3hBzgB,EASjBgb,EAAAA,EAAAA,IAAU1Y,IAAA,IAAC,MACZ2Y,GACD3Y,EAAA,MAAM,CACLoe,OAAQ,EAERC,WAAY,EACZC,YAAa,EACbC,YAAa,QACb3R,aAAc+L,EAAMiB,MAAQjB,GAAOkB,QAAQxB,QAC3CmG,kBAAmB,OACnB7G,SAAU,CAAC,CACT1Z,MAAO,CACL0f,UAAU,GAEZ3V,MAAO,CACLuP,SAAU,WACVkH,OAAQ,EACRC,KAAM,EACN3F,MAAO,SAER,CACD9a,MAAO,CACL2f,OAAO,GAET5V,MAAO,CACL4E,YAAa+L,EAAMiB,KAAO,QAAQjB,EAAMiB,KAAKC,QAAQ8E,0BAA2BC,EAAAA,GAAAA,IAAMjG,EAAMkB,QAAQxB,QAAS,OAE9G,CACDpa,MAAO,CACLwf,QAAS,SAEXzV,MAAO,CACL6W,WAAY,KAEb,CACD5gB,MAAO,CACLwf,QAAS,SACTI,YAAa,cAEf7V,MAAO,CACL6W,WAAYlG,EAAMmG,QAAQ,GAC1BC,YAAapG,EAAMmG,QAAQ,KAE5B,CACD7gB,MAAO,CACLwf,QAAS,SACTI,YAAa,YAEf7V,MAAO,CACLuU,UAAW5D,EAAMmG,QAAQ,GACzBtC,aAAc7D,EAAMmG,QAAQ,KAE7B,CACD7gB,MAAO,CACL4f,YAAa,YAEf7V,MAAO,CACLgX,OAAQ,OACRR,kBAAmB,EACnBS,iBAAkB,SAEnB,CACDhhB,MAAO,CACL8f,UAAU,GAEZ/V,MAAO,CACLkX,UAAW,UACXF,OAAQ,SAET,CACD/gB,MAAOib,IAAA,IAAC,WACN5a,GACD4a,EAAA,QAAO5a,EAAWc,QAAQ,EAC3B4I,MAAO,CACL4Q,QAAS,OACTK,UAAW,SACXkG,OAAQ,EACRC,eAAgB,QAChBC,gBAAiB,QACjB,sBAAuB,CACrBC,QAAS,KACTJ,UAAW,YAGd,CACDjhB,MAAOmb,IAAA,IAAC,WACN9a,GACD8a,EAAA,OAAK9a,EAAWc,UAAuC,aAA3Bd,EAAWuf,WAA0B,EAClE7V,MAAO,CACL,sBAAuB,CACrB+Q,MAAO,OACPwG,UAAW,eAAe5G,EAAMiB,MAAQjB,GAAOkB,QAAQxB,UACvD+G,eAAgB,aAGnB,CACDnhB,MAAOob,IAAA,IAAC,WACN/a,GACD+a,EAAA,MAAgC,aAA3B/a,EAAWuf,aAA8Bvf,EAAWc,QAAQ,EAClE4I,MAAO,CACLwX,cAAe,SACf,sBAAuB,CACrBR,OAAQ,OACRS,WAAY,eAAe9G,EAAMiB,MAAQjB,GAAOkB,QAAQxB,UACxDgH,gBAAiB,aAGpB,CACDphB,MAAOsb,IAAA,IAAC,WACNjb,GACDib,EAAA,MAA8B,UAAzBjb,EAAW2a,WAAoD,aAA3B3a,EAAWuf,WAA0B,EAC/E7V,MAAO,CACL,YAAa,CACX+Q,MAAO,OAET,WAAY,CACVA,MAAO,SAGV,CACD9a,MAAOub,IAAA,IAAC,WACNlb,GACDkb,EAAA,MAA8B,SAAzBlb,EAAW2a,WAAmD,aAA3B3a,EAAWuf,WAA0B,EAC9E7V,MAAO,CACL,YAAa,CACX+Q,MAAO,OAET,WAAY,CACVA,MAAO,UAId,KACK2G,IAAiBhiB,EAAAA,EAAAA,IAAO,OAAQ,CACpCE,KAAM,aACNN,KAAM,UACN8Z,kBAAmBA,CAACnZ,EAAOoZ,KACzB,MAAM,WACJ/Y,GACEL,EACJ,MAAO,CAACoZ,EAAOsI,QAAoC,aAA3BrhB,EAAWuf,aAA8BxG,EAAOuI,gBAAgB,GAPrEliB,EASpBgb,EAAAA,EAAAA,IAAUgB,IAAA,IAAC,MACZf,GACDe,EAAA,MAAM,CACLd,QAAS,eACT3Q,YAAa,QAAQ0Q,EAAMmG,QAAQ,YACnCxF,aAAc,QAAQX,EAAMmG,QAAQ,YACpCe,WAAY,SACZlI,SAAU,CAAC,CACT1Z,MAAO,CACL4f,YAAa,YAEf7V,MAAO,CACLmR,WAAY,QAAQR,EAAMmG,QAAQ,YAClCjf,cAAe,QAAQ8Y,EAAMmG,QAAQ,eAG1C,KACKgB,GAAuBhiB,EAAAA,YAAiB,SAAiBC,EAASC,GACtE,MAAMC,GAAQC,EAAAA,EAAAA,GAAgB,CAC5BD,MAAOF,EACPH,KAAM,gBAEF,SACJ+f,GAAW,EAAK,SAChBve,EAAQ,UACRjB,EAAS,YACT0f,EAAc,aAAY,UAC1B/d,GAAYV,GAA4B,aAAhBye,EAA6B,MAAQ,MAAI,SACjEE,GAAW,EAAK,MAChBH,GAAQ,EAAK,KACb3M,GAAqB,OAAdnR,EAAqB,iBAAchB,GAAS,UACnDma,EAAY,SAAQ,QACpBwE,EAAU,eACPpf,GACDJ,EACEK,EAAa,IACdL,EACH0f,WACA7d,YACAie,WACAH,QACAC,cACA5M,OACAgI,YACAwE,WAEIlf,EAtNkBD,KACxB,MAAM,SACJqf,EAAQ,SACRve,EAAQ,QACRb,EAAO,SACPwf,EAAQ,MACRH,EAAK,YACLC,EAAW,UACX5E,EAAS,QACTwE,GACEnf,EACEyZ,EAAQ,CACZtZ,KAAM,CAAC,OAAQkf,GAAY,WAAYF,EAASG,GAAS,QAAyB,aAAhBC,GAA8B,WAAYE,GAAY,WAAY3e,GAAY,eAAgBA,GAA4B,aAAhBye,GAA8B,uBAAsC,UAAd5E,GAAyC,aAAhB4E,GAA8B,iBAAgC,SAAd5E,GAAwC,aAAhB4E,GAA8B,iBACjW8B,QAAS,CAAC,UAA2B,aAAhB9B,GAA8B,oBAErD,OAAOrf,EAAAA,EAAAA,GAAeuZ,EAAOtY,GAAAA,EAAwBlB,EAAQ,EAuM7CG,CAAkBJ,GAClC,OAAoBK,EAAAA,EAAAA,KAAK+e,GAAa,CACpC3d,GAAID,EACJ3B,WAAWS,EAAAA,EAAAA,GAAKL,EAAQE,KAAMN,GAC9B8S,KAAMA,EACNjT,IAAKA,EACLM,WAAYA,EACZ,mBAA6B,cAAT2S,GAAuC,OAAdnR,GAAsC,aAAhB+d,OAA4C/e,EAAd+e,KAC9Fxf,EACHe,SAAUA,GAAwBT,EAAAA,EAAAA,KAAK+gB,GAAgB,CACrDvhB,UAAWI,EAAQohB,QACnBrhB,WAAYA,EACZc,SAAUA,IACP,MAET,IAMI0gB,KACFA,GAAQC,sBAAuB,GAiEjC,YCtSaC,GAAwBA,KACnC,MAAOjS,EAASK,IAActC,EAAAA,EAAAA,UAAmB,KAC1CkC,EAAWK,IAAgBvC,EAAAA,EAAAA,UAAqB,KAChDmC,EAASK,IAAcxC,EAAAA,EAAAA,UAAmB,KAC1CP,EAASgD,IAAczC,EAAAA,EAAAA,WAAkB,IACzChI,EAAOtB,IAAYsJ,EAAAA,EAAAA,UAAwB,OAC3CmU,EAAcC,IAAmBpU,EAAAA,EAAAA,UAAiB,IAClDqU,EAAUC,IAAetU,EAAAA,EAAAA,UAAiB,IAE3C0C,EAAkBpL,UACtB,IACEmL,GAAW,GACX/L,EAAS,MAETqB,QAAQ4K,IAAI,mFAGZ,MAAM4R,QAAsB1R,EAAAA,EAAcC,qBAK1C,GAHA/K,QAAQ4K,IAAI,wDAAoD4R,EAAc1a,OAAQ,WACtFua,EAAgBG,EAAc1a,QAED,IAAzB0a,EAAc1a,OAMhB,OALA9B,QAAQ4K,IAAI,+DACZL,EAAW,IACXC,EAAa,IACbC,EAAW,SACX8R,EAAY,WAKd,MAAME,EAAgB,IAAIC,IAC1BF,EAAclf,SAAQyP,IAChBA,EAAO7B,QAAU6B,EAAO7B,OAAOnH,QACjC0Y,EAAcE,IAAI5P,EAAO7B,OAAOnH,OAClC,IAGF,MAAMwH,EAAyBzE,MAAM8V,KAAKH,GACvCnR,OACA9P,KAAIgQ,IAAU,CACbzO,GAAIyO,EAAWxH,cAAcpD,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IACzE7G,KAAMyR,MAGVxL,QAAQ4K,IAAI,yDAAgDW,EAAazJ,QAGzE,MAAM+a,EAAkB,IAAIC,IAC5BN,EAAclf,SAAQyP,IAChBA,EAAOpB,UAAYoB,EAAOpB,SAAS5H,QAAUgJ,EAAO7B,QAAU6B,EAAO7B,OAAOnH,QAC9E8Y,EAAgBE,IAAIhQ,EAAOpB,SAAS5H,OAAQgJ,EAAO7B,OAAOnH,OAC5D,IAGF,MAAMkI,EAA6BnF,MAAM8V,KAAKC,EAAgBG,WAC3D1R,MAAK,CAAAnP,EAAAkZ,KAAA,IAAEvJ,GAAE3P,GAAG4P,GAAEsJ,EAAA,OAAKvJ,EAAEE,cAAcD,EAAE,IACrCvQ,KAAI+Z,IAAA,IAAE0H,EAAczR,GAAW+J,EAAA,MAAM,CACpCxY,GAAIkgB,EAAajZ,cAAcpD,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAC3E7G,KAAMkjB,EACN9R,OAAQK,EACT,IAEHxL,QAAQ4K,IAAI,2DAAkDqB,EAAenK,QAG7E,MAAMoK,EAAyBsQ,EAC5B5e,QAAOmP,GAAUA,EAAO,gBAAkBA,EAAO,eAAehJ,SAChEvI,KAAIuR,IAAM,CACThQ,GAAIgQ,EAAO,eACXhT,KAAMgT,EAAO,eACb5B,OAAQ4B,EAAO7B,QAAU,GACzBQ,SAAUqB,EAAOpB,UAAY,GAC7BQ,WAAYY,EAAO,mBAGvB/M,QAAQ4K,IAAI,yDAAgDsB,EAAapK,QA8C/E,SACE0a,EACAtS,EACAC,EACAC,GAQA,GANApK,QAAQ4K,IAAI,wEACZ5K,QAAQ4K,IAAI,oDAA0C4R,EAAc1a,UACpE9B,QAAQ4K,IAAI,0DAAgDV,EAAQpI,UACpE9B,QAAQ4K,IAAI,4DAAkDT,EAAUrI,UACxE9B,QAAQ4K,IAAI,0DAAgDR,EAAQtI,UAEhEsI,EAAQtI,OAAS,EAAG,CAEtB,MAAMob,EAAc9S,EAAQ5O,KAAI2R,GAAKA,EAAEpT,OAAMuR,OAC7CtL,QAAQ4K,IAAI,8DAAoDsS,EAAY,OAC5Eld,QAAQ4K,IAAI,6DAAmDsS,EAAYA,EAAYpb,OAAS,OAGhG,MAAMqb,EAA0C,CAAC,EACjD/S,EAAQ9M,SAAQyP,IACd,MAAMqQ,EAAcrQ,EAAOhT,KAAKsjB,OAAO,GAAGC,cAC1CH,EAAaC,IAAgBD,EAAaC,IAAgB,GAAK,CAAC,IAGlEpd,QAAQ4K,IAAI,4DACZ2S,OAAOC,KAAKL,GAAc7R,OAAOhO,SAAQmgB,IACvCzd,QAAQ4K,IAAI,uCAA6B6S,MAAWN,EAAaM,aAAkB,IAIrF,MAAMC,EAAkBtT,EAAQvN,MAAKsQ,GAAKA,EAAEpT,KAAKiK,cAAcC,SAAS,sBAClE0Z,EAAqBvT,EAAQvN,MAAKsQ,GAAKA,EAAEpT,KAAKiK,cAAcC,SAAS,yBAsB3E,GApBAjE,QAAQ4K,IAAI,sEAA4D8S,KACxE1d,QAAQ4K,IAAI,yEAA+D+S,KAEvED,GACF1d,QAAQ4K,IAAI,gEAAsD8S,EAAgB3jB,SAEhF4jB,GACF3d,QAAQ4K,IAAI,mEAAyD+S,EAAmB5jB,SAItFmQ,EAAQpI,OAAS,IACnB9B,QAAQ4K,IAAI,sDACZV,EAAQ5M,SAAQ6N,IACd,MAAMyS,EAAgBxT,EAAQxM,QAAOuP,GAAKA,EAAEhC,SAAWA,EAAOpR,OAC9DiG,QAAQ4K,IAAI,uCAA6BO,EAAOpR,SAAS6jB,EAAc9b,iBAAiB,KAKxFqI,EAAUrI,OAAS,EAAG,CACxB9B,QAAQ4K,IAAI,yEACWT,EAAU3O,KAAIkQ,IAAQ,CAC3C3R,KAAM2R,EAAS3R,KACf8jB,MAAOzT,EAAQxM,QAAOuP,GAAKA,EAAEzB,WAAaA,EAAS3R,OAAM+H,WACvDwJ,MAAK,CAACQ,EAAGC,IAAMA,EAAE8R,MAAQ/R,EAAE+R,QAAOC,MAAM,EAAG,IAEhCxgB,SAAQoO,IACrB1L,QAAQ4K,IAAI,uCAA6Bc,EAAS3R,SAAS2R,EAASmS,gBAAgB,GAExF,CACF,CAEA7d,QAAQ4K,IAAI,6DACd,CA/GMmT,CAAoBvB,EAAejR,EAAcU,EAAgBC,GAGjE3B,EAAWgB,GACXf,EAAayB,GACbxB,EAAWyB,GACXqQ,EAAY,uBAEZvc,QAAQ4K,IAAI,yDAEd,CAAE,MAAO7K,GACPC,QAAQC,MAAM,uCAAmCF,GACjDpB,EAAS,iDACT4L,EAAW,IACXC,EAAa,IACbC,EAAW,IACX4R,EAAgB,GAChBE,EAAY,QACd,CAAC,QACC7R,GAAW,EACb,GAQF,OAJAtC,EAAAA,EAAAA,YAAU,KACRuC,GAAiB,GAChB,IAEI,CACLT,UACAC,YACAC,UACA1C,UACAzH,QACAoK,QAASM,EACTyR,eACAE,WACD,EA4EH,MCwFA,GAvRoC0B,KAClC,MAAM,QACJ9T,EAAO,UACPC,EAAS,QACTC,EAAO,QACP1C,EAAO,MACPzH,EAAK,aACLmc,EAAY,SACZE,EAAQ,QACRjS,GACE8R,KAEJ,GAAIzU,EACF,OACE/L,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAAClJ,QAAQ,OAAO4G,cAAc,SAASrH,WAAW,SAAS4J,EAAG,EAAE3iB,SAAA,EAClET,EAAAA,EAAAA,KAACqjB,EAAAA,EAAgB,CAACC,KAAM,MACxBtjB,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAKyE,GAAI,CAAEC,GAAI,GAAI/iB,SAAC,0DAGxCT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,QAAQ5Y,MAAM,iBAAiBqd,GAAI,CAAEC,GAAI,GAAI/iB,SAAC,wEAOxE,GAAI0E,EACF,OACEtE,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAACC,EAAG,EAAE3iB,SAAA,EACRT,EAAAA,EAAAA,KAACyjB,EAAAA,EAAK,CAACC,SAAS,QAAQH,GAAI,CAAEI,GAAI,GAAIljB,SACnC0E,KAEHnF,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,QAAOre,SAAC,4FAQlC,MAAMmjB,EAAgD,CAAC,EACvDtU,EAAQ9M,SAAQyP,IACd,MAAMqQ,EAAcrQ,EAAOhT,KAAKsjB,OAAO,GAAGC,cAC1CoB,EAAmBtB,IAAgBsB,EAAmBtB,IAAgB,GAAK,CAAC,IAG9E,MAAMuB,EAAoBvU,EAAQ5O,KAAI2R,GAAKA,EAAEpT,OAAMuR,OAC7CoS,EAAkBtT,EAAQvN,MAAKsQ,GAAKA,EAAEpT,KAAKiK,cAAcC,SAAS,sBAClE0Z,EAAqBvT,EAAQvN,MAAKsQ,GAAKA,EAAEpT,KAAKiK,cAAcC,SAAS,yBAGrE2a,EAAe1U,EAAQ1O,KAAI2P,IAAM,CACrCpR,KAAMoR,EAAOpR,KACb8jB,MAAOzT,EAAQxM,QAAOuP,GAAKA,EAAEhC,SAAWA,EAAOpR,OAAM+H,WACnDwJ,MAAK,CAACQ,EAAGC,IAAMA,EAAE8R,MAAQ/R,EAAE+R,QAAOC,MAAM,EAAG,GAGzCe,EAAiB1U,EAAU3O,KAAIkQ,IAAQ,CAC3C3R,KAAM2R,EAAS3R,KACf8jB,MAAOzT,EAAQxM,QAAOuP,GAAKA,EAAEzB,WAAaA,EAAS3R,OAAM+H,WACvDwJ,MAAK,CAACQ,EAAGC,IAAMA,EAAE8R,MAAQ/R,EAAE+R,QAAOC,MAAM,EAAG,IAE/C,OACEniB,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAACC,EAAG,EAAE3iB,SAAA,EACRT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAvjB,SAAC,+CAItCT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,QAAQ5Y,MAAM,iBAAiB+d,WAAS,EAAAxjB,SAAC,uKAM7DI,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAAClJ,QAAQ,OAAOiK,IAAK,EAAGX,GAAI,CAAEI,GAAI,EAAGQ,SAAU,QAAS1jB,SAAA,EAC1DT,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOld,UAC5BT,EAAAA,EAAAA,KAACokB,EAAAA,EAAI,CAAA3jB,UACHI,EAAAA,EAAAA,MAACwjB,EAAAA,EAAW,CAAA5jB,SAAA,EACVT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAK5Y,MAAM,UAASzF,SAAC,mBAGzCT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAIre,SACrB6gB,EAAagD,oBAEhBtkB,EAAAA,EAAAA,KAACukB,EAAAA,EAAI,CACHtZ,MAAOuW,EACP8B,KAAK,QACLpd,MAAOob,EAAe,IAAO,UAAY,UACzCiC,GAAI,CAAEC,GAAI,aAMlBxjB,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOld,UAC5BT,EAAAA,EAAAA,KAACokB,EAAAA,EAAI,CAAA3jB,UACHI,EAAAA,EAAAA,MAACwjB,EAAAA,EAAW,CAAA5jB,SAAA,EACVT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAK5Y,MAAM,UAASzF,SAAC,aAGzCT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAIre,SACrB2O,EAAQpI,iBAMjBhH,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOld,UAC5BT,EAAAA,EAAAA,KAACokB,EAAAA,EAAI,CAAA3jB,UACHI,EAAAA,EAAAA,MAACwjB,EAAAA,EAAW,CAAA5jB,SAAA,EACVT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAK5Y,MAAM,UAASzF,SAAC,eAGzCT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAIre,SACrB4O,EAAUrI,iBAMnBhH,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOld,UAC5BT,EAAAA,EAAAA,KAACokB,EAAAA,EAAI,CAAA3jB,UACHI,EAAAA,EAAAA,MAACwjB,EAAAA,EAAW,CAAA5jB,SAAA,EACVT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAK5Y,MAAM,UAASzF,SAAC,aAGzCT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAIre,SACrB6O,EAAQtI,oBAQnBnG,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAAClJ,QAAQ,OAAOiK,IAAK,EAAGX,GAAI,CAAEY,SAAU,QAAS1jB,SAAA,EACnDT,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOld,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACukB,GAAI,CAAEH,EAAG,GAAI3iB,SAAA,EAClBT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAvjB,SAAC,0BAItCI,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIljB,SAAA,EACjBT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,YAAWre,SAAC,gCAGhCT,EAAAA,EAAAA,KAACukB,EAAAA,EAAI,CACHtZ,MAAOqW,EAAe,IAAO,aAAU,YACvCpb,MAAOob,EAAe,IAAO,UAAY,QACzCgC,KAAK,cAITziB,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIljB,SAAA,EACjBT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,YAAWre,SAAC,yBAGhCI,EAAAA,EAAAA,MAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAOre,SAAA,CAAC,WACjBojB,EAAkB,IAAM,MAAM,QAEzChjB,EAAAA,EAAAA,MAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAOre,SAAA,CAAC,UAClBojB,EAAkBA,EAAkB7c,OAAS,IAAM,MAAM,WAIrEnG,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIljB,SAAA,EACjBT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,YAAWre,SAAC,6BAGhCT,EAAAA,EAAAA,KAACukB,EAAAA,EAAI,CACHtZ,MAAO2X,EAAkB,aAAU,YACnC1c,MAAO0c,EAAkB,UAAY,QACrCU,KAAK,UAENV,IACC/hB,EAAAA,EAAAA,MAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAQyE,GAAI,CAAEC,GAAI,GAAI/iB,SAAA,CAAC,IACvCmiB,EAAgB3jB,KAAK,WAK7B4B,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAACI,GAAI,CAAEI,GAAI,GAAIljB,SAAA,EACjBT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,YAAWre,SAAC,gCAGhCT,EAAAA,EAAAA,KAACukB,EAAAA,EAAI,CACHtZ,MAAO4X,EAAqB,aAAU,YACtC3c,MAAO2c,EAAqB,UAAY,QACxCS,KAAK,UAENT,IACChiB,EAAAA,EAAAA,MAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAQyE,GAAI,CAAEC,GAAI,GAAI/iB,SAAA,CAAC,IACvCoiB,EAAmB5jB,KAAK,gBAOpCe,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOld,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACukB,GAAI,CAAEH,EAAG,GAAI3iB,SAAA,EAClBT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAvjB,SAAC,yBAGtCT,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACI,GAAI,CAAElV,UAAW,IAAKnP,SAAU,QAASuB,SAC3CgiB,OAAOC,KAAKkB,GAAoBpT,OAAO9P,KAAIiiB,IAC1C9hB,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAAclJ,QAAQ,OAAOC,eAAe,gBAAgBqJ,GAAI,CAAEI,GAAI,GAAIljB,SAAA,EAC5EI,EAAAA,EAAAA,MAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAOre,SAAA,CAAEkiB,EAAO,QACpC9hB,EAAAA,EAAAA,MAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAOre,SAAA,CAAEmjB,EAAmBjB,GAAQ,gBAFhDA,gBAUpB9hB,EAAAA,EAAAA,MAACsiB,EAAAA,EAAG,CAAClJ,QAAQ,OAAOiK,IAAK,EAAGX,GAAI,CAAEC,GAAI,EAAGW,SAAU,QAAS1jB,SAAA,EAC1DT,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOld,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACukB,GAAI,CAAEH,EAAG,GAAI3iB,SAAA,EAClBT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAvjB,SAAC,mCAGtCT,EAAAA,EAAAA,KAACwkB,EAAAA,EAAI,CAACjL,OAAK,EAAA9Y,SACRqjB,EAAapjB,KAAI,CAAC2P,EAAQzP,KACzBC,EAAAA,EAAAA,MAAC1B,EAAAA,SAAc,CAAAsB,SAAA,EACbT,EAAAA,EAAAA,KAACykB,GAAQ,CAAAhkB,UACPT,EAAAA,EAAAA,KAAC0kB,GAAY,CACXpH,QAASjN,EAAOpR,KAChBse,UAAW,GAAGlN,EAAO0S,oBAGxBniB,EAAQkjB,EAAa9c,OAAS,IAAKhH,EAAAA,EAAAA,KAACmhB,GAAO,MAPzB9Q,EAAOpR,gBAcpCe,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACzF,KAAK,IAAIC,SAAS,QAAOld,UAC5BI,EAAAA,EAAAA,MAAC7B,EAAAA,EAAK,CAACukB,GAAI,CAAEH,EAAG,GAAI3iB,SAAA,EAClBT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAKkF,cAAY,EAAAvjB,SAAC,sCAGtCT,EAAAA,EAAAA,KAACmjB,EAAAA,EAAG,CAACI,GAAI,CAAElV,UAAW,IAAKnP,SAAU,QAASuB,UAC5CT,EAAAA,EAAAA,KAACwkB,EAAAA,EAAI,CAACjL,OAAK,EAAA9Y,SACRsjB,EAAerjB,KAAI,CAACkQ,EAAUhQ,KAC7BC,EAAAA,EAAAA,MAAC1B,EAAAA,SAAc,CAAAsB,SAAA,EACbT,EAAAA,EAAAA,KAACykB,GAAQ,CAAAhkB,UACPT,EAAAA,EAAAA,KAAC0kB,GAAY,CACXpH,QAAS1M,EAAS3R,KAClBse,UAAW,GAAG3M,EAASmS,oBAG1BniB,EAAQmjB,EAAe/c,OAAS,IAAKhH,EAAAA,EAAAA,KAACmhB,GAAO,MAP3BvQ,EAAS3R,oBAiBzCqiB,EAAe,MACdzgB,EAAAA,EAAAA,MAAC4iB,EAAAA,EAAK,CAACC,SAAS,UAAUH,GAAI,CAAEC,GAAI,GAAI/iB,SAAA,EACtCT,EAAAA,EAAAA,KAAC2e,EAAAA,EAAU,CAACG,QAAQ,KAAIre,SAAC,8DAGzBI,EAAAA,EAAAA,MAAC8d,EAAAA,EAAU,CAACG,QAAQ,QAAOre,SAAA,CAAC,kCACM6gB,EAAagD,iBAAiB,gKAMhE,EC3LGK,GAAyBlgB,UACpC,MAAMmgB,EAAgB,CACpB,CACE3iB,GAAI,sBACJ3B,MAAO,sBACPukB,YAAa,2CAEf,CACE5iB,GAAI,kBACJ3B,MAAO,kBACPukB,YAAa,sCAEf,CACE5iB,GAAI,oBACJ3B,MAAO,oBACPukB,YAAa,+CAEf,CACE5iB,GAAI,qBACJ3B,MAAO,qBACPukB,YAAa,4CAIXC,EAA2B,GAEjC,IAAK,MAAMC,KAAUH,EACnB,IAEE,MAAMI,QAAyBrgB,EAAAA,EAAAA,KAAQC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,eACvCmgB,EAAiBlgB,KAAK1C,MAAK2C,GAAOA,EAAI9C,KAAO8iB,EAAO9iB,aAG3DiF,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAckgB,EAAO9iB,IAAK,CAC7CA,GAAI8iB,EAAO9iB,GACX3B,MAAOykB,EAAOzkB,MACduF,KAAM,eAAeof,KAAeF,EAAO9iB,KAC3CC,SAAU+iB,EACVjf,KAAM,YACNE,MAAO,UACPiB,aAAa,IAAIC,MAAOC,cACxBE,QAAQ,EACRC,OAAQud,EAAO9iB,KAEjB6iB,EAAeniB,KAAKoiB,EAAOzkB,OAC3B4E,QAAQ4K,IAAI,iCAA4BiV,EAAOzkB,SAEnD,CAAE,MAAO6E,GACPD,QAAQC,MAAM,uCAAkC4f,EAAOzkB,SAAU6E,EACnE,CAGF,OAAO2f,CAAc,ECyDvB,GAvN+BI,KAC7B,MAAOC,EAAcC,IAAmBjY,EAAAA,EAAAA,WAAS,IAC1CiJ,EAASiP,IAAclY,EAAAA,EAAAA,UAAiB,KACxCmY,EAAaC,IAAkBpY,EAAAA,EAAAA,UAAuC,SACtEqY,EAAgBC,IAAqBtY,EAAAA,EAAAA,UAAS,KAC9CuY,EAAmBC,IAAwBxY,EAAAA,EAAAA,UAAS,IAErDyY,EAAcA,CAACC,EAAahb,KAChCwa,EAAWQ,GACXN,EAAe1a,GACfpD,YAAW,IAAM4d,EAAW,KAAK,IAAK,EAmFxC,OACExkB,EAAAA,EAAAA,MAAA,OAAKwI,MAAO,CACV2E,gBAAiB,UACjBwS,OAAQ,oBACRsF,aAAc,MACd7kB,QAAS,OACTwe,OAAQ,UACRhf,SAAA,EACAT,EAAAA,EAAAA,KAAA,MAAIqJ,MAAO,CAAEnD,MAAO,UAAW2X,aAAc,QAASpd,SAAC,8CAIvDT,EAAAA,EAAAA,KAAA,KAAGqJ,MAAO,CAAEnD,MAAO,UAAW2X,aAAc,QAASpd,SAAC,0HAIrD2V,IACCpW,EAAAA,EAAAA,KAAA,OAAKqJ,MArCa0c,MACtB,MAAMC,EAAY,CAChB/kB,QAAS,OACT6kB,aAAc,MACdjI,aAAc,OACdqD,WAAY,WACZ+E,WAAY,YACZC,SAAU,QAGZ,OAAQZ,GACN,IAAK,UACH,MAAO,IAAKU,EAAWhY,gBAAiB,UAAW9H,MAAO,UAAWsa,OAAQ,qBAC/E,IAAK,QACH,MAAO,IAAKwF,EAAWhY,gBAAiB,UAAW9H,MAAO,UAAWsa,OAAQ,qBAC/E,QACE,MAAO,IAAKwF,EAAWhY,gBAAiB,UAAW9H,MAAO,UAAWsa,OAAQ,qBACjF,EAoBgBuF,GAAkBtlB,SAC3B2V,KAKLvV,EAAAA,EAAAA,MAAA,OAAKwI,MAAO,CAAEwU,aAAc,QAASpd,SAAA,EACnCT,EAAAA,EAAAA,KAAA,MAAIqJ,MAAO,CAAEnD,MAAO,UAAW2X,aAAc,QAASpd,SAAC,6CAGvDI,EAAAA,EAAAA,MAAA,KAAGwI,MAAO,CAAEnD,MAAO,UAAWggB,SAAU,OAAQrI,aAAc,QAASpd,SAAA,CAAC,cAEtET,EAAAA,EAAAA,KAAA,SAAM,yDACNA,EAAAA,EAAAA,KAAA,SAAM,yCACNA,EAAAA,EAAAA,KAAA,SAAM,qFAERA,EAAAA,EAAAA,KAAA,UACEwB,QAlHeiD,UACrB2gB,GAAgB,GAChB,IACE,MAAMe,ODIsB1hB,WAKhC,IACES,QAAQ4K,IAAI,2DAGZ,MACM5M,SAD2ByB,EAAAA,EAAAA,KAAQC,EAAAA,EAAAA,IAAWC,EAAAA,GAAI,gBAClBC,KAAKpE,KAAIqE,IAAG,CAChD9C,GAAI8C,EAAI9C,MACL8C,EAAIC,WAGT,IAAIohB,EAAe,EACfC,GAAY,EACZC,EAAQ,GAGZ,IAAK,MAAMC,KAAYrjB,EAEhBqjB,EAASjmB,OAAmC,KAA1BimB,EAASjmB,MAAM2I,QAAoC,cAAnBsd,EAASjmB,MAuBlC,QAAnBimB,EAASjmB,QAClB+lB,GAAY,EACZC,EAAQC,EAAStkB,IAvBG,QAAhBskB,EAAStkB,IAAgBskB,EAAStkB,GAAGiH,cAAcC,SAAS,cAExDtB,EAAAA,EAAAA,KAAU9C,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc0hB,EAAStkB,IAAK,CAClD3B,MAAO,MACPuF,KAAM,kBACN3D,SAAU,KACV8D,KAAM,UACNE,MAAO,UACPiB,aAAa,IAAIC,MAAOC,cACxBE,QAAQ,EACRC,OAAQ+e,EAAStkB,KAEnBokB,GAAY,EACZC,EAAQC,EAAStkB,GACjBiD,QAAQ4K,IAAI,2BAAsByW,EAAStkB,cAGrCukB,EAAAA,EAAAA,KAAUzhB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAc0hB,EAAStkB,KAC/CmkB,IACAlhB,QAAQ4K,IAAI,+CAAgCyW,EAAStkB,OA4B3D,OAnBKokB,IACHC,EAAQ,YACFpf,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcyhB,GAAQ,CACzCrkB,GAAIqkB,EACJhmB,MAAO,MACPuF,KAAM,kBACN3D,SAAU,KACV8D,KAAM,UACNE,MAAO,UACPiB,aAAa,IAAIC,MAAOC,cACxBE,QAAQ,EACRC,OAAQ8e,IAEVphB,QAAQ4K,IAAI,gCAMP,CACL2W,QAASL,EACTM,UAAU,EACV9B,oBAL0BD,GAAuB2B,GAQrD,CAAE,MAAOnhB,GAEP,MADAD,QAAQC,MAAM,yCAAqCA,GAC7CA,CACR,GCpFyBwhB,GAErB,IAAIC,EAAa,+BACjBA,GAAc,8BAAeT,EAAOM,8BACpCG,GAAc,qDAEVT,EAAOvB,cAAc5d,OAAS,IAChC4f,GAAc,wCAA8BT,EAAOvB,cAAc1Y,KAAK,WAGxE0a,GAAc,yDAEdhB,EAAYgB,EAAY,WAGxBnf,YAAW,KACTof,OAAOC,SAASC,QAAQ,GACvB,IAEL,CAAE,MAAO5hB,GACPD,QAAQC,MAAM,gBAAiBA,GAC/BygB,EAAY,wBAAmBzgB,aAAiBgR,MAAQhR,EAAMiR,QAAU,kBAAmB,QAC7F,CAAC,QACCgP,GAAgB,EAClB,GAwFMzb,SAAUwb,EACV9b,MAAO,CACLpI,QAAS,YACT+M,gBAAiBmX,EAAe,UAAY,UAC5Cjf,MAAO,QACPsa,OAAQ,OACRsF,aAAc,MACdkB,OAAQ7B,EAAe,cAAgB,UACvCe,SAAU,OACVe,WAAY,QACZxmB,SAED0kB,EAAe,iCAAyB,gDAK7CtkB,EAAAA,EAAAA,MAAA,OAAKwI,MAAO,CAAEuX,UAAW,oBAAqBpG,WAAY,QAAS/Z,SAAA,EACjET,EAAAA,EAAAA,KAAA,MAAIqJ,MAAO,CAAEnD,MAAO,UAAW2X,aAAc,QAASpd,SAAC,4CAGvDT,EAAAA,EAAAA,KAAA,KAAGqJ,MAAO,CAAEnD,MAAO,UAAWggB,SAAU,OAAQrI,aAAc,QAASpd,SAAC,iDAIxEI,EAAAA,EAAAA,MAAA,OAAKwI,MAAO,CAAE4Q,QAAS,OAAQiK,IAAK,OAAQrG,aAAc,OAAQsG,SAAU,QAAS1jB,SAAA,EACnFT,EAAAA,EAAAA,KAAA,SACE6K,KAAK,OACLM,YAAY,uCACZ5K,MAAOilB,EACPhc,SAAW/H,GAAMgkB,EAAkBhkB,EAAEiI,OAAOnJ,MAAM2I,cAAcpD,QAAQ,OAAQ,MAChFuD,MAAO,CACLpI,QAAS,WACTuf,OAAQ,oBACRsF,aAAc,MACdnI,SAAU,QACVD,KAAM,QAGV1d,EAAAA,EAAAA,KAAA,SACE6K,KAAK,OACLM,YAAY,0CACZ5K,MAAOmlB,EACPlc,SAAW/H,GAAMkkB,EAAqBlkB,EAAEiI,OAAOnJ,OAC/C8I,MAAO,CACLpI,QAAS,WACTuf,OAAQ,oBACRsF,aAAc,MACdnI,SAAU,QACVD,KAAM,WAKZ1d,EAAAA,EAAAA,KAAA,UACEwB,QA5IsBiD,UAC5B,GAAK+gB,EAAevc,QAAWyc,EAAkBzc,OAAjD,CAKAmc,GAAgB,GAChB,UDkH8B3gB,eAChCyiB,EACAC,GAEsB,IADtBlC,EAAmBnc,UAAA9B,OAAA,QAAA7G,IAAA2I,UAAA,GAAAA,UAAA,GAAG,MAEtB,IAEE,MAAMse,EAAcF,EAAShe,cAAcpD,QAAQ,OAAQ,KAAKA,QAAQ,cAAe,IAevF,aAbMoB,EAAAA,EAAAA,KAAOnC,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,aAAcuiB,GAAc,CAC/CnlB,GAAImlB,EACJ9mB,MAAO6mB,EACPthB,KAAM,eAAeof,KAAemC,IACpCllB,SAAU+iB,EACVjf,KAAM,YACNE,MAAO,UACPiB,aAAa,IAAIC,MAAOC,cACxBE,QAAQ,EACRC,OAAQ4f,IAGVliB,QAAQ4K,IAAI,qCAAgCqX,MACrC,CACT,CAAE,MAAOhiB,GAEP,OADAD,QAAQC,MAAM,2CAAuCA,IAC9C,CACT,CACF,CC5I4BkiB,CAAmB7B,EAAgBE,IAGvDE,EAAY,gCAA2BF,gBAAiC,WACxED,EAAkB,IAClBE,EAAqB,IAGrBle,YAAW,KACTof,OAAOC,SAASC,QAAQ,GACvB,MAEHnB,EAAY,wCAAoC,QAEpD,CAAE,MAAOzgB,GACPD,QAAQC,MAAM,+BAAgCA,GAC9CygB,EAAY,iBAAYzgB,aAAiBgR,MAAQhR,EAAMiR,QAAU,kBAAmB,QACtF,CAAC,QACCgP,GAAgB,EAClB,CAvBA,MAFEQ,EAAY,+CAA2C,QAyBzD,EAkHMjc,SAAUwb,IAAiBK,EAAevc,SAAWyc,EAAkBzc,OACvEI,MAAO,CACLpI,QAAS,YACT+M,gBAAiBmX,EAAe,UAAY,UAC5Cjf,MAAO,QACPsa,OAAQ,OACRsF,aAAc,MACdkB,QAAS7B,GAAiBK,EAAevc,QAAWyc,EAAkBzc,OAA0B,UAAhB,cAChFid,SAAU,OACVe,WAAY,QACZxmB,SAED0kB,EAAe,mBAAgB,iCAIpCtkB,EAAAA,EAAAA,MAAA,OAAKwI,MAAO,CACVuU,UAAW,OACX3c,QAAS,OACT+M,gBAAiB,UACjB8X,aAAc,MACdI,SAAU,OACVhgB,MAAO,WACPzF,SAAA,EACAT,EAAAA,EAAAA,KAAA,UAAAS,SAAQ,UAAc,+KAGpB,EC1JV,GAjD4B6mB,KAC1B,MAAM,YAAEC,IAAgBC,EAAAA,EAAAA,MAEjBC,EAAUC,KADAC,EAAAA,EAAAA,OACexa,EAAAA,EAAAA,UAAc,QACvCya,EAAgBC,IAAqB1a,EAAAA,EAAAA,WAAkB,GAe9D,OAbAG,EAAAA,EAAAA,YAAU,KACc7I,WACpB,GAAI8iB,EAAa,CACf,MAAMO,GAAU/iB,EAAAA,EAAAA,IAAIF,EAAAA,GAAI,YAAa0iB,EAAYQ,KAC3CC,QAAiB1iB,EAAAA,EAAAA,IAAOwiB,GAC1BE,EAASziB,UACXmiB,EAAYM,EAAShjB,OAEzB,GAEFijB,EAAe,GACd,CAACV,KAGF1mB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,sBAAqBiB,SAAA,EAClCT,EAAAA,EAAAA,KAACkoB,EAAAA,EAAO,CAACT,SAAUA,KACnB5mB,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,eAAciB,SAAA,EAC3BI,EAAAA,EAAAA,MAAA,OAAKrB,UAAU,aAAYiB,SAAA,CAAC,mBAE1BT,EAAAA,EAAAA,KAAA,UACEwB,QAASA,IAAMqmB,GAAmBD,GAClCve,MAAO,CACL6W,WAAY,OACZjf,QAAS,WACT+M,gBAAiB4Z,EAAiB,UAAY,UAC9C1hB,MAAO,QACPsa,OAAQ,OACRsF,aAAc,MACdkB,OAAQ,UACRd,SAAU,QACVzlB,SAEDmnB,EAAiB,mBAAqB,iCAG3C5nB,EAAAA,EAAAA,KAACQ,EAAAA,EAAU,KACXR,EAAAA,EAAAA,KAACklB,GAAY,IACZ0C,GAAiB5nB,EAAAA,EAAAA,KAACkjB,GAAiB,KAAMljB,EAAAA,EAAAA,KAAC0S,EAAW,SAEpD,C", "sources": ["../node_modules/@mui/material/esm/Card/cardClasses.js", "../node_modules/@mui/material/esm/Card/Card.js", "../node_modules/@mui/material/esm/ListItemText/listItemTextClasses.js", "components/shared/StatsCards.tsx", "../node_modules/@mui/material/esm/Divider/dividerClasses.js", "../node_modules/@mui/material/esm/CardContent/cardContentClasses.js", "../node_modules/@mui/material/esm/CardContent/CardContent.js", "components/shared/Modal.tsx", "components/admin/business/utils/cardUtils.ts", "components/admin/business/hooks/useCardManagement.ts", "components/admin/business/hooks/usePageConfiguration.ts", "components/admin/business/components/CardSelector.tsx", "components/admin/business/components/CardManagement.tsx", "components/admin/business/components/FieldConfigItem.tsx", "components/admin/business/components/PageBuilderContent.tsx", "components/admin/business/types/PageBuilderTypes.ts", "components/admin/business/hooks/useOfficeDataSimple.ts", "components/admin/business/components/CheckboxDropdown.tsx", "components/admin/business/components/ReportConfiguration.tsx", "components/admin/business/utils/dropdownPersistence.ts", "components/admin/business/PageBuilder.tsx", "components/admin/business/hooks/usePageBuilderState.ts", "../node_modules/@mui/material/esm/ListItem/listItemClasses.js", "../node_modules/@mui/material/esm/ListItemButton/listItemButtonClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/listItemSecondaryActionClasses.js", "../node_modules/@mui/material/esm/ListItemSecondaryAction/ListItemSecondaryAction.js", "../node_modules/@mui/material/esm/ListItem/ListItem.js", "../node_modules/@mui/material/esm/ListItemText/ListItemText.js", "../node_modules/@mui/material/esm/Divider/Divider.js", "components/admin/business/hooks/useOfficeDataEnhanced.ts", "components/admin/business/OfficeLoadingTest.tsx", "components/admin/business/utils/mmuSetup.ts", "components/admin/MMUSetupTool.tsx", "components/admin/AdminPage.tsx"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root']);\nexport default cardClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport Paper from \"../Paper/index.js\";\nimport { getCardUtilityClass } from \"./cardClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardUtilityClass, classes);\n};\nconst CardRoot = styled(Paper, {\n  name: 'MuiCard',\n  slot: 'Root'\n})({\n  overflow: 'hidden'\n});\nconst Card = /*#__PURE__*/React.forwardRef(function Card(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCard'\n  });\n  const {\n    className,\n    raised = false,\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    raised\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardRoot, {\n    className: clsx(classes.root, className),\n    elevation: raised ? 8 : undefined,\n    ref: ref,\n    ownerState: ownerState,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Card.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the card will use raised styling.\n   * @default false\n   */\n  raised: chainPropTypes(PropTypes.bool, props => {\n    if (props.raised && props.variant === 'outlined') {\n      return new Error('MUI: Combining `raised={true}` with `variant=\"outlined\"` has no effect.');\n    }\n    return null;\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Card;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemTextUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemText', slot);\n}\nconst listItemTextClasses = generateUtilityClasses('MuiListItemText', ['root', 'multiline', 'dense', 'inset', 'primary', 'secondary']);\nexport default listItemTextClasses;", "import React from 'react';\nimport './StatsCards.css';\n\nconst stats = [\n  { title: 'SB Accounts', value: 123456 },\n  { title: 'BD Revenue', value: '₹24,343' },\n  { title: 'No. Aadhaar Trans', value: 1259 },\n  { title: 'PLI', value: '₹99,99,999' }\n];\n\nconst StatsCards: React.FC = () => {\n  return (\n    <div className=\"stats-grid\">\n      {stats.map((stat, index) => (\n        <div className={`stat-card card-${index}`} key={index}>\n          <h3>{stat.title}</h3>\n          <p className=\"stat-value\">{stat.value}</p>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default StatsCards;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDividerUtilityClass(slot) {\n  return generateUtilityClass('MuiDivider', slot);\n}\nconst dividerClasses = generateUtilityClasses('MuiDivider', ['root', 'absolute', 'fullWidth', 'inset', 'middle', 'flexItem', 'light', 'vertical', 'withChildren', 'withChildrenVertical', 'textAlignRight', 'textAlignLeft', 'wrapper', 'wrapperVertical']);\nexport default dividerClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getCardContentUtilityClass(slot) {\n  return generateUtilityClass('MuiCardContent', slot);\n}\nconst cardContentClasses = generateUtilityClasses('MuiCardContent', ['root']);\nexport default cardContentClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getCardContentUtilityClass } from \"./cardContentClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardContentUtilityClass, classes);\n};\nconst CardContentRoot = styled('div', {\n  name: 'MuiCardContent',\n  slot: 'Root'\n})({\n  padding: 16,\n  '&:last-child': {\n    paddingBottom: 24\n  }\n});\nconst CardContent = /*#__PURE__*/React.forwardRef(function CardContent(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiCardContent'\n  });\n  const {\n    className,\n    component = 'div',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    component\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(CardContentRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? CardContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardContent;", "import React from 'react';\nimport './Modal.css';\n\ninterface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n}\n\nconst Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"modal-overlay\" onClick={onClose}>\n      <div className=\"modal-content\" onClick={e => e.stopPropagation()}>\n        <div className=\"modal-header\">\n          <h2>{title}</h2>\n          <button className=\"close-button\" onClick={onClose}>&times;</button>\n        </div>\n        <div className=\"modal-body\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Modal;", "import { <PERSON>a<PERSON>older, FaFile<PERSON>lt, Fa<PERSON>og, FaFolderOpen } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\n// Helper function to generate card style (icon and color)\nexport const generateCardStyle = (title: string) => {\n  const hash = title\n    .split('')\n    .reduce((acc, char) => acc + char.charCodeAt(0), 0);\n  \n  const icons = [FaFolder, FaFileAlt, FaCog, FaFolderOpen]; // Add more icons if needed\n  const colors = ['#FFC107', '#2196F3', '#4CAF50', '#E91E63', '#9C27B0'];\n\n  const icon = icons[hash % icons.length];\n  const color = colors[hash % colors.length];\n  \n  return { icon, color };\n};\n\n// Helper function to check if a card is a main card\nexport const isMainCard = (cardId: string, allCategories: Category[]): boolean => {\n  const card = allCategories.find(c => c.id === cardId);\n  return card ? !card.parentId : false;\n};\n\n// Helper function to check if a card is a leaf card\nexport const isLeafCard = (cardId: string, allCategories: Category[]): boolean => {\n  return !allCategories.some(c => c.parentId === cardId);\n};\n\n// Helper function to organize cards into a tree structure\nexport const organizeCards = (list: Category[]): Category[] => {\n  const map: { [key: string]: Category } = {};\n  const roots: Category[] = [];\n  list.forEach(item => {\n    map[item.id] = { ...item, children: [] }; \n  });\n  list.forEach(item => {\n    if (item.parentId && map[item.parentId]) {\n      map[item.parentId].children?.push(map[item.id]);\n    } else {\n      roots.push(map[item.id]);\n    }\n  });\n  return roots;\n};\n\n// Helper function to get all descendant IDs\nexport const getAllDescendantIds = (parentId: string, allCategories: Category[]): string[] => {\n  let descendants: string[] = [];\n  const children = allCategories.filter(c => c.parentId === parentId);\n  for (const child of children) {\n    descendants.push(child.id);\n    descendants = descendants.concat(getAllDescendantIds(child.id, allCategories));\n  }\n  return descendants;\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc, collection, getDocs, writeBatch, deleteDoc, updateDoc } from 'firebase/firestore';\nimport { Category } from '../types/PageBuilderTypes';\nimport { generateCardStyle, getAllDescendantIds } from '../utils/cardUtils';\n\ninterface UseCardManagementProps {\n  categories: Category[];\n  setCategories: (categories: Category[]) => void;\n  selectedCard: string;\n  setSelectedCard: (card: string) => void;\n  newCardId: string;\n  setNewCardId: (id: string) => void;\n  newCardTitle: string;\n  setNewCardTitle: (title: string) => void;\n  actionType: string;\n  setActionType: (type: string) => void;\n  setIsLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setShowConfirmModal: (show: boolean) => void;\n  setIsAddingNewCard: (adding: boolean) => void;\n  setPageConfig: (config: any) => void;\n  setFields: (fields: any[]) => void;\n  setEditingCard: (card: Category | null) => void;\n  setShowEditModal: (show: boolean) => void;\n  setCardToDelete: (id: string | null) => void;\n  setShowDeleteConfirmModal: (show: boolean) => void;\n}\n\nexport const useCardManagement = (props: UseCardManagementProps) => {\n  const {\n    categories,\n    setCategories,\n    selectedCard,\n    setSelectedCard,\n    newCardId,\n    setNewCardId,\n    newCardTitle,\n    setNewCardTitle,\n    actionType,\n    setActionType,\n    setIsLoading,\n    setError,\n    setSuccess,\n    setShowConfirmModal,\n    setIsAddingNewCard,\n    setPageConfig,\n    setFields,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n  } = props;\n\n  const fetchCategories = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const querySnapshot = await getDocs(collection(db, 'categories'));\n      const fetchedCategories = querySnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() } as Category));\n      setCategories(fetchedCategories);\n    } catch (err) {\n      setError('Failed to fetch categories.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  }, [setCategories, setIsLoading, setError]);\n\n  const checkDuplicateId = async (id: string): Promise<boolean> => {\n    const docRef = doc(db, 'categories', id);\n    const docSnap = await getDoc(docRef);\n    return docSnap.exists();\n  };\n\n  const handleAddNewCard = async () => {\n    if (!newCardId || !newCardTitle) {\n      setError('Report ID and Title are required.');\n      return;\n    }\n    setIsLoading(true);\n    const isDuplicate = await checkDuplicateId(newCardId);\n    if (isDuplicate) {\n      setError('This Report ID already exists. Please use a unique ID.');\n      setIsLoading(false);\n      return;\n    }\n    setIsLoading(false);\n    setShowConfirmModal(true);\n  };\n\n  const handleConfirmCreate = async () => {\n    if (!newCardId || !newCardTitle) {\n        setError('Report ID and Title cannot be empty.');\n        setShowConfirmModal(false);\n        return;\n    }\n    let parentIdToSet: string | null = null;\n    if (actionType === 'createNestedCard' && selectedCard) {\n      parentIdToSet = selectedCard;\n    } else if (actionType === 'addNewCardGlobal') {\n      parentIdToSet = null; \n    } else if (selectedCard && actionType !== 'addNewCardGlobal') {\n        parentIdToSet = selectedCard;\n    } else if (!selectedCard && actionType !== 'createNestedCard') { \n        parentIdToSet = null;\n    }\n\n    const parentPath = parentIdToSet ? categories.find(c => c.id === parentIdToSet)?.path : '/categories';\n    const newPath = `${parentPath}/${newCardId}`.replace(/\\/+/g, '/');\n\n    try {\n      setIsLoading(true);\n      setShowConfirmModal(false);\n      const cardRef = doc(db, 'categories', newCardId);\n      const { icon: generatedIcon, color: generatedColor } = generateCardStyle(newCardTitle);\n      \n      await setDoc(cardRef, {\n        id: newCardId,\n        title: newCardTitle,\n        path: newPath,\n        parentId: parentIdToSet,\n        lastUpdated: new Date().toISOString(),\n        icon: generatedIcon.name,\n        color: generatedColor,\n        fields: [],\n        isPage: true,\n        pageId: newCardId,\n      });\n  \n      await fetchCategories(); \n      \n      setNewCardId('');\n      setNewCardTitle('');\n      setIsAddingNewCard(false);\n      setActionType(''); \n      setSelectedCard(newCardId);\n      setSuccess(`Report \"${newCardTitle}\" has been created successfully!`);\n      setTimeout(() => setSuccess(null), 3000);\n      \n    } catch (err) {\n      setError('Error creating new report. Check console for details.');\n      console.error('Error creating card:', err);\n    } finally {\n      setIsLoading(false); \n    }\n  };\n\n  const handleEditCard = (card: Category) => {\n    setEditingCard(card);\n    setNewCardTitle(card.title);\n    setShowEditModal(true);\n  };\n\n  const handleUpdateCard = async () => {\n    const editingCard = categories.find(c => c.id === selectedCard);\n    if (!editingCard || !newCardTitle) return;\n    try {\n      setIsLoading(true);\n      const cardRef = doc(db, 'categories', editingCard.id);\n      await updateDoc(cardRef, { title: newCardTitle, lastUpdated: new Date().toISOString() });\n      await fetchCategories();\n      setShowEditModal(false);\n      setEditingCard(null);\n      setNewCardTitle('');\n      setSuccess('Report updated successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to update report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleDeleteClick = (cardId: string) => {\n    setCardToDelete(cardId);\n    setShowDeleteConfirmModal(true);\n  };\n\n  const handleConfirmDelete = async () => {\n    if (!selectedCard) return;\n    setIsLoading(true);\n    try {\n      const batch = writeBatch(db);\n      const allDescendants = getAllDescendantIds(selectedCard, categories);\n      const idsToDelete = [selectedCard, ...allDescendants];\n\n      for (const id of idsToDelete) {\n        batch.delete(doc(db, 'categories', id));\n        batch.delete(doc(db, 'pages', id));\n      }\n      await batch.commit();\n      await fetchCategories();\n\n      setShowDeleteConfirmModal(false);\n      setCardToDelete(null);\n      setSelectedCard('');\n      setPageConfig(null);\n      setFields([]);\n      setSuccess('Report and all its nested items deleted successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError('Failed to delete report.');\n      console.error(err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return {\n    fetchCategories,\n    handleAddNewCard,\n    handleConfirmCreate,\n    handleEditCard,\n    handleUpdateCard,\n    handleDeleteClick,\n    handleConfirmDelete,\n  };\n};\n", "import { useCallback } from 'react';\nimport { db } from '../../../../config/firebase';\nimport { doc, setDoc, getDoc } from 'firebase/firestore';\nimport { FormField, PageConfig, Category } from '../types/PageBuilderTypes';\nimport { <PERSON><PERSON>ield as DynamicFormField, FormConfig as DynamicFormConfig, FormFieldOption } from '../../../shared/DynamicForm';\nimport { supabasePageService } from '../services/supabasePageService';\n\ninterface UsePageConfigurationProps {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  setPageConfig: (config: PageConfig | null) => void;\n  fields: FormField[];\n  setFields: (fields: FormField[]) => void;\n  setAvailableDynamicFields: (fields: DynamicFormField[]) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  setSuccess: (success: string | null) => void;\n  setPreviewContent: (content: string) => void;\n  setIsPreviewOpen: (open: boolean) => void;\n  // New dropdown values - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  // Setters for dropdown values\n  setSelectedRegions: (regions: string[]) => void;\n  setSelectedDivisions: (divisions: string[]) => void;\n  setSelectedOffices: (offices: string[]) => void;\n  setSelectedFrequency: (frequency: string) => void;\n}\n\nexport const usePageConfiguration = (props: UsePageConfigurationProps) => {\n  const {\n    categories,\n    selectedCard,\n    pageConfig,\n    setPageConfig,\n    fields,\n    setFields,\n    setAvailableDynamicFields,\n    setLoading,\n    setError,\n    setSuccess,\n    setPreviewContent,\n    setIsPreviewOpen,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  } = props;\n\n  const fetchDynamicFormFields = useCallback(async (formId: string) => {\n    if (!formId) return;\n    console.log(`Fetching dynamic form fields for formId: ${formId}`);\n    try {\n      const formConfigRef = doc(db, 'formConfigs', formId);\n      const formConfigSnap = await getDoc(formConfigRef);\n      if (formConfigSnap.exists()) {\n        const formConfigData = formConfigSnap.data() as DynamicFormConfig;\n        setAvailableDynamicFields(formConfigData.fields || []);\n        console.log('Fetched dynamic fields:', formConfigData.fields);\n      } else {\n        console.log(`No dynamic form configuration found for formId: ${formId}`);\n        setAvailableDynamicFields([]);\n      }\n    } catch (err) {\n      console.error('Error fetching dynamic form fields:', err);\n      setError('Failed to fetch dynamic form fields.');\n      setAvailableDynamicFields([]);\n    }\n  }, [setAvailableDynamicFields, setError]);\n\n  const loadPageConfig = useCallback(async (cardId: string) => {\n    if (!cardId) {\n      console.log('loadPageConfig called with no cardId');\n      return;\n    }\n    console.log(`loadPageConfig called for cardId: ${cardId}`);\n    setLoading(true);\n    setError(null);\n    try {\n      // Try loading from Firebase first\n      const docRef = doc(db, 'pages', cardId);\n      const docSnap = await getDoc(docRef);\n\n      let data: PageConfig | null = null;\n\n      if (docSnap.exists()) {\n        // Loading from Firebase\n        data = docSnap.data() as PageConfig;\n      } else {\n        // If not found in Firebase, try Supabase\n        try {\n          data = await supabasePageService.loadPageConfig(cardId);\n        } catch (supabaseError) {\n          // Not found in either database, will create new config\n        }\n      }\n\n      if (data) {\n        setPageConfig(data);\n        setFields(data.fields || []);\n        // Load saved dropdown values - handle both old single values and new arrays\n        // Only set if current state is empty to preserve user selections\n        const savedRegions = data.selectedRegions || (data.selectedRegion ? [data.selectedRegion] : []);\n        const savedDivisions = data.selectedDivisions || (data.selectedDivision ? [data.selectedDivision] : []);\n        const savedOffices = data.selectedOffices || (data.selectedOffice ? [data.selectedOffice] : []);\n        const savedFrequency = data.selectedFrequency || '';\n\n        // Only update if current selections are empty (preserve user's current selections)\n        if (selectedRegions.length === 0 && savedRegions.length > 0) {\n          setSelectedRegions(savedRegions);\n        }\n        if (selectedDivisions.length === 0 && savedDivisions.length > 0) {\n          setSelectedDivisions(savedDivisions);\n        }\n        if (selectedOffices.length === 0 && savedOffices.length > 0) {\n          setSelectedOffices(savedOffices);\n        }\n        if (!selectedFrequency && savedFrequency) {\n          setSelectedFrequency(savedFrequency);\n        }\n      } else {\n        // Create new page config\n        const card = categories.find(c => c.id === cardId);\n        setPageConfig({\n          id: cardId,\n          title: card?.title || 'New Page',\n          fields: [],\n          lastUpdated: new Date().toISOString(),\n        });\n        setFields([]);\n        // Reset dropdown values for new page\n        setSelectedRegions([]);\n        setSelectedDivisions([]);\n        setSelectedOffices([]);\n        setSelectedFrequency('');\n      }\n    } catch (err) {\n      setError('Failed to load page configuration.');\n      console.error(err);\n      setPageConfig(null);\n      setFields([]);\n    } finally {\n      setLoading(false);\n    }\n  }, [categories, setLoading, setError, setPageConfig, setFields, setSelectedRegions, setSelectedDivisions, setSelectedOffices, setSelectedFrequency, selectedRegions, selectedDivisions, selectedOffices, selectedFrequency]);\n\n  const addField = () => {\n    const newField: FormField = {\n      id: `field_${Date.now()}`,\n      type: 'text',\n      label: 'New Field',\n      placeholder: '',\n      options: [],\n      required: false,\n      region: '',\n      division: '',\n      office: '',\n    };\n    setFields([...fields, newField]);\n  };\n\n  const addFieldFromDynamic = (dynamicField: DynamicFormField) => {\n    console.log('Attempting to add dynamic field:', dynamicField);\n    const newField: FormField = {\n      id: dynamicField.id,\n      type: dynamicField.type,\n      label: dynamicField.label,\n      placeholder: dynamicField.placeholder,\n      options: dynamicField.options ? dynamicField.options.map((opt: string | FormFieldOption) => {\n        if (typeof opt === 'string') {\n          return { label: opt, value: opt };\n        } else {\n          return { label: opt.label, value: opt.value };\n        }\n      }) : undefined,\n      required: dynamicField.required,\n      defaultValue: dynamicField.defaultValue,\n      min: dynamicField.min,\n      max: dynamicField.max,\n      sectionTitle: undefined,\n      columns: undefined,\n      buttonText: undefined,\n      buttonType: undefined,\n      onClickAction: undefined,\n      value: undefined,\n    };\n\n    if (fields.some(field => field.id === newField.id)) {\n        console.warn(`Duplicate field ID detected: \"${newField.id}\". Field not added.`);\n        setError(`Field with ID \"${newField.id}\" already exists in the page configuration.`);\n        setTimeout(() => setError(null), 3000);\n        return;\n    }\n\n    console.log('Adding new field to state:', newField);\n    setFields([...fields, newField]);\n    setSuccess(`Added field \"${newField.label}\" to page configuration.`);\n    setTimeout(() => setSuccess(null), 3000);\n  };\n\n  const updateField = (index: number, updatedField: FormField) => {\n    const updatedFields = [...fields];\n    updatedFields[index] = updatedField;\n    setFields(updatedFields);\n  };\n\n  const removeField = (index: number) => {\n    setFields(fields.filter((_, i) => i !== index));\n  };\n\n  const handleSave = async () => {\n    if (!selectedCard || !pageConfig) {\n      setError('No report selected or page configuration loaded.');\n      return;\n    }\n\n    // Validate that report frequency is selected\n    if (!selectedFrequency) {\n      setError('Report frequency is required. Please select a frequency before saving.');\n      return;\n    }\n\n    setLoading(true);\n    console.log('Attempting to save page configuration for cardId:', selectedCard);\n    console.log('Fields being saved:', fields);\n    console.log('Report frequency:', selectedFrequency);\n\n    try {\n      const cleanedFields = fields.map(field => {\n        const cleanedField: any = {};\n        for (const key in field) {\n          if (field[key] !== undefined) {\n            cleanedField[key] = field[key];\n          } else {\n            cleanedField[key] = null;\n          }\n        }\n        return cleanedField;\n      });\n\n      const updatedPageConfig: PageConfig = {\n        ...pageConfig,\n        id: selectedCard,\n        title: categories.find(c => c.id === selectedCard)?.title || pageConfig.title,\n        fields: cleanedFields,\n        lastUpdated: new Date().toISOString(),\n        selectedRegions,\n        selectedDivisions,\n        selectedOffices,\n        selectedFrequency,\n      };\n\n      // Save to both Firebase and Supabase\n      const savePromises = [];\n\n      // Save to Firebase\n      savePromises.push(\n        setDoc(doc(db, 'pages', selectedCard), updatedPageConfig)\n          .catch(err => {\n            console.error('Firebase save failed:', err);\n            throw new Error(`Firebase save failed: ${err.message}`);\n          })\n      );\n\n      // Save to Supabase\n      savePromises.push(\n        supabasePageService.savePageConfig(updatedPageConfig)\n          .catch(err => {\n            console.error('Supabase save failed:', err);\n            throw new Error(`Supabase save failed: ${err.message}`);\n          })\n      );\n\n      // Wait for both saves to complete\n      await Promise.all(savePromises);\n\n      setPageConfig(updatedPageConfig);\n      setSuccess('Page configuration saved successfully!');\n      setTimeout(() => setSuccess(null), 3000);\n\n    } catch (err) {\n      console.error('Failed to save page configuration:', err);\n      setError(`Failed to save page configuration: ${err instanceof Error ? err.message : 'Unknown error'}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handlePreview = () => {\n    if (!pageConfig || fields.length === 0) {\n      alert('No page configuration or fields to preview.');\n      return;\n    }\n\n    const generatedPreview = `\n      <h1>${pageConfig.title}</h1>\n      <form>\n        ${fields.map(field => {\n          let fieldHtml = '';\n          switch (field.type) {\n            case 'text':\n            case 'number':\n            case 'date':\n            case 'textarea':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <input type=\"${field.type}\" class=\"form-control\" placeholder=\"${field.placeholder || ''}\" ${field.required ? 'required' : ''} />\n                </div>\n              `;\n              break;\n            case 'dropdown':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  <select class=\"form-control\" ${field.required ? 'required' : ''}>\n                    <option value=\"\">Select ${field.label}</option>\n                    ${field.options?.map(option => `<option value=\"${option.value}\">${option.label}</option>`).join('') || ''}\n                  </select>\n                </div>\n              `;\n              break;\n            case 'checkbox':\n              fieldHtml = `\n                <div class=\"form-check mb-3\">\n                  <input type=\"checkbox\" class=\"form-check-input\" id=\"${field.id}\" ${field.required ? 'required' : ''} />\n                  <label class=\"form-check-label\" for=\"${field.id}\">${field.label}${field.required ? ' *' : ''}</label>\n                </div>\n              `;\n              break;\n            case 'radio':\n              fieldHtml = `\n                <div class=\"form-group mb-3\">\n                  <label class=\"form-label\">${field.label}${field.required ? ' *' : ''}</label>\n                  ${field.options?.map((option, i) => `\n                    <div class=\"form-check\">\n                      <input class=\"form-check-input\" type=\"radio\" name=\"${field.id}\" id=\"${field.id}-${i}\" value=\"${option.value}\" ${field.required ? 'required' : ''}>\n                      <label class=\"form-check-label\" for=\"${field.id}-${i}\">${option.label}</label>\n                    </div>\n                  `).join('') || ''}\n                </div>\n              `;\n              break;\n            case 'section':\n              fieldHtml = `\n                <div class=\"card mt-3 mb-3\">\n                  <div class=\"card-header\">${field.sectionTitle || 'Section'}</div>\n                  <div class=\"card-body\">\n                    <p>Fields for this section would appear here in the actual form.</p>\n                  </div>\n                </div>\n              `;\n              break;\n            case 'button':\n              fieldHtml = `\n                <button type=\"button\" class=\"btn btn-primary mt-3\">${field.buttonText || 'Button'}</button>\n              `;\n              break;\n            default:\n              fieldHtml = `<p>Unsupported field type: ${field.type}</p>`;\n          }\n          return fieldHtml;\n        }).join('')}\n      </form>\n    `;\n\n    setPreviewContent(generatedPreview);\n    setIsPreviewOpen(true);\n  };\n\n  return {\n    fetchDynamicFormFields,\n    loadPageConfig,\n    addField,\n    addFieldFromDynamic,\n    updateField,\n    removeField,\n    handleSave,\n    handlePreview,\n  };\n};\n", "import React from 'react';\nimport { Category } from '../types/PageBuilderTypes';\nimport { organizeCards, isLeafCard, isMainCard } from '../utils/cardUtils';\n\ninterface CardSelectorProps {\n  categories: Category[];\n  selectedCard: string;\n  onCardChange: (cardId: string) => void;\n  actionType: string;\n  onActionChange: (action: string) => void;\n  isLoading: boolean;\n  onCreateAction: () => void;\n  onWebPageAction: () => void;\n}\n\nconst CardSelector: React.FC<CardSelectorProps> = ({\n  categories,\n  selectedCard,\n  onCardChange,\n  actionType,\n  onActionChange,\n  isLoading,\n  onCreateAction,\n  onWebPageAction,\n}) => {\n  const renderCardOptions = (cards: Category[], level = 0): React.ReactElement[] => {\n    return cards.flatMap(card => {\n      // Handle undefined or empty titles gracefully\n      let displayTitle = card.title;\n\n      // If title is undefined, null, or empty, check if it's an MMU-related entry\n      if (!displayTitle || displayTitle.trim() === '' || displayTitle === 'undefined') {\n        // Check if this might be an MMU entry based on ID or other properties\n        if (card.id === 'mmu' || card.id.toLowerCase().includes('mmu')) {\n          displayTitle = 'MMU';\n        } else {\n          // Skip undefined entries that are not MMU-related\n          console.warn(`Skipping undefined entry with ID: ${card.id}`);\n          return [];\n        }\n      }\n\n      // Also handle the case where title is literally \"undefined\" string\n      if (displayTitle === 'undefined') {\n        if (card.id === 'mmu' || card.id.toLowerCase().includes('mmu')) {\n          displayTitle = 'MMU';\n        } else {\n          // Skip undefined entries that are not MMU-related\n          console.warn(`Skipping undefined title entry with ID: ${card.id}`);\n          return [];\n        }\n      }\n\n      return [\n        <option key={card.id} value={card.id} style={{ paddingLeft: `${level * 20}px` }}>\n          {`${'--'.repeat(level)} ${displayTitle}`}\n        </option>,\n        ...(card.children && card.children.length > 0 ? renderCardOptions(card.children, level + 1) : []),\n      ];\n    });\n  };\n\n  const handleCardChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newSelectedCard = e.target.value;\n    onCardChange(newSelectedCard);\n  };\n\n  const handleActionChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    const newAction = e.target.value;\n    onActionChange(newAction);\n    \n    if (newAction === 'createNestedCard' || newAction === 'addNewCardGlobal') {\n      onCreateAction();\n    } else if (newAction === 'createWebPage') {\n      onWebPageAction();\n    }\n  };\n\n  return (\n    <div className=\"card-selector\">\n      <select\n        value={selectedCard}\n        onChange={handleCardChange}\n        className=\"form-select\"\n        disabled={isLoading}\n      >\n        <option value=\"\">{isLoading ? 'Loading Reports...' : 'Select or Create New Report'}</option>\n        {renderCardOptions(organizeCards(categories))}\n      </select>\n\n      <div className=\"action-dropdown-container\">\n        <select\n          value={actionType}\n          onChange={handleActionChange}\n          className=\"form-select action-dropdown\"\n        >\n          <option value=\"\">Select Action...</option>\n          <option value=\"addNewCardGlobal\" disabled={!!selectedCard}>\n            Create New Main Report\n          </option>\n          {selectedCard && (\n            <>\n              <option value=\"createNestedCard\">\n                Create Nested Report\n              </option>\n              <option\n                value=\"createWebPage\"\n                disabled={!isLeafCard(selectedCard, categories) || isMainCard(selectedCard, categories)}\n              >\n                Create/Edit Web Page for this Report\n              </option>\n            </>\n          )}\n        </select>\n      </div>\n    </div>\n  );\n};\n\nexport default CardSelector;\n", "import React from 'react';\nimport { FaEdit, FaTrash } from 'react-icons/fa';\nimport { Category } from '../types/PageBuilderTypes';\n\ninterface CardManagementProps {\n  selectedCard: string;\n  categories: Category[];\n  onEditCard: (card: Category) => void;\n  onDeleteCard: (cardId: string) => void;\n}\n\nconst CardManagement: React.FC<CardManagementProps> = ({\n  selectedCard,\n  categories,\n  onEditCard,\n  onDeleteCard,\n}) => {\n  const selectedCategory = categories.find(c => c.id === selectedCard);\n\n  if (!selectedCategory) {\n    return null;\n  }\n\n  return (\n    <div className=\"card-management\">\n      <h3>Report Details: \"{selectedCategory.title}\"</h3>\n      <div className=\"card-actions\">\n        <button\n          onClick={() => onEditCard(selectedCategory)}\n          className=\"edit-button btn btn-outline-primary btn-sm me-2\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaEdit as React.ComponentType<any>)} Edit Name\n        </button>\n        <button\n          onClick={() => onDeleteCard(selectedCard)}\n          className=\"delete-button btn btn-outline-danger btn-sm\"\n          disabled={!selectedCard}\n        >\n          {React.createElement(FaTrash as React.ComponentType<any>)} Delete Report\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CardManagement;\n", "import React from 'react';\nimport { FaTrash } from 'react-icons/fa';\nimport { FormField } from '../types/PageBuilderTypes';\n\ninterface FieldConfigItemProps {\n  field: FormField;\n  index: number;\n  onUpdate: (index: number, field: FormField) => void;\n  onRemove: (index: number) => void;\n}\n\nconst FieldConfigItem: React.FC<FieldConfigItemProps> = ({\n  field,\n  index,\n  onUpdate,\n  onRemove,\n}) => {\n  const handleOptionChange = (optIndex: number, value: string, key: 'label' | 'value') => {\n    const newOptions = [...(field.options || [])];\n    newOptions[optIndex] = { ...newOptions[optIndex], [key]: value };\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const addOption = () => {\n    const newOptions = [...(field.options || []), { label: '', value: '' }];\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const removeOption = (optIndex: number) => {\n    const newOptions = field.options?.filter((_, i) => i !== optIndex);\n    onUpdate(index, { ...field, options: newOptions });\n  };\n\n  const handleDefaultValueChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { value, type } = e.target;\n    let newDefaultValue: any = value;\n    if (type === 'checkbox') {\n      newDefaultValue = (e.target as HTMLInputElement).checked;\n    }\n    onUpdate(index, { ...field, defaultValue: newDefaultValue });\n  };\n\n  return (\n    <div className=\"field-config-item card mb-3\">\n      <div className=\"card-header d-flex justify-content-between align-items-center\">\n        <strong>{field.label || 'Unnamed Field'}</strong> ({field.type})\n        <button onClick={() => onRemove(index)} className=\"btn btn-danger btn-sm\">\n          {React.createElement(FaTrash as React.ComponentType<any>)} Remove\n        </button>\n      </div>\n      <div className=\"card-body\">\n        {/* Field Type Selector */}\n        <div className=\"form-group\">\n          <label htmlFor={`field-type-${index}`} className=\"form-label\">Type: </label>\n          <select\n            id={`field-type-${index}`}\n            className=\"form-control\"\n            value={field.type}\n            onChange={(e) => onUpdate(index, {\n              ...field, \n              type: e.target.value as FormField['type'], \n              options: field.type !== 'dropdown' && field.type !== 'radio' && field.type !== 'checkbox-group' ? undefined : field.options, \n              placeholder: field.type === 'section' || field.type === 'button' ? undefined : field.placeholder \n            })}\n          >\n            <option value=\"text\">Text</option>\n            <option value=\"textarea\">Textarea</option>\n            <option value=\"number\">Number</option>\n            <option value=\"date\">Date</option>\n            <option value=\"dropdown\">Dropdown</option>\n            <option value=\"radio\">Radio Group</option>\n            <option value=\"checkbox\">Checkbox (Single)</option>\n            <option value=\"checkbox-group\">Checkbox Group</option>\n            <option value=\"switch\">Switch</option>\n            <option value=\"file\">File Upload</option>\n            <option value=\"section\">Section Header</option>\n            <option value=\"button\">Button</option>\n          </select>\n        </div>\n\n        <div className=\"form-group\">\n          <label htmlFor={`field-label-${index}`} className=\"form-label\">Label: </label>\n          <input\n            id={`field-label-${index}`}\n            type=\"text\"\n            className=\"form-control\"\n            value={field.label}\n            onChange={(e) => onUpdate(index, {...field, label: e.target.value})}\n            required\n          />\n        </div>\n\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-placeholder-${index}`} className=\"form-label\">Placeholder: </label>\n            <input\n              id={`field-placeholder-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.placeholder || ''}\n              onChange={(e) => onUpdate(index, {...field, placeholder: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'number' && (\n          <>\n            <div className=\"form-group\">\n              <label htmlFor={`field-min-${index}`} className=\"form-label\">Min Value: </label>\n              <input\n                id={`field-min-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.min === undefined ? '' : field.min}\n                onChange={(e) => onUpdate(index, {...field, min: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n            <div className=\"form-group\">\n              <label htmlFor={`field-max-${index}`} className=\"form-label\">Max Value: </label>\n              <input\n                id={`field-max-${index}`}\n                type=\"number\"\n                className=\"form-control\"\n                value={field.max === undefined ? '' : field.max}\n                onChange={(e) => onUpdate(index, {...field, max: e.target.value === '' ? undefined : parseFloat(e.target.value)})}\n              />\n            </div>\n          </>\n        )}\n\n        {['dropdown', 'radio', 'checkbox-group'].includes(field.type) && (\n          <div className=\"form-group field-options-config\">\n            <label className=\"form-label\">Options: </label>\n            {field.options?.map((opt, optIndex) => (\n              <div key={optIndex} className=\"input-group mb-2\">\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Label\"\n                  value={opt.label}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'label')}\n                />\n                <input\n                  type=\"text\"\n                  className=\"form-control\"\n                  placeholder=\"Option Value\"\n                  value={opt.value}\n                  onChange={(e) => handleOptionChange(optIndex, e.target.value, 'value')}\n                />\n                <button type=\"button\" onClick={() => removeOption(optIndex)} className=\"btn btn-outline-danger\">\n                  Remove\n                </button>\n              </div>\n            ))}\n            <button type=\"button\" onClick={addOption} className=\"btn btn-secondary btn-sm\">\n              Add Option\n            </button>\n          </div>\n        )}\n\n        {/* Default Value - Type specific handling */}\n        {['text', 'textarea', 'number', 'date'].includes(field.type) && (\n            <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <input\n                    id={`field-default-value-${index}`}\n                    type={field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n            </div>\n        )}\n\n        {(field.type === 'checkbox' || field.type === 'switch') && (\n            <div className=\"form-group form-check\">\n                <input\n                    id={`field-default-value-${index}`}\n                    type=\"checkbox\"\n                    className=\"form-check-input\"\n                    checked={Boolean(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                />\n                <label htmlFor={`field-default-value-${index}`} className=\"form-check-label\">Default Checked: </label>\n            </div>\n        )}\n\n        {['dropdown', 'radio'].includes(field.type) && field.options && field.options.length > 0 && (\n             <div className=\"form-group\">\n                <label htmlFor={`field-default-value-${index}`} className=\"form-label\">Default Value: </label>\n                <select\n                    id={`field-default-value-${index}`}\n                    className=\"form-control\"\n                    value={field.defaultValue === undefined ? '' : String(field.defaultValue)}\n                    onChange={handleDefaultValueChange}\n                >\n                    <option value=\"\">-- Select Default --</option>\n                    {field.options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}\n                </select>\n            </div>\n        )}\n\n        {field.type === 'checkbox-group' && (\n            <div className=\"form-group\">\n                <label className=\"form-label\">Default Values (comma-separated): </label>\n                <input\n                    type=\"text\"\n                    className=\"form-control\"\n                    value={Array.isArray(field.defaultValue) ? field.defaultValue.join(',') : ''}\n                    onChange={(e) => onUpdate(index, {...field, defaultValue: e.target.value.split(',').map(s => s.trim()).filter(s => s)})}\n                    placeholder=\"value1,value2\"\n                />\n            </div>\n        )}\n\n        {field.type === 'button' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-button-text-${index}`} className=\"form-label\">Button Text: </label>\n            <input\n              id={`field-button-text-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.buttonText || ''}\n              onChange={(e) => onUpdate(index, {...field, buttonText: e.target.value})}\n            />\n          </div>\n        )}\n\n        {field.type === 'section' && (\n          <div className=\"form-group\">\n            <label htmlFor={`field-section-title-${index}`} className=\"form-label\">Section Title: </label>\n            <input\n              id={`field-section-title-${index}`}\n              type=\"text\"\n              className=\"form-control\"\n              value={field.sectionTitle || ''}\n              onChange={(e) => onUpdate(index, {...field, sectionTitle: e.target.value})}\n            />\n          </div>\n        )}\n\n        {/* Required Checkbox (excluding button and section) */}\n        {!['button', 'section'].includes(field.type) && (\n          <div className=\"form-group form-check\">\n            <input\n              id={`field-required-${index}`}\n              type=\"checkbox\"\n              className=\"form-check-input\"\n              checked={!!field.required}\n              onChange={(e) => onUpdate(index, {...field, required: e.target.checked})}\n            />\n            <label htmlFor={`field-required-${index}`} className=\"form-check-label\"> Required</label>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default FieldConfigItem;\n", "import React from 'react';\nimport { FaPlus, FaSave } from 'react-icons/fa';\nimport { FormField, PageConfig } from '../types/PageBuilderTypes';\nimport FieldConfigItem from './FieldConfigItem';\n\ninterface PageBuilderContentProps {\n  pageConfig: PageConfig;\n  fields: FormField[];\n  onAddField: () => void;\n  onUpdateField: (index: number, field: FormField) => void;\n  onRemoveField: (index: number) => void;\n  onSave: () => void;\n  onPreview: () => void;\n  loading: boolean;\n}\n\nconst PageBuilderContent: React.FC<PageBuilderContentProps> = ({\n  pageConfig,\n  fields,\n  onAddField,\n  onUpdateField,\n  onRemoveField,\n  onSave,\n  onPreview,\n  loading,\n}) => {\n  return (\n    <div className=\"builder-content\">\n      <h4>Page Configuration for: {pageConfig.title}</h4>\n      \n      <h5>Current Page Fields:</h5>\n      {fields.map((field, index) => (\n        <FieldConfigItem\n          key={field.id || index}\n          field={field}\n          index={index}\n          onUpdate={onUpdateField}\n          onRemove={onRemoveField}\n        />\n      ))}\n      \n      <button onClick={onAddField} className=\"btn btn-info mt-3\">\n        {React.createElement(FaPlus as React.ComponentType<any>)} Add Field\n      </button>\n      \n      <button \n        onClick={onSave} \n        className=\"btn btn-success mt-3 ms-2\" \n        disabled={loading || !pageConfig || fields.length === 0}\n      >\n        {React.createElement(FaSave as React.ComponentType<any>)} {loading ? 'Saving...' : 'Save Page Configuration'}\n      </button>\n      \n      <button \n        onClick={onPreview} \n        className=\"btn btn-secondary mt-3 ms-2\" \n        disabled={!pageConfig || fields.length === 0}\n      >\n        Preview Page\n      </button>\n    </div>\n  );\n};\n\nexport default PageBuilderContent;\n", "// Interfaces for PageBuilder component\n\nexport interface FormFieldOption {\n  label: string;\n  value: string;\n}\n\n// NOTE: This FormField interface is for the PageBuilder's internal state\n// and represents the configuration being built for a specific 'page'.\n// It's slightly different from the DynamicFormField used by the DynamicForm component.\nexport interface FormField {\n  id: string;\n  type: 'text' | 'textarea' | 'number' | 'date' | 'dropdown' | 'radio' | 'checkbox' | 'checkbox-group' | 'section' | 'button' | 'file' | 'switch';\n  label: string;\n  placeholder?: string;\n  region?: string;\n  division?: string;\n  office?: string;\n  options?: FormFieldOption[]; // For dropdown, radio, checkbox\n  required?: boolean;\n  value?: any; // Current value of the field (might not be used in builder, but kept for consistency)\n  // For section type\n  sectionTitle?: string;\n  columns?: number; // Number of columns for fields within the section\n  // For button type\n  buttonText?: string;\n  buttonType?: string;\n  onClickAction?: string;\n  defaultValue?: any;\n  min?: number;\n  max?: number;\n  [key: string]: any; // Add this index signature\n}\n\nexport interface PageConfig {\n  id: string;\n  title: string;\n  fields: FormField[];\n  lastUpdated: string;\n  isPage?: boolean; // New field\n  pageId?: string;\n  // Report configuration - updated to support both old single values and new arrays\n  selectedRegion?: string; // Keep for backward compatibility\n  selectedDivision?: string; // Keep for backward compatibility\n  selectedOffice?: string; // Keep for backward compatibility\n  selectedRegions?: string[]; // New array-based selections\n  selectedDivisions?: string[]; // New array-based selections\n  selectedOffices?: string[]; // New array-based selections\n  selectedFrequency?: string;\n}\n\nexport interface Category {\n  id: string;\n  title: string;\n  path: string; // e.g., /categories/parent-id/child-id\n  parentId: string | null;\n  children?: Category[];\n  icon?: string; // Icon name (e.g., 'FaFolder')\n  color?: string; // Color for the icon/card\n  fields?: FormField[]; // If storing form fields directly on category for some reason\n  lastUpdated?: string;\n  isPage: boolean; // New field\n  pageId: string; \n}\n\nexport interface PageBuilderState {\n  categories: Category[];\n  selectedCard: string;\n  pageConfig: PageConfig | null;\n  fields: FormField[];\n  availableDynamicFields: any[];\n  isLoading: boolean;\n  loading: boolean;\n  error: string | null;\n  success: string | null;\n  isAddingNewCard: boolean;\n  newCardId: string;\n  newCardTitle: string;\n  showConfirmModal: boolean;\n  editingCard: Category | null;\n  showEditModal: boolean;\n  cardToDelete: string | null;\n  showDeleteConfirmModal: boolean;\n  actionType: string;\n  isPreviewOpen: boolean;\n  previewContent: string;\n  // New dropdown states - updated to arrays for multiple selections\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n}\n\n// Report frequency options\nexport interface ReportFrequency {\n  value: string;\n  label: string;\n}\n\nexport const REPORT_FREQUENCIES: ReportFrequency[] = [\n  { value: 'daily', label: 'Daily' },\n  { value: 'weekly', label: 'Weekly' },\n  { value: 'monthly', label: 'Monthly' }\n];\n\n// Location hierarchy interfaces - matching Supabase table structure\nexport interface Region {\n  id: string;\n  name: string;\n}\n\nexport interface Division {\n  id: string;\n  name: string;\n  region: string; // matches the Region column in Supabase\n}\n\nexport interface Office {\n  id: string; // Now uses office name instead of facility ID\n  name: string;\n  region: string; // matches the Region column in Supabase\n  division: string; // matches the Division column in Supabase\n  facilityId?: string; // Keep facility ID for reference/mapping\n}\n\n// Supabase office record interface (matches actual table structure)\nexport interface SupabaseOfficeRecord {\n  'Facility ID': string;\n  Region: string;\n  Division: string;\n  'Office name': string;\n}\n", "import { useState, useEffect } from 'react';\nimport { supabase } from '../../../../config/supabaseClient';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n}\n\nexport const useOfficeDataSimple = (): UseOfficeDataReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataSimple: Fetching with enhanced pagination...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allData = await OfficeService.fetchAllOfficeData();\n\n      console.log('✅ useOfficeDataSimple: Fetched', allData.length, 'office records');\n\n      // Process regions exactly like SQL: SELECT DISTINCT \"Region\" FROM offices ORDER BY \"Region\"\n      const distinctRegions = allData\n        ?.map(row => row.Region)\n        .filter((region, index, array) => array.indexOf(region) === index)\n        .filter((region): region is string => region != null && region.trim() !== '') // Type guard to ensure string\n        .sort();\n\n      // Process regions successfully\n\n      const regionsArray: Region[] = distinctRegions?.map(regionName => ({\n        id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: regionName,\n      })) || [];\n\n      // Process divisions exactly like SQL: SELECT DISTINCT \"Region\", \"Division\" FROM offices ORDER BY \"Region\", \"Division\"\n      const distinctDivisions = allData\n        ?.map(row => ({ region: row.Region, division: row.Division }))\n        .filter((item, index, array) =>\n          array.findIndex(x => x.region === item.region && x.division === item.division) === index\n        )\n        .filter((item): item is { region: string; division: string } =>\n          item.region != null && item.division != null &&\n          item.region.trim() !== '' && item.division.trim() !== ''\n        )\n        .sort((a, b) => a.region.localeCompare(b.region) || a.division.localeCompare(b.division));\n\n      const divisionsArray: Division[] = distinctDivisions?.map(item => ({\n        id: item.division.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n        name: item.division,\n        region: item.region,\n      })) || [];\n\n      // Process all offices - USE OFFICE NAME AS ID instead of Facility ID\n      const officesArray: Office[] = allData\n        ?.filter(row => row['Office name'] && row.Region && row.Division)\n        .map(row => ({\n          id: row['Office name'], // ✅ FIXED: Use office name as ID for form targeting\n          name: row['Office name'],\n          region: row.Region || '',\n          division: row.Division || '',\n          facilityId: row['Office name'], // Use office name as facility ID for consistency\n        })) || [];\n\n      // Data processing completed successfully\n\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n\n    } catch (err) {\n      console.error('🚨 SIMPLE: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n  };\n};\n", "import React, { useState, useRef, useEffect } from 'react';\n\ninterface Option {\n  id: string;\n  name: string;\n}\n\ninterface CheckboxDropdownProps {\n  id: string;\n  label: string;\n  options: Option[];\n  selectedValues: string[];\n  onChange: (values: string[]) => void;\n  disabled?: boolean;\n  placeholder?: string;\n}\n\nconst CheckboxDropdown: React.FC<CheckboxDropdownProps> = ({\n  id,\n  label,\n  options,\n  selectedValues,\n  onChange,\n  disabled = false,\n  placeholder = \"-- Select Options --\"\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleCheckboxChange = (optionId: string) => {\n    if (selectedValues.includes(optionId)) {\n      // Remove from selection\n      onChange(selectedValues.filter(id => id !== optionId));\n    } else {\n      // Add to selection\n      onChange([...selectedValues, optionId]);\n    }\n  };\n\n  const handleSelectAll = () => {\n    if (selectedValues.length === options.length) {\n      // Deselect all\n      onChange([]);\n    } else {\n      // Select all\n      onChange(options.map(option => option.id));\n    }\n  };\n\n  const getDisplayText = () => {\n    if (selectedValues.length === 0) {\n      return placeholder;\n    } else if (selectedValues.length === 1) {\n      const selectedOption = options.find(option => option.id === selectedValues[0]);\n      return selectedOption?.name || placeholder;\n    } else {\n      return `${selectedValues.length} selected`;\n    }\n  };\n\n  const isAllSelected = selectedValues.length === options.length && options.length > 0;\n  const isIndeterminate = selectedValues.length > 0 && selectedValues.length < options.length;\n\n  return (\n    <div className=\"form-group\">\n      <label htmlFor={id} className=\"form-label\">{label}:</label>\n      <div className=\"dropdown\" ref={dropdownRef}>\n        <button\n          id={id}\n          className={`btn btn-outline-secondary dropdown-toggle w-100 text-start ${disabled ? 'disabled' : ''}`}\n          type=\"button\"\n          onClick={() => !disabled && setIsOpen(!isOpen)}\n          disabled={disabled}\n          style={{ \n            backgroundColor: disabled ? '#e9ecef' : 'white',\n            borderColor: '#ced4da'\n          }}\n        >\n          <span className={selectedValues.length === 0 ? 'text-muted' : ''}>\n            {getDisplayText()}\n          </span>\n        </button>\n        \n        {isOpen && !disabled && (\n          <div className=\"dropdown-menu show w-100\" style={{ maxHeight: '300px', overflowY: 'auto' }}>\n            {/* Select All Option */}\n            {options.length > 1 && (\n              <>\n                <div className=\"dropdown-item\">\n                  <div className=\"form-check\">\n                    <input\n                      className=\"form-check-input\"\n                      type=\"checkbox\"\n                      id={`${id}-select-all`}\n                      checked={isAllSelected}\n                      ref={(input) => {\n                        if (input) input.indeterminate = isIndeterminate;\n                      }}\n                      onChange={handleSelectAll}\n                    />\n                    <label className=\"form-check-label fw-bold\" htmlFor={`${id}-select-all`}>\n                      Select All ({options.length})\n                    </label>\n                  </div>\n                </div>\n                <hr className=\"dropdown-divider\" />\n              </>\n            )}\n            \n            {/* Individual Options */}\n            {options.map(option => (\n              <div key={option.id} className=\"dropdown-item\">\n                <div className=\"form-check\">\n                  <input\n                    className=\"form-check-input\"\n                    type=\"checkbox\"\n                    id={`${id}-${option.id}`}\n                    checked={selectedValues.includes(option.id)}\n                    onChange={() => handleCheckboxChange(option.id)}\n                  />\n                  <label className=\"form-check-label\" htmlFor={`${id}-${option.id}`}>\n                    {option.name}\n                  </label>\n                </div>\n              </div>\n            ))}\n            \n            {options.length === 0 && (\n              <div className=\"dropdown-item text-muted\">\n                <em>No options available</em>\n              </div>\n            )}\n          </div>\n        )}\n      </div>\n      \n      {/* Selected count indicator */}\n      {selectedValues.length > 0 && (\n        <small className=\"text-muted mt-1 d-block\">\n          {selectedValues.length} of {options.length} selected\n        </small>\n      )}\n    </div>\n  );\n};\n\nexport default CheckboxDropdown;\n", "import React, { useEffect } from 'react';\nimport { REPORT_FREQUENCIES } from '../types/PageBuilderTypes';\nimport { useOfficeDataSimple as useOfficeData } from '../hooks/useOfficeDataSimple';\nimport CheckboxDropdown from './CheckboxDropdown';\n\ninterface ReportConfigurationProps {\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n  onRegionsChange: (regions: string[]) => void;\n  onDivisionsChange: (divisions: string[]) => void;\n  onOfficesChange: (offices: string[]) => void;\n  onFrequencyChange: (frequency: string) => void;\n}\n\nconst ReportConfiguration: React.FC<ReportConfigurationProps> = ({\n  selectedRegions,\n  selectedDivisions,\n  selectedOffices,\n  selectedFrequency,\n  onRegionsChange,\n  onDivisionsChange,\n  onOfficesChange,\n  onFrequencyChange,\n}) => {\n  // Use custom hook to fetch office data from Supabase\n  const { regions, divisions, offices, loading, error, refetch } = useOfficeData();\n\n  // Filter divisions based on selected regions\n  const selectedRegionNames = selectedRegions.map(regionId =>\n    regions.find(r => r.id === regionId)?.name\n  ).filter(Boolean);\n\n  const availableDivisions = selectedRegions.length > 0\n    ? divisions.filter(division => selectedRegionNames.includes(division.region))\n    : divisions; // Show all divisions if no regions selected\n\n  // Filter offices based on selected divisions\n  const selectedDivisionNames = selectedDivisions.map(divisionId =>\n    divisions.find(d => d.id === divisionId)?.name\n  ).filter(Boolean);\n\n  const availableOffices = selectedDivisions.length > 0\n    ? offices.filter(office =>\n        selectedRegionNames.includes(office.region) &&\n        selectedDivisionNames.includes(office.division)\n      )\n    : selectedRegions.length > 0\n      ? offices.filter(office => selectedRegionNames.includes(office.region))\n      : offices; // Show all offices if no filters applied\n\n  // Reset dependent selections when parent selections change\n  useEffect(() => {\n    if (selectedRegions.length > 0) {\n      // Remove divisions that don't belong to selected regions\n      const validDivisions = selectedDivisions.filter(divisionId => {\n        const division = divisions.find(d => d.id === divisionId);\n        return division && selectedRegionNames.includes(division.region);\n      });\n\n      if (validDivisions.length !== selectedDivisions.length) {\n        onDivisionsChange(validDivisions);\n      }\n    }\n  }, [selectedRegions, selectedDivisions, divisions, selectedRegionNames, onDivisionsChange]);\n\n  useEffect(() => {\n    if (selectedDivisions.length > 0) {\n      // Remove offices that don't belong to selected regions/divisions\n      const validOffices = selectedOffices.filter(officeId => {\n        const office = offices.find(o => o.id === officeId);\n        return office &&\n               selectedRegionNames.includes(office.region) &&\n               selectedDivisionNames.includes(office.division);\n      });\n\n      if (validOffices.length !== selectedOffices.length) {\n        onOfficesChange(validOffices);\n      }\n    }\n  }, [selectedDivisions, selectedOffices, offices, selectedRegionNames, selectedDivisionNames, onOfficesChange]);\n\n  return (\n    <div className=\"report-configuration mt-3 mb-3\">\n      <h5>Report Configuration</h5>\n\n      {loading && (\n        <div className=\"alert alert-info\">\n          <div className=\"d-flex align-items-center\">\n            <div className=\"spinner-border spinner-border-sm me-2\" role=\"status\">\n              <span className=\"visually-hidden\">Loading...</span>\n            </div>\n            Loading office data...\n          </div>\n        </div>\n      )}\n\n      {error && (\n        <div className=\"alert alert-danger\">\n          <strong>Error:</strong> {error}\n          <button\n            className=\"btn btn-sm btn-outline-danger ms-2\"\n            onClick={refetch}\n          >\n            Retry\n          </button>\n        </div>\n      )}\n\n      {!loading && !error && (\n        <div className=\"row\">\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"region-select\"\n              label=\"Select Regions\"\n              options={regions}\n              selectedValues={selectedRegions}\n              onChange={onRegionsChange}\n              disabled={loading}\n              placeholder=\"-- Select Regions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"division-select\"\n              label=\"Select Divisions\"\n              options={availableDivisions}\n              selectedValues={selectedDivisions}\n              onChange={onDivisionsChange}\n              disabled={selectedRegions.length === 0 || loading}\n              placeholder=\"-- Select Divisions --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <CheckboxDropdown\n              id=\"office-select\"\n              label=\"Select Offices\"\n              options={availableOffices}\n              selectedValues={selectedOffices}\n              onChange={onOfficesChange}\n              disabled={selectedDivisions.length === 0 || loading}\n              placeholder=\"-- Select Offices --\"\n            />\n          </div>\n\n          <div className=\"col-md-3\">\n            <div className=\"form-group\">\n              <label htmlFor=\"frequency-select\" className=\"form-label\">\n                Report Frequency: <span className=\"text-danger\">*</span>\n              </label>\n              <select\n                id=\"frequency-select\"\n                className={`form-select ${!selectedFrequency ? 'is-invalid' : ''}`}\n                value={selectedFrequency}\n                onChange={(e) => onFrequencyChange(e.target.value)}\n                disabled={loading}\n                required\n              >\n                <option value=\"\">-- Select Frequency --</option>\n                {REPORT_FREQUENCIES.map(frequency => (\n                  <option key={frequency.value} value={frequency.value}>\n                    {frequency.label}\n                  </option>\n                ))}\n              </select>\n              {!selectedFrequency && (\n                <div className=\"invalid-feedback\">\n                  Report frequency is required.\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ReportConfiguration;\n", "/**\n * Utility functions for persisting dropdown selections in localStorage\n * This helps maintain selected values across component re-renders and page refreshes\n */\n\ninterface DropdownSelections {\n  selectedRegions: string[];\n  selectedDivisions: string[];\n  selectedOffices: string[];\n  selectedFrequency: string;\n}\n\nconst STORAGE_KEY = 'admin_dropdown_selections';\n\n/**\n * Save dropdown selections to localStorage\n */\nexport const saveDropdownSelections = (selections: DropdownSelections): void => {\n  try {\n    localStorage.setItem(STORAGE_KEY, JSON.stringify(selections));\n  } catch (error) {\n    console.warn('Failed to save dropdown selections to localStorage:', error);\n  }\n};\n\n/**\n * Load dropdown selections from localStorage\n */\nexport const loadDropdownSelections = (): DropdownSelections | null => {\n  try {\n    const stored = localStorage.getItem(STORAGE_KEY);\n    if (stored) {\n      return JSON.parse(stored);\n    }\n  } catch (error) {\n    console.warn('Failed to load dropdown selections from localStorage:', error);\n  }\n  return null;\n};\n\n/**\n * Clear dropdown selections from localStorage\n */\nexport const clearDropdownSelections = (): void => {\n  try {\n    localStorage.removeItem(STORAGE_KEY);\n  } catch (error) {\n    console.warn('Failed to clear dropdown selections from localStorage:', error);\n  }\n};\n\n/**\n * Check if selections have changed\n */\nexport const hasSelectionsChanged = (\n  current: DropdownSelections,\n  previous: DropdownSelections\n): boolean => {\n  return (\n    JSON.stringify(current.selectedRegions) !== JSON.stringify(previous.selectedRegions) ||\n    JSON.stringify(current.selectedDivisions) !== JSON.stringify(previous.selectedDivisions) ||\n    JSON.stringify(current.selectedOffices) !== JSON.stringify(previous.selectedOffices) ||\n    current.selectedFrequency !== previous.selectedFrequency\n  );\n};\n", "import React, { useEffect } from 'react';\nimport Modal from '../../shared/Modal';\nimport './PageBuilder.css';\n\n// Import refactored components and hooks\nimport { usePageBuilderState } from './hooks/usePageBuilderState';\nimport { useCardManagement } from './hooks/useCardManagement';\nimport { usePageConfiguration } from './hooks/usePageConfiguration';\nimport CardSelector from './components/CardSelector';\nimport CardManagement from './components/CardManagement';\nimport PageBuilderContent from './components/PageBuilderContent';\nimport ReportConfiguration from './components/ReportConfiguration';\n// Debug components removed - Supabase integration working\nimport { isLeafCard, isMainCard } from './utils/cardUtils';\nimport { saveDropdownSelections, loadDropdownSelections } from './utils/dropdownPersistence';\n\n// All interfaces and utilities are now imported from separate files\n\nconst PageBuilder: React.FC = () => {\n  // Use custom hooks for state management\n  const state = usePageBuilderState();\n\n  // Debug mode removed - using working SQL-based implementation\n\n  // Initialize custom hooks\n  const cardManagement = useCardManagement({\n    categories: state.categories,\n    setCategories: state.setCategories,\n    selectedCard: state.selectedCard,\n    setSelectedCard: state.setSelectedCard,\n    newCardId: state.newCardId,\n    setNewCardId: state.setNewCardId,\n    newCardTitle: state.newCardTitle,\n    setNewCardTitle: state.setNewCardTitle,\n    actionType: state.actionType,\n    setActionType: state.setActionType,\n    setIsLoading: state.setIsLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setShowConfirmModal: state.setShowConfirmModal,\n    setIsAddingNewCard: state.setIsAddingNewCard,\n    setPageConfig: state.setPageConfig,\n    setFields: state.setFields,\n    setEditingCard: state.setEditingCard,\n    setShowEditModal: state.setShowEditModal,\n    setCardToDelete: state.setCardToDelete,\n    setShowDeleteConfirmModal: state.setShowDeleteConfirmModal,\n  });\n\n  const pageConfiguration = usePageConfiguration({\n    categories: state.categories,\n    selectedCard: state.selectedCard,\n    pageConfig: state.pageConfig,\n    setPageConfig: state.setPageConfig,\n    fields: state.fields,\n    setFields: state.setFields,\n    setAvailableDynamicFields: state.setAvailableDynamicFields,\n    setLoading: state.setLoading,\n    setError: state.setError,\n    setSuccess: state.setSuccess,\n    setPreviewContent: state.setPreviewContent,\n    setIsPreviewOpen: state.setIsPreviewOpen,\n    selectedRegions: state.selectedRegions,\n    selectedDivisions: state.selectedDivisions,\n    selectedOffices: state.selectedOffices,\n    selectedFrequency: state.selectedFrequency,\n    setSelectedRegions: state.setSelectedRegions,\n    setSelectedDivisions: state.setSelectedDivisions,\n    setSelectedOffices: state.setSelectedOffices,\n    setSelectedFrequency: state.setSelectedFrequency,\n  });\n\n  // Initialize data on component mount\n  useEffect(() => {\n    cardManagement.fetchCategories();\n\n    // Load saved dropdown selections from localStorage\n    const savedSelections = loadDropdownSelections();\n    if (savedSelections) {\n      if (savedSelections.selectedRegions.length > 0) {\n        state.setSelectedRegions(savedSelections.selectedRegions);\n      }\n      if (savedSelections.selectedDivisions.length > 0) {\n        state.setSelectedDivisions(savedSelections.selectedDivisions);\n      }\n      if (savedSelections.selectedOffices.length > 0) {\n        state.setSelectedOffices(savedSelections.selectedOffices);\n      }\n      if (savedSelections.selectedFrequency) {\n        state.setSelectedFrequency(savedSelections.selectedFrequency);\n      }\n    }\n  }, []);\n\n  // Save dropdown selections to localStorage whenever they change\n  useEffect(() => {\n    const selections = {\n      selectedRegions: state.selectedRegions,\n      selectedDivisions: state.selectedDivisions,\n      selectedOffices: state.selectedOffices,\n      selectedFrequency: state.selectedFrequency,\n    };\n\n    // Only save if at least one selection is made\n    if (selections.selectedRegions.length > 0 ||\n        selections.selectedDivisions.length > 0 ||\n        selections.selectedOffices.length > 0 ||\n        selections.selectedFrequency) {\n      saveDropdownSelections(selections);\n    }\n  }, [state.selectedRegions, state.selectedDivisions, state.selectedOffices, state.selectedFrequency]);\n\n  // Handle card and action changes\n  useEffect(() => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.actionType === 'createWebPage') {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n      pageConfiguration.fetchDynamicFormFields(state.selectedCard);\n    } else if (state.selectedCard && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && state.actionType === 'createWebPage') {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n    } else if (!state.selectedCard) {\n      state.setPageConfig(null);\n      state.setFields([]);\n      state.setAvailableDynamicFields([]);\n      state.setActionType('');\n    }\n\n    if (state.selectedCard && state.actionType !== 'createWebPage') {\n        state.setAvailableDynamicFields([]);\n    }\n  }, [state.selectedCard, state.categories, state.actionType]);\n\n  // Event handlers for UI interactions\n  const handleCardChange = (cardId: string) => {\n    state.setSelectedCard(cardId);\n    state.setActionType('');\n    if (!cardId) {\n        state.setPageConfig(null);\n        state.setFields([]);\n    } else {\n        const cardIsLeaf = isLeafCard(cardId, state.categories);\n        const cardIsMain = isMainCard(cardId, state.categories);\n        if(!cardIsLeaf || cardIsMain) {\n            state.setPageConfig(null);\n            state.setFields([]);\n        }\n    }\n  };\n\n  const handleActionChange = (action: string) => {\n    state.setActionType(action);\n  };\n\n  const handleCreateAction = () => {\n    state.setNewCardId('');\n    state.setNewCardTitle('');\n    state.setIsAddingNewCard(true);\n  };\n\n  const handleWebPageAction = () => {\n    if (state.selectedCard && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories)) {\n      pageConfiguration.loadPageConfig(state.selectedCard);\n    } else if (state.selectedCard) {\n      state.setError('Web page can only be created/edited for a final nested report (not a main report).');\n      state.setPageConfig(null);\n      state.setFields([]);\n    }\n  };\n\n  // Event handlers for report configuration dropdowns - updated for arrays\n  const handleRegionsChange = (regions: string[]) => {\n    const previousRegions = state.selectedRegions;\n    state.setSelectedRegions(regions);\n\n    // Only reset dependent dropdowns if regions actually changed\n    // This prevents losing selections when component re-renders\n    if (JSON.stringify(regions) !== JSON.stringify(previousRegions)) {\n      state.setSelectedDivisions([]);\n      state.setSelectedOffices([]);\n    }\n  };\n\n  const handleDivisionsChange = (divisions: string[]) => {\n    const previousDivisions = state.selectedDivisions;\n    state.setSelectedDivisions(divisions);\n\n    // Only reset dependent dropdown if divisions actually changed\n    // This prevents losing selections when component re-renders\n    if (JSON.stringify(divisions) !== JSON.stringify(previousDivisions)) {\n      state.setSelectedOffices([]);\n    }\n  };\n\n  const handleOfficesChange = (offices: string[]) => {\n    state.setSelectedOffices(offices);\n  };\n\n  const handleFrequencyChange = (frequency: string) => {\n    state.setSelectedFrequency(frequency);\n  };\n\n  // All card management functions are now handled by the useCardManagement hook\n\n  // All page builder functions are now handled by the usePageConfiguration hook\n\n  // All rendering functions are now handled by separate components\n  \n  // All field rendering is now handled by the FieldConfigItem component\n\n  return (\n    <>\n      <div className=\"page-builder\">\n        {state.error && <div className=\"error-message\">{state.error}</div>}\n        {state.success && (\n          <div className=\"success-message\">\n            {state.success}\n          </div>\n        )}\n        <h2>Report & Page Builder</h2>\n\n        <CardSelector\n          categories={state.categories}\n          selectedCard={state.selectedCard}\n          onCardChange={handleCardChange}\n          actionType={state.actionType}\n          onActionChange={handleActionChange}\n          isLoading={state.isLoading}\n          onCreateAction={handleCreateAction}\n          onWebPageAction={handleWebPageAction}\n        />\n\n        {/* Modal for adding/creating new card */}\n        {state.isAddingNewCard && (\n          <Modal\n            isOpen={state.isAddingNewCard}\n            onClose={() => {\n              state.setIsAddingNewCard(false);\n              state.setActionType('');\n              state.setNewCardId('');\n              state.setNewCardTitle('');\n            }}\n            title={\n              state.actionType === 'addNewCardGlobal' ? \"Create New Main Report\" :\n              state.selectedCard && state.actionType === 'createNestedCard' ? `Add Nested Report under \"${state.categories.find(c => c.id === state.selectedCard)?.title}\"` :\n              \"Create New Report\"\n            }\n          >\n            <div className=\"new-card-form\">\n              <input\n                type=\"text\"\n                placeholder=\"Report ID (e.g., 'new-report-id')\"\n                value={state.newCardId}\n                onChange={(e) => state.setNewCardId(e.target.value.toLowerCase().replace(/\\s+/g, '-'))}\n                className=\"form-control mb-2\"\n              />\n              <input\n                type=\"text\"\n                placeholder=\"Report Title\"\n                value={state.newCardTitle}\n                onChange={(e) => state.setNewCardTitle(e.target.value)}\n                className=\"form-control mb-2\"\n              />\n              <div className=\"form-buttons modal-buttons\">\n                <button\n                  onClick={cardManagement.handleConfirmCreate}\n                  disabled={state.isLoading || !state.newCardId || !state.newCardTitle}\n                  className=\"btn btn-primary\"\n                >\n                  {state.isLoading ? 'Creating...' : 'Confirm & Create Report'}\n                </button>\n                <button onClick={() => {\n                  state.setIsAddingNewCard(false);\n                  state.setActionType('');\n                  state.setNewCardId('');\n                  state.setNewCardTitle('');\n                }} className=\"btn btn-secondary\">\n                  Cancel\n                </button>\n              </div>\n            </div>\n          </Modal>\n        )}\n\n        {/* Conditional Rendering for Card Management OR Page Builder OR Warnings */}\n        {state.selectedCard && (\n          <>\n            {/* Card Management Section */}\n            {!(state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig) && (\n              <CardManagement\n                selectedCard={state.selectedCard}\n                categories={state.categories}\n                onEditCard={cardManagement.handleEditCard}\n                onDeleteCard={cardManagement.handleDeleteClick}\n              />\n            )}\n\n            {/* Report Configuration Dropdowns */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && (\n              <ReportConfiguration\n                selectedRegions={state.selectedRegions}\n                selectedDivisions={state.selectedDivisions}\n                selectedOffices={state.selectedOffices}\n                selectedFrequency={state.selectedFrequency}\n                onRegionsChange={handleRegionsChange}\n                onDivisionsChange={handleDivisionsChange}\n                onOfficesChange={handleOfficesChange}\n                onFrequencyChange={handleFrequencyChange}\n              />\n            )}\n\n            {/* Page Builder Content */}\n            {state.actionType === 'createWebPage' && isLeafCard(state.selectedCard, state.categories) && !isMainCard(state.selectedCard, state.categories) && state.pageConfig && (\n              <PageBuilderContent\n                pageConfig={state.pageConfig}\n                fields={state.fields}\n                onAddField={pageConfiguration.addField}\n                onUpdateField={pageConfiguration.updateField}\n                onRemoveField={pageConfiguration.removeField}\n                onSave={pageConfiguration.handleSave}\n                onPreview={pageConfiguration.handlePreview}\n                loading={state.loading}\n              />\n            )}\n\n            {/* Warning Messages */}\n            {state.actionType === 'createWebPage' && (!isLeafCard(state.selectedCard, state.categories) || isMainCard(state.selectedCard, state.categories)) && (\n              <div className=\"warning-message mt-3 p-2 bg-warning text-dark rounded\">\n                Page configuration is only available for final nested reports (which are not main reports). Please select an appropriate nested report to configure its page, or create one.\n              </div>\n            )}\n            {state.actionType !== 'createWebPage' && !isLeafCard(state.selectedCard, state.categories) && (\n              <div className=\"info-message mt-3 p-2 bg-info text-dark rounded\">\n                This is a parent report. You can create nested reports under it or select an existing nested report to manage or configure its page.\n              </div>\n            )}\n          </>\n        )}\n\n        {!state.selectedCard && state.actionType === '' && (\n          <div className=\"info-message mt-3 p-3 bg-light border rounded\">\n            <p>Select a report from the dropdown to manage it or configure its web page (if applicable).</p>\n            <p>If no reports exist, or to create a new top-level report, choose \"Create New Main Report\" from the action dropdown after clearing any selection.</p>\n          </div>\n        )}\n\n        {/* Modals for Edit and Delete Confirmation */}\n        {state.showEditModal && state.editingCard && (\n          <Modal\n            isOpen={state.showEditModal}\n            onClose={() => {\n              state.setShowEditModal(false);\n              state.setNewCardTitle('');\n              state.setEditingCard(null);\n            }}\n            title={`Edit Report: ${state.editingCard.title}`}\n          >\n            <input\n              type=\"text\"\n              value={state.newCardTitle}\n              onChange={(e) => state.setNewCardTitle(e.target.value)}\n              placeholder=\"New Report Title\"\n              className=\"form-control mb-2\"\n            />\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleUpdateCard}\n                className=\"btn btn-primary\"\n                disabled={state.isLoading || !state.newCardTitle.trim()}\n              >\n                {state.isLoading ? 'Updating...' : 'Update Title'}\n              </button>\n              <button\n                onClick={() => {\n                  state.setShowEditModal(false);\n                  state.setNewCardTitle('');\n                  state.setEditingCard(null);\n                }}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {state.showDeleteConfirmModal && state.cardToDelete && (\n          <Modal\n            isOpen={state.showDeleteConfirmModal}\n            onClose={() => state.setShowDeleteConfirmModal(false)}\n            title=\"Confirm Deletion\"\n          >\n            <p>Are you sure you want to delete the report \"{state.categories.find(c => c.id === state.cardToDelete)?.title}\" and ALL its nested reports and associated page configurations? This action cannot be undone.</p>\n            <div className=\"form-buttons modal-buttons\">\n              <button\n                onClick={cardManagement.handleConfirmDelete}\n                className=\"btn btn-danger\"\n                disabled={state.isLoading}\n              >\n                {state.isLoading ? 'Deleting...' : 'Confirm Delete'}\n              </button>\n              <button\n                onClick={() => state.setShowDeleteConfirmModal(false)}\n                className=\"btn btn-secondary\"\n              >\n                Cancel\n              </button>\n            </div>\n          </Modal>\n        )}\n\n        {/* Preview Modal */}\n        <Modal\n          isOpen={state.isPreviewOpen}\n          onClose={() => state.setIsPreviewOpen(false)}\n          title=\"Page Preview\"\n        >\n          <div dangerouslySetInnerHTML={{ __html: state.previewContent }} />\n        </Modal>\n      </div>\n    </>\n  );\n};\n\nexport default PageBuilder;\n\n\n", "import { useState } from 'react';\nimport { Category, FormField, PageConfig } from '../types/PageBuilderTypes';\nimport { FormField as DynamicFormField } from '../../../shared/DynamicForm';\n\nexport const usePageBuilderState = () => {\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [selectedCard, setSelectedCard] = useState<string>('');\n  const [pageConfig, setPageConfig] = useState<PageConfig | null>(null);\n  const [fields, setFields] = useState<FormField[]>([]);\n  const [availableDynamicFields, setAvailableDynamicFields] = useState<DynamicFormField[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  const [isAddingNewCard, setIsAddingNewCard] = useState<boolean>(false);\n  const [newCardId, setNewCardId] = useState<string>('');\n  const [newCardTitle, setNewCardTitle] = useState<string>('');\n  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);\n\n  const [editingCard, setEditingCard] = useState<Category | null>(null);\n  const [showEditModal, setShowEditModal] = useState<boolean>(false);\n\n  const [cardToDelete, setCardToDelete] = useState<string | null>(null);\n  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState<boolean>(false);\n\n  const [actionType, setActionType] = useState<string>('');\n\n  // State for Preview Modal\n  const [isPreviewOpen, setIsPreviewOpen] = useState(false);\n  const [previewContent, setPreviewContent] = useState('');\n\n  // New dropdown states - updated to arrays for multiple selections\n  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);\n  const [selectedDivisions, setSelectedDivisions] = useState<string[]>([]);\n  const [selectedOffices, setSelectedOffices] = useState<string[]>([]);\n  const [selectedFrequency, setSelectedFrequency] = useState<string>('');\n\n  return {\n    // State values\n    categories,\n    selectedCard,\n    pageConfig,\n    fields,\n    availableDynamicFields,\n    isLoading,\n    loading,\n    error,\n    success,\n    isAddingNewCard,\n    newCardId,\n    newCardTitle,\n    showConfirmModal,\n    editingCard,\n    showEditModal,\n    cardToDelete,\n    showDeleteConfirmModal,\n    actionType,\n    isPreviewOpen,\n    previewContent,\n    selectedRegions,\n    selectedDivisions,\n    selectedOffices,\n    selectedFrequency,\n\n    // State setters\n    setCategories,\n    setSelectedCard,\n    setPageConfig,\n    setFields,\n    setAvailableDynamicFields,\n    setIsLoading,\n    setLoading,\n    setError,\n    setSuccess,\n    setIsAddingNewCard,\n    setNewCardId,\n    setNewCardTitle,\n    setShowConfirmModal,\n    setEditingCard,\n    setShowEditModal,\n    setCardToDelete,\n    setShowDeleteConfirmModal,\n    setActionType,\n    setIsPreviewOpen,\n    setPreviewContent,\n    setSelectedRegions,\n    setSelectedDivisions,\n    setSelectedOffices,\n    setSelectedFrequency,\n  };\n};\n", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemUtilityClass(slot) {\n  return generateUtilityClass('MuiListItem', slot);\n}\nconst listItemClasses = generateUtilityClasses('MuiListItem', ['root', 'container', 'dense', 'alignItemsFlexStart', 'divider', 'gutters', 'padding', 'secondaryAction']);\nexport default listItemClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemButton', slot);\n}\nconst listItemButtonClasses = generateUtilityClasses('MuiListItemButton', ['root', 'focusVisible', 'dense', 'alignItemsFlexStart', 'disabled', 'divider', 'gutters', 'selected']);\nexport default listItemButtonClasses;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemSecondaryActionClassesUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemSecondaryAction', slot);\n}\nconst listItemSecondaryActionClasses = generateUtilityClasses('MuiListItemSecondaryAction', ['root', 'disableGutters']);\nexport default listItemSecondaryActionClasses;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemSecondaryActionClassesUtilityClass } from \"./listItemSecondaryActionClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disableGutters,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', disableGutters && 'disableGutters']\n  };\n  return composeClasses(slots, getListItemSecondaryActionClassesUtilityClass, classes);\n};\nconst ListItemSecondaryActionRoot = styled('div', {\n  name: 'MuiListItemSecondaryAction',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.disableGutters && styles.disableGutters];\n  }\n})({\n  position: 'absolute',\n  right: 16,\n  top: '50%',\n  transform: 'translateY(-50%)',\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.disableGutters,\n    style: {\n      right: 0\n    }\n  }]\n});\n\n/**\n * Must be used as the last child of ListItem to function properly.\n *\n * @deprecated Use the `secondaryAction` prop in the `ListItem` component instead. This component will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n */\nconst ListItemSecondaryAction = /*#__PURE__*/React.forwardRef(function ListItemSecondaryAction(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemSecondaryAction'\n  });\n  const {\n    className,\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const ownerState = {\n    ...props,\n    disableGutters: context.disableGutters\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemSecondaryActionRoot, {\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref,\n    ...other\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemSecondaryAction.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally an `IconButton` or selection control.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nListItemSecondaryAction.muiName = 'ListItemSecondaryAction';\nexport default ListItemSecondaryAction;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport elementTypeAcceptingRef from '@mui/utils/elementTypeAcceptingRef';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport isHostComponent from \"../utils/isHostComponent.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport isMuiElement from \"../utils/isMuiElement.js\";\nimport useForkRef from \"../utils/useForkRef.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { getListItemUtilityClass } from \"./listItemClasses.js\";\nimport { listItemButtonClasses } from \"../ListItemButton/index.js\";\nimport ListItemSecondaryAction from \"../ListItemSecondaryAction/index.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const overridesResolver = (props, styles) => {\n  const {\n    ownerState\n  } = props;\n  return [styles.root, ownerState.dense && styles.dense, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart, ownerState.divider && styles.divider, !ownerState.disableGutters && styles.gutters, !ownerState.disablePadding && styles.padding, ownerState.hasSecondaryAction && styles.secondaryAction];\n};\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes,\n    dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  } = ownerState;\n  const slots = {\n    root: ['root', dense && 'dense', !disableGutters && 'gutters', !disablePadding && 'padding', divider && 'divider', alignItems === 'flex-start' && 'alignItemsFlexStart', hasSecondaryAction && 'secondaryAction'],\n    container: ['container']\n  };\n  return composeClasses(slots, getListItemUtilityClass, classes);\n};\nexport const ListItemRoot = styled('div', {\n  name: 'MuiListItem',\n  slot: 'Root',\n  overridesResolver\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'flex',\n  justifyContent: 'flex-start',\n  alignItems: 'center',\n  position: 'relative',\n  textDecoration: 'none',\n  width: '100%',\n  boxSizing: 'border-box',\n  textAlign: 'left',\n  variants: [{\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding,\n    style: {\n      paddingTop: 8,\n      paddingBottom: 8\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && ownerState.dense,\n    style: {\n      paddingTop: 4,\n      paddingBottom: 4\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !ownerState.disableGutters,\n    style: {\n      paddingLeft: 16,\n      paddingRight: 16\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !ownerState.disablePadding && !!ownerState.secondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.secondaryAction,\n    style: {\n      [`& > .${listItemButtonClasses.root}`]: {\n        paddingRight: 48\n      }\n    }\n  }, {\n    props: {\n      alignItems: 'flex-start'\n    },\n    style: {\n      alignItems: 'flex-start'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.divider,\n    style: {\n      borderBottom: `1px solid ${(theme.vars || theme).palette.divider}`,\n      backgroundClip: 'padding-box'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.button,\n    style: {\n      transition: theme.transitions.create('background-color', {\n        duration: theme.transitions.duration.shortest\n      }),\n      '&:hover': {\n        textDecoration: 'none',\n        backgroundColor: (theme.vars || theme).palette.action.hover,\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: 'transparent'\n        }\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.hasSecondaryAction,\n    style: {\n      // Add some space to avoid collision as `ListItemSecondaryAction`\n      // is absolutely positioned.\n      paddingRight: 48\n    }\n  }]\n})));\nconst ListItemContainer = styled('li', {\n  name: 'MuiListItem',\n  slot: 'Container'\n})({\n  position: 'relative'\n});\n\n/**\n * Uses an additional container component if `ListItemSecondaryAction` is the last child.\n */\nconst ListItem = /*#__PURE__*/React.forwardRef(function ListItem(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItem'\n  });\n  const {\n    alignItems = 'center',\n    children: childrenProp,\n    className,\n    component: componentProp,\n    components = {},\n    componentsProps = {},\n    ContainerComponent = 'li',\n    ContainerProps: {\n      className: ContainerClassName,\n      ...ContainerProps\n    } = {},\n    dense = false,\n    disableGutters = false,\n    disablePadding = false,\n    divider = false,\n    secondaryAction,\n    slotProps = {},\n    slots = {},\n    ...other\n  } = props;\n  const context = React.useContext(ListContext);\n  const childContext = React.useMemo(() => ({\n    dense: dense || context.dense || false,\n    alignItems,\n    disableGutters\n  }), [alignItems, context.dense, dense, disableGutters]);\n  const listItemRef = React.useRef(null);\n  const children = React.Children.toArray(childrenProp);\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  const hasSecondaryAction = children.length && isMuiElement(children[children.length - 1], ['ListItemSecondaryAction']);\n  const ownerState = {\n    ...props,\n    alignItems,\n    dense: childContext.dense,\n    disableGutters,\n    disablePadding,\n    divider,\n    hasSecondaryAction\n  };\n  const classes = useUtilityClasses(ownerState);\n  const handleRef = useForkRef(listItemRef, ref);\n  const Root = slots.root || components.Root || ListItemRoot;\n  const rootProps = slotProps.root || componentsProps.root || {};\n  const componentProps = {\n    className: clsx(classes.root, rootProps.className, className),\n    ...other\n  };\n  let Component = componentProp || 'li';\n\n  // v4 implementation, deprecated in v6, will be removed in a future major release\n  if (hasSecondaryAction) {\n    // Use div by default.\n    Component = !componentProps.component && !componentProp ? 'div' : Component;\n\n    // Avoid nesting of li > li.\n    if (ContainerComponent === 'li') {\n      if (Component === 'li') {\n        Component = 'div';\n      } else if (componentProps.component === 'li') {\n        componentProps.component = 'div';\n      }\n    }\n    return /*#__PURE__*/_jsx(ListContext.Provider, {\n      value: childContext,\n      children: /*#__PURE__*/_jsxs(ListItemContainer, {\n        as: ContainerComponent,\n        className: clsx(classes.container, ContainerClassName),\n        ref: handleRef,\n        ownerState: ownerState,\n        ...ContainerProps,\n        children: [/*#__PURE__*/_jsx(Root, {\n          ...rootProps,\n          ...(!isHostComponent(Root) && {\n            as: Component,\n            ownerState: {\n              ...ownerState,\n              ...rootProps.ownerState\n            }\n          }),\n          ...componentProps,\n          children: children\n        }), children.pop()]\n      })\n    });\n  }\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsxs(Root, {\n      ...rootProps,\n      as: Component,\n      ref: handleRef,\n      ...(!isHostComponent(Root) && {\n        ownerState: {\n          ...ownerState,\n          ...rootProps.ownerState\n        }\n      }),\n      ...componentProps,\n      children: [children, secondaryAction && /*#__PURE__*/_jsx(ListItemSecondaryAction, {\n        children: secondaryAction\n      })]\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Defines the `align-items` style property.\n   * @default 'center'\n   */\n  alignItems: PropTypes.oneOf(['center', 'flex-start']),\n  /**\n   * The content of the component if a `ListItemSecondaryAction` is used it must\n   * be the last child.\n   */\n  children: chainPropTypes(PropTypes.node, props => {\n    const children = React.Children.toArray(props.children);\n\n    // React.Children.toArray(props.children).findLastIndex(isListItemSecondaryAction)\n    let secondaryActionIndex = -1;\n    for (let i = children.length - 1; i >= 0; i -= 1) {\n      const child = children[i];\n      if (isMuiElement(child, ['ListItemSecondaryAction'])) {\n        secondaryActionIndex = i;\n        break;\n      }\n    }\n\n    //  is ListItemSecondaryAction the last child of ListItem\n    if (secondaryActionIndex !== -1 && secondaryActionIndex !== children.length - 1) {\n      return new Error('MUI: You used an element after ListItemSecondaryAction. ' + 'For ListItem to detect that it has a secondary action ' + 'you must pass it as the last child to ListItem.');\n    }\n    return null;\n  }),\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * @deprecated Use the `slots` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @deprecated Use the `slotProps` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The container component used when a `ListItemSecondaryAction` is the last child.\n   * @default 'li'\n   * @deprecated Use the `component` or `slots.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerComponent: elementTypeAcceptingRef,\n  /**\n   * Props applied to the container component if used.\n   * @default {}\n   * @deprecated Use the `slotProps.root` prop instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  ContainerProps: PropTypes.object,\n  /**\n   * If `true`, compact vertical padding designed for keyboard and mouse input is used.\n   * The prop defaults to the value inherited from the parent List component.\n   * @default false\n   */\n  dense: PropTypes.bool,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * If `true`, all padding is removed.\n   * @default false\n   */\n  disablePadding: PropTypes.bool,\n  /**\n   * If `true`, a 1px light border is added to the bottom of the list item.\n   * @default false\n   */\n  divider: PropTypes.bool,\n  /**\n   * The element to display at the end of ListItem.\n   */\n  secondaryAction: PropTypes.node,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItem;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport Typography, { typographyClasses } from \"../Typography/index.js\";\nimport ListContext from \"../List/ListContext.js\";\nimport { styled } from \"../zero-styled/index.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport listItemTextClasses, { getListItemTextUtilityClass } from \"./listItemTextClasses.js\";\nimport useSlot from \"../utils/useSlot.js\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    inset,\n    primary,\n    secondary,\n    dense\n  } = ownerState;\n  const slots = {\n    root: ['root', inset && 'inset', dense && 'dense', primary && secondary && 'multiline'],\n    primary: ['primary'],\n    secondary: ['secondary']\n  };\n  return composeClasses(slots, getListItemTextUtilityClass, classes);\n};\nconst ListItemTextRoot = styled('div', {\n  name: 'MuiListItemText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${listItemTextClasses.primary}`]: styles.primary\n    }, {\n      [`& .${listItemTextClasses.secondary}`]: styles.secondary\n    }, styles.root, ownerState.inset && styles.inset, ownerState.primary && ownerState.secondary && styles.multiline, ownerState.dense && styles.dense];\n  }\n})({\n  flex: '1 1 auto',\n  minWidth: 0,\n  marginTop: 4,\n  marginBottom: 4,\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.primary})`]: {\n    display: 'block'\n  },\n  [`.${typographyClasses.root}:where(& .${listItemTextClasses.secondary})`]: {\n    display: 'block'\n  },\n  variants: [{\n    props: ({\n      ownerState\n    }) => ownerState.primary && ownerState.secondary,\n    style: {\n      marginTop: 6,\n      marginBottom: 6\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.inset,\n    style: {\n      paddingLeft: 56\n    }\n  }]\n});\nconst ListItemText = /*#__PURE__*/React.forwardRef(function ListItemText(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemText'\n  });\n  const {\n    children,\n    className,\n    disableTypography = false,\n    inset = false,\n    primary: primaryProp,\n    primaryTypographyProps,\n    secondary: secondaryProp,\n    secondaryTypographyProps,\n    slots = {},\n    slotProps = {},\n    ...other\n  } = props;\n  const {\n    dense\n  } = React.useContext(ListContext);\n  let primary = primaryProp != null ? primaryProp : children;\n  let secondary = secondaryProp;\n  const ownerState = {\n    ...props,\n    disableTypography,\n    inset,\n    primary: !!primary,\n    secondary: !!secondary,\n    dense\n  };\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = {\n    slots,\n    slotProps: {\n      primary: primaryTypographyProps,\n      secondary: secondaryTypographyProps,\n      ...slotProps\n    }\n  };\n  const [RootSlot, rootSlotProps] = useSlot('root', {\n    className: clsx(classes.root, className),\n    elementType: ListItemTextRoot,\n    externalForwardedProps: {\n      ...externalForwardedProps,\n      ...other\n    },\n    ownerState,\n    ref\n  });\n  const [PrimarySlot, primarySlotProps] = useSlot('primary', {\n    className: classes.primary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SecondarySlot, secondarySlotProps] = useSlot('secondary', {\n    className: classes.secondary,\n    elementType: Typography,\n    externalForwardedProps,\n    ownerState\n  });\n  if (primary != null && primary.type !== Typography && !disableTypography) {\n    primary = /*#__PURE__*/_jsx(PrimarySlot, {\n      variant: dense ? 'body2' : 'body1',\n      component: primarySlotProps?.variant ? undefined : 'span',\n      ...primarySlotProps,\n      children: primary\n    });\n  }\n  if (secondary != null && secondary.type !== Typography && !disableTypography) {\n    secondary = /*#__PURE__*/_jsx(SecondarySlot, {\n      variant: \"body2\",\n      color: \"textSecondary\",\n      ...secondarySlotProps,\n      children: secondary\n    });\n  }\n  return /*#__PURE__*/_jsxs(RootSlot, {\n    ...rootSlotProps,\n    children: [primary, secondary]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Alias for the `primary` prop.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the children won't be wrapped by a Typography component.\n   * This can be useful to render an alternative Typography variant by wrapping\n   * the `children` (or `primary`) text, and optional `secondary` text\n   * with the Typography component.\n   * @default false\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * If `true`, the children are indented.\n   * This should be used if there is no left avatar or left icon.\n   * @default false\n   */\n  inset: PropTypes.bool,\n  /**\n   * The main content element.\n   */\n  primary: PropTypes.node,\n  /**\n   * These props will be forwarded to the primary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.primary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  primaryTypographyProps: PropTypes.object,\n  /**\n   * The secondary content element.\n   */\n  secondary: PropTypes.node,\n  /**\n   * These props will be forwarded to the secondary typography component\n   * (as long as disableTypography is not `true`).\n   * @deprecated Use `slotProps.secondary` instead. This prop will be removed in a future major release. See [Migrating from deprecated APIs](/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  secondaryTypographyProps: PropTypes.object,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    primary: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    secondary: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    primary: PropTypes.elementType,\n    root: PropTypes.elementType,\n    secondary: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemText;", "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport { styled } from \"../zero-styled/index.js\";\nimport memoTheme from \"../utils/memoTheme.js\";\nimport { useDefaultProps } from \"../DefaultPropsProvider/index.js\";\nimport { getDividerUtilityClass } from \"./dividerClasses.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    absolute,\n    children,\n    classes,\n    flexItem,\n    light,\n    orientation,\n    textAlign,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', absolute && 'absolute', variant, light && 'light', orientation === 'vertical' && 'vertical', flexItem && 'flexItem', children && 'withChildren', children && orientation === 'vertical' && 'withChildrenVertical', textAlign === 'right' && orientation !== 'vertical' && 'textAlignRight', textAlign === 'left' && orientation !== 'vertical' && 'textAlignLeft'],\n    wrapper: ['wrapper', orientation === 'vertical' && 'wrapperVertical']\n  };\n  return composeClasses(slots, getDividerUtilityClass, classes);\n};\nconst DividerRoot = styled('div', {\n  name: 'MuiDivider',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.absolute && styles.absolute, styles[ownerState.variant], ownerState.light && styles.light, ownerState.orientation === 'vertical' && styles.vertical, ownerState.flexItem && styles.flexItem, ownerState.children && styles.withChildren, ownerState.children && ownerState.orientation === 'vertical' && styles.withChildrenVertical, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && styles.textAlignRight, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && styles.textAlignLeft];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  margin: 0,\n  // Reset browser default style.\n  flexShrink: 0,\n  borderWidth: 0,\n  borderStyle: 'solid',\n  borderColor: (theme.vars || theme).palette.divider,\n  borderBottomWidth: 'thin',\n  variants: [{\n    props: {\n      absolute: true\n    },\n    style: {\n      position: 'absolute',\n      bottom: 0,\n      left: 0,\n      width: '100%'\n    }\n  }, {\n    props: {\n      light: true\n    },\n    style: {\n      borderColor: theme.vars ? `rgba(${theme.vars.palette.dividerChannel} / 0.08)` : alpha(theme.palette.divider, 0.08)\n    }\n  }, {\n    props: {\n      variant: 'inset'\n    },\n    style: {\n      marginLeft: 72\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'horizontal'\n    },\n    style: {\n      marginLeft: theme.spacing(2),\n      marginRight: theme.spacing(2)\n    }\n  }, {\n    props: {\n      variant: 'middle',\n      orientation: 'vertical'\n    },\n    style: {\n      marginTop: theme.spacing(1),\n      marginBottom: theme.spacing(1)\n    }\n  }, {\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      height: '100%',\n      borderBottomWidth: 0,\n      borderRightWidth: 'thin'\n    }\n  }, {\n    props: {\n      flexItem: true\n    },\n    style: {\n      alignSelf: 'stretch',\n      height: 'auto'\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => !!ownerState.children,\n    style: {\n      display: 'flex',\n      textAlign: 'center',\n      border: 0,\n      borderTopStyle: 'solid',\n      borderLeftStyle: 'solid',\n      '&::before, &::after': {\n        content: '\"\"',\n        alignSelf: 'center'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.children && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before, &::after': {\n        width: '100%',\n        borderTop: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderTopStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.orientation === 'vertical' && ownerState.children,\n    style: {\n      flexDirection: 'column',\n      '&::before, &::after': {\n        height: '100%',\n        borderLeft: `thin solid ${(theme.vars || theme).palette.divider}`,\n        borderLeftStyle: 'inherit'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '90%'\n      },\n      '&::after': {\n        width: '10%'\n      }\n    }\n  }, {\n    props: ({\n      ownerState\n    }) => ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical',\n    style: {\n      '&::before': {\n        width: '10%'\n      },\n      '&::after': {\n        width: '90%'\n      }\n    }\n  }]\n})));\nconst DividerWrapper = styled('span', {\n  name: 'MuiDivider',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.wrapper, ownerState.orientation === 'vertical' && styles.wrapperVertical];\n  }\n})(memoTheme(({\n  theme\n}) => ({\n  display: 'inline-block',\n  paddingLeft: `calc(${theme.spacing(1)} * 1.2)`,\n  paddingRight: `calc(${theme.spacing(1)} * 1.2)`,\n  whiteSpace: 'nowrap',\n  variants: [{\n    props: {\n      orientation: 'vertical'\n    },\n    style: {\n      paddingTop: `calc(${theme.spacing(1)} * 1.2)`,\n      paddingBottom: `calc(${theme.spacing(1)} * 1.2)`\n    }\n  }]\n})));\nconst Divider = /*#__PURE__*/React.forwardRef(function Divider(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDivider'\n  });\n  const {\n    absolute = false,\n    children,\n    className,\n    orientation = 'horizontal',\n    component = children || orientation === 'vertical' ? 'div' : 'hr',\n    flexItem = false,\n    light = false,\n    role = component !== 'hr' ? 'separator' : undefined,\n    textAlign = 'center',\n    variant = 'fullWidth',\n    ...other\n  } = props;\n  const ownerState = {\n    ...props,\n    absolute,\n    component,\n    flexItem,\n    light,\n    orientation,\n    role,\n    textAlign,\n    variant\n  };\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DividerRoot, {\n    as: component,\n    className: clsx(classes.root, className),\n    role: role,\n    ref: ref,\n    ownerState: ownerState,\n    \"aria-orientation\": role === 'separator' && (component !== 'hr' || orientation === 'vertical') ? orientation : undefined,\n    ...other,\n    children: children ? /*#__PURE__*/_jsx(DividerWrapper, {\n      className: classes.wrapper,\n      ownerState: ownerState,\n      children: children\n    }) : null\n  });\n});\n\n/**\n * The following flag is used to ensure that this component isn't tabbable i.e.\n * does not get highlight/focus inside of MUI List.\n */\nif (Divider) {\n  Divider.muiSkipListHighlight = true;\n}\nprocess.env.NODE_ENV !== \"production\" ? Divider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Absolutely position the element.\n   * @default false\n   */\n  absolute: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, a vertical divider will have the correct height when used in flex container.\n   * (By default, a vertical divider will have a calculated height of `0px` if it is the child of a flex container.)\n   * @default false\n   */\n  flexItem: PropTypes.bool,\n  /**\n   * If `true`, the divider will have a lighter color.\n   * @default false\n   * @deprecated Use <Divider sx={{ opacity: 0.6 }} /> (or any opacity or color) instead. See [Migrating from deprecated APIs](https://mui.com/material-ui/migration/migrating-from-deprecated-apis/) for more details.\n   */\n  light: PropTypes.bool,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The text alignment.\n   * @default 'center'\n   */\n  textAlign: PropTypes.oneOf(['center', 'left', 'right']),\n  /**\n   * The variant to use.\n   * @default 'fullWidth'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['fullWidth', 'inset', 'middle']), PropTypes.string])\n} : void 0;\nexport default Divider;", "import { useState, useEffect } from 'react';\nimport { Region, Division, Office } from '../types/PageBuilderTypes';\nimport OfficeService from '../../../../services/officeService';\n\ninterface UseOfficeDataEnhancedReturn {\n  regions: Region[];\n  divisions: Division[];\n  offices: Office[];\n  loading: boolean;\n  error: string | null;\n  refetch: () => Promise<void>;\n  totalRecords: number;\n  approach: string;\n}\n\n/**\n * Enhanced office data hook with comprehensive pagination\n * Mirrors the successful Flutter implementation to overcome 1000-record limit\n */\nexport const useOfficeDataEnhanced = (): UseOfficeDataEnhancedReturn => {\n  const [regions, setRegions] = useState<Region[]>([]);\n  const [divisions, setDivisions] = useState<Division[]>([]);\n  const [offices, setOffices] = useState<Office[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [totalRecords, setTotalRecords] = useState<number>(0);\n  const [approach, setApproach] = useState<string>('');\n\n  const fetchOfficeData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      console.log('🏢 useOfficeDataEnhanced: Starting comprehensive office data fetch...');\n\n      // Use enhanced OfficeService with comprehensive pagination\n      const allOfficeData = await OfficeService.fetchAllOfficeData();\n      \n      console.log('✅ useOfficeDataEnhanced: Fetched office records:', allOfficeData.length, 'records');\n      setTotalRecords(allOfficeData.length);\n\n      if (allOfficeData.length === 0) {\n        console.log('⚠️ useOfficeDataEnhanced: No office records found');\n        setRegions([]);\n        setDivisions([]);\n        setOffices([]);\n        setApproach('no-data');\n        return;\n      }\n\n      // Process regions - get unique regions\n      const uniqueRegions = new Set<string>();\n      allOfficeData.forEach(office => {\n        if (office.Region && office.Region.trim()) {\n          uniqueRegions.add(office.Region.trim());\n        }\n      });\n\n      const regionsArray: Region[] = Array.from(uniqueRegions)\n        .sort()\n        .map(regionName => ({\n          id: regionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed regions:', regionsArray.length);\n\n      // Process divisions - get unique divisions with their regions\n      const uniqueDivisions = new Map<string, string>();\n      allOfficeData.forEach(office => {\n        if (office.Division && office.Division.trim() && office.Region && office.Region.trim()) {\n          uniqueDivisions.set(office.Division.trim(), office.Region.trim());\n        }\n      });\n\n      const divisionsArray: Division[] = Array.from(uniqueDivisions.entries())\n        .sort(([a], [b]) => a.localeCompare(b))\n        .map(([divisionName, regionName]) => ({\n          id: divisionName.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, ''),\n          name: divisionName,\n          region: regionName,\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed divisions:', divisionsArray.length);\n\n      // Process offices - use office name as ID for consistency\n      const officesArray: Office[] = allOfficeData\n        .filter(office => office['Office name'] && office['Office name'].trim())\n        .map(office => ({\n          id: office['Office name'], // Use office name as ID for form targeting\n          name: office['Office name'],\n          region: office.Region || '',\n          division: office.Division || '',\n          facilityId: office['Office name'], // Keep for reference\n        }));\n\n      console.log('📊 useOfficeDataEnhanced: Processed offices:', officesArray.length);\n\n      // Log comprehensive statistics\n      logOfficeStatistics(allOfficeData, regionsArray, divisionsArray, officesArray);\n\n      // Set the processed data\n      setRegions(regionsArray);\n      setDivisions(divisionsArray);\n      setOffices(officesArray);\n      setApproach('enhanced-pagination');\n\n      console.log('✅ useOfficeDataEnhanced: Data processing complete');\n\n    } catch (err) {\n      console.error('❌ useOfficeDataEnhanced: Error:', err);\n      setError('Failed to load office data. Please try again.');\n      setRegions([]);\n      setDivisions([]);\n      setOffices([]);\n      setTotalRecords(0);\n      setApproach('error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch data on mount\n  useEffect(() => {\n    fetchOfficeData();\n  }, []);\n\n  return {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    refetch: fetchOfficeData,\n    totalRecords,\n    approach,\n  };\n};\n\n/**\n * Log comprehensive statistics about the processed office data\n */\nfunction logOfficeStatistics(\n  allOfficeData: any[],\n  regions: Region[],\n  divisions: Division[],\n  offices: Office[]\n): void {\n  console.log('📊 useOfficeDataEnhanced: === COMPREHENSIVE STATISTICS ===');\n  console.log(`📊 useOfficeDataEnhanced: Raw records: ${allOfficeData.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed regions: ${regions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed divisions: ${divisions.length}`);\n  console.log(`📊 useOfficeDataEnhanced: Processed offices: ${offices.length}`);\n\n  if (offices.length > 0) {\n    // Alphabetical range\n    const sortedNames = offices.map(o => o.name).sort();\n    console.log(`📊 useOfficeDataEnhanced: Office range - First: \"${sortedNames[0]}\"`);\n    console.log(`📊 useOfficeDataEnhanced: Office range - Last: \"${sortedNames[sortedNames.length - 1]}\"`);\n\n    // Letter distribution\n    const letterCounts: { [key: string]: number } = {};\n    offices.forEach(office => {\n      const firstLetter = office.name.charAt(0).toUpperCase();\n      letterCounts[firstLetter] = (letterCounts[firstLetter] || 0) + 1;\n    });\n\n    console.log('📊 useOfficeDataEnhanced: Letter distribution:');\n    Object.keys(letterCounts).sort().forEach(letter => {\n      console.log(`📊 useOfficeDataEnhanced: ${letter}: ${letterCounts[letter]} offices`);\n    });\n\n    // Check for specific offices\n    const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n    const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n    \n    console.log(`📊 useOfficeDataEnhanced: Contains \"Tirupur division\": ${!!tirupurDivision}`);\n    console.log(`📊 useOfficeDataEnhanced: Contains \"Coimbatore division\": ${!!coimbatoreDivision}`);\n\n    if (tirupurDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Tirupur division: \"${tirupurDivision.name}\"`);\n    }\n    if (coimbatoreDivision) {\n      console.log(`📊 useOfficeDataEnhanced: Found Coimbatore division: \"${coimbatoreDivision.name}\"`);\n    }\n\n    // Region breakdown\n    if (regions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Regions found:');\n      regions.forEach(region => {\n        const regionOffices = offices.filter(o => o.region === region.name);\n        console.log(`📊 useOfficeDataEnhanced: ${region.name}: ${regionOffices.length} offices`);\n      });\n    }\n\n    // Division breakdown\n    if (divisions.length > 0) {\n      console.log('📊 useOfficeDataEnhanced: Top 10 divisions by office count:');\n      const divisionCounts = divisions.map(division => ({\n        name: division.name,\n        count: offices.filter(o => o.division === division.name).length\n      })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n      divisionCounts.forEach(division => {\n        console.log(`📊 useOfficeDataEnhanced: ${division.name}: ${division.count} offices`);\n      });\n    }\n  }\n\n  console.log('📊 useOfficeDataEnhanced: === END STATISTICS ===');\n}\n\nexport default useOfficeDataEnhanced;\n", "import React from 'react';\nimport {\n  Box,\n  Typography,\n  Card,\n  CardContent,\n  CircularProgress,\n  Alert,\n  Chip,\n  Paper,\n  List,\n  ListItem,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport { useOfficeDataEnhanced } from './hooks/useOfficeDataEnhanced';\n\n/**\n * Test component to verify enhanced office loading functionality\n * This component displays comprehensive statistics about the loaded office data\n */\nconst OfficeLoadingTest: React.FC = () => {\n  const {\n    regions,\n    divisions,\n    offices,\n    loading,\n    error,\n    totalRecords,\n    approach,\n    refetch\n  } = useOfficeDataEnhanced();\n\n  if (loading) {\n    return (\n      <Box display=\"flex\" flexDirection=\"column\" alignItems=\"center\" p={4}>\n        <CircularProgress size={60} />\n        <Typography variant=\"h6\" sx={{ mt: 2 }}>\n          Loading office data with comprehensive pagination...\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n          This may take a moment as we fetch ALL records from the database\n        </Typography>\n      </Box>\n    );\n  }\n\n  if (error) {\n    return (\n      <Box p={4}>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n        <Typography variant=\"body2\">\n          Failed to load office data. Please check the console for detailed error information.\n        </Typography>\n      </Box>\n    );\n  }\n\n  // Calculate statistics\n  const letterDistribution: { [key: string]: number } = {};\n  offices.forEach(office => {\n    const firstLetter = office.name.charAt(0).toUpperCase();\n    letterDistribution[firstLetter] = (letterDistribution[firstLetter] || 0) + 1;\n  });\n\n  const sortedOfficeNames = offices.map(o => o.name).sort();\n  const tirupurDivision = offices.find(o => o.name.toLowerCase().includes('tirupur division'));\n  const coimbatoreDivision = offices.find(o => o.name.toLowerCase().includes('coimbatore division'));\n\n  // Top regions by office count\n  const regionCounts = regions.map(region => ({\n    name: region.name,\n    count: offices.filter(o => o.region === region.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 5);\n\n  // Top divisions by office count\n  const divisionCounts = divisions.map(division => ({\n    name: division.name,\n    count: offices.filter(o => o.division === division.name).length\n  })).sort((a, b) => b.count - a.count).slice(0, 10);\n\n  return (\n    <Box p={4}>\n      <Typography variant=\"h4\" gutterBottom>\n        Office Loading Test - Enhanced Pagination\n      </Typography>\n      \n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\n        This test verifies that the enhanced office loading system can fetch ALL records from the Supabase database,\n        overcoming the default 1000-record pagination limit.\n      </Typography>\n\n      {/* Summary Cards */}\n      <Box display=\"flex\" gap={3} sx={{ mb: 4, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Total Records\n              </Typography>\n              <Typography variant=\"h4\">\n                {totalRecords.toLocaleString()}\n              </Typography>\n              <Chip\n                label={approach}\n                size=\"small\"\n                color={totalRecords > 1000 ? \"success\" : \"warning\"}\n                sx={{ mt: 1 }}\n              />\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Regions\n              </Typography>\n              <Typography variant=\"h4\">\n                {regions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Divisions\n              </Typography>\n              <Typography variant=\"h4\">\n                {divisions.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"250px\">\n          <Card>\n            <CardContent>\n              <Typography variant=\"h6\" color=\"primary\">\n                Offices\n              </Typography>\n              <Typography variant=\"h4\">\n                {offices.length}\n              </Typography>\n            </CardContent>\n          </Card>\n        </Box>\n      </Box>\n\n      {/* Verification Results */}\n      <Box display=\"flex\" gap={3} sx={{ flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Verification Results\n            </Typography>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Records exceed 1000 limit:\n              </Typography>\n              <Chip\n                label={totalRecords > 1000 ? \"✅ YES\" : \"❌ NO\"}\n                color={totalRecords > 1000 ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Alphabetical Range:\n              </Typography>\n              <Typography variant=\"body2\">\n                First: \"{sortedOfficeNames[0] || 'N/A'}\"\n              </Typography>\n              <Typography variant=\"body2\">\n                Last: \"{sortedOfficeNames[sortedOfficeNames.length - 1] || 'N/A'}\"\n              </Typography>\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Tirupur Division Found:\n              </Typography>\n              <Chip\n                label={tirupurDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={tirupurDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {tirupurDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{tirupurDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n\n            <Box sx={{ mb: 2 }}>\n              <Typography variant=\"subtitle2\">\n                Coimbatore Division Found:\n              </Typography>\n              <Chip\n                label={coimbatoreDivision ? \"✅ YES\" : \"❌ NO\"}\n                color={coimbatoreDivision ? \"success\" : \"error\"}\n                size=\"small\"\n              />\n              {coimbatoreDivision && (\n                <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                  \"{coimbatoreDivision.name}\"\n                </Typography>\n              )}\n            </Box>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Letter Distribution\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              {Object.keys(letterDistribution).sort().map(letter => (\n                <Box key={letter} display=\"flex\" justifyContent=\"space-between\" sx={{ mb: 1 }}>\n                  <Typography variant=\"body2\">{letter}:</Typography>\n                  <Typography variant=\"body2\">{letterDistribution[letter]} offices</Typography>\n                </Box>\n              ))}\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      <Box display=\"flex\" gap={3} sx={{ mt: 3, flexWrap: 'wrap' }}>\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 5 Regions by Office Count\n            </Typography>\n            <List dense>\n              {regionCounts.map((region, index) => (\n                <React.Fragment key={region.name}>\n                  <ListItem>\n                    <ListItemText\n                      primary={region.name}\n                      secondary={`${region.count} offices`}\n                    />\n                  </ListItem>\n                  {index < regionCounts.length - 1 && <Divider />}\n                </React.Fragment>\n              ))}\n            </List>\n          </Paper>\n        </Box>\n\n        <Box flex=\"1\" minWidth=\"400px\">\n          <Paper sx={{ p: 3 }}>\n            <Typography variant=\"h6\" gutterBottom>\n              Top 10 Divisions by Office Count\n            </Typography>\n            <Box sx={{ maxHeight: 300, overflow: 'auto' }}>\n              <List dense>\n                {divisionCounts.map((division, index) => (\n                  <React.Fragment key={division.name}>\n                    <ListItem>\n                      <ListItemText\n                        primary={division.name}\n                        secondary={`${division.count} offices`}\n                      />\n                    </ListItem>\n                    {index < divisionCounts.length - 1 && <Divider />}\n                  </React.Fragment>\n                ))}\n              </List>\n            </Box>\n          </Paper>\n        </Box>\n      </Box>\n\n      {/* Success Message */}\n      {totalRecords > 1000 && (\n        <Alert severity=\"success\" sx={{ mt: 3 }}>\n          <Typography variant=\"h6\">\n            🎉 Success! Enhanced Office Loading is Working\n          </Typography>\n          <Typography variant=\"body2\">\n            The system successfully loaded {totalRecords.toLocaleString()} office records, \n            which exceeds the default 1000-record Supabase limit. This confirms that the \n            comprehensive pagination solution is working correctly.\n          </Typography>\n        </Alert>\n      )}\n    </Box>\n  );\n};\n\nexport default OfficeLoadingTest;\n", "/**\n * MMU Setup and Database Cleanup Utility\n * This utility helps set up MMU properly and clean up undefined entries\n */\n\nimport { db } from '../../../../config/firebase';\nimport { collection, getDocs, doc, setDoc, deleteDoc, updateDoc } from 'firebase/firestore';\n\ninterface CategoryData {\n  id: string;\n  title: string;\n  path: string;\n  parentId: string | null;\n  icon?: string;\n  color?: string;\n  lastUpdated: string;\n  isPage: boolean;\n  pageId: string;\n}\n\n/**\n * Clean up undefined entries and ensure MMU is properly set up\n */\nexport const setupMMUAndCleanup = async (): Promise<{\n  cleaned: number;\n  mmuSetup: boolean;\n  nestedReports: string[];\n}> => {\n  try {\n    console.log('🔧 Starting MMU setup and database cleanup...');\n    \n    // Get all categories\n    const categoriesSnapshot = await getDocs(collection(db, 'categories'));\n    const categories = categoriesSnapshot.docs.map(doc => ({\n      id: doc.id,\n      ...doc.data()\n    })) as CategoryData[];\n\n    let cleanedCount = 0;\n    let mmuExists = false;\n    let mmuId = '';\n\n    // First pass: Clean up undefined entries and find/fix MMU\n    for (const category of categories) {\n      // Check if this is an undefined entry\n      if (!category.title || category.title.trim() === '' || category.title === 'undefined') {\n        // Check if this should be MMU\n        if (category.id === 'mmu' || category.id.toLowerCase().includes('mmu')) {\n          // Fix MMU entry\n          await updateDoc(doc(db, 'categories', category.id), {\n            title: 'MMU',\n            path: '/categories/mmu',\n            parentId: null,\n            icon: 'FaTruck',\n            color: '#28a745',\n            lastUpdated: new Date().toISOString(),\n            isPage: false,\n            pageId: category.id\n          });\n          mmuExists = true;\n          mmuId = category.id;\n          console.log(`✅ Fixed MMU entry: ${category.id}`);\n        } else {\n          // Delete undefined entries that are not MMU\n          await deleteDoc(doc(db, 'categories', category.id));\n          cleanedCount++;\n          console.log(`🗑️ Deleted undefined entry: ${category.id}`);\n        }\n      } else if (category.title === 'MMU') {\n        mmuExists = true;\n        mmuId = category.id;\n      }\n    }\n\n    // Create MMU if it doesn't exist\n    if (!mmuExists) {\n      mmuId = 'mmu';\n      await setDoc(doc(db, 'categories', mmuId), {\n        id: mmuId,\n        title: 'MMU',\n        path: '/categories/mmu',\n        parentId: null,\n        icon: 'FaTruck',\n        color: '#28a745',\n        lastUpdated: new Date().toISOString(),\n        isPage: false,\n        pageId: mmuId\n      });\n      console.log('✅ Created MMU category');\n    }\n\n    // Create default nested reports under MMU\n    const nestedReports = await createMMUNestedReports(mmuId);\n\n    return {\n      cleaned: cleanedCount,\n      mmuSetup: true,\n      nestedReports\n    };\n\n  } catch (error) {\n    console.error('❌ Error in MMU setup and cleanup:', error);\n    throw error;\n  }\n};\n\n/**\n * Create default nested reports under MMU\n */\nexport const createMMUNestedReports = async (mmuParentId: string): Promise<string[]> => {\n  const nestedReports = [\n    {\n      id: 'vehicle-maintenance',\n      title: 'Vehicle Maintenance',\n      description: 'Vehicle maintenance and service records'\n    },\n    {\n      id: 'fuel-management',\n      title: 'Fuel Management',\n      description: 'Fuel consumption and cost tracking'\n    },\n    {\n      id: 'driver-management',\n      title: 'Driver Management',\n      description: 'Driver assignments and performance tracking'\n    },\n    {\n      id: 'route-optimization',\n      title: 'Route Optimization',\n      description: 'Route planning and optimization reports'\n    }\n  ];\n\n  const createdReports: string[] = [];\n\n  for (const report of nestedReports) {\n    try {\n      // Check if report already exists\n      const existingSnapshot = await getDocs(collection(db, 'categories'));\n      const exists = existingSnapshot.docs.some(doc => doc.id === report.id);\n\n      if (!exists) {\n        await setDoc(doc(db, 'categories', report.id), {\n          id: report.id,\n          title: report.title,\n          path: `/categories/${mmuParentId}/${report.id}`,\n          parentId: mmuParentId,\n          icon: 'FaFileAlt',\n          color: '#007bff',\n          lastUpdated: new Date().toISOString(),\n          isPage: true,\n          pageId: report.id\n        });\n        createdReports.push(report.title);\n        console.log(`✅ Created nested report: ${report.title}`);\n      }\n    } catch (error) {\n      console.error(`❌ Error creating nested report ${report.title}:`, error);\n    }\n  }\n\n  return createdReports;\n};\n\n/**\n * Add a custom nested report under MMU\n */\nexport const addCustomMMUReport = async (\n  reportId: string,\n  reportTitle: string,\n  mmuParentId: string = 'mmu'\n): Promise<boolean> => {\n  try {\n    // Sanitize the report ID\n    const sanitizedId = reportId.toLowerCase().replace(/\\s+/g, '-').replace(/[^a-z0-9-]/g, '');\n    \n    await setDoc(doc(db, 'categories', sanitizedId), {\n      id: sanitizedId,\n      title: reportTitle,\n      path: `/categories/${mmuParentId}/${sanitizedId}`,\n      parentId: mmuParentId,\n      icon: 'FaFileAlt',\n      color: '#007bff',\n      lastUpdated: new Date().toISOString(),\n      isPage: true,\n      pageId: sanitizedId\n    });\n\n    console.log(`✅ Created custom MMU report: ${reportTitle}`);\n    return true;\n  } catch (error) {\n    console.error(`❌ Error creating custom MMU report:`, error);\n    return false;\n  }\n};\n", "import React, { useState } from 'react';\nimport { setupMMUAndCleanup, addCustomMMUReport } from './business/utils/mmuSetup';\n\nconst MMUSetupTool: React.FC = () => {\n  const [isProcessing, setIsProcessing] = useState(false);\n  const [message, setMessage] = useState<string>('');\n  const [messageType, setMessageType] = useState<'success' | 'error' | 'info'>('info');\n  const [customReportId, setCustomReportId] = useState('');\n  const [customReportTitle, setCustomReportTitle] = useState('');\n\n  const showMessage = (msg: string, type: 'success' | 'error' | 'info') => {\n    setMessage(msg);\n    setMessageType(type);\n    setTimeout(() => setMessage(''), 5000);\n  };\n\n  const handleSetupMMU = async () => {\n    setIsProcessing(true);\n    try {\n      const result = await setupMMUAndCleanup();\n      \n      let successMsg = `✅ MMU Setup Complete!\\n`;\n      successMsg += `🗑️ Cleaned ${result.cleaned} undefined entries\\n`;\n      successMsg += `📁 MMU category is properly configured\\n`;\n      \n      if (result.nestedReports.length > 0) {\n        successMsg += `📄 Created nested reports: ${result.nestedReports.join(', ')}\\n`;\n      }\n      \n      successMsg += `\\n🔄 Please refresh the page to see changes.`;\n      \n      showMessage(successMsg, 'success');\n      \n      // Auto-refresh after 3 seconds\n      setTimeout(() => {\n        window.location.reload();\n      }, 3000);\n      \n    } catch (error) {\n      console.error('Setup failed:', error);\n      showMessage(`❌ Setup failed: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const handleAddCustomReport = async () => {\n    if (!customReportId.trim() || !customReportTitle.trim()) {\n      showMessage('❌ Please enter both Report ID and Title', 'error');\n      return;\n    }\n\n    setIsProcessing(true);\n    try {\n      const success = await addCustomMMUReport(customReportId, customReportTitle);\n      \n      if (success) {\n        showMessage(`✅ Successfully created \"${customReportTitle}\" under MMU!`, 'success');\n        setCustomReportId('');\n        setCustomReportTitle('');\n        \n        // Auto-refresh after 2 seconds\n        setTimeout(() => {\n          window.location.reload();\n        }, 2000);\n      } else {\n        showMessage('❌ Failed to create custom report', 'error');\n      }\n    } catch (error) {\n      console.error('Failed to add custom report:', error);\n      showMessage(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`, 'error');\n    } finally {\n      setIsProcessing(false);\n    }\n  };\n\n  const getMessageStyle = () => {\n    const baseStyle = {\n      padding: '15px',\n      borderRadius: '4px',\n      marginBottom: '20px',\n      whiteSpace: 'pre-line' as const,\n      fontFamily: 'monospace',\n      fontSize: '14px'\n    };\n\n    switch (messageType) {\n      case 'success':\n        return { ...baseStyle, backgroundColor: '#d4edda', color: '#155724', border: '1px solid #c3e6cb' };\n      case 'error':\n        return { ...baseStyle, backgroundColor: '#f8d7da', color: '#721c24', border: '1px solid #f5c6cb' };\n      default:\n        return { ...baseStyle, backgroundColor: '#d1ecf1', color: '#0c5460', border: '1px solid #bee5eb' };\n    }\n  };\n\n  return (\n    <div style={{\n      backgroundColor: '#fff3cd',\n      border: '1px solid #ffeaa7',\n      borderRadius: '8px',\n      padding: '20px',\n      margin: '20px 0'\n    }}>\n      <h3 style={{ color: '#856404', marginBottom: '15px' }}>\n        🚛 MMU Setup & Management Tool\n      </h3>\n      \n      <p style={{ color: '#856404', marginBottom: '20px' }}>\n        This tool will fix the \"undefined\" issues and properly set up the MMU (Mail Motor Unit) category with nested reports.\n      </p>\n\n      {message && (\n        <div style={getMessageStyle()}>\n          {message}\n        </div>\n      )}\n\n      {/* Main Setup Section */}\n      <div style={{ marginBottom: '30px' }}>\n        <h4 style={{ color: '#856404', marginBottom: '10px' }}>\n          🔧 Setup MMU & Clean Database\n        </h4>\n        <p style={{ color: '#856404', fontSize: '14px', marginBottom: '15px' }}>\n          This will:\n          <br />• Remove all undefined entries from the database\n          <br />• Create or fix the MMU category\n          <br />• Add default nested reports (Vehicle Maintenance, Fuel Management, etc.)\n        </p>\n        <button\n          onClick={handleSetupMMU}\n          disabled={isProcessing}\n          style={{\n            padding: '12px 24px',\n            backgroundColor: isProcessing ? '#6c757d' : '#28a745',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: isProcessing ? 'not-allowed' : 'pointer',\n            fontSize: '16px',\n            fontWeight: 'bold'\n          }}\n        >\n          {isProcessing ? '🔄 Setting up MMU...' : '🚀 Setup MMU & Clean Database'}\n        </button>\n      </div>\n\n      {/* Custom Report Section */}\n      <div style={{ borderTop: '1px solid #ffeaa7', paddingTop: '20px' }}>\n        <h4 style={{ color: '#856404', marginBottom: '10px' }}>\n          ➕ Add Custom Nested Report to MMU\n        </h4>\n        <p style={{ color: '#856404', fontSize: '14px', marginBottom: '15px' }}>\n          Add a custom report under the MMU category:\n        </p>\n        \n        <div style={{ display: 'flex', gap: '10px', marginBottom: '15px', flexWrap: 'wrap' }}>\n          <input\n            type=\"text\"\n            placeholder=\"Report ID (e.g., 'vehicle-tracking')\"\n            value={customReportId}\n            onChange={(e) => setCustomReportId(e.target.value.toLowerCase().replace(/\\s+/g, '-'))}\n            style={{\n              padding: '8px 12px',\n              border: '1px solid #ced4da',\n              borderRadius: '4px',\n              minWidth: '200px',\n              flex: '1'\n            }}\n          />\n          <input\n            type=\"text\"\n            placeholder=\"Report Title (e.g., 'Vehicle Tracking')\"\n            value={customReportTitle}\n            onChange={(e) => setCustomReportTitle(e.target.value)}\n            style={{\n              padding: '8px 12px',\n              border: '1px solid #ced4da',\n              borderRadius: '4px',\n              minWidth: '200px',\n              flex: '1'\n            }}\n          />\n        </div>\n        \n        <button\n          onClick={handleAddCustomReport}\n          disabled={isProcessing || !customReportId.trim() || !customReportTitle.trim()}\n          style={{\n            padding: '10px 20px',\n            backgroundColor: isProcessing ? '#6c757d' : '#007bff',\n            color: 'white',\n            border: 'none',\n            borderRadius: '4px',\n            cursor: (isProcessing || !customReportId.trim() || !customReportTitle.trim()) ? 'not-allowed' : 'pointer',\n            fontSize: '14px',\n            fontWeight: 'bold'\n          }}\n        >\n          {isProcessing ? '➕ Adding...' : '➕ Add Custom Report'}\n        </button>\n      </div>\n\n      <div style={{ \n        marginTop: '20px', \n        padding: '10px', \n        backgroundColor: '#e2e3e5', \n        borderRadius: '4px',\n        fontSize: '12px',\n        color: '#6c757d'\n      }}>\n        <strong>Note:</strong> After using this tool, the page will automatically refresh to show the changes. \n        The MMU category will appear properly in the dropdown without any \"undefined\" entries.\n      </div>\n    </div>\n  );\n};\n\nexport default MMUSetupTool;\n", "import React, { useEffect, useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { doc, getDoc } from 'firebase/firestore';\nimport { db } from '../../config/firebase';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Sidebar from '../shared/Sidebar';\nimport StatsCards from '../shared/StatsCards';\nimport PageBuilder from './business/PageBuilder';\nimport OfficeLoadingTest from './business/OfficeLoadingTest';\nimport MMUSetupTool from './MMUSetupTool';\n\nconst AdminPage: React.FC = () => {\n  const { currentUser } = useAuth();\n  const navigate = useNavigate();\n  const [userData, setUserData] = useState<any>(null);\n  const [showOfficeTest, setShowOfficeTest] = useState<boolean>(false);\n\n  useEffect(() => {\n    const fetchUserData = async () => {\n      if (currentUser) {\n        const userRef = doc(db, 'employees', currentUser.uid);\n        const userSnap = await getDoc(userRef);\n        if (userSnap.exists()) {\n          setUserData(userSnap.data());\n        }\n      }\n    };\n    fetchUserData();\n  }, [currentUser]);\n\n  return (\n    <div className=\"dashboard-container\">\n      <Sidebar userData={userData} />\n      <div className=\"main-content\">\n        <div className=\"page-title\">\n          Admin Dashboard\n          <button\n            onClick={() => setShowOfficeTest(!showOfficeTest)}\n            style={{\n              marginLeft: '20px',\n              padding: '8px 16px',\n              backgroundColor: showOfficeTest ? '#dc3545' : '#007bff',\n              color: 'white',\n              border: 'none',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '14px'\n            }}\n          >\n            {showOfficeTest ? 'Hide Office Test' : 'Show Office Loading Test'}\n          </button>\n        </div>\n        <StatsCards />\n        <MMUSetupTool />\n        {showOfficeTest ? <OfficeLoadingTest /> : <PageBuilder />}\n      </div>\n    </div>\n  );\n};\n\nexport default AdminPage;"], "names": ["getCardUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "CardRoot", "styled", "Paper", "name", "overflow", "React", "inProps", "ref", "props", "useDefaultProps", "className", "raised", "other", "ownerState", "classes", "composeClasses", "root", "useUtilityClasses", "_jsx", "clsx", "elevation", "undefined", "getListItemTextUtilityClass", "stats", "title", "value", "StatsCards", "children", "map", "stat", "index", "_jsxs", "getDividerUtilityClass", "getCardContentUtilityClass", "CardContentRoot", "padding", "paddingBottom", "component", "as", "_ref", "isOpen", "onClose", "onClick", "e", "stopPropagation", "isMainCard", "cardId", "allCategories", "card", "find", "c", "id", "parentId", "isLeafCard", "some", "organizeCards", "list", "roots", "for<PERSON>ach", "item", "_map$item$parentId$ch", "push", "getAllDescendantIds", "descendants", "filter", "child", "concat", "useCardManagement", "categories", "setCategories", "selected<PERSON><PERSON>", "setSelectedCard", "newCardId", "setNewCardId", "newCardTitle", "setNewCardTitle", "actionType", "setActionType", "setIsLoading", "setError", "setSuccess", "setShowConfirmModal", "setIsAddingNewCard", "setPageConfig", "setFields", "setEditingCard", "setShowEditModal", "setCardToDelete", "setShowDeleteConfirmModal", "fetchCategories", "useCallback", "async", "fetchedCategories", "getDocs", "collection", "db", "docs", "doc", "data", "err", "console", "error", "handleAddNewCard", "doc<PERSON>ef", "getDoc", "exists", "checkDuplicateId", "handleConfirmCreate", "_categories$find", "parentIdToSet", "newPath", "path", "replace", "cardRef", "icon", "generatedIcon", "color", "generatedColor", "hash", "split", "reduce", "acc", "char", "charCodeAt", "icons", "FaFolder", "FaFileAlt", "FaCog", "FaFolderOpen", "colors", "length", "generateCardStyle", "setDoc", "lastUpdated", "Date", "toISOString", "fields", "isPage", "pageId", "setTimeout", "handleEditCard", "handleUpdateCard", "editingCard", "updateDoc", "handleDeleteClick", "handleConfirmDelete", "batch", "writeBatch", "allDescendants", "idsToDelete", "delete", "commit", "onCardChange", "onActionChange", "isLoading", "onCreateAction", "onWebPageAction", "renderCardOptions", "cards", "level", "arguments", "flatMap", "displayTitle", "trim", "toLowerCase", "includes", "warn", "style", "paddingLeft", "repeat", "onChange", "newSelectedCard", "target", "disabled", "newAction", "_Fragment", "onEditCard", "onDeleteCard", "selectedCate<PERSON><PERSON>", "FaEdit", "FaTrash", "_field$options2", "field", "onUpdate", "onRemove", "handleOptionChange", "optIndex", "key", "newOptions", "options", "handleDefaultValueChange", "type", "newDefaultValue", "checked", "defaultValue", "label", "htmlFor", "placeholder", "required", "min", "parseFloat", "max", "opt", "_field$options", "_", "i", "removeOption", "addOption", "String", "Boolean", "Array", "isArray", "join", "s", "buttonText", "sectionTitle", "pageConfig", "onAddField", "onUpdateField", "onRemoveField", "onSave", "onPreview", "loading", "FieldConfigItem", "FaPlus", "FaSave", "REPORT_FREQUENCIES", "<PERSON><PERSON><PERSON><PERSON>", "setIsOpen", "useState", "dropdownRef", "useRef", "useEffect", "handleClickOutside", "event", "current", "contains", "document", "addEventListener", "removeEventListener", "isAllSelected", "isIndeterminate", "backgroundColor", "borderColor", "getDisplayText", "selectedOption", "option", "maxHeight", "overflowY", "input", "indeterminate", "handleSelectAll", "handleCheckboxChange", "optionId", "selectedRegions", "selectedDivisions", "selectedOffices", "selectedFrequency", "onRegionsChange", "onDivisionsChange", "onOfficesChange", "onFrequencyChange", "regions", "divisions", "offices", "refetch", "useOfficeDataSimple", "setRegions", "setDivisions", "setOffices", "setLoading", "fetchOfficeData", "log", "allData", "OfficeService", "fetchAllOfficeData", "distinctRegions", "row", "Region", "region", "array", "indexOf", "sort", "regionsArray", "regionName", "distinctDivisions", "division", "Division", "findIndex", "x", "a", "b", "localeCompare", "divisionsArray", "officesArray", "facilityId", "useOfficeData", "selectedRegionNames", "regionId", "_regions$find", "r", "availableDivisions", "selectedDivisionNames", "divisionId", "_divisions$find", "d", "availableOffices", "office", "validDivisions", "validOffices", "officeId", "o", "role", "CheckboxDropdown", "frequency", "STORAGE_KEY", "PageBuilder", "_state$categories$fin", "_state$categories$fin2", "state", "usePageBuilderState", "availableDynamicFields", "setAvailableDynamicFields", "success", "isAddingNewCard", "showConfirmModal", "showEditModal", "cardToDelete", "showDeleteConfirmModal", "isPreviewOpen", "setIsPreviewOpen", "previewContent", "setPreviewContent", "setSelectedRegions", "setSelectedDivisions", "setSelectedOffices", "setSelectedFrequency", "cardManagement", "pageConfiguration", "fetchDynamicFormFields", "formId", "formConfigRef", "formConfigSnap", "formConfigData", "loadPageConfig", "docSnap", "supabasePageService", "supabaseError", "savedRegions", "selectedRegion", "savedDivisions", "selectedDivision", "savedOffices", "selectedOffice", "savedFrequency", "addField", "newField", "now", "addFieldFromDynamic", "dynamicField", "columns", "buttonType", "onClickAction", "updateField", "updatedField", "<PERSON><PERSON><PERSON>s", "removeField", "handleSave", "cleanedFields", "cleanedField", "updatedPageConfig", "savePromises", "catch", "Error", "message", "savePageConfig", "Promise", "all", "handlePreview", "alert", "generatedPreview", "fieldHtml", "usePageConfiguration", "savedSelections", "loadDropdownSelections", "stored", "localStorage", "getItem", "JSON", "parse", "selections", "setItem", "stringify", "saveDropdownSelections", "CardSelector", "cardIsLeaf", "cardIsMain", "action", "handleCreateAction", "handleWebPageAction", "Modal", "CardManagement", "ReportConfiguration", "previousRegions", "previousDivisions", "PageBuilderContent", "dangerouslySetInnerHTML", "__html", "getListItemUtilityClass", "getListItemSecondaryActionClassesUtilityClass", "ListItemSecondaryActionRoot", "overridesResolver", "styles", "disableGutters", "position", "right", "top", "transform", "variants", "ListItemSecondaryAction", "context", "ListContext", "slots", "mui<PERSON><PERSON>", "ListItemRoot", "dense", "alignItems", "alignItemsFlexStart", "divider", "gutters", "disablePadding", "hasSecondaryAction", "secondaryAction", "memoTheme", "theme", "display", "justifyContent", "textDecoration", "width", "boxSizing", "textAlign", "_ref2", "paddingTop", "_ref3", "_ref4", "paddingRight", "_ref5", "_ref6", "listItemButtonClasses", "_ref7", "borderBottom", "vars", "palette", "backgroundClip", "_ref8", "button", "transition", "transitions", "create", "duration", "shortest", "hover", "_ref9", "ListItemContainer", "childrenProp", "componentProp", "components", "componentsProps", "ContainerComponent", "ContainerProps", "ContainerClassName", "slotProps", "childContext", "listItemRef", "toArray", "isMuiElement", "container", "handleRef", "useForkRef", "Root", "rootProps", "componentProps", "Component", "Provider", "isHostComponent", "pop", "ListItemTextRoot", "listItemTextClasses", "primary", "secondary", "inset", "multiline", "flex", "min<PERSON><PERSON><PERSON>", "marginTop", "marginBottom", "typographyClasses", "disableTypography", "primaryProp", "primaryTypographyProps", "secondaryProp", "secondaryTypographyProps", "externalForwardedProps", "RootSlot", "rootSlotProps", "useSlot", "elementType", "PrimarySlot", "primarySlotProps", "Typography", "SecondarySlot", "secondarySlotProps", "variant", "DividerRoot", "absolute", "light", "orientation", "vertical", "flexItem", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "withChildrenVertical", "textAlignRight", "textAlignLeft", "margin", "flexShrink", "borderWidth", "borderStyle", "borderBottomWidth", "bottom", "left", "dividerChannel", "alpha", "marginLeft", "spacing", "marginRight", "height", "borderRightWidth", "alignSelf", "border", "borderTopStyle", "borderLeftStyle", "content", "borderTop", "flexDirection", "borderLeft", "DividerWrapper", "wrapper", "wrapperVertical", "whiteSpace", "Divider", "muiSkipListHighlight", "useOfficeDataEnhanced", "totalRecords", "setTotalRecords", "approach", "setApproach", "allOfficeData", "uniqueRegions", "Set", "add", "from", "uniqueDivisions", "Map", "set", "entries", "divisionName", "sortedNames", "letterCounts", "firstLetter", "char<PERSON>t", "toUpperCase", "Object", "keys", "letter", "tirupurDivision", "coimbatoreDivision", "regionOffices", "count", "slice", "logOfficeStatistics", "OfficeLoadingTest", "Box", "p", "CircularProgress", "size", "sx", "mt", "<PERSON><PERSON>", "severity", "mb", "letterDistribution", "sortedOfficeNames", "regionCounts", "divisionCounts", "gutterBottom", "paragraph", "gap", "flexWrap", "Card", "<PERSON><PERSON><PERSON><PERSON>", "toLocaleString", "Chip", "List", "ListItem", "ListItemText", "createMMUNestedReports", "nestedReports", "description", "createdReports", "report", "existingSnapshot", "mmuParentId", "MMUSetupTool", "isProcessing", "setIsProcessing", "setMessage", "messageType", "setMessageType", "customReportId", "setCustomReportId", "customReportTitle", "setCustomReportTitle", "showMessage", "msg", "borderRadius", "getMessageStyle", "baseStyle", "fontFamily", "fontSize", "result", "cleanedCount", "mmuExists", "mmuId", "category", "deleteDoc", "cleaned", "mmuSetup", "setupMMUAndCleanup", "successMsg", "window", "location", "reload", "cursor", "fontWeight", "reportId", "reportTitle", "sanitizedId", "addCustomMMUReport", "AdminPage", "currentUser", "useAuth", "userData", "setUserData", "useNavigate", "showOfficeTest", "setShowOfficeTest", "userRef", "uid", "userSnap", "fetchUserData", "Sidebar"], "sourceRoot": ""}