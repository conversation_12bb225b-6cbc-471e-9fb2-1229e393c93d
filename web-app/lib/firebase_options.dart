// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - ',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - ',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCoBTaAwoQoR5B6FipxgyCF70ukN2rN2A0',
    appId: '1:88739308700:web:66a8e34809583e53c1b959',
    messagingSenderId: '88739308700',
    projectId: 'employeemanagementsystem-6e893',
    authDomain: 'employeemanagementsystem-6e893.firebaseapp.com',
    storageBucket: 'employeemanagementsystem-6e893.firebasestorage.app',
    // measurementId: 'YOUR_WEB_MEASUREMENT_ID', // Optional
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDMWxZboR9ugtAk-tZom2zFt2W-9wbeeGU',
    appId: 'Y1:88739308700:android:26cdd35a93583d3dc1b959',
    messagingSenderId: '88739308700',
    projectId: 'employeemanagementsystem-6e893',
    storageBucket: 'employeemanagementsystem-6e893.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDhWujOga5S0ImuN3cz6lB-mmiz8g5qG_c',
    appId: '1:88739308700:ios:0a667e92da9b9715c1b959',
    messagingSenderId: '88739308700',
    projectId: 'employeemanagementsystem-6e893',
    storageBucket: 'employeemanagementsystem-6e893.firebasestorage.app',
    iosBundleId: 'com.example.mobileapp',
    // androidClientId: 'YOUR_ANDROID_CLIENT_ID_FOR_IOS', // Optional, if using Google Sign-In
    // iosClientId: 'YOUR_IOS_CLIENT_ID', // Optional, if using Google Sign-In
  );

  
}