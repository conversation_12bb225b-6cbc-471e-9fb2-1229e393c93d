.dynamic-form {
  background: #ffffff;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.form-title {
  font-size: 1.8em;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-weight: 600;
  margin-bottom: 8px;
  color: #555;
  font-size: 0.95em;
}

.form-control,
.form-select,
.file-input {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1em;
  box-sizing: border-box;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.form-control:focus,
.form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  outline: none;
}

.form-control.is-invalid,
.file-input.is-invalid {
  border-color: #dc3545;
}

.error-message {
  color: #dc3545;
  font-size: 0.85em;
  margin-top: 5px;
}

.required-asterisk {
  color: #dc3545;
  margin-left: 4px;
}

/* Radio and Checkbox styling */
.radio-group, .checkbox-group {
  display: flex;
  flex-direction: column; /* Stack options vertically */
  gap: 10px;
}

.radio-item, .checkbox-item {
  display: flex;
  align-items: center;
  gap: 10px; /* Space between radio/checkbox and its label */
}

.radio-item label, .checkbox-item label, .checkbox-label-outside {
  font-weight: normal; /* Labels for options are normal weight */
  margin-bottom: 0; /* Remove default margin from .form-label */
  font-size: 0.9em;
}

.radio-item input[type="radio"],
.checkbox-item input[type="checkbox"] {
  margin-right: 5px; /* Keep some space if gap is not enough */
  width: auto; /* Override width: 100% from .form-control */
  accent-color: #007bff; /* Modern way to color radio/checkbox */
}

.form-group-checkbox .form-label {
    margin-bottom: 0; /* Adjust if label is outside */
}

/* Section Title */
.section-title {
  font-size: 1.3em;
  font-weight: 600;
  margin-top: 25px; /* More space above a new section */
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
  color: #333;
}

/* File Upload */
.file-upload {
  /* Basic styling, can be enhanced */
}

.file-label {
  display: inline-block;
  padding: 10px 15px;
  background-color: #f8f9fa;
  border: 1px solid #ced4da;
  border-radius: 6px;
  cursor: pointer;
  text-align: center;
  font-size: 0.95em;
  transition: background-color 0.2s ease-in-out;
}

.file-label:hover {
  background-color: #e9ecef;
}

/* Form Buttons */
.form-buttons {
  display: flex;
  gap: 12px;
  margin-top: 20px;
  justify-content: center;
}

.submit-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.05em;
  font-weight: 500;
  flex: 1;
  max-width: 200px;
  transition: background-color 0.2s ease-in-out;
}

.submit-button:hover {
  background-color: #0056b3;
}

.submit-button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.clear-button {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.05em;
  font-weight: 500;
  flex: 1;
  max-width: 200px;
  transition: background-color 0.2s ease-in-out;
}

.clear-button:hover {
  background-color: #545b62;
}

.clear-button:disabled {
  background-color: #adb5bd;
  cursor: not-allowed;
}

/* Legacy Submit Button (for backward compatibility) */
.form-submit-button.btn-primary {
  padding: 12px 25px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1.05em;
  font-weight: 500;
  transition: background-color 0.2s ease-in-out;
  display: block; /* Make it block to center it easily if needed */
  margin: 20px auto 0; /* Center button */
}

.form-submit-button.btn-primary:hover {
  background-color: #0056b3;
}

.form-loading, .form-error, .form-no-fields {
  padding: 20px;
  text-align: center;
  font-size: 1.1em;
  color: #555;
  background-color: #f9f9f9;
  border: 1px dashed #ddd;
  border-radius: 8px;
  margin-top: 20px;
}

.form-error {
  color: #dc3545;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dynamic-form {
    padding: 20px;
  }
  .form-title {
    font-size: 1.5em;
  }
  .form-buttons {
    flex-direction: column;
    gap: 8px;
  }
  .submit-button,
  .clear-button {
    max-width: none;
    width: 100%;
  }
  .form-submit-button.btn-primary {
    width: 100%;
  }
}

/* Calculated Field Styles */
.calculated-field {
  background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
  border: 2px solid #2196f3;
  border-radius: 8px;
  padding: 15px;
  margin: 10px 0;
  position: relative;
  overflow: hidden;
}

.calculated-field::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #2196f3, #9c27b0, #2196f3);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.calculated-value-display {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
}

.calculated-icon {
  font-size: 24px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.calculated-value {
  font-size: 20px;
  font-weight: bold;
  color: #1976d2;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  min-width: 100px;
  text-align: center;
  font-family: 'Courier New', monospace;
}

.calculated-info {
  display: block;
  font-style: italic;
  color: #666;
  margin-top: 5px;
}

/* Calculated Field Configuration in Admin */
.calculated-field-config {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 15px;
  margin: 10px 0;
}

.calculated-field-config .alert {
  margin-bottom: 15px;
}

.calculated-field-config .form-group {
  margin-bottom: 15px;
}

.calculated-field-config .row .form-group {
  margin-bottom: 10px;
}